FROM eclipse-temurin:21-jdk
LABEL org.opencontainers.image.authors="Doorbit Team"

USER root

RUN apt-get update && apt-get install -y tzdata ca-certificates passwd && update-ca-certificates && \
    cp /usr/share/zoneinfo/Europe/Berlin /etc/localtime && \
    echo "Europe/Berlin" > /etc/timezone && \
    groupadd --system dbt && useradd --system -g dbt dbt

USER dbt

ARG target
ARG graphqlSchema

COPY $target/bff.jar /bff.jar
COPY $graphqlSchema /schema

# --add-modules, add-exports, add-opens etc. is for Hazelcast
ENTRYPOINT ["java", \
    "--add-modules", "java.se", \
    "--add-exports", "java.base/jdk.internal.ref=ALL-UNNAMED", \
    "--add-opens", "java.base/java.lang=ALL-UNNAMED", \
    "--add-opens", "java.base/sun.nio.ch=ALL-UNNAMED", \
    "--add-opens", "java.management/sun.management=ALL-UNNAMED", \
    "--add-opens", "jdk.management/com.sun.management.internal=ALL-UNNAMED", \
    "-XX:MaxRAMPercentage=80", \
    "-jar", "/bff.jar"]
