FROM eclipse-temurin:21-jdk-alpine
LABEL org.opencontainers.image.authors="Doorbit Team"

USER root

RUN apk update && apk --no-cache add 'tzdata' 'ca-certificates' && update-ca-certificates && \
    cp /usr/share/zoneinfo/Europe/Berlin /etc/localtime && \
    echo "Europe/Berlin" > /etc/timezone && \
    addgroup -S dbt && adduser -S -G dbt dbt

USER dbt

ARG target

COPY $target/app.jar /app.jar

ENTRYPOINT ["java", "-XX:MaxRAMPercentage=80", "-jar","app.jar"]