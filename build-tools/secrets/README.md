# Secrets maintenance

Secrets are managed in kubernetes for kubernetes-deployed software and managed in "Google Secret Manager" for Cloud Run services and other non-k8s deployed software. All secrets are stored in keepass in this repo.

## Managing GCP Secret Manager secrets

1. Go to the Terraform Github Repo and create initial placeholder secrets.
2. Go to GCP Secret Manager in Cloud Console and override the placeholder by a new "version" of the secret.
3. Add new secret to keypass.

## Managing Kubernetes secrets.

1. Open Keepass
2. Open the respective secret-execution script there
3. Make sure you are on the right project before though `gcloud config set project PROJECT_ID`
4. Update the secret by running the script in a terminal.
5. Expected output is something like `secret xy was deleted… secret xy was created`
6. Restart all PODs of the deployment in order to inject any new secret into the deployment.

