## Evebi Speicherdatei auslesen und ändern

Die Evebi Speicherdatei heißt [Name].evex. Es ist ein zip Archiv, d. h. um an den Inhalt zu gelangen, die Datei in .zip umbenennen. Dateiendungen müssen in Windows ggfs. zunächst erstmal zur Anzeige gebracht werden. Siehe [hier](https://vtcri.kayako.com/article/296-view-file-extensions-windows-10#:~:text=Open%20File%20Explorer%3B%20if%20you,items%20to%20see%20hidden%20files)

Im Zip Archiv befindet sich eine file-hashes und die projekt.xml Datei. Die projekt.xml Datei an einen Ort entpacken.

Nun kann in der projekt.xml eine Änderung vorgenommen werden.

## Evebi Speicherdatei erzeugen

Neben der projekt.xml liegt auch noch eine file-hashes Datei im Zip Archiv. Diese beinhaltet die sha1 Checksumme der projekt.xml Datei.
Man kann also nicht einfach die projekt.xml bearbeiten, ein neues ZIP archiv erzeugen und dieses zu .evex umnennen. Evebi stürzt dann ab.

Also muss die file-hashes Datei nach jeder Änderung neu erzeugt werden, dann das zip Archiv erzeugt werden, Umnennung zu .evex und fertig.

Hierfür gibt es das Script `./make-evex.sh projekt.xml`. Es erzeugt die file-hashes Datei und erzeugt wie beschrieben die .evex Datei.

Das Script wurde für `#!/usr/bin/env bash` geschrieben, genauer gesagt für Windows Mingw64 (Git Bash).
Es greift auf das Tool `zip` zu, das standardmäßig nicht für mingw installiert ist. Um es zu installieren, bitte die folgenden 8 Steps durchführen wie hier beschrieben: [Stackoverflow](https://stackoverflow.com/questions/38782928/how-to-add-man-and-zip-to-git-bash-installation-on-windows/55749636#55749636)
