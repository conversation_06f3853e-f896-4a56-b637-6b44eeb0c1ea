#!/usr/bin/env bash
###############################################################################
# Erzeuge .evex-Datei aus "projekt.xml" und einer temporären "file-hashes" 
# im selben Ordner. Diese "file-hashes" wird anschließend wieder gelöscht.
#
# Aufruf:
#   ./erzeuge_evex.sh /Pfad/zu/projekt.xml
#
###############################################################################

# 1) Argument prüfen
if [ -z "$1" ]; then
  echo "Bitte den Pfad zur projekt.xml an<PERSON>ben, z.B.:"
  echo "  $0 projekt.xml"
  exit 1
fi

# 2) Variablen vorbereiten
PROJEKT_XML="$1"

# Ordner, in dem projekt.xml liegt
FOLDER="$(dirname "$PROJEKT_XML")"

# Dateiname ohne Pfad, z.B. "projekt.xml"
PROJEKT_XML_BASENAME="$(basename "$PROJEKT_XML")"

# Dateiname ohne .xml-Endung, z.B. "projekt" 
# (falls die Datei "projekt.xml" heißt)
BASENAME_NOEXT="${PROJEKT_XML_BASENAME%.xml}"

# Zielnamen definieren
ZIP_NAME="${BASENAME_NOEXT}.zip"
EVEX_NAME="${BASENAME_NOEXT}.evex"

# 3) Existiert die projekt.xml?
if [ ! -f "$PROJEKT_XML" ]; then
  echo "Fehler: '$PROJEKT_XML' existiert nicht oder ist keine Datei."
  exit 1
fi

# 4) SHA1-Hash aus projekt.xml berechnen
sha1_value=$(sha1sum "$PROJEKT_XML" | awk '{print $1}')

# 5) Datei "file-hashes" anlegen (oder überschreiben)
echo "projekt.xml=sha1:${sha1_value}" > "file-hashes"

# 6) Zip-Archiv erstellen (nur die beiden relevanten Dateien)
#    -j => keine Verzeichnisstruktur ins Archiv aufnehmen
zip -j "${ZIP_NAME}" \
    "projekt.xml" \
    "file-hashes"

# 7) ZIP-Datei in .evex umbenennen
mv "${ZIP_NAME}" "${EVEX_NAME}"

# 8) Temporäre "file-hashes"-Datei löschen
rm -f "file-hashes"

# 9) Erfolgsmeldung
echo "Fertig! Die Datei '${EVEX_NAME}' wurde erzeugt."
