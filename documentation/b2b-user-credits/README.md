## B2B User Credits

- create new group in keycloak for B2B customers (e.g. `E&V`)
- create admin group in keycloak for B2B customers (e.g. `E&V Admin`)
- let users configure their billingId in their profile (only allowed for users in B2B group)
  - billingId will be charged for all requests of this user
- let B2B admins create users on their behalf and share credentials with them + set billingId implicitly (only allowed for users in B2B Admin group)
 

![B2B User Credits](./user-profile-b2b-credits.excalidraw.png)

billingId == group id of keycloak group

Workshop 23.01.2024

Neue API

1. EV Barmbek muss von Dorbittern angel<PERSON>t werden (z. <PERSON><PERSON> <PERSON>)
2. UserManager Rolle muss einem User von Doorbittern hinzugefügt werden (Scholz). Dieser wird "seine Makler" seiner "Business Entität" selber hinzufügen, also:
3. UserManager darf selber User anlegen oder bestehende User finden und seiner Gruppe hinzufügen
4. UserManager darf EV Barmbek Doorbit User hinzufügen und löschen.

Man Darf als Makler mehreren Business Entitäten angehören.
Der User wählt im Fall von mehreren BusinessEntität-Mitgliedschaften selber aus, auf welche Rechnung sein Inserat gehen soll.

Beispielszenario:

EV Barmbek
- Scholz (UserManager und Makler)
- Lucka (Makler)

EV Langenhorn
- Padberg (UserManager)
- Pusch (Makler)


Technische Abbildung Beispiel:

Gruppe Business-Owner

- Scholz
- Padberg

Gruppe EV Barmbek
- Scholz
- Lucka

Gruppe EV Langenhorn
- Padberg
- Pusch


Aus User Sicht:

User Scholz

groups: EV Barmbek, UserManager

User Lucka:

groups: EV Barmbek
