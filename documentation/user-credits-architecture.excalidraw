{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 251, "versionNonce": 1712717680, "isDeleted": false, "id": "PcXfy-z46V84bq9ZxsIve", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 152.0829679825306, "y": 315.3736449154522, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 201, "height": 80, "seed": 1398706400, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "UxKK95Tl-tDXAP_lLdDxT"}, {"id": "jquis2pT6vcjCRZjdZPf2", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false}, {"type": "text", "version": 206, "versionNonce": 769650576, "isDeleted": false, "id": "UxKK95Tl-tDXAP_lLdDxT", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 212.51499770665168, "y": 337.8736449154522, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 80.13594055175781, "height": 35, "seed": 1319346464, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "Stripe", "textAlign": "center", "verticalAlign": "middle", "containerId": "PcXfy-z46V84bq9ZxsIve", "originalText": "Stripe", "lineHeight": 1.25, "baseline": 25}, {"type": "rectangle", "version": 312, "versionNonce": 156990864, "isDeleted": false, "id": "dfTDzBYykTsNxkWNSqIj-", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 170.0465535212661, "y": 530.370574500816, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 166, "height": 80, "seed": 117459232, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "b30cIfm6EzWN5V2RpaB4_"}, {"id": "jquis2pT6vcjCRZjdZPf2", "type": "arrow"}, {"id": "Og-sOsdI8hGdjNnXPU0zq", "type": "arrow"}, {"id": "VcdPm62_Arsqm4wVXWE4L", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false}, {"type": "text", "version": 266, "versionNonce": 1783686000, "isDeleted": false, "id": "b30cIfm6EzWN5V2RpaB4_", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 226.79656878005517, "y": 552.870574500816, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 52.499969482421875, "height": 35, "seed": 240788768, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "BFF", "textAlign": "center", "verticalAlign": "middle", "containerId": "dfTDzBYykTsNxkWNSqIj-", "originalText": "BFF", "lineHeight": 1.25, "baseline": 25}, {"type": "rectangle", "version": 409, "versionNonce": 1076021104, "isDeleted": false, "id": "yYiR3UCrUXsCURtFQDE7W", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 112.43838682270598, "y": 748.4244557344994, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 293, "height": 89, "seed": 1220292832, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "8w_irWcziB0-2HlWp2zjz"}, {"id": "Og-sOsdI8hGdjNnXPU0zq", "type": "arrow"}, {"id": "RwussBtN2azlGci2AaDTx", "type": "arrow"}, {"id": "VcdPm62_Arsqm4wVXWE4L", "type": "arrow"}, {"id": "GLS7isq_EGdZj5Ysl3t7W", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false}, {"type": "text", "version": 474, "versionNonce": 1575200656, "isDeleted": false, "id": "8w_irWcziB0-2HlWp2zjz", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 135.73845853901457, "y": 775.4244557344994, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 246.3998565673828, "height": 35, "seed": 1203967200, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "Stripe-Integration", "textAlign": "center", "verticalAlign": "middle", "containerId": "yYiR3UCrUXsCURtFQDE7W", "originalText": "Stripe-Integration", "lineHeight": 1.25, "baseline": 25}, {"type": "text", "version": 243, "versionNonce": 807714160, "isDeleted": false, "id": "e2mLRxMzEIVICdTPC2-pO", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 24.338042558601273, "y": 169.1291788914591, "strokeColor": "#e03131", "backgroundColor": "transparent", "width": 525, "height": 33.6, "seed": 730769696, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 28, "fontFamily": 3, "text": "UserJourney: User pays a package", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "UserJourney: User pays a package", "lineHeight": 1.2, "baseline": 26}, {"type": "arrow", "version": 675, "versionNonce": 670806384, "isDeleted": false, "id": "jquis2pT6vcjCRZjdZPf2", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 242.18854120855963, "y": 409.5636172006625, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.1045087078232427, "height": 103.95636521146639, "seed": 1084013856, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328359860, "link": null, "locked": false, "startBinding": {"elementId": "PcXfy-z46V84bq9ZxsIve", "focus": 0.11812564096869627, "gap": 14.189972285210331}, "endBinding": {"elementId": "dfTDzBYykTsNxkWNSqIj-", "focus": -0.07192558412070521, "gap": 16.850592088687108}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [3.1045087078232427, 103.95636521146639]]}, {"type": "text", "version": 206, "versionNonce": 1770753904, "isDeleted": false, "id": "UIhZrF8m-n1nlFUf544r3", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 280.70225012107767, "y": 425.7059106584257, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 281.25, "height": 48, "seed": 1076403424, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Sends event via webhook:\n \"invoice.paid\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sends event via webhook:\n \"invoice.paid\"", "lineHeight": 1.2, "baseline": 43}, {"type": "text", "version": 313, "versionNonce": 1917542288, "isDeleted": false, "id": "VIexemOuha7hbvTFqUfpn", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -27.49403585505638, "y": 210.48516310468858, "strokeColor": "#f08c00", "backgroundColor": "transparent", "width": 667.96875, "height": 24, "seed": 1693313248, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "also treated in this diagram: Cache invalidation mechanic", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "also treated in this diagram: Cache invalidation mechanic", "lineHeight": 1.2, "baseline": 19}, {"type": "arrow", "version": 383, "versionNonce": 845415280, "isDeleted": false, "id": "Og-sOsdI8hGdjNnXPU0zq", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 255.9200517679228, "y": 620.4469363639752, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 2.636410117423111, "height": 114.68384010790328, "seed": 2002674912, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328359860, "link": null, "locked": false, "startBinding": {"elementId": "dfTDzBYykTsNxkWNSqIj-", "focus": -0.02052343839649549, "gap": 10.07636186315915}, "endBinding": {"elementId": "yYiR3UCrUXsCURtFQDE7W", "focus": 0.006417070316328582, "gap": 13.293679262620913}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [2.636410117423111, 114.68384010790328]]}, {"type": "text", "version": 141, "versionNonce": 249097616, "isDeleted": false, "id": "rvXIXGyrA19BUWaU7tP-O", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 283.602358000865, "y": 656.0384729491865, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 328.125, "height": 24, "seed": 1072045344, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "/stripe-integration/webhooks", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "/stripe-integration/webhooks", "lineHeight": 1.2, "baseline": 19}, {"type": "rectangle", "version": 459, "versionNonce": 430076784, "isDeleted": false, "id": "vKnAVRo72rEkFCwAO0jwt", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 117.32928212019215, "y": 996.454350092954, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 293, "height": 89, "seed": 1557885216, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "Bb4TtLE-nKakGm5_y-ynW"}, {"id": "RwussBtN2azlGci2AaDTx", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false}, {"type": "text", "version": 538, "versionNonce": 512015248, "isDeleted": false, "id": "Bb4TtLE-nKakGm5_y-ynW", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 177.56132252546558, "y": 1023.454350092954, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 172.53591918945312, "height": 35, "seed": 1318079776, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "User-Credits", "textAlign": "center", "verticalAlign": "middle", "containerId": "vKnAVRo72rEkFCwAO0jwt", "originalText": "User-Credits", "lineHeight": 1.25, "baseline": 25}, {"type": "arrow", "version": 495, "versionNonce": 804394352, "isDeleted": false, "id": "RwussBtN2azlGci2AaDTx", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 258.7665905433462, "y": 849.8941622290332, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1.069011775410786, "height": 130.5023008124416, "seed": 1571234080, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328359860, "link": null, "locked": false, "startBinding": {"elementId": "yYiR3UCrUXsCURtFQDE7W", "focus": -0.0020077825284598866, "gap": 12.***************}, "endBinding": {"elementId": "vKnAVRo72rEkFCwAO0jwt", "focus": -0.04512842300072169, "gap": 16.057887051479156}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-1.069011775410786, 130.5023008124416]]}, {"type": "text", "version": 248, "versionNonce": 1414551408, "isDeleted": false, "id": "nvpumK68dh5d5nSDF78tv", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 287.5569731769999, "y": 907.8156391630893, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 773.4375, "height": 24, "seed": 372600096, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Top-Up user credits as per \"productId to credits-volumina\" mapping", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Top-Up user credits as per \"productId to credits-volumina\" mapping", "lineHeight": 1.2, "baseline": 19}, {"type": "arrow", "version": 480, "versionNonce": 904783728, "isDeleted": false, "id": "VcdPm62_Arsqm4wVXWE4L", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 132.00877624903887, "y": 731.1761612957439, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 47.**************, "height": 123.91127551888405, "seed": 31599840, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328359860, "link": null, "locked": false, "startBinding": {"elementId": "yYiR3UCrUXsCURtFQDE7W", "focus": -0.5632356635316431, "gap": 17.248294438755465}, "endBinding": {"elementId": "dfTDzBYykTsNxkWNSqIj-", "focus": 0.6880897273093575, "gap": 19.58290645026591}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-29.000511291653766, -56.682817524595976], [18.***************, -123.91127551888405]]}, {"type": "text", "version": 466, "versionNonce": 2033445232, "isDeleted": false, "id": "4dQQCY4m7i21lSWfxVJZ3", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -194.9241266097108, "y": 651.1612216326414, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 281.25, "height": 72, "seed": 596072672, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Cache instruction: \nCacheEvict \"userCredits\"\n for userId", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Cache instruction: \nCacheEvict \"userCredits\"\n for userId", "lineHeight": 1.2, "baseline": 67}, {"type": "rectangle", "version": 288, "versionNonce": 72532368, "isDeleted": false, "id": "Xa-MgOUQ1xo7gPM3T0y8T", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -894.245612572679, "y": 343.14583613816706, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 201, "height": 80, "seed": 83713936, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "B6BrBmFi9qHlWidzK-iV0"}, {"id": "0zq_TuvfQcZw_hMVRDvuX", "type": "arrow"}], "updated": 1689328169594, "link": null, "locked": false}, {"type": "text", "version": 243, "versionNonce": 811589488, "isDeleted": false, "id": "B6BrBmFi9qHlWidzK-iV0", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -833.8135828485579, "y": 365.64583613816706, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 80.13594055175781, "height": 35, "seed": 2068508048, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328169594, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "Stripe", "textAlign": "center", "verticalAlign": "middle", "containerId": "Xa-MgOUQ1xo7gPM3T0y8T", "originalText": "Stripe", "lineHeight": 1.25, "baseline": 25}, {"type": "rectangle", "version": 350, "versionNonce": 1783045520, "isDeleted": false, "id": "FbgSQNwnrFl6FEa0xATAk", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -876.2820270339435, "y": 558.1427657235309, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 166, "height": 80, "seed": 750173072, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "4CfJN-H5uhj0gRxZuGmkB"}, {"id": "0zq_TuvfQcZw_hMVRDvuX", "type": "arrow"}, {"id": "YDcH6dWkaNgCss1g_S4BR", "type": "arrow"}], "updated": 1689328199200, "link": null, "locked": false}, {"type": "text", "version": 303, "versionNonce": 2073795984, "isDeleted": false, "id": "4CfJN-H5uhj0gRxZuGmkB", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -819.5320117751544, "y": 580.6427657235309, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 52.499969482421875, "height": 35, "seed": 460900752, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328169594, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "BFF", "textAlign": "center", "verticalAlign": "middle", "containerId": "FbgSQNwnrFl6FEa0xATAk", "originalText": "BFF", "lineHeight": 1.25, "baseline": 25}, {"type": "rectangle", "version": 446, "versionNonce": 393167728, "isDeleted": false, "id": "dGSEapty15lywwvOvX9zB", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -933.8901937325036, "y": 776.196646957214, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 293, "height": 89, "seed": 1120475024, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "62WLNJsTLJ4J-jFnkde6e"}, {"id": "YDcH6dWkaNgCss1g_S4BR", "type": "arrow"}, {"id": "YvyqlpduAIVlQtRRbxz3K", "type": "arrow"}], "updated": 1689328199201, "link": null, "locked": false}, {"type": "text", "version": 511, "versionNonce": 645703536, "isDeleted": false, "id": "62WLNJsTLJ4J-jFnkde6e", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -910.590122016195, "y": 803.1966469572143, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 246.3998565673828, "height": 35, "seed": 1994368400, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328169594, "link": null, "locked": false, "fontSize": 28, "fontFamily": 1, "text": "Stripe-Integration", "textAlign": "center", "verticalAlign": "middle", "containerId": "dGSEapty15lywwvOvX9zB", "originalText": "Stripe-Integration", "lineHeight": 1.25, "baseline": 25}, {"type": "text", "version": 331, "versionNonce": 1077938544, "isDeleted": false, "id": "j3MEqbk_ynXayxfDWdH39", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1076.0369454037811, "y": 210.08342070128936, "strokeColor": "#e03131", "backgroundColor": "transparent", "width": 590.625, "height": 33.6, "seed": 1156871056, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328181329, "link": null, "locked": false, "fontSize": 28, "fontFamily": 3, "text": "UserJourney: User completes checkout", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "UserJourney: User completes checkout", "lineHeight": 1.2, "baseline": 26}, {"type": "arrow", "version": 772, "versionNonce": 1433520496, "isDeleted": false, "id": "0zq_TuvfQcZw_hMVRDvuX", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -804.1400393466499, "y": 437.33580842337744, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 3.1045087078232427, "height": 103.95636521146639, "seed": 1396670864, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328169827, "link": null, "locked": false, "startBinding": {"elementId": "Xa-MgOUQ1xo7gPM3T0y8T", "focus": 0.11812564096869628, "gap": 14.189972285210388}, "endBinding": {"elementId": "FbgSQNwnrFl6FEa0xATAk", "focus": -0.07192558412070521, "gap": 16.850592088687108}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [3.1045087078232427, 103.95636521146639]]}, {"type": "text", "version": 275, "versionNonce": 241906576, "isDeleted": false, "id": "lbG4TtO7kYWkX0w60mPEd", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -765.6263304341319, "y": 453.47810188114056, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 339.84375, "height": 48, "seed": 1090047888, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328194602, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Sends event via webhook:\n \"checkout.session.completed\"", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Sends event via webhook:\n \"checkout.session.completed\"", "lineHeight": 1.2, "baseline": 43}, {"type": "arrow", "version": 480, "versionNonce": 2035751792, "isDeleted": false, "id": "YDcH6dWkaNgCss1g_S4BR", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -790.4085287872867, "y": 648.2191275866902, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 2.636410117423111, "height": 114.68384010790328, "seed": 1419127696, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328169827, "link": null, "locked": false, "startBinding": {"elementId": "FbgSQNwnrFl6FEa0xATAk", "focus": -0.020523438396495456, "gap": 10.076361863159264}, "endBinding": {"elementId": "dGSEapty15lywwvOvX9zB", "focus": 0.006417070316328529, "gap": 13.293679262620572}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [2.636410117423111, 114.68384010790328]]}, {"type": "text", "version": 178, "versionNonce": 334462864, "isDeleted": false, "id": "uDoFtPPTxY-dbCMqm7_Fj", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -762.7262225543445, "y": 683.8106641719013, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 328.125, "height": 24, "seed": 1978284432, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328169594, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "/stripe-integration/webhooks", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "/stripe-integration/webhooks", "lineHeight": 1.2, "baseline": 19}, {"type": "rectangle", "version": 559, "versionNonce": 596725136, "isDeleted": false, "id": "UgsMAislMohbvpq6flDyB", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1412.7805549821499, "y": 782.9950155714574, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 293, "height": 89, "seed": 764184464, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "jNL91YQUIZv8iYEl2RagT"}, {"id": "YvyqlpduAIVlQtRRbxz3K", "type": "arrow"}], "updated": 1689328227890, "link": null, "locked": false}, {"type": "text", "version": 666, "versionNonce": 320869232, "isDeleted": false, "id": "jNL91YQUIZv8iYEl2RagT", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1390.2604133805874, "y": 814.9950155714574, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 247.959716796875, "height": 25, "seed": 397743504, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328227890, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "StripeCustomerRepository", "textAlign": "center", "verticalAlign": "middle", "containerId": "UgsMAislMohbvpq6flDyB", "originalText": "StripeCustomerRepository", "lineHeight": 1.25, "baseline": 18}, {"type": "arrow", "version": 716, "versionNonce": 1051469168, "isDeleted": false, "id": "YvyqlpduAIVlQtRRbxz3K", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -946.3599002270378, "y": 823.0232292700575, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 157.36276770363406, "height": 1.1872479981274182, "seed": 161211280, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1689328227890, "link": null, "locked": false, "startBinding": {"elementId": "dGSEapty15lywwvOvX9zB", "focus": -0.0020077825284583557, "gap": 12.469706494534194}, "endBinding": {"elementId": "UgsMAislMohbvpq6flDyB", "focus": -0.04512842300072317, "gap": 16.05788705147802}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-157.36276770363406, 1.1872479981274182]]}, {"type": "text", "version": 456, "versionNonce": 1730130800, "isDeleted": false, "id": "mhSo22rHKREDbZ-aONoWj", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": -1531.239771783168, "y": 901.3144988593045, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 1054.6875, "height": 24, "seed": 1903751568, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328276219, "link": null, "locked": false, "fontSize": 20, "fontFamily": 3, "text": "Map client-reference-id (=doorbit userId, see pricing-table snippet) to stripe customerId ", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Map client-reference-id (=doorbit userId, see pricing-table snippet) to stripe customerId ", "lineHeight": 1.2, "baseline": 19}, {"id": "wPxjUa-FexXghVmV6GvBs", "type": "text", "x": -1048.951155026582, "y": 790.8390767622093, "width": 46.875, "height": 24, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 834127216, "version": 12, "versionNonce": 217390480, "isDeleted": false, "boundElements": null, "updated": 1689328283255, "link": null, "locked": false, "text": "save", "fontSize": 20, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 19, "containerId": null, "originalText": "save", "lineHeight": 1.2, "isFrameName": false}, {"type": "rectangle", "version": 725, "versionNonce": 1399694736, "isDeleted": false, "id": "7HtW_KmzGwJAXQxUxeHo9", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 653.8387410157868, "y": 747.3135939964743, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 293, "height": 89, "seed": 1138435952, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "nQIJ5IWaaxm5fNAwdQNJE"}, {"id": "GLS7isq_EGdZj5Ysl3t7W", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false}, {"type": "text", "version": 831, "versionNonce": 534068080, "isDeleted": false, "id": "nQIJ5IWaaxm5fNAwdQNJE", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 676.3588826173493, "y": 779.3135939964743, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 247.959716796875, "height": 25, "seed": 1323612528, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1689328359640, "link": null, "locked": false, "fontSize": 20, "fontFamily": 1, "text": "StripeCustomerRepository", "textAlign": "center", "verticalAlign": "middle", "containerId": "7HtW_KmzGwJAXQxUxeHo9", "originalText": "StripeCustomerRepository", "lineHeight": 1.25, "baseline": 18}, {"id": "GLS7isq_EGdZj5Ysl3t7W", "type": "arrow", "x": 414.1046588133079, "y": 794.5104753085927, "width": 222.776654922249, "height": 4.09077741490546, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 836412272, "version": 407, "versionNonce": 1889499504, "isDeleted": false, "boundElements": null, "updated": 1689328359860, "link": null, "locked": false, "points": [[0, 0], [222.776654922249, -4.09077741490546]], "lastCommittedPoint": null, "startBinding": {"elementId": "yYiR3UCrUXsCURtFQDE7W", "focus": 0.093987631629449, "gap": 8.6662719906019}, "endBinding": {"elementId": "wdB-ue9zHDWaRR8Z3faQE", "focus": 1.9321695420178677, "gap": 14.424701478341149}, "startArrowhead": null, "endArrowhead": "arrow"}, {"id": "ru2qiUr1EM4M4NGLpgMA7", "type": "text", "x": 435.8008739871033, "y": 759.1964162408772, "width": 178.125, "height": 19.2, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 564199792, "version": 210, "versionNonce": 1249660272, "isDeleted": false, "boundElements": null, "updated": 1689328359640, "link": null, "locked": false, "text": "find doorbit userId", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15, "containerId": null, "originalText": "find doorbit userId", "lineHeight": 1.2, "isFrameName": false}, {"id": "wdB-ue9zHDWaRR8Z3faQE", "type": "text", "x": 430.4351431554594, "y": 804.8443993720284, "width": 196.875, "height": 19.2, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": null, "seed": 809953168, "version": 132, "versionNonce": 1682801040, "isDeleted": false, "boundElements": [{"id": "GLS7isq_EGdZj5Ysl3t7W", "type": "arrow"}], "updated": 1689328359640, "link": null, "locked": false, "text": "by stripe customer id", "fontSize": 16, "fontFamily": 3, "textAlign": "left", "verticalAlign": "top", "baseline": 15, "containerId": null, "originalText": "by stripe customer id", "lineHeight": 1.2, "isFrameName": false}, {"id": "3-z70piyW0KEEPJ5Qnskw", "type": "line", "x": -343.9265782793966, "y": 74.49708062401567, "width": 17.793214591770493, "height": 1144.6968054038953, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "seed": 1517173136, "version": 61, "versionNonce": 1135746448, "isDeleted": false, "boundElements": null, "updated": 1689328349225, "link": null, "locked": false, "points": [[0, 0], [17.793214591770493, 1144.6968054038953]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}