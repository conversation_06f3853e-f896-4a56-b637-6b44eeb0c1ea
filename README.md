# portal-applications

This repo contains (micro) [services](/services), [devsetup](/devsetup) and [build tools](/build-tools) that control
scaling and container resource configs.

## Features

Diese Features umfasst die Plattform:

### App zur Erstellung von Immobilieninseraten

* Die einfachste Inseraterstellung auf dem Markt
* Interaktive 3D Aufnahme der Immobilie mit dem Apple iPhone 12 Pro oder höher (Pro wegen des LIDAR Sensor)
* Erstellung von Bildern
* Automatische Erstellung der Lagebeschreibung mit Künstlicher Intelligenz und mehr als 60 Mikrolagefaktoren

### Portal zur Suche nach Immobilien

* Suche über PLZ, Landkreis, Bundesland mit wählbarem Umkreis
* Suche in mehreren Bereichen gleichzeitig
* Suche nach Immobilienart (Haus, Wohnung, Grundstück)
* Suche nach Immobilien Features (z.<PERSON><PERSON><PERSON>, Wohnfläche, Balkon, Energieeffizienzklasse, uvm.)
* <PERSON><PERSON> nach Entfernungen von POIs (z.B. Schulen, Supermärkte, Ärzte, uvm.) in der Nähe
* Kachel- und Kartenansicht
* Schnellansicht der Attraktivität jeder Immobilie in der Kachelansicht

### Portal zur Darstellung der Immobilien

* Fotogalerie mit automatischer Sortieroptimierung
* Intelligente Highlightberechnung stellt die Alleinstellungsmerkmale der Immobilie und Immobilienlage heraus
* Merkzettel für Immobilien für Suchende
* Darstellung von über 50 Immobilien Features
* Darstellung von über 60 POIs im Umkreis, interaktiv mit Karte
* Umfangreiche Attraktivitätsbewertung der Immobilie sowie Mikro- und Makrolage
* Darstellung von über 20 Fakten zur Makrolage der Immobilie (z. B. Altersverteilung, Einwohnerentwicklung, Kriminalität, Bildung etc.)
* Darstellung von 3D Immobilienmodellen und 2D Grundriss
* Verfügbare Besichtigungstermine und Terminvereinbarung mit dem Anbieter direkt auf der Seite
* Schnellübersicht der Nachhaltigkeit der Immobilie

### Besichtigungsterminkalender für Immobilienanbieter

* Pflege und Verwaltung von Terminen für Besichtigungen
* Einstellbarkeit von Formularfeldern durch den Immobilienanbieter (die der Interessent pflegen kann oder muss)
* Annahme von Terminanfragen von Interessenten durch den Immobilienanbieter
* Übersicht der Interessenten und deren Formularfelder
* Kalenderexport für den Immobilienanbieter (ICS Termine für Apple Calendar, Outlook, Google Calendar etc.)

## Services managed in this Repo


| Name        | Purpose                                                                                                      | Deployment                    | Customer facing |
| ----------- | ------------------------------------------------------------------------------------------------------------ | ----------------------------- | --------------- |
| Website     | Frontend Vue 3 project                                                                                       | Google Cloud Storage Bucket   | Yes             |
| bff         | Backend For Fronted that is the man in the middle between the Frontend and backends.                         | GKE                           | Yes             |
| Listing     | Backend that hosts all properties including the geo data that users can search for.                          | GKE                           | Yes             |
| Geo service | Backend for Point Of Interest calculation (async triggered by Property) for a given geo coordinate (Lat/Lon) | Google Cloud Run (serverless) | No              |
| UserProfile | Backend for user profile management                                                                          | GKE                           | Yes             |

## Tools

We are relying on a couple of tools and standard software for our product.
Read more about them in [`devesetup`](/devsetup).


| Name      | Purpose                                           | Deployment | Customer facing |
| --------- | ------------------------------------------------- | ---------- | --------------- |
| osm2pgsql | CLI client to import OSM BPF data into PostgreSql | none       | No              |
| Keycloak  | Used for OIDC authentication of our users         | GKE        | YES             |

## Setup localhost development environment

Please refer to [devsetup](/devsetup) as well as [services](/services) and read carefully to avoid friction.

# How to hotfix live

1. Git auf den Stand zurückrollen, der LIVE ist. Am besten den zuletzt gelaufenen GitHub Actions Flow angucken, dort steht auch der GIT Sha, mit dem live deployed wurde. Bei Kubernetes Deployments sieht man es am Deployment (steht in der Version) oder im GitOps Repository.
2. Feature Branch erstellen und Hotfix auf diesem Stand implementieren. GIT pushen.
3. Wenn die Website live deployed werden soll, dann kann bei dem Workflow Start der Branch angegeben werden, der für den Build genutzt werden soll. Hier den feature branch angeben und dann wird der Hotfix auf LIVE ausgespielt.
4. Wenn ein Kubernetes Deployment live deployed werden soll, verhält es sich ähnlich. Hier muss nur die GH Action zum Bauen ausgewählt werden und auch hier dann den Feature Branch zum Bauen auswählen. Diese Version wird dann zunächst auf integ deployed und diesen Git SHA kann man dann im GitOps Repo für Live eintragen.


Service landscape and network diagram

![Service Landscape](/documentation/assets/enterprise-architecture.png)

### GCP Services and SAAS


| No on image | Name              | Link                                                                        |
| ----------- | ----------------- | --------------------------------------------------------------------------- |
| 1           | Cloudflare        | [Link](https://dash.cloudflare.com)                                         |
| 2           | Cloud Armor       | [Link](https://console.cloud.google.com/net-security/securitypolicies/list) |
| 3           | GCP Load Balancer | [Link](https://console.cloud.google.com/net-services/loadbalancing/list)    |
| 4 - 13      | GKE               | [Link](https://console.cloud.google.com/kubernetes/workload/overview)       |
| 14          | Cloud Storage     | [Link](https://console.cloud.google.com/storage/browser)                    |
| a)          | OpenSearch        | [Link](https://console.aiven.io/)                                           |
| b)          | MongoDB           | [Link](https://cloud.mongodb.com/)                                          |
| c)          | PostgreSQl        | [Link](https://console.cloud.google.com/sql/instances)                      |

## Simplified

![Service Landscape](/documentation/assets/enterprise-architecture-simplified.png)

Die Infrastrukturlandschaft sieht im wesentlichen wie folgt aus.
Ruft ein User doorbit.com auf, gelangt er als erstes zu Cloudflare. Cloudflare liefert uns viele wichtige Funktionen, wie etwa ein CDN, SSL Zertifikat vom Client bis in die GCP,
Botabwehr, uvm. für relativ geringe Kosten. Zwei unserer Mitarbeiter, davon der CTO hat bei Mediamarktsaturn sehr gute Erfahrungen damit gemacht.
Cloudflare schickt den Request weiter in die Google Cloud, wo er vom Google Loadbalancer entgegengenommen wird. Der Loadbalancer erfüllt zusätzlich ein weiteres Sicherheitsmerkmal,
da das Google Produkt "Cloud Armor" als IP Firewall davor geschaltet ist.
Der Loadbalancer verteilt den Request dann entweder in Google Kubernetes Engine zum BFF oder in Cloud Storage. Dies hängt davon ab, ob der Request eine API aufrufen soll (Kubernetes)
oder eine statische Resource abruft (Cloud Storage).

In Kubernetes entwickeln und verwalten wir inzwischen 7 eigensentwickelte Microservices, die im Wesentlichen nach dem DDD Prinzip geschnitten und entwickelt werden.
Zusätzlich betreiben wir mit Keycloak und Nominatim 2 Open Source Services, die das Sessionmanagement und die Geocodierung für Addressanfragen übernehmen.
Kubernetes in Verbindung mit der Google Cloud versetzt uns in die Lage im Prinzip beliebig hoch zu skalieren.
Das Betreiben von 250 oder mehr Applikationsreplica zur Erreichung von extrem hohen Durchsatzraten ist erfahrungsgemäß mit der GCP und der Google Kubernetes Engine
kein Problem und wurde bei Mediasaturn vielfach erfolgreich praktiziert. Unsere Mitarbeiter sind langjährig erfahren im Umgang mit mehr als 20.000 Requests pro Sekunde
auf einer Applikation.

Als Datenbanken nutzen wir hauptsächlich MongoDB für Persistierung, OpenSearch als OpenSource Variante von ElasticSearch sowie PostgreSQL für Geodaten und als Keycloak Persistence.

Für den Betrieb setzen wir auf umfangreiche Applikationsmetriken und einer Reihe von Testparadigmen. Wesentlich hierbei sind minütlich ausführende
Uptime Checks der GCP, die die Verfügbarkeit durch automatisiertes Testen überwachen. Sollte einer fehlschlagen werden wir auf verschiedenen Benachrichtigungskanälen
informiert, darunter Google Chat, E-Mail, SMS und Telefonanruf.

Die Performanz der Plattform wird durch regelmäßig ausgeführte Loadtests überwacht und sichergestellt.

**Es folgt eine Auflistung unseres Tech Stacks**

### Frontend

- Typescript 5
- Vue3 & Vuetify
- yarn & webpack
- GraphQL & Apollo

### Backend

- Programmiersprachen: Kotlin, Java
- Web Application Framework: Spring Boot
- REST & Open API Specification 3.0
- Hexagonal Architecture & Domain Driven Design

### Infrastructure

- Cloudflare
- GCP
- Google Cloud Loadbalancer
- Google Cloud Armor
- Google Kubernetes Engine
- Google Cloud Storage
- Google Artifact Registry
- Sealed Secrets
- FluxCD, Helm Charts & Terraform

## Property creation flow

A typical property creation flow looks like this:

![Property Creation Flow](/documentation/assets/create-property.png)

## Services und Ports

* Geo: 8080
* Property: 8081
* Search: 8082
* bff: 8083
* Userprofile: 8084
* Website: 8087
* Nominatim: 9090

# Tools und How-Tos

Hilfreiche Doku: http://www.urbangeobigdata.it/wp-content/uploads/2017/05/Working_with_OSM_Data.pdf

Infos auf Map: https://overpass-turbo.eu/#

Tags: https://taginfo.openstreetmap.org/keys/building#overview

Download von Daten: https://download.geofabrik.de/

# Hilfreiche QGIS Filter

* Alle Städte, Kleinstädte, Dörfer und [Weiler](https://de.wikipedia.org/wiki/Weiler)

```
"place" IN ('city','town','village','hamlet') auf nodes
```

![image](https://user-images.githubusercontent.com/20859755/189719713-b717e138-d3d7-4cd7-8635-8d6c0b6d315a.png)

* Schulen und Kindergärten
  Nicht einheitlich.

```
"amenity" = 'school' auf nodes und polygons aber auch "education" = 'school'  oder "building" = 'school'.
```

* Landkreise
  Decken komplett NDS ab inklusive Bremen

```
"admin_level" = '6' auf Polygone oder lines
```

* Kreise / Städte
  Decken Kreisfreie Städte nicht ab, die müsste man irgendwie dazuwichsen. Ich finde admin level 8 als maximale
  Detailstufe am Anfang erstmal super.
  Admin Level 6 für einen Überblick der Preise, zum Beispiel als Report.

```
"admin_level" = '8' + '10' für kreisfreie Städte auf Polygone oder lines
```

# Weitere Resourcen

https://inspire-geoportal.ec.europa.eu/theme_selection.html?view=qsTheme (europaweite datenquellen)

https://www.lvermgeo.sachsen-anhalt.de/datei/anzeigen/id/4630,501/architektur_gdi_de_technik3.3.0.pdf (hier wird die
architektur von der gdi-de beschrieben)

https://metaver.de/portal/ (deutschlandweite datenquellen)

https://gdk.gdi-de.org/geonetwork/srv/eng/catalog.search#/home (alternative für deutschlandweite datenquellen)

https://www.govdata.de/web/guest/daten (noch eine alternative für deutschlandweite datenquellen)

https://ogcapi.ogc.org/ (der neue vorgeschriebene europaweite standard)

https://www.geoportal.de/ (viewer für einige deutschlandweite daten)

https://gdz.bkg.bund.de/index.php/default/digitale-geodaten.html (hier ist auch noch eine übersicht über die wichtigsten
datenquellen)

https://mis.bkg.bund.de/portal/ (wieder deutschlandweite datenquellen)

open data projekte: https://codefor.de/projekte/ und https://okfn.de/projekte/

https://www-genesis.destatis.de/genesis/online?Menu=Webservice#abreadcrumb (nicht geodatenquelle von deutschland)

https://datasetsearch.research.google.com/search?src=0&query=alkis&docid=L2cvMTFyX2NqY3l2aA%3D%3D (google datensatzsuche
anhand des beispiels ALKIS)

https://opendata-esri-de.opendata.arcgis.com/search (Super Datenquelle für alles mögliche in Deutschland und man kann es
direkt ausprobieren/anzeigen)

https://hub.arcgis.com/search (Internationale Version vom oberen Link)

## Codex test setup

Codex automatically runs `.codex/setup.sh` before executing tests. This script installs Node.js, yarn, Java and Maven so the project builds correctly. Ensure the script is executable and kept up to date with any dependency changes.

