# Installation

```agsl
yarn install
```

# Usage

```agsl
node index.js
```

This will build the project and compile the final HTML outputs into the `dist` directory.


# Development

There are 2 relevant folders for development:

1. `src` - This is where the source files are located as "templates".
2. `includes` - This is where the "partials" are located. These are the files that are included in the source files.

Do not work directly in the `dist` directory. This is the final output of the build process.
These files are not even tracked by git, so they will be overwritten by the build process.

# Template Engine

To define a new include, simply goto the HTML file in the `src` directory and add the following:

```html
{{my-include}}
```

Then goto the `includes` directory and create a file called `my-include.html` and define the contents of the include.
`node index.js` will automatically build the include into the final output.

## assets-subfolder.html

Use this include instead of assets.html if you want to portray a page that is in a subfolder of the site.
For example: /de/blog/some-blog-post.html <-- use assets-subfolder.html then, because "/blog/" in this project 
is a subfolder.