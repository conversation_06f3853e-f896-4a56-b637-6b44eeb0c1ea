/**
    Dies ist von GPT erzeugter Code
    Bitte nicht bearbeiten bzw. wenn, dann von GPT ändern lassen
    Die weitere Dynamisierung von includes wird nicht supportet.
    Dies ist lediglich ein 5m Quickwin um die Vielzahl an Code Duplications im Webseitenprojekt zu reduzieren.
    Wird eine weitere Dynamisierung benötigt, sollte Handlebars oder ein anderes Template-Engine verwendet werden.

    Unterstützt werden eine beliebige Anzahl von Template.htmls im Ordner includes.
    Der Beispielhafte Placeholder mit dem Namen {{template}} wird dann durch den Inhalt einer template.html ersetzt.
*/

const fs = require('fs').promises;
const path = require('path');
const beautify_html = require('js-beautify').html;

async function replacePlaceholdersWithTemplates(sourceFolder, templatesFolder, outputFolder) {
    try {
        const templates = await loadTemplates(templatesFolder);
        await processFolderRecursively(sourceFolder, templates, outputFolder);
        console.log('Alle HTML-Dateien wurden erfolgreich bearbeitet.');
    } catch (error) {
        console.error(error);
    }
}

async function processFolderRecursively(folder, templates, outputFolder) {
    const entries = await fs.readdir(folder, { withFileTypes: true });

    for (const entry of entries) {
        const fullPath = path.join(folder, entry.name);
        if (entry.isDirectory()) {
            // Erstelle den entsprechenden Unterordner im Ausgabeverzeichnis
            const outputSubFolder = path.join(outputFolder, entry.name);
            await fs.mkdir(outputSubFolder, { recursive: true });

            // Rekursive Verarbeitung für Unterordner
            await processFolderRecursively(fullPath, templates, outputSubFolder);
        } else if (path.extname(entry.name) === '.html') {
            // Verarbeite HTML-Datei
            await processHtmlFile(fullPath, templates, outputFolder);
        }
    }
}

async function loadTemplates(templatesFolder) {
    const templateFiles = await fs.readdir(templatesFolder);
    const templates = {};

    for (const file of templateFiles) {
        const filePath = path.join(templatesFolder, file);
        const templateName = path.basename(file, '.html');
        templates[templateName] = await fs.readFile(filePath, 'utf8');
    }

    return templates;
}

async function processHtmlFile(htmlFile, templates, outputFolder) {
    let htmlContent = await fs.readFile(htmlFile, 'utf8');

    for (const [placeholder, templateContent] of Object.entries(templates)) {
        htmlContent = htmlContent.replace(new RegExp(`{{${placeholder}}}`, 'g'), templateContent);
    }

    htmlContent = beautify_html(htmlContent, { indent_size: 4, space_in_empty_paren: true });

    const outputFilePath = path.join(outputFolder, path.basename(htmlFile));
    await fs.writeFile(outputFilePath, htmlContent, 'utf8');
    console.log(`Datei bearbeitet: ${outputFilePath}`);
}

async function copyAssets(srcFolder, destFolder) {
    try {
        // Erstelle den Zielordner, falls er nicht existiert
        await fs.mkdir(destFolder, { recursive: true });

        // Kopiere den Inhalt rekursiv
        await copyFolderRecursive(srcFolder, destFolder);
        console.log(`'${srcFolder}' wurde erfolgreich nach '${destFolder}' kopiert.`);
    } catch (error) {
        console.error(error);
    }
}

async function copyFolderRecursive(src, dest) {
    // Erstelle den Zielordner
    await fs.mkdir(dest, { recursive: true });

    // Lese den Inhalt des Quellordners
    let entries = await fs.readdir(src, { withFileTypes: true });

    for (let entry of entries) {
        let srcPath = path.join(src, entry.name);
        let destPath = path.join(dest, entry.name);

        if (entry.isDirectory()) {
            // Rekursives Kopieren für Unterordner
            await copyFolderRecursive(srcPath, destPath);
        } else {
            // Kopiere die Datei
            await fs.copyFile(srcPath, destPath);
        }
    }
}

async function copyFile(srcFile, destFile) {
    try {
        // Stelle sicher, dass der Zielordner existiert
        await fs.mkdir(path.dirname(destFile), { recursive: true });

        // Kopiere die Datei
        await fs.copyFile(srcFile, destFile);
        console.log(`'${srcFile}' wurde erfolgreich nach '${destFile}' kopiert.`);
    } catch (error) {
        console.error(error);
    }
}

// Verwende die Funktion
copyAssets('src/assets', 'dist/assets')
    .catch(error => {
        console.error(error);
    });

copyFile('src/favicon.svg', 'dist/favicon.svg')
    .catch(error => {
        console.error(error);
    });

copyFile('src/robots.txt', 'dist/robots.txt')
    .catch(error => {
        console.error(error);
    });

// Verwende die Funktion
replacePlaceholdersWithTemplates('src', 'includes', 'dist')
    .catch(error => {
        console.error(error);
    });

