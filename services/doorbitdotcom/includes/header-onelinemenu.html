<header class="appbar">
    <a class="logo-container hidden-xl"
       href="/de/"></a>
    <input class="side-menu"
           id="side-menu"
           type="checkbox"/>
    <label class="hamb"
           for="side-menu"><span class="hamb-line"></span></label>
    <nav class="nav">
        <ul class="menu">
             <!--<li><a class="nav-overview"
                   data-id="header-home"
                   href="/de/">Übersicht</a></li>-->
            <li><a class="nav-products"
                   data-id="header-products"
                   href="/de/index.html">Produkte</a></li>
			<li><a class="nav-docs"
				   data-id="header-docs"
				   href="/de/docs/" target="_blank">Dokumentation</a></li>
            <li><a class="nav-aboutus"
                   data-id="header-about-us"
                   href="/de/about-us.html">Über uns</a></li>
			<li style="margin-top: auto;">
				<a class="show-xl" style="display: flex; align-items: center; gap: 0.5rem;" data-id="header-login"
                   href="/login/" rel="noopener" target="_blank">
					<svg fill="var(--lighterGray)"
					 height="1.5rem"
					 viewBox="0 0 24 24"
					 width="1.5rem"
					 xmlns="http://www.w3.org/2000/svg"><title>account</title>
					<path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
				</svg> 
				MyAccount einloggen
				</a>
			</li>
			<li>
				<a class="show-xl" style="display: flex; align-items: center; gap: 0.5rem;"
                   href="tel:+************" data-id="header-phone-nr">
					<svg fill="var(--lighterGray)"
					 height="1.5rem"
					 viewBox="0 0 24 24"
					 width="1.5rem"
					 xmlns="http://www.w3.org/2000/svg"><title>calendar-cursor</title><path d="M22.86 17.74C22.77 17.85 22.65 17.91 22.53 17.94L20.66 18.3L21.95 21.14C22.11 21.43 21.97 21.79 21.67 21.93L19.53 22.94C19.44 23 19.36 23 19.27 23C19.05 23 18.84 22.88 18.74 22.66L17.45 19.83L15.96 21.04C15.86 21.12 15.74 21.17 15.59 21.17C15.26 21.17 15 20.9 15 20.57V11.6C15 11.27 15.26 11 15.59 11C15.74 11 15.88 11.05 16 11.13L22.77 16.89C23.04 17.12 23.07 17.5 22.86 17.74M12 15V10H7V15H12M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H13V19H5V8H19V11.06L21 12.76V5C21 3.9 20.1 3 19 3Z" /></svg> 

				</a>
			</li>
        </ul>
    </nav>
    <a class="logo-container show-xl"
       data-id="header-logo"
       href="/de/"></a>
    <div class="btn-group-header">
		<p class="copytext-dm phone-nr"></p>
		<a data-id="header-book-a-demo"
		   href="https://meet.brevo.com/my-company-3204/demo"
		   rel="noopener"
		   target="_blank">
			<button class="btn btn-primary" style="color: var(--darkerGray);">
				<svg fill="var(--darkerGray)"
					 height="1.5rem"
					 viewBox="0 0 24 24"
					 width="1.5rem"
					 xmlns="http://www.w3.org/2000/svg"><title>calendar-cursor</title><path d="M22.86 17.74C22.77 17.85 22.65 17.91 22.53 17.94L20.66 18.3L21.95 21.14C22.11 21.43 21.97 21.79 21.67 21.93L19.53 22.94C19.44 23 19.36 23 19.27 23C19.05 23 18.84 22.88 18.74 22.66L17.45 19.83L15.96 21.04C15.86 21.12 15.74 21.17 15.59 21.17C15.26 21.17 15 20.9 15 20.57V11.6C15 11.27 15.26 11 15.59 11C15.74 11 15.88 11.05 16 11.13L22.77 16.89C23.04 17.12 23.07 17.5 22.86 17.74M12 15V10H7V15H12M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H13V19H5V8H19V11.06L21 12.76V5C21 3.9 20.1 3 19 3Z" /></svg>
				<span>Demo</span> <span class="hidden-md">buchen</span>
			</button>
		</a>
		<a data-id="header-login"
		   href="/login/"
		   rel="noopener"
		   target="_blank" class="hidden-md">
			<button class="btn btn-secondary" style="background-color: var(--darkerGray)">
				<svg fill="var(--lighterGray)"
					 height="1.5rem"
					 viewBox="0 0 24 24"
					 width="1.5rem"
					 xmlns="http://www.w3.org/2000/svg"><title>account</title>
					<path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
				</svg>
				<p>Login</p>
			</button>
		</a>
	</div>
</header>