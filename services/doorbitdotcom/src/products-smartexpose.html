<!doctype html>
<html lang="de">
    <head>
        <title>doorbit - SmartExposé</title>
        {{head}}
        {{assets}}
    </head>
    <body>

        {{header-twolinemenu}}

        <div class="container product-height">
            <!-- Section one -->
            <section class="one product-height"
                     id="section-1">
                <div class="hidden-md">
                    <video autoplay class="s1-video" loop muted playsinline>
                        <source src="assets/video/doorbit_vid.mp4"
                                type="video/mp4"/>
                    </video>
                </div>
                <a class="fab-arrow" href="#section-2">
                    <div style="margin-bottom: -37px;">
                        <svg fill="var(--lightGreen)"
                             height="2.7rem"
                             viewBox="0 0 24 24"
                             width="2.7rem"
                             xmlns="http://www.w3.org/2000/svg"><title>chevron-down</title>
                            <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                        </svg>
                    </div>
                    <svg fill="var(--lightGreen)"
                         height="2.7rem"
                         viewBox="0 0 24 24"
                         width="2.7rem"
                         xmlns="http://www.w3.org/2000/svg"><title>chevron-down</title>
                        <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
                    </svg>
                </a>
                <div class="hidden-xl flex-1">
                </div>
                <div class="s1-video-position">
                    <div class="show-md">
                        <video autoplay
                               class="s1-video"
                               loop
                               muted
                               playsinline>
                            <source src="assets/video/doorbit_vid.mp4"
                                    type="video/mp4"/>
                        </video>
                    </div>
                    <div class="box primary-box-dm">
                        <h1 class="h-dm">doorbit’s SmartExposé <span class="highlight-dm">zur Erstellung von Immobilien&shy;exposés</span></h1>
                        <p class="copytext-dm copytext-xl">Die SmartExposé Lösung ermöglicht Maklern und Eigentümern die schnelle Erfassung von Immobilien zur Erstellung von Exposés.</p>
                        <div class="btn-group">
                            <a data-id="book-a-demo"
                               href="https://meet.brevo.com/my-company-3204/demo"
                               rel="noopener"
                               target="_blank">
                                <button class="btn btn-dm-primary"><p>Jetzt Demo buchen</p></button>
                            </a>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Section two -->
            <section class="two-products product-height p-0-sm"
                     id="section-2">
                <div class="box box-features"
                     style="justify-content: center;">
                    <h2><span class="highlight">6 gute Gründe</span></h2>
                    <p style="text-align:center; margin-bottom: 1rem;"></p>
                    <div class="box-features-grid">
                        <a href="#section-3">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/house.svg);"></div>
                                <h5>Grundriss und 3D Modell Erstellung</h5>
                                <p class="copytext-sm"
                                   style="text-align: center;">(Aktuell nur mit iPhone Pro Modellen möglich)</p>
                            </div>
                        </a>
                        <a href="#section-4">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/text-generator.svg);"></div>
                                <h5>Mit KI erstellte Lage- und Objektbeschreibung inkl. interaktive Karten</h5>
                            </div>
                        </a>
                        <a href="#section-5">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/new-house.svg);"></div>
                                <h5>KI Virtual Staging</h5>
                            </div>
                        </a>
                        <a href="#section-6">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/bar-graph.svg);"></div>
                                <h5>Marktwertermittlung in Echtzeit</h5>
                                <div class="ph-powered-box">
                                    <p class="copytext-sm"
                                       style="text-align: center; width:unset;">powered by</p>
                                    <div class="ic-ph-sm"
                                         style="background-image: url(assets/ic/price-hubble-logo.svg); background-position: center;"></div>
                                </div>
                            </div>
                        </a>
                        <a href="#section-7">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/turn2.svg);"></div>
                                <h5>Export der Web-Exposès zu Immoportalen und CRM’s</h5>
                            </div>
                        </a>
                        <a href="#section-8">
                            <div class="box secondary-box">
                                <div class="ic-big"
                                     style="background-image: url(assets/ic/preview.svg);"></div>
                                <h5>Vorschau und Bearbeiten im Webportal</h5>
                            </div>
                        </a>
                    </div>
                </div>
            </section>
            <!-- Section three -->
            <section class="product-height background-divider p-xl-0"
                     id="section-3">
                <div class="tertiary-box">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">01</span></h2>
                        <h3>Grundriss und 3D Modell Erstellung</h3>
                        <p class="copytext-xl">Die kinderleichte Erfassung der Immobilie mit einem iPhone Pro Modell führt zu einem neuen Kundenerlebnis - es erschafft ein umfassendes Verständnis von Abmessungen und Layouts, um die Entscheidungsfindung deutlich zu vereinfachen.</p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>clock-time-eight-outline</title>
                                    <path d="M12 20C16.4 20 20 16.4 20 12S16.4 4 12 4 4 7.6 4 12 7.6 20 12 20M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2M12.5 12.8L7.7 15.6L7 14.2L11 11.9V7H12.5V12.8Z"/>
                                </svg>
                                <h5>Zeitersparnis durch weniger Rückfragen</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>cube-outline</title>
                                    <path d="M21,16.5C21,16.88 20.79,17.21 20.47,17.38L12.57,21.82C12.41,21.94 12.21,22 12,22C11.79,22 11.59,21.94 11.43,21.82L3.53,17.38C3.21,17.21 3,16.88 3,16.5V7.5C3,7.12 3.21,6.79 3.53,6.62L11.43,2.18C11.59,2.06 11.79,2 12,2C12.21,2 12.41,2.06 12.57,2.18L20.47,6.62C20.79,6.79 21,7.12 21,7.5V16.5M12,4.15L6.04,7.5L12,10.85L17.96,7.5L12,4.15M5,15.91L11,19.29V12.58L5,9.21V15.91M19,15.91V9.21L13,12.58V19.29L19,15.91Z"/>
                                </svg>
                                <h5>einfache Erstellung des Grundrisses</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>gesture-double-tap</title>
                                    <path d="M10,9A1,1 0 0,1 11,8A1,1 0 0,1 12,9V13.47L13.21,13.6L18.15,15.79C18.68,16.03 19,16.56 19,17.14V21.5C18.97,22.32 18.32,22.97 17.5,23H11C10.62,23 10.26,22.85 10,22.57L5.1,18.37L5.84,17.6C6.03,17.39 6.3,17.28 6.58,17.28H6.8L10,19V9M11,5A4,4 0 0,1 15,9C15,10.5 14.2,11.77 13,12.46V11.24C13.61,10.69 14,9.89 14,9A3,3 0 0,0 11,6A3,3 0 0,0 8,9C8,9.89 8.39,10.69 9,11.24V12.46C7.8,11.77 7,10.5 7,9A4,4 0 0,1 11,5M11,3A6,6 0 0,1 17,9C17,10.7 16.29,12.23 15.16,13.33L14.16,12.88C15.28,11.96 16,10.56 16,9A5,5 0 0,0 11,4A5,5 0 0,0 6,9C6,11.05 7.23,12.81 9,13.58V14.66C6.67,13.83 5,11.61 5,9A6,6 0 0,1 11,3Z"/>
                                </svg>
                                <h5>einfache Bedienung</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-big img-big-fullsize img-smartexpose-01"></div>
            </section>
            <!-- Section four -->
            <section class="product-height p-xl-0"
                     id="section-4"
                     style="flex-direction: row-reverse">
                <div class="tertiary-box"
                     style="justify-content: start">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">02</span></h2>
                        <h3>Mit KI erstellte Lage- und Objektbeschreibung</h3>
                        <p class="copytext-xl">Die Lagebeschreibung erfolgt automatisch mithilfe von GEO-Standortdaten, Objektinformationen, soziodemografischer Daten und KI-Texttechnologie. Kein Zeitverlust mehr für manuelle Recherche und Texterstellung. Interaktive Karten blenden Informationen zu unzähligen Point Of Interests ein und aus uvm.</p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>clock-time-eight-outline</title>
                                    <path d="M12 20C16.4 20 20 16.4 20 12S16.4 4 12 4 4 7.6 4 12 7.6 20 12 20M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2M12.5 12.8L7.7 15.6L7 14.2L11 11.9V7H12.5V12.8Z"/>
                                </svg>
                                <h5>Kein Rechercheaufwand</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>clipboard-text-play-outline</title>
                                    <path d="M19,3H14.82C14.25,1.44 12.53,0.64 11,1.2C10.14,1.5 9.5,2.16 9.18,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H15V19H5V5H7V7H17V5H19V14H21V5A2,2 0 0,0 19,3M12,5A1,1 0 0,1 11,4A1,1 0 0,1 12,3A1,1 0 0,1 13,4A1,1 0 0,1 12,5M17,16V22L22,19L17,16M17,11H7V9H17V11M15,15H7V13H15V15Z"/>
                                </svg>
                                <h5>KI generierte Texte</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>star-outline</title>
                                    <path d="M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z"/>
                                </svg>
                                <h5>Hohe Qualität</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-big img-big-fullsize img-smartexpose-02"><a href="https://www.openstreetmap.org/copyright"
                                                                                rel="noopener"
                                                                                target="_blank"><p class="osm-lic"
                                                                                                   style="color:var(--green)">© OpenStreetMap</p></a></div>
            </section>
            <!-- Section five -->
            <section id="section-9"
                     class="product-height background-divider">
                <div class="tertiary-box">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">03</span></h2>
                        <h3>Virtual Staging</h3>
                        <p class="copytext-xl">Virtual Staging bezieht sich auf die innovative Praxis, Immobilien digital zu möblieren und zu gestalten,
                            um potenziellen Käufern oder Mietern ein realistisches Bild der Räumlichkeiten zu vermitteln. Durch den Einsatz von Computergrafiken
                            und Bildbearbeitungstechniken können leere oder unfertige Immobilien visuell ansprechend eingerichtet werden, was dazu beiträgt,
                            das Interesse der Kunden zu wecken und die Vermarktung zu optimieren.
                            <br><br>
                            <a href="https://doorbit.com/de/blog/neu-virtual-staging.html" target="_blank" data-id="products-portal-mehrerfahrenvirtualstaging">
                                <button class="btn btn-primary" style="color: var(--darkerGray);" control-id="ControlID-1">
                                    <span>Mehr erfahren</span>
                                </button>
                            </a>
                        </p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><title>hand-heart-outline</title><path d="M16 3.23C16.71 2.41 17.61 2 18.7 2C19.61 2 20.37 2.33 21 3C21.63 3.67 21.96 4.43 22 5.3C22 6 21.67 6.81 21 7.76S19.68 9.5 19.03 10.15C18.38 10.79 17.37 11.74 16 13C14.61 11.74 13.59 10.79 12.94 10.15S11.63 8.71 10.97 7.76C10.31 6.81 10 6 10 5.3C10 4.39 10.32 3.63 10.97 3C11.62 2.37 12.4 2.04 13.31 2C14.38 2 15.27 2.41 16 3.23M22 19V20L14 22.5L7 20.56V22H1V11H8.97L15.13 13.3C16.25 13.72 17 14.8 17 16H19C20.66 16 22 17.34 22 19M5 20V13H3V20H5M19.9 18.57C19.74 18.24 19.39 18 19 18H13.65C13.11 18 12.58 17.92 12.07 17.75L9.69 16.96L10.32 15.06L12.7 15.85C13 15.95 15 16 15 16C15 15.63 14.77 15.3 14.43 15.17L8.61 13H7V18.5L13.97 20.41L19.9 18.57Z" /></svg>
                                <h5>optimierte Vermarktung</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><title>palette-swatch-outline</title><path d="M2.5 19.6L3.8 20.2V11.2L1.4 17C1 18.1 1.5 19.2 2.5 19.6M15.2 4.8L20.2 16.8L12.9 19.8L7.9 7.9V7.8L15.2 4.8M15.3 2.8C15 2.8 14.8 2.8 14.5 2.9L7.1 6C6.4 6.3 5.9 7 5.9 7.8C5.9 8 5.9 8.3 6 8.6L11 20.5C11.3 21.3 12 21.7 12.8 21.7C13.1 21.7 13.3 21.7 13.6 21.6L21 18.5C22 18.1 22.5 16.9 22.1 15.9L17.1 4C16.8 3.2 16 2.8 15.3 2.8M10.5 9.9C9.9 9.9 9.5 9.5 9.5 8.9S9.9 7.9 10.5 7.9C11.1 7.9 11.5 8.4 11.5 8.9S11.1 9.9 10.5 9.9M5.9 19.8C5.9 20.9 6.8 21.8 7.9 21.8H9.3L5.9 13.5V19.8Z" /></svg>
                                <h5>Auswahl aus 8 Stilen</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <title>emoticon-cool-outline</title>
                                    <path d="M19,10C19,11.38 16.88,12.5 15.5,12.5C14.12,12.5 12.75,11.38 12.75,10H11.25C11.25,11.38 9.88,12.5 8.5,12.5C7.12,12.5 5,11.38 5,10H4.25C4.09,10.64 4,11.31 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12C20,11.31 19.91,10.64 19.75,10H19M12,4C9.04,4 6.45,5.61 5.07,8H18.93C17.55,5.61 14.96,4 12,4M22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2A10,10 0 0,1 22,12M12,17.23C10.25,17.23 8.71,16.5 7.81,15.42L9.23,14C9.68,14.72 10.75,15.23 12,15.23C13.25,15.23 14.32,14.72 14.77,14L16.19,15.42C15.29,16.5 13.75,17.23 12,17.23Z"/>
                                </svg>
                                <h5>verbessertes Raumgefühl</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-big img-portal-03" style="position:relative; background-size: 90% auto;">
                    <div class="radio-group">
                        <div style="display:flex; gap: .5rem; flex:1;">
                            <input id="before" name="g1" type="radio"><label for="before" style="border-top-left-radius: 30px; border-bottom-left-radius: 30px;">Vorher</label>
                        </div>
                        <div style="display:flex; gap: .5rem; flex:1;">
                            <input id="after" checked="checked" name="g1" type="radio"><label for="after" style="border-top-right-radius: 30px; border-bottom-right-radius: 30px;">Nachher</label>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Section six -->
            <section class="product-height"
                     id="section-6"
                     style="flex-direction: row-reverse;">
                <div class="tertiary-box"
                     style="justify-content: start;">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">04</span></h2>
                        <h3>Marktwertermittlung in Echtzeit</h3>
                        <p class="copytext-xl">Profitieren Sie von automatisierter Immobilienbewertung. Mit Hilfe von Advanced Valuation Models (AVM) bieten wir eine präzise und objektive Schätzung des Immobilienwerts, was den Kauf- oder Mietprozess für alle Beteiligten erheblich beschleunigt.</p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>clock-time-eight-outline</title>
                                    <path d="M12 20C16.4 20 20 16.4 20 12S16.4 4 12 4 4 7.6 4 12 7.6 20 12 20M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2M12.5 12.8L7.7 15.6L7 14.2L11 11.9V7H12.5V12.8Z"/>
                                </svg>
                                <h5>Sofortige Preisermittlung</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>finance</title>
                                    <path d="M6,16.5L3,19.44V11H6M11,14.66L9.43,13.32L8,14.64V7H11M16,13L13,16V3H16M18.81,12.81L17,11H22V16L20.21,14.21L13,21.36L9.53,18.34L5.75,22H3L9.47,15.66L13,18.64"/>
                                </svg>
                                <h5>Datengetriebene Analytik</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>arm-flex-outline</title>
                                    <path d="M7 7.76V16.25H11.08L11.68 15.34C12.84 13.55 14.93 12.75 16.47 12.75C17 12.75 17.45 12.84 17.79 13C18.7 13.41 18.95 14.18 19 14.74C19.08 15.87 18.5 17.03 17.5 17.71C16.6 18.33 14.44 19 11.87 19C10.12 19 7.61 18.69 5.12 17.3C5.41 14.85 6 10.88 7 7.76M7 3C4 7.09 3 18.34 3 18.34C5.9 20.31 9.08 21 11.87 21C14.86 21 17.39 20.21 18.64 19.36C21.64 17.32 21.94 12.71 18.64 11.18C18 10.89 17.26 10.75 16.47 10.75C14.17 10.75 11.5 11.96 10 14.25H9V7.09H11L12 4L7 3Z"/>
                                </svg>
                                <div style="display:flex; flex-direction: column; gap: .75rem;">

                                    <div class="ic-ph-md"
                                         style="background-image: url(assets/ic/price-hubble-logo.svg); background-position: center;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-big img-smartexpose-04"></div>
            </section>
            <!-- Section seven -->
            <section class="product-height background-divider"
                     id="section-7">
                <div class="tertiary-box">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">05</span></h2>
                        <h3>Export der Web-Exposès</h3>
                        <p class="copytext-xl">Schnittstellen und Exportfunktionen, um die Exposés auf den großen Immobilienportalen mit nur einem Klick zu veröffentlichen sowie bequem mit OnOffice oder Propstack zu synchronisieren.</p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>clock-time-eight-outline</title>
                                    <path d="M12 20C16.4 20 20 16.4 20 12S16.4 4 12 4 4 7.6 4 12 7.6 20 12 20M12 2C17.5 2 22 6.5 22 12S17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2M12.5 12.8L7.7 15.6L7 14.2L11 11.9V7H12.5V12.8Z"/>
                                </svg>
                                <h5>Kein manuelles übertragen</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>cursor-default-click-outline</title>
                                    <path d="M11.5,11L17.88,16.37L17,16.55L16.36,16.67C15.73,16.8 15.37,17.5 15.65,18.07L15.92,18.65L17.28,21.59L15.86,22.25L14.5,19.32L14.24,18.74C13.97,18.15 13.22,17.97 12.72,18.38L12.21,18.78L11.5,19.35V11M10.76,8.69A0.76,0.76 0 0,0 10,9.45V20.9C10,21.32 10.34,21.66 10.76,21.66C10.95,21.66 11.11,21.6 11.24,21.5L13.15,19.95L14.81,23.57C14.94,23.84 15.21,24 15.5,24C15.61,24 15.72,24 15.83,23.92L18.59,22.64C18.97,22.46 19.15,22 18.95,21.63L17.28,18L19.69,17.55C19.85,17.5 20,17.43 20.12,17.29C20.39,16.97 20.35,16.5 20,16.21L11.26,8.86L11.25,8.87C11.12,8.76 10.95,8.69 10.76,8.69M15,10V8H20V10H15M13.83,4.76L16.66,1.93L18.07,3.34L15.24,6.17L13.83,4.76M10,0H12V5H10V0M3.93,14.66L6.76,11.83L8.17,13.24L5.34,16.07L3.93,14.66M3.93,3.34L5.34,1.93L8.17,4.76L6.76,6.17L3.93,3.34M7,10H2V8H7V10"/>
                                </svg>
                                <h5>Plattform Offenheit</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>content-copy</title>
                                    <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
                                </svg>
                                <h5>Keine unterschiedlichen Informationen</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-big img-smartexpose-05"></div>
            </section>
            <!-- Section eight -->
            <section class="product-height p-xl-0"
                     id="section-8"
                     style="flex-direction: row-reverse;">
                <div class="tertiary-box"
                     style="justify-content: start;">
                    <div class="box product-box p-0-sm">
                        <h2><span class="highlight">06</span></h2>
                        <h3>Vorschau und Bearbeiten im Webportal</h3>
                        <p class="copytext-xl">Erhalten Sie eine Vorschau Ihrer Exposés und bearbeiten Sie diese im Bedarfsfall in unserem benutzerfreundlichen Webportal. Unsere Plattform ermöglicht eine nahtlose Anpassung und Optimierung Ihrer Immobilienexposés. Teilen Sie das Ergebnis direkt mit Ihren Kollegen, Auftraggebern oder Interessenten.</p>
                        <div class="products-benefits-container">
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>eye-outline</title>
                                    <path d="M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9M12,4.5C17,4.5 21.27,7.61 23,12C21.27,16.39 17,19.5 12,19.5C7,19.5 2.73,16.39 1,12C2.73,7.61 7,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C15.76,17.5 19.17,15.36 20.82,12C19.17,8.64 15.76,6.5 12,6.5C8.24,6.5 4.83,8.64 3.18,12Z"/>
                                </svg>
                                <h5>Sofort das Ergebnis sehen</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>handshake-outline</title>
                                    <path d="M21.71 8.71C22.96 7.46 22.39 6 21.71 5.29L18.71 2.29C17.45 1.04 16 1.61 15.29 2.29L13.59 4H11C9.1 4 8 5 7.44 6.15L3 10.59V14.59L2.29 15.29C1.04 16.55 1.61 18 2.29 18.71L5.29 21.71C5.83 22.25 6.41 22.45 6.96 22.45C7.67 22.45 8.32 22.1 8.71 21.71L11.41 19H15C16.7 19 17.56 17.94 17.87 16.9C19 16.6 19.62 15.74 19.87 14.9C21.42 14.5 22 13.03 22 12V9H21.41L21.71 8.71M20 12C20 12.45 19.81 13 19 13L18 13L18 14C18 14.45 17.81 15 17 15L16 15L16 16C16 16.45 15.81 17 15 17H10.59L7.31 20.28C7 20.57 6.82 20.4 6.71 20.29L3.72 17.31C3.43 17 3.6 16.82 3.71 16.71L5 15.41V11.41L7 9.41V11C7 12.21 7.8 14 10 14S13 12.21 13 11H20V12M20.29 7.29L18.59 9H11V11C11 11.45 10.81 12 10 12S9 11.45 9 11V8C9 7.54 9.17 6 11 6H14.41L16.69 3.72C17 3.43 17.18 3.6 17.29 3.71L20.28 6.69C20.57 7 20.4 7.18 20.29 7.29Z"/>
                                </svg>
                                <h5>Kollaboratives Arbeiten</h5>
                            </div>
                            <div class="divider-v"></div>
                            <div class="products-benefits">
                                <svg class="ic-benefit"
                                     viewBox="0 0 24 24"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <title>monitor-cellphone-star</title>
                                    <path d="M23,11H18A1,1 0 0,0 17,12V21A1,1 0 0,0 18,22H23A1,1 0 0,0 24,21V12A1,1 0 0,0 23,11M23,20H18V13H23V20M20,2H2C0.89,2 0,2.89 0,4V16A2,2 0 0,0 2,18H9V20H7V22H15V20H13V18H15V16H2V4H20V9H22V4C22,2.89 21.1,2 20,2M11.97,9L11,6L10.03,9H7L9.47,10.76L8.53,13.67L11,11.87L13.47,13.67L12.53,10.76L15,9H11.97Z"/>
                                </svg>
                                <h5>Professionelle Darstellung</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="img img-md img-smartexpose-06"></div>
            </section>

            {{overlays}}
            {{footer}}
        </div>
    </body>
</html>