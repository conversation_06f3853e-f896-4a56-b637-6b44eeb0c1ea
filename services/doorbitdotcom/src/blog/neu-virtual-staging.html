<!doctype html>
<html lang="de">
    <head>
        <title>Neu bei Doorbit: Virtual Staging.</title>
		{{head}}
        {{assets-subfolder}}
	</head>
    <body>

        {{header-onelinemenu}}
        <div class="container-footer-content">
            <main class="blogpost">
                <article>
        <!-- Blogpost Content START -->
                    <h1>Neu bei Doorbit: Virtual Staging.</h1>
                    <div class="date">15. Februar 2024</div>

                    <p>
                        <br>
                        Wir bieten dir ab sofort die Möglichkeit, Immobilienbilder mit virtuellen Möbeln und Dekorationen zu versehen.
                        Interessenten können sich so besser vorstellen, wie die Räume eingerichtet aussehen könnten. Die KI erzeugt hierfür
                        in Sekunden fotorealistische Bilder von Räumen und entfernt bei Bedarf auch vorhandene Möbel.
                        <br>
                    </p>

                    <br>
                    <iframe class="youtubevideo" width="560" height="315" src="https://www.youtube.com/embed/otragwJZNEY?si=J8V-NRh3UtgCcJvn" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>

                    <h2>So funktioniert's:</h2>

                    <p>
                        <ol>
                            <li>Melde Dich mit Deinem Doorbit-Konto an.</li>
                            <li>Klicke auf "Exposé erstellen".</li>
                            <li>Lade ein Foto hoch.</li>
                            <li>Die KI versieht das Foto mit Möbeln, Dekorationen und Lampen.</li>
                            <li>Fotos herunterladen und überall nutzen oder direkt auf allen gängigen Immoportalen veröffentlichen.</li>
                        </ol>
                    </p>

                    <h2>Beispiele</h2>
                    <p>
                        Die folgenden Beispiele sind mit Doorbit Virtual Staging entstanden - ganz automatisch, ohne manuelles Platzieren von Möbeln!
                        <br><br>Mit vorheriger Entrümpelung:
                    </p>
                    <a href="../assets/img/vorher-nachher4.webp" target="_blank" data-id="neu-vs-vorhernachher4">
                        <img src="../assets/img/vorher-nachher4.webp" alt="Vorher Nachher 2 Vergleich Virtual Staging">
                    </a>

                    <p>
                        Altes Parkett wird durch Teppiche und moderne Möbel überdeckt:
                    </p>
                    <a href="../assets/img/vorher-nachher2.webp" target="_blank" data-id="neu-vs-vorhernachher2">
                        <img src="../assets/img/vorher-nachher2.webp" alt="Vorher Nachher 2 Vergleich Virtual Staging">
                    </a>

                    <p>
                        Weiteres Beispiel:
                    </p>
                    <a href="../assets/img/vorher-nachher3.webp" target="_blank" data-id="neu-vs-vorhernachher3">
                        <img src="../assets/img/vorher-nachher3.webp" alt="Vorher Nachher 2 Vergleich Virtual Staging">
                    </a>

                    <h2>Preise</h2>

                    <p>
                        Für neuregistrierte Nutzer sind die ersten beiden Virtual Stagings <strong>kostenlos</strong>.
                        <br>
                        Virtual Staging ist Bestandteil der Doorbit-Abonnements und kann in allen Paketen genutzt werden. Als Alternative kannst Du sonst auch einzelne
                        Virtual Stagings flexibel kaufen. Die Preise beginnen ab 2,50 € pro Foto.
                        <br>
                        <h3>Das ist inklusive</h3>
                        <ul>
                            <li>9 Varianten pro Virtual Staging</li>
                            <li>Auswahl aus 4 verschiedenen Raumtypen</li>
                            <li>Auswahl aus 8 verschiedenen Möblierungsstilen</li>
                            <li>Flexible Wahl zwischen Abonnement oder Einzelkauf (oder beides)</li>
                        </ul>
                    </p>

                    <a href="/login/" data-id="neu-vs-login">
                        <button class="btn btn-primary blogpost-cta" style="color: var(--darkerGray);">
                            <span>Zur Registrierung</span>
                        </button>
                    </a>

        <!-- Blogpost Content END  -->
                </article>
            </main>
        </div>
        {{overlays}}
        {{footer}}

    </body>
</html>