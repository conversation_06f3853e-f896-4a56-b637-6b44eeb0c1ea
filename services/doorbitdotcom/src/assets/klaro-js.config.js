export const klaroJsConfig = {
    // With the 0.7.0 release we introduce a 'version' paramter that will make
    // it easier for us to keep configuration files backwards-compatible in the future.
    version: 1,

    // You can customize the ID of the DIV element that <PERSON><PERSON><PERSON> will create
    // when starting up. If undefined, <PERSON><PERSON><PERSON> will use 'klaro'.
    elementID: 'klaro',

    // You can override CSS style variables here. For IE11, <PERSON>lar<PERSON> will
    // dynamically inject the variables into the CSS. If you still consider
    // supporting IE9-10 (which you probably shouldn't) you need to use Klaro
    // with an external stylesheet as the dynamic replacement won't work there.
    /** styling: {
     theme: ['light', 'top', 'wide'],
     }, **/

    // Setting this to true will keep <PERSON><PERSON><PERSON> from automatically loading itself
    // when the page is being loaded.
    noAutoLoad: false,

    // Setting this to true will render the descriptions of the consent
    // modal and consent notice are HTML. Use with care.
    htmlTexts: false,

    // Setting 'embedded' to true will render the Klaro modal and notice without
    // the modal background, allowing you to e.g. embed them into a specific element
    // of your website, such as your privacy notice.
    embedded: false,

    // You can group services by their purpose in the modal. This is advisable
    // if you have a large number of services. Users can then enable or disable
    // entire groups of services instead of having to enable or disable every service.
    groupByPurpose: true,

    // How Klaro should store the user's preferences. It can be either 'cookie'
    // (the default) or 'localStorage'.
    storageMethod: 'cookie',

    // You can customize the name of the cookie that Klaro uses for storing
    // user consent decisions. If undefined, Klaro will use 'klaro'.
    cookieName: 'klaro',

    // You can also set a custom expiration time for the Klaro cookie.
    // By default, it will expire after 120 days.
    cookieExpiresAfterDays: 365,

    // You can change to cookie domain for the consent manager itself.
    // Use this if you want to get consent once for multiple matching domains.
    // If undefined, Klaro will use the current domain.
    //cookieDomain: '.github.com',

    // You can change to cookie path for the consent manager itself.
    // Use this to restrict the cookie visibility to a specific path.
    // If undefined, Klaro will use '/' as cookie path.
    //cookiePath: '/',

    // Defines the default state for services (true=enabled by default).
    default: false,

    // If "mustConsent" is set to true, Klaro will directly display the consent
    // manager modal and not allow the user to close it before having actively
    // consented or declines the use of third-party services.
    mustConsent: true,

    // Show "accept all" to accept all services instead of "ok" that only accepts
    // required and "default: true" services
    acceptAll: true,

    // replace "decline" with cookie manager modal
    hideDeclineAll: false,

    // hide "learnMore" link
    hideLearnMore: false,

    // show cookie notice as modal
    noticeAsModal: false,

    // You can also remove the 'Realized with Klaro!' text in the consent modal.
    // Please don't do this! We provide Klaro as a free open source tool.
    // Placing a link to our website helps us spread the word about it,
    // which ultimately enables us to make Klaro! better for everyone.
    // So please be fair and keep the link enabled. Thanks :)
    disablePoweredBy: true,

    // you can specify an additional class (or classes) that will be added to the Klaro `div`
    // additionalClass: 'my-klaro',

    // You can define the UI language directly here. If undefined, Klaro will
    // use the value given in the global "lang" variable. If that does
    // not exist, it will use the value given in the "lang" attribute of your
    // HTML tag. If that also doesn't exist, it will use 'en'.
    lang: 'de',

    translations: {
        zz: {
            privacyPolicyUrl: '/privacyPolicy/',
        },
        en: {
            privacyPolicy: {
                name: "privacy policy",
                text: "To learn more, please read our {privacyPolicy}."
            },
            consentModal: {
                title: "We respect your privacy",
                description: "Here you can assess and customize the services that we'd like to use on this website. You're in charge! Enable or disable services as you see fit."
            },
            consentNotice: {
                testing: "Testing mode!",
                title: "Cookie Consent",
                changeDescription: "There were changes since your last visit, please renew your consent.",
                description: "Hi! Could we please enable some additional services for {purposes}? You can always change or withdraw your consent later.",
                learnMore: "Let me choose"
            },
            purposes: {
                functional: {
                    title: "Service Provision",
                    description: "These services are essential for the correct functioning of this website. You cannot disable them here as the service would not work correctly otherwise.\n"
                },
                analytics: {
                    title: "Analytics",
                    description: "These services process personal information to help us improve the service that this website offers."
                }
            },
            purposeItem: {
                service: "A simple service that I install on my computer.",
                services: "Several simple services that I install on my computer."
            },
            ok: "Accept all",
            save: "Save",
            decline: "I decline",
            close: "Close",
            acceptAll: "Accept all",
            acceptSelected: "Accept selected",
            service: {
                disableAll: {
                    title: "enable or disable all services",
                    description: "Use this switch to enable or disable all services."
                },
                optOut: {
                    title: "(opt-out)",
                    description: "This services is loaded by default (but you can opt out)"
                },
                required: {
                    title: "(always required)",
                    description: "This services is always required"
                },
                purposes: "Processing purposes",
                purpose: "Processing purpose"
            },
            contextualConsent: {
                description: "Do you want to load external content supplied by {title}?",
                acceptOnce: "Yes",
                acceptAlways: "Always"
            }
        },
        de: {
            privacyPolicy: {
                name: "Datenschutzerklärung",
                text: "Um mehr zu erfahren, lesen Sie bitte unsere {privacyPolicy}."
            },
            consentModal: {
                title: "Wir respektieren Ihre Privatsphäre",
                description: "Hier können Sie die Dienste bewerten und anpassen, die wir auf dieser Website nutzen möchten. Sie haben die Kontrolle! Aktivieren oder deaktivieren Sie Dienste nach Ihrem Ermessen."
            },
            consentNotice: {
                testing: "Testmodus!",
                title: "Cookie-Zustimmung",
                changeDescription: "Es gab Änderungen seit Ihrem letzten Besuch, bitte erneuern Sie Ihre Zustimmung.",
                description: "Hallo! Könnten wir bitte einige zusätzliche Dienste für {purposes} aktivieren? Sie können Ihre Zustimmung jederzeit ändern oder zurückziehen.",
                learnMore: "Mehr informationen"
            },
            purposes: {
                functional: {
                    title: "Funktional",
                    description: "Diese Dienste sind für die korrekte Funktion dieser Website unerlässlich. Sie können sie hier nicht deaktivieren, da der Dienst sonst nicht richtig funktionieren würde."
                },
                analytics: {
                    title: "Analysezwecke",
                    description: "Diese Dienste verarbeiten personenbezogene Informationen, um uns bei der Verbesserung des Dienstes zu helfen, den diese Website anbietet."
                }
            },
            purposeItem: {
                service: "Ein einfacher Dienst, den ich auf meinem Computer installiere.",
                services: "einfache Dienste, die ich auf meinem Computer installiere."
            },
            ok: "Alle akzeptieren",
            save: "Speichern",
            decline: "Alle ablehnen",
            close: "Schließen",
            acceptAll: "Alle akzeptieren",
            acceptSelected: "Ausgewählte akzeptieren",
            service: {
                disableAll: {
                    title: "Alle Dienste aktivieren oder deaktivieren",
                    description: "Verwenden Sie diesen Schalter, um alle Dienste zu aktivieren oder zu deaktivieren."
                },
                optOut: {
                    title: "(Opt-out)",
                    description: "Dieser Dienst wird standardmäßig geladen (aber Sie können sich abmelden)"
                },
                required: {
                    title: "(immer erforderlich)",
                    description: "Dieser Dienst ist immer erforderlich"
                },
                purposes: "Verarbeitungszwecke",
                purpose: "Verarbeitungszweck"
            },
            contextualConsent: {
                description: "Möchten Sie externen Inhalt, bereitgestellt von {title}, laden?",
                acceptOnce: "Ja",
                acceptAlways: "Immer"
            }
        }

    },
    /*
    Here you specify the third-party services that Klaro will manage for you.
    */
    services: [
        {
            name: 'Klaro.js',
            purposes: ['functional'],
            required: true,
            cookies: [
                'klaro',
            ],
            translations: {
                en: {
                    description: "This service is used to manage user consent and preferences for third party services used on this website."
                },
                de: {
                    description: "Dieser Dienst wird verwendet, um die Zustimmung und Einstellungen der Benutzer für Drittanbieterdienste zu verwalten, die auf dieser Website verwendet werden."
                }
            }
        },
        {
            name: 'Keycloak',
            purposes: ['functional'],
            required: true,
            cookies: [
                'KEYCLOAK_SESSION',
                'KEYCLOAK_IDENTITY_LEGACY',
                'KEYCLOAK_IDENTITY',
                'KEYCLOAK_SESSION_LEGACY',
                'AUTH_SESSION_ID_LEGACY',
                'AUTH_SESSION_ID'
            ],
            translations: {
                en: {
                    description: "This service is used to manage user authentication and authorization."
                },
                de: {
                    description: "Dieser Dienst wird verwendet, um die Benutzerauthentifizierung und -autorisierung zu verwalten."
                }
            }
        },
        {
            name: 'google-tag-manager',
            purposes: ['analytics'],
            onAccept: `
                // we notify the tag manager about all services that were accepted. You can define
                // a custom event in GTM to load the service if consent was given.
                for(let k of Object.keys(opts.consents)){
                    if (opts.consents[k]){
                        let eventName = 'klaro-'+k+'-accepted'
                        dataLayer.push({'event': eventName})
                    }
                }
                // if consent for Google Analytics was granted we enable analytics storage
                if (opts.consents[opts.vars.googleAnalyticsName || 'google-analytics']){
                    console.log("Google analytics usage was granted")
                    gtag('consent', 'update', {'analytics_storage': 'granted'})
                }
                // if consent for Google Ads was granted we enable ad storage
                if (opts.consents[opts.vars.adStorageName || 'google-ads']){
                    console.log("Google ads usage was granted")
                    gtag('consent', 'update', {'ad_storage': 'granted'})
                }
            `,
            onInit: `
                // initialization code here (will be executed only once per page-load)
                window.dataLayer = window.dataLayer || [];
                window.gtag = function(){dataLayer.push(arguments)}
                gtag('consent', 'default', {'ad_storage': 'denied', 'analytics_storage': 'denied'})
                gtag('set', 'ads_data_redaction', true)
            `,
            onDecline: `
                // initialization code here (will be executed only once per page-load)
                window.dataLayer = window.dataLayer || [];
                window.gtag = function(){dataLayer.push(arguments)}
                gtag('consent', 'default', {'ad_storage': 'denied', 'analytics_storage': 'denied'})
                gtag('set', 'ads_data_redaction', true)
            `,
            vars: {
                googleAnalytics: 'google-analytics'
            }
        },
        {
            // In GTM, you should define a custom event trigger named `klaro-google-analytics-accepted` which should trigger the Google Analytics integration.
            name: 'google-analytics',
            purposes: ['analytics'],
            cookies: [
                /^_ga(_.*)?/ // we delete the Google Analytics cookies if the user declines its use
            ],
        }
    ],

    /*
    You can define an optional callback function that will be called each time the
    consent state for any given service changes. The consent value will be passed as
    the first parameter to the function (true=consented). The `service` config will
    be passed as the second parameter.
    */
    /* callback: function (consent, service) {
        console.log(
            'User consent for service ' + service.name + ': consent=' + consent
        );
    },*/

};