@font-face {
    font-family: "Montserrat";
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url("fonts/Montserrat-Medium.ttf") format('truetype');
}

@font-face {
    font-family: "Montserrat";
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url("fonts/Montserrat-SemiBold.ttf") format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url("fonts/Roboto-Regular.ttf") format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url("fonts/Roboto-Medium.ttf") format('truetype');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url("fonts/Roboto-Bold.ttf") format('truetype');
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
	position:relative;
    overflow: auto;
    background-color: var(--lightestGray);
    font-family: "Roboto", sans-serif;
    background-image: url(img/s8-products-portal-bg.webp);
    background-size: cover;
    background-position: center;
}

a {
    text-decoration: none;
}

:root {
    --lightestGray: #F8FAFC;
    --lighterGray: #F0F2F4;
    --lightGray: #E0E2E8;
    --gray: #848BA5;
    --darkGray: #444A69;
    --darkerGray: #343951;
    --darkestGray: #2A2E3F;
    --lightestGreen: #ECFDF2;
    --lightGreen: #43E77F;
    --green: #009F64;
    --darkGreen: #005D3A;
    --darkestGreen: #002814;
    --orange: #FF7F54;
    --darkOrange: #F05B29;
    --spacingGapXL: 3rem;
    --spacingGap: 1.5rem;
    --spacingGapSm: 1rem;
    --borderRadius: 2rem;
	--header-height: 71px;
	--footer-height: 3rem;
}
@media screen and (max-width: 600px) {
    :root {
		--header-height: 61px;
    }
}
@media screen and (max-width: 800px) {
    :root {
        --spacingGapXL: 1.5rem;
        --spacingGap: 1rem;
        --spacingGapSm: .75rem;
    }
}
@media screen and (max-height: 800px) {
    :root {
        --spacingGapXL: 1.5rem;
        --spacingGap: 1rem;
        --spacingGapSm: .75rem;
    }
}
@media (min-width: 801px) and (max-width: 1200px) {
    :root {
        --spacingGapXL: 2.25rem;
        --spacingGap: 1.25rem;
        --spacingGapSm: .9rem;
    }
}
@media (min-height: 801px) and (max-height: 1200px) {
    :root {
        --spacingGapXL: 2.25rem;
        --spacingGap: 1.25rem;
        --spacingGapSm: .9rem;
    }
}

.collapse-container:has(.collapsing:checked) {
    box-shadow: 0 2px 9px 0 rgb(0 0 0 / 8%);
    border-radius: var(--borderRadius);
}

.collapse-label {
    display: flex;
    justify-content: start;
    padding: var(--spacingGapSm) var(--spacingGap);
    background: var(--lightestGray);
    border: 1px solid var(--lightGray);
    cursor: pointer;
    border-radius: var(--borderRadius);
    transition: all 0.3s ease-out;
}

.collapse-label:hover > h5 {
    color: var(--darkGreen);
}

.collapse-label > h5::after {
    content: "";
    display: inline-block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid currentColor;
    border-left: 6px solid transparent;
    vertical-align: middle;
    margin-left: 1rem;
    transform: translateY(-2px);
    transition: transform .3s ease-out;
}

.collapsing:checked + .collapse-label > h5::after {
    transform: rotate(180deg);
}

.collapse-content {
    max-height: 0px;
    overflow: hidden;
    transition: max-height .4s ease;
}

.collapsing:checked + .collapse-label + .collapse-content {
    max-height: 25rem;
}

.collapsing:checked + .collapse-label {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    background-color: var(--lightestGray)
}

.collapse-content .content-inner {
    background: var(--lightestGray);
    border: 1px solid var(--lightGray);
    border-bottom-left-radius: var(--borderRadius);
    border-bottom-right-radius: var(--borderRadius);
    padding: var(--spacingGapSm) var(--spacingGap);
    border-top: 0;
    gap: var(--spacingGapSm);
    display: flex;
    flex-direction: column;
}

input.side-menu, input.collapsing {
    display: none;
}

.side-menu {

}

#hubspot-messages-iframe-container {
    margin-right: 16px
}

.appbar {
    position: initial;
    background-color: var(--darkestGray);
    width: 100vw;
    z-index: 3;
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: .5rem 1.5rem;
}

.hamb-line {
    background: var(--lightestGray);
    display: block;
    height: 2px;
    position: relative;
    width: 24px;

}

.hamb-line::before,
.hamb-line::after {
    background: var(--lightestGray);
    content: '';
    display: block;
    height: 100%;
    position: absolute;
    transition: all .2s ease-out;
    width: 100%;
}

.hamb-line::before {
    top: 8px;
}

.hamb-line::after {
    top: -8px;
}

.hamb {
    cursor: pointer;
    padding: 1.5rem;
    display: none;
}

.menu {
    display: flex;
    flex-direction: row;
    list-style: none;
}

.menu > ul {
    padding-left: 2rem;
    list-style: none;
}

.menu a {
    font-size: 1.075rem;
    color: var(--lightGray);
    height: 3.5rem;
    position: relative;
    padding: 1rem 1.5rem;
    border-radius: var(--borderRadius);
}

.menu a:hover {
    background-image: linear-gradient(rgb(255 255 255 / 10%), rgb(255 255 255 / 10%));
}

.menu a.active:before {
    content: "";
    width: 3.125rem;
    height: 4px;
    background-color: var(--lightGreen);
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: var(--borderRadius);
}

.nav {
    max-height: none;
    top: 0;
    position: relative;
    float: right;
    width: fit-content;
    background-color: transparent;
}

.footer {
    height: var(--footer-height);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: .5rem 4rem;
    background-color: var(--darkerGray);
    z-index: 2;
	gap: var(--spacingGapXL);
    width: 100vw;
    color: var(--lightestGray);
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.15);
	flex-wrap: wrap;
}

.menu-footer {
    gap: var(--spacingGapSm);
	list-style: none;
}

.productbar {
    height: var(--header-height);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: .5rem 4rem;
    background-color: var(--darkerGray);
    z-index: 1;
    width: 100vw;
    color: var(--lightestGray);
    box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.logo-container {
    display: flex;
    align-items: center;
	width: 8.75rem;
	height: inherit;
	background-image: url(ic/dbt_logo_white.svg);
	background-repeat: no-repeat;
    background-size: contain;
	background-position: center;
    transform: translateY(-1px);
}

.menu-main {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
}

.container {
    overflow: auto;
    overflow-x: hidden;
    height: calc(100vh - var(--header-height));
    width: 100vw;
    scroll-behavior: smooth;
}
@media (min-width: 1200px) {
	body {
		height: 100vh;
	}
	.container::-webkit-scrollbar {
		width: 17px;
	}

	.container::-webkit-scrollbar-track {
		background-color: var(--lighterGray);
	}

	.container::-webkit-scrollbar-thumb {
		border-radius: var(--borderRadius);
		border: 4px solid transparent;
		background-clip: content-box;
		background-color: #c7cad3;
	}

	.container::-webkit-scrollbar-thumb:hover {
		border: 2px solid transparent;
		background-color: #9ca1af;
	}
}
.container-footer-content {
	overflow: auto;

	height: calc(100vh - var(--header-height) - var(--footer-height));
	width: 100vw;
	scroll-behavior: smooth;
	display: flex;
	align-items: center;
	flex-direction: column;
	gap: 2rem;
	padding: var(--spacingGapXL);
}
.stmp {
	gap: 3rem;
}
.stmp ul {
	padding-left: 2rem;
    gap: 0.75rem;
    display: flex;
    flex-direction: column;
}
.stmp-grid-item a:hover {
	color: var(--green)
}
.stmp-grid-item li a {
	color: var(--darkGreen);
	display: flex; 
	align-items: center; 
	gap: .25rem;
}
.stmp-grid-item h4 a, .stmp-grid-item h5 a {
	color: inherit;
	font-size: inherit;
	font-weight: inherit;
}
.stmp-grid-item {
	display: flex; 
	flex-direction: column; 
	gap: 1rem; 
	max-width: 400px; 
	width: 100%;
	align-items: start;
}

.overview-height-footer {
    height: calc(100vh - var(--header-height) - var(--footer-height));
}

.product-height {
    height: calc(100vh - 2 * var(--header-height));
}

.product-height-footer {
    height: calc(100vh - 2 * var(--header-height) - var(--footer-height));
}

section {
    height: calc(100vh - var(--header-height));
    width: calc(100vw - 17px);
    scroll-snap-align: start;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 4rem;
    box-sizing: border-box;
}

.faq {
    position: relative;
    background-image: url(img/s8-products-portal-bg.webp);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}

.faq::after {
    content: "";
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: var(--lighterGray);
    mix-blend-mode: multiply;
    top: var(--header-height);
    left: 0;
}

.box {
    padding: var(--spacingGapXL);
    display: flex;
    flex-direction: column;
    gap: var(--spacingGap);
    z-index: 1;
}

.box-price {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: var(--spacingGapXL);
    z-index: 1;
    justify-content: space-between;
    align-items: start;
    flex: 1;
    border: 1px solid transparent;
    border-radius: var(--borderRadius);
	position: relative;
}
a.box-price:hover {
   transform: scale(1.05);
	background-blend-mode: soft-light;
}

.box-price-green {
    background: -webkit-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -webkit-linear-gradient(235deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
    background: -o-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -o-linear-gradient(235deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
    background: linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    linear-gradient(325deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
}

.box-price-orange {
    background: -webkit-linear-gradient(var(--darkerGray), var(--darkestGray)) padding-box,
    -webkit-linear-gradient(235deg, var(--darkerGray) 80%, rgba(255, 127, 84, 0.6)) border-box;
    background: -o-linear-gradient(var(--darkerGray), var(--darkestGray)) padding-box,
    -o-linear-gradient(235deg, var(--darkerGray) 80%, rgba(255, 127, 84, 0.6)) border-box;
    background: linear-gradient(var(--darkerGray), var(--darkestGray)) padding-box,
    linear-gradient(325deg, var(--darkerGray) 80%, rgba(255, 127, 84, 0.6)) border-box;
}
.box-price-white {
    background: -webkit-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -webkit-linear-gradient(235deg, var(--darkerGray) 80%, rgba(255, 255, 255, 0.6)) border-box;
    background: -o-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -o-linear-gradient(235deg, var(--darkerGray) 80%, rgba(255, 255, 255, 0.6)) border-box;
    background: linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    linear-gradient(325deg, var(--darkerGray) 80%, rgba(255, 255, 255, 0.6)) border-box;
}

.box-features {
    max-width: 1600px;
    align-items: center;
    width: 100%;
    flex: 1;
    justify-content: space-evenly;
}

.box-features-grid {
    display: grid;
    width: 100%;
    gap: var(--spacingGap);
    grid-template-columns: repeat(3, 1fr);
}
.box-features-grid .secondary-box {
	padding:var(--spacingGap);
}
.box-test {
	flex-direction: row;
}

.product-box {
    text-align: center;
    align-items: center;
    max-width: 40rem;
}

.product-box-white {
    background-color: var(--lightestGray);
    border-radius: var(--borderRadius);
    background: rgba(248, 250, 252, 0.80);
    backdrop-filter: blur(17px);
    text-align: center;
    align-items: center;
    max-width: 40rem;
}

.primary-box {
    align-items: flex-start;
    max-width: 40rem;
}

.primary-box-dm {
    background-color: var(--darkestGray);
    border-radius: var(--borderRadius);
    background: rgba(40, 43, 54, 0.80);
    backdrop-filter: blur(17px);
    align-items: flex-start;
    max-width: 40rem;
}

.primary-box-dm-v {
    align-items: flex-start;
    max-width: 45rem;
}

.secondary-box-dm {
    flex: 1;
    justify-content: start;
    align-items: center;
    border: 1px solid transparent;
    border-radius: var(--borderRadius);
    gap: var(--spacingGapSm);
    background: -webkit-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -webkit-linear-gradient(235deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
    background: -o-linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    -o-linear-gradient(235deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
    background: linear-gradient(var(--darkerGray), var(--darkerGray)) padding-box,
    linear-gradient(325deg, var(--darkerGray) 80%, rgba(67, 231, 127, 0.6)) border-box;
}

.secondary-box-dm ul {
    padding-left: 1rem;
}

.secondary-box {
    flex: 1;
    height: 100%;
    justify-content: start;
    align-items: center;
    background-color: var(--lighterGray);
    align-self: stretch;
    border: 1px solid var(--lightGray);
    border-radius: var(--borderRadius);
    gap: var(--spacingGapSm);
    text-align: center;
    transition: background-color ease-out .3s;
}

a > .secondary-box:hover {
    background-color: var(--lightGray)
}

.tertiary-box {
    display: flex;
    justify-content: end;
    align-items: center;
    flex: 1;
    height: 100%;
}

.products-benefits {
    flex: 1;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: .5rem;
}

.products-benefits-container {
    display: flex;
    flex-direction: row;
    align-items: start;
    gap: 2rem;
    width: 100%;
}

.benefits-container {
    display: flex;
    flex-direction: row;
    gap: var(--spacingGapXL);
    width: 100%;
    color: var(--lightestGray)
}

.divider-v {
    border-right: 1px solid var(--lightGray);
    height: 5rem;
    align-self: center;
}

.divider-h {
    border-bottom: 1px solid var(--gray);
    margin: 1rem 0rem;
}

.btn {
    display: flex;
    padding: .8rem 1.25rem;
    justify-content: center;
    height: 3rem;
    box-sizing: border-box;
    align-items: center;
    border-radius: var(--borderRadius);
    border: 0;
    gap: .5rem;
}

.btn-xs {
    display: flex;
    padding: .5rem 1rem;
    justify-content: center;
    height: 2rem;
    box-sizing: border-box;
    align-items: center;
    border-radius: var(--borderRadius);
    border: 0;
    gap: .5rem;
	background-color: var(--darkGray)
}

.btn:hover {
    background-image: linear-gradient(rgb(255 255 255 / 10%), rgb(255 255 255 / 10%));
}

.btn-dm-primary {
    background: var(--lightGreen);
    color: var(--darkestGray);
}

.btn-dm-primary:hover, .btn-primary:hover {
    background-image: linear-gradient(rgb(0 0 0 / 15%), rgb(0 0 0 / 10%));
}

.btn-dm-secondary {
    background: var(--darkGreen);
}

.btn-dm-secondary p {
    color: var(--lightestGray);
}

.btn-dm-tertiary {
    background: transparent;
    border: 1px solid var(--gray);
}

.btn-dm-tertiary p {
    color: var(--lightestGray);
}

.btn-dm-tertiary > p {
    color: var(--lightestGray);
    font-size: 1rem;
    line-height: normal;
}

.btn-primary {
    background: var(--orange);
}

.btn-primary p {
    color: var(--darkestGray);
}

.btn-secondary {
    background: var(--Green);
}

.btn-secondary p {
    color: var(--lightestGray);
}

.btn-group {
    display: flex;
    flex-direction: row;
    gap: var(--spacingGap);
    align-items: center;
}
.btn-group-header {
    display: flex;
    flex-direction: row;
    gap: var(--spacingGapSm);
    align-items: center;
}
.btn-media-group {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    align-items: center;
}
.btn-media {
	height: 20px; 
	width: 20px; 
	display: block;
	padding: 1rem;
	background-repeat: no-repeat;
	background-size: 20px, 20px;
    background-position: center;
}

.input-group {
    display: flex;
    flex-direction: row;
    gap: var(--spacingGapSm);
    width: 100%;
    align-items: center;
    max-width: 50rem;
	background-color: var(--lightestGray); 
	height: 5rem; 
	padding: 2rem; 
	border-radius: 4rem; 
	box-shadow: 0 2px 10px 0px rgb(0 0 0 / 20%); 
	background: rgb(255 255 255 / 80%); 
	backdrop-filter: blur(8px);
}
.img {
	display: flex;
	flex: 1;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
	z-index: 1;
}
.img-big {

    padding: 2rem;
    background-size: 80% auto;
    min-height: 20rem;
}

.img-big-fullsize {
    background-size: cover;
    background-position: center;
}

.img-md {

    padding: 2rem;
    background-size: 60% auto;
    min-height: 20rem;
}

.img-sm {
    background-size: contain;
    min-height: 10rem;
    max-height: 10rem;
    margin-bottom: 1rem;
}

.img-sign {
	 background-repeat: no-repeat; 
	background-position: center; 
	width: 100%;
}

.img-targets {
    width: 7rem;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    border-radius: 1rem;
}
.osm-lic {
	position: absolute;
    z-index: 2;
    background-color: var(--lighterGray);
    font-size: .8rem;
    width: max-content;
    padding: 0.2rem 0.4rem;
    bottom: 0;
    right: 0;
}

.flex-1 {
    flex: 1;
}

.p-xl-0 {
    padding: 0;
}

/* ==== SECTION ONE ==== */
.one {
    background-repeat: no-repeat;
    background-size: cover;
    flex-direction: row;
    position: relative;
    background-image: url(img/doorbit_section1.webp);
}

.one-products-expose {
    background-color: var(--darkestGray);
    position: relative;
}

.one-products-expose:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    background-image: url("img/s1-products-smartexpose.webp");
    background-repeat: no-repeat;
    background-size: auto 120%;
    background-position: right 30%;
}

.s1-video {
    object-fit: cover;
    width: 125%;
    height: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
}
.s1-video-position {
	display:flex; 
	justify-content:center; 
	align-items:center; 
	flex:1; 
	height: 100%;
}

.fab-arrow {
    position: absolute;
    bottom: 2rem;
    right: 50%;
    width: 4rem;
    height: 4rem;
    background: rgba(40, 43, 54, 0.5);
    backdrop-filter: blur(17px);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

/* ==== SECTION TWO ==== */

.two {
    background-color: var(--darkestGray);
    flex-direction: column;
}

.two-products {
    flex-direction: column;
}

.flip-card {
    background-color: transparent;
    max-width: 433px;
    height: 200px;
    width: 100%;
    perspective: 800px;
}

.flip-card-container {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.3s ease-out;
    transform-style: preserve-3d;
    border-radius: var(--borderRadius);
}

.flip-card:hover .flip-card-container {
    transform: rotateY(-180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: var(--borderRadius);
}

.flip-card-front {
    border: 1px solid var(--gray);
    display: flex;
    justify-content: center;
    align-items: center;
}

.flip-card-back {
    background-color: var(--darkerGray);
    transform: rotateY(-180deg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: var(--spacingGap);
}

.ic-big {
    width: 60px;
    height: 60px;
    background-repeat: no-repeat;
    background-size: contain;
}
.ic-ph-sm {
    width: 104px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: contain;
}
.ic-ph-md {
    width: 143px;
    height: 33px;
    background-repeat: no-repeat;
    background-size: contain;
}
.btn-shiny {
	display: flex;
	position: relative;
	padding: .225rem 1rem; 
	border-radius: 3rem; 
	max-width: max-content;
	border: 1px solid var(--lightGreen); 
	font-size: .9rem;
	overflow: hidden;
}
.btn-shiny-o {
	color: var(--orange);
	border: 1px solid var(--orange); 
}
.btn-shiny:before {
	content: "";
    display: block;
    position: absolute;
    height: 100%;
    top: 0;
    background: rgba(255, 255, 255, 0.6);
    width: 60px;
    left: 0;
    filter: blur(30px);
    animation: btn-shiny 4.5s ease-in-out infinite;
 } 
@keyframes btn-shiny {
  0% {
    opacity: 0.5;
    transform: translateX(-100px) skewX(-15deg);
    transition-property: opacity, transform;
  }
  20% {
    opacity: 0.6;
    transform: translateX(300px) skewX(-15deg);
    transition-property: opacity, transform;
  }
  100% {
    opacity: 0.6;
    transform: translateX(300px) skewX(-15deg);
    transition-property: opacity, transform;
  }
}

/* ==== SECTION FIVE ==== */

.five-products-expose {
    background-color: var(--darkestGray);
    position: relative;
}

.five-products-expose:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url("img/s5-products-smartexpose.webp");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}

.img-faq {
    background-color: var(--darkestGray);
    position: relative;
}

.img-faq:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url(img/doorbit_section6.webp);
    background-repeat: no-repeat;
    background-size: cover;
    top: 0;
    left: 0;
    background-position: top right;
    z-index: -1;
}

.faq-header {
    display: flex;
    gap: 1rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    background-color: var(--darkerGray);
    padding: var(--spacingGap);
    height: 20rem;
    z-index: 1
}

.faq-footer {
    display: flex;
    gap: 1rem;
    flex-direction: row;
    justify-content: center;
    align-items: start;
    width: 100%;
    background-color: var(--lightestGray);
    padding: var(--spacingGapXL);
    z-index: 1;
}
/* ==== SECTION NINE ==== */
.nine {
    background: linear-gradient(var(--lightestGray) 60%, var(--darkestGray) 60%);
    position: relative;
}

.nine:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 40%;
    bottom: 0;
    right: 0;
    background-image: url(img/doorbit_section6.webp);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: right 70%;
    opacity: .5;
}

.img-s9-products-portal {
    background-image: url(img/s4-portal.webp);
    height: 560px;
    width: 100%;
    background-size: contain;
    display: flex;
    padding: 2rem;
    background-repeat: no-repeat;
    background-position: center center;
    z-index: 1;
}
svg.ic-benefit {
	fill:var(--green);
    height: 36px;
	width: 36px;
	min-width: 36px;
}
.ph-powered-box {
	display:flex; 
	flex-direction: row; 
	align-items: center;
	gap: .75rem;
}
.price-table-stack {
	gap: var(--spacingGap);
}
.price-table-container {
	display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
	transform: scale(.9);
    gap: var(--spacingGapXL);
}
.job-row {
	padding: 0.75rem 1.5rem;
	border-radius: var(--borderRadius)
}
.job-row:nth-of-type(2n +1) {
	background-color: var(--lighterGray);
}
.job-row:hover {
	background-color: var(--lightGray);
}
.job-row-grid {
	display: grid; 
	gap: 1rem; 
	grid-template-columns: 2fr 1fr 1fr 1fr;
}

/* ==== Images SRC & Mobile Optimized ==== */
.img-aboutus-intro-bg {
    background-color: var(--darkestGray);
    position: relative;
}

.img-aboutus-intro-bg:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    background-image: url(img/s1-about-us.webp);
    background-repeat: no-repeat;
    background-size: auto 120%;
    background-position: right 30%;
}
.img-portal-03-bg {
    background-color: var(--lightestGray);
    position: relative;
}

.img-portal-03-bg:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url(img/s5-products-portal.webp);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}
.img-smartenergy-03-bg {
    background-color: var(--lightestGray);
    position: relative;
}

.img-smartenergy-03-bg:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url(img/s5-products-smartenergy_wo_solar.webp);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}

.img-portal-intro-bg {
    background-color: var(--darkestGray);
    position: relative;
}

.img-portal-intro-bg:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    background-image: url(img/s1-products-portal.webp);
    background-repeat: no-repeat;
    background-size: auto 110%;
    background-position: right bottom;
}
.img-smartenergy-intro-bg {
    background-color: var(--darkestGray);
    position: relative;
}
.img-smartenergy-intro-bg:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    background-image: url(img/s1-products-smartexpose.webp);
    background-repeat: no-repeat;
    background-size: auto 120%;
    background-position: right 30%;
}
.img-pricetable {
    background-color: var(--darkestGray);
    position: relative;
	padding: unset;
}

.img-pricetable:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url(img/doorbit_section6.webp);
    background-repeat: no-repeat;
    background-size: cover;
    opacity: .5;
    top: 0;
    left: 0;
}
.img-smartexpose-home {
	background-image: url(img/s3-smartexpose.webp);
}
.img-portal-home {
	background-image: url(img/s4-portal.webp);
}
.img-smartenergy-home {
	background-image: url(img/s5-smartenergy.webp);
}
.img-smartexpose-intro {
	background-image: url(img/s1-products-smartexpose_app.webp);
    background-size: 70% auto;
    background-position: 30% center;
}
.img-smartexpose-01 {
	background-image: url(img/s3-products-smartexpose.webp);
}
.img-smartexpose-02 {
	background-image: url(img/s6-products-smartexpose.webp);
	position: relative;
}
.img-smartexpose-03 {
	background-image: url(img/s4-products-smartexpose.webp);
}
.img-smartexpose-04 {
	background-image: url(img/s8-products-smartexpose-price.webp);
	mix-blend-mode: darken;
}
.img-smartexpose-05 {
	background-image: url(img/s7-products-smartexpose.webp); 
	mix-blend-mode: darken;
}
.img-smartexpose-06 {
	background-image: url(img/s3-products-portal-web.webp), url(img/s3-products-portal.webp);
    background-size: 80% auto, cover;
}
.img-portal-intro {
	background-image: url(img/s4-portal-web.webp);
}
.img-portal-02 {
	background-image: url(img/s4-products-portal.webp);
}
.img-portal-03:before {
	content: "";
	position: absolute;
	opacity: 0;
	background-image: url(img/virtual-staging1.webp)
}
.img-portal-03:has(input[id=before]:focus, input[id=before]:checked) {
	background-image: url(img/virtual-staging1.webp)
}
.img-portal-03:has(input[id=after]:focus, input[id=after]:checked) {
	background-image: url(img/virtual-staging2.webp)
}
.img-portal-03{
	transition: background-image ease-out .3s;
}
.img-portal-04 {
	background-image: url(img/s6-products-portal.webp);
}
.img-portal-05 {
	background-image: url(img/s7-products-portal.webp);
}
.img-portal-06 {
	background-image: url(img/s8-products-portal.webp);
}
.img-smartenergy-intro {
	background-image: url(img/s5-smartenergy.webp);
    background-size: 70% auto;
    background-position: 30% center;
}
.img-smartenergy-01 {
	background-image: url(img/s3-products-smartenergy.webp);
}
.img-smartenergy-02 {
	background-image: url(img/Massedaten_screen.webp);
}
.img-smartenergy-04 {
	background-image: url(img/3_screens.webp);
}
.img-smartenergy-05 {
	background-image: url(img/s6-products-smartenergy.webp);
}
.img-smartenergy-06 {
	background-image: url(img/Export.webp);
}
.img-smartenergy-07 {
	background-image: url(img/s9-products-smartenergy.webp);
}
.img-aboutus-01 {
	background-image: url(img/s2-about-us.webp); 
	mix-blend-mode: darken;
}
.img-aboutus-02 {
	background-image: url(img/s3-about-us.webp);
}
                     
@media (max-width: 800px) {
	.img-pricetable:before {
    	background-image: url(img/mobile/doorbit_section6.webp);
	}
	.img-smartexpose-home {
		background-image: url(img/mobile/s3-smartexpose.webp);
	}
	.img-portal-home {
		background-image: url(img/mobile/s4-portal.webp);
	}
	.img-smartenergy-home {
		background-image: url(img/mobile/s5-smartenergy.webp);
	}
	.img-smartexpose-intro {
		background-image: url(img/mobile/s1-products-smartexpose_app.webp);
        background-size: contain;
        background-position: center;
	}
	.one-products-expose:before {
		background-image: url(img/mobile/s1-products-smartexpose.webp);
	}
	.img-smartexpose-01 {
		background-image: url(img/mobile/s3-products-smartexpose.webp);
	}
	.img-smartexpose-02 {
		background-image: url(img/mobile/s6-products-smartexpose.webp);
	}
	.img-smartexpose-03 {
		background-image: url(img/mobile/s4-products-smartexpose.webp);
	}
	.img-smartexpose-04 {
		background-image: url(img/mobile/s8-products-smartexpose-price.webp);
	}	
	.img-smartexpose-05 {
		background-image: url(img/mobile/s7-products-smartexpose.webp); 
	}
	.img-smartexpose-06 {
		background-image: url(img/mobile/s3-products-portal-web.webp), url(img/mobile/s3-products-portal.webp);
	}
	.img-portal-intro {
		background-image: url(img/mobile/s4-portal-web.webp);
		background-position: 30% center;
	}
	.img-portal-intro-bg:before {
        background-size: auto 100%;
        opacity: .2;
        background-position: right center;
    	background-image: url(img/mobile/s1-products-portal.webp);
    }
	.img-portal-02 {
		background-image: url(img/mobile/s4-products-portal.webp);
	}
	.img-portal-03-bg:before {
		background-image: url(img/mobile/s5-products-portal.webp);
	}
	.img-portal-04 {
		background-image: url(img/mobile/s6-products-portal.webp);
	}
	.img-portal-05 {
		background-image: url(img/mobile/s7-products-portal.webp);
	}
	.img-portal-06 {
		background-image: url(img/mobile/s8-products-portal.webp);
	}
	.img-aboutus-intro-bg:before {
		background-image: url(img/mobile/s1-about-us.webp);
	}
	.img-aboutus-01 {
		background-image: url(img/mobile/s2-about-us.webp);
	}
	.img-aboutus-02 {
		background-image: url(img/mobile/s3-about-us.webp);
	}
}

.phone-nr {
	font-size: .95rem;
}
@media (max-width: 1450px) {
	.phone-nr {
		display: none;
	}
}
/* ==== Mobile Optimized ==== */

@media (max-width: 800px) {
	.price-table-container {
		transform: unset;
	}
    #hubspot-messages-iframe-container {
        margin-right: 0px
    }
	.appbar .btn p {
        display: none;
    }
	.appbar {
		position: fixed;
	}
	.job-row-grid {
		grid-template-columns: 2fr 1fr 1fr;
	}
	.job-row-grid > p:last-of-type {
		display: none;
	}
	.ph-powered-box {
		flex-direction: column; 
	}
	svg.ic-benefit {
		height: 24px;
		width: 24px;
		min-width: 24px;
	}
	.faq-footer {
		gap: 0rem;
		flex-direction: column;
		align-items: center;
	}
	.menu-footer{
		padding: 1rem 0;
		flex-wrap: wrap;
		justify-content: center;
	}

	.container, .container-footer-content {
		height: auto !important;
	}
	.premium-grid > .premium-container {
        flex-direction: column !important;
    }
	.input-group {
		flex-direction: column;
		height: auto; 
	}
	.img-s9-products-portal {
		height: auto;
		background-image: url(img/s5-products-portal.webp);
		background-size: cover;
	}
	.box-test {
		flex-direction: column;
	}
    .p-0-sm {
        padding: 0rem 0rem 1rem  !important;
    }

    .faq-header {
        height: 15rem;
    }

    .flip-card-back, .flip-card:hover .flip-card-container {
        transform: unset;
    }

    .flip-card-front {
        display: none;
    }

    .flip-card p {
        font-size: 1.2rem;
    }

    .hidden-md {
        display: none !important;
    }

    .show-md {
        display: inherit;
    }

    .primary-box-dm {
        max-width: unset;
        border-radius: unset;
		width: 100%;
    }

    section.one, section.one-about-us {
        padding: unset;
    }

    section {
        flex-direction: column-reverse !important;
    }

	section:first-of-type {
		margin-top: var(--header-height);
	}

    .s1-video {
        object-fit: unset;
        width: 100%;
        height: auto;
        position: unset;
        border-radius: 0rem;
    }
	.s1-video-position {
		flex-direction: column;
	}

    html {
        font-size: 14px;
    }

    .img-big, .img-md, .img-big-fullsize {
        background-size: contain;
    }

    .img-sm {
        min-height: 10rem !important;
    }

    h1, h1 > span.highlight-dm, h1 > span.highlight {
        font-size: 2.5rem !important;
    }

    h2, h2 > span.highlight-dm, h2 > span.highlight {
        font-size: 2.25rem !important;
    }

    h3, h3 > span.highlight {
        font-size: 2rem !important;
    }

    h5, h5 > span.highlight {
        font-size: 1.1rem !important;
    }

    p {
        font-size: 1.2rem !important;
    }

    .one-products-expose:before {
        opacity: .5;
    }

    .img-targets {
        width: 6rem;
        height: 80%;
    }

    .copytext-dm {
        text-align: left;
    }

    .box-features-grid {
        grid-template-columns: repeat(auto-fill, minmax(min(170px, 100%), 1fr)) !important;
    }
	.btn-group {
		flex-direction: column;
		width: 100%;
	}
}

@media (max-height: 820px) {
    section, section.product-height {
        height: auto !important;
    }

    .container {
        scroll-snap-type: unset;
    }

    .img-big, .img-md {
        background-size: contain;
        max-height: 400px;
    }

    .fab-arrow {
        display: none;
    }

    .box {
        padding: 2rem;
    }
}

@media (max-height: 1000px) {
	.box-price {
		padding: 1.5rem;
		gap: 1rem;
	}
	.price-table-container {
		gap: var(--spacingGapSm);
	}
}

@media (max-width: 1200px) {
	.price-table-stack {
		transform: unset;
	}
    .products-benefits-container {
        gap: 1rem;
		flex-direction: column;
    }
	.products-benefits + .divider-v {
        display: none;
    }
	.products-benefits {
		flex-direction: row;
	}
	.ic-benefit + h5 {
		text-align: start;
	}
	.footer {
		height: unset;
		min-height: var(--footer-height);
		gap: 1rem;
    	padding: 2rem;
	}
	.osm-lic {
		font-size: .8rem !important;
		right: 50%;
		transform: translateX(50%);
	}
    .p-xl-0 {
        padding: 2rem;
    }

    .img-sm {
        min-height: 15rem;
    }

    .background-divider {
        background-color: var(--lighterGray);
    }

    .box-features-grid {
        grid-template-columns: 1fr 1fr;
    }

    .tertiary-box {
        justify-content: center;
    }

    .hidden-xl {
        display: none !important;
    }

    .box {
        padding: 2rem;
    }

    .fab-arrow {
        display: none;
    }

    .show-xl {
        display: inherit;
    }

    section, section.product-height {
        height: auto;
    }

    section {
        width: 100vw;
        gap: 2rem;
    }

    .overview-height-footer {
        height: auto;
    }

    .product-height-footer {
        height: auto;
    }

    .product-height {
        height: calc(100vh - var(--header-height));
    }

    .container {
        scroll-snap-type: unset;
    }

    .img-big, .img-md {
        background-size: contain;
    }
}

@media (min-width: 801px) {
    .show-md {
        display: none !important;
    }
}

@media (min-width: 1201px) {
    .show-xl {
        display: none !important;
    }
}

@media (max-width: 1200px) {

    .nav {
        max-height: 0;
        transition: max-height .3s ease-out;
        width: 100%;
        height: 100%;
        position: fixed;
        background-color: var(--darkestGray);
        overflow: hidden;
        top: var(--header-height);
        left: 0;
        display: flex;
        align-items: flex-start;
        z-index: 3;
    }

    .benefits-container {
        flex-direction: column;
    }

    .appbar {
        padding: 0.5rem 1.5rem 0.5rem 1rem;

    }

    .menu {
        flex-direction: column;
        width: 100%;
        padding: 2rem;
		height: 100%;
    }

    .menu a {
        display: block;
    }

    .hamb {
        display: block;
    }

    .side-menu:checked ~ nav {
        max-height: calc(100% - var(--header-height));
    }

    .side-menu:checked ~ .hamb .hamb-line {
        background: transparent;
    }

    .side-menu:checked ~ .hamb .hamb-line::before {
        transform: rotate(-45deg);
        top: 0;
    }

    .side-menu:checked ~ .hamb .hamb-line::after {
        transform: rotate(45deg);
        top: 0;
    }

    .menu a.active:before {
        width: 1.25rem;
        bottom: 46%;
        left: 0;
        transform: rotate(90deg);
    }
}

@media screen and (max-width: 1400px) {
    .premium-grid {
        grid-template-columns: 1fr !important;
    }

    .premium-grid > .premium-container {
        flex-direction: row;
    }

    .premium-container > .img-sm {
        min-width: 10rem;
        min-height: auto;
    }

}

/* ==== Typography ==== */

h1, h1 > span.highlight, h1 > span.highlight-dm {
    font-family: "Montserrat", sans-serif;
    color: var(--darkestGray);
    font-size: 3.125rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
	hyphens: auto;
}

h1 > span.highlight-dm {
    color: var(--lightGreen);
}

h1 > span.highlight {
    color: var(--green);
}

.h-dm {
    color: var(--lightestGray);
}

h2, h2 > span.highlight-dm, h2 > span.highlight {
    color: var(--darkestGray);
    font-family: "Montserrat", sans-serif;
    font-size: 2.75rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.3px;
	hyphens: auto;
}

h2 > span.highlight-dm {
    color: var(--lightGreen);
}

h2 > span.highlight {
    color: var(--green);
}

h3, h3 > span.highlight {
    color: var(--darkerGray);
    font-family: "Montserrat", sans-serif;
    font-size: 2.25rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
	hyphens: auto;
}

h3 > span.highlight {
    color: var(--lightGreen);
}

h4, h4 > span.highlight {
    color: var(--darkestGray);
    font-family: "Montserrat", sans-serif;
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
	hyphens: auto;
}

h4 > span.highlight {
    color: var(--lightGreen);
}

h5, h5 > span.highlight {
    color: var(--darkestGray);
    font-family: "Roboto", sans-serif;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
	hyphens: auto;
}

h5 > span.highlight {
    color: var(--lightGreen);
}

p {
    color: var(--darkGray);
    font-size: 1.313rem;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    text-align: left;
    max-width: 650px;
    width: 100%;
	hyphens: auto;
}

a {
    color: var(--darkGray);
    font-size: 1.188rem;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
}

a > button:hover {
    cursor: pointer;
}

p.copytext-xl {
    margin-bottom: .6rem;
}

p.copytext-sm {
    font-size: 1.063rem;
}

p.copytext-input {
    font-size: 1.1rem;
    color: var(--gray);
}

p.copytext-dm {
    color: var(--lightGray);
}

.btn p, .btn span {
    font-family: "Roboto", sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 700;
    letter-spacing: 1.25px;
    white-space: nowrap;
}

a.active {
    color: var(--lightestGray);
}

.menu-footer > li > a {
    color: var(--lightGray);
    height: 2rem;
    position: relative;
    padding: 0.5rem 1rem;
    line-height: normal;
    border-radius: var(--borderRadius);
    box-sizing: border-box;
    font-size: 14px;
}

a, a + img {
    color: var(--lightGreen);
    text-decoration: unset;
}

a:hover.no-hover {
    background-image: unset;
}

a.no-hover {
    opacity: .5;
}

.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    visibility: hidden;
    opacity: 0;
    z-index: 3;
    transition: opacity .5s;

}

.overlay:target {
    visibility: visible;
    opacity: 1;
    z-index: 4;
}

.popup {
    margin: var(--header-height) auto;
    background: #fff;
    border-radius: 5px;
    width: 100%;
    max-width: 55rem;
    position: relative;
    max-height: calc(100vh - var(--header-height));
    overflow: auto;
}

.popup .close {
    position: absolute;
    top: 1rem;
    right: 2rem;
    font-size: 2rem;
    font-weight: bold;
    text-decoration: none;
    color: var(--green);
}

.popup .close:hover {
    color: var(--darkGreen);
}

.popup .content {
    max-height: 30%;
    overflow: auto;
}

.premium-text {
    display: flex;
    flex-direction: column;
	align-items: center;
    gap: 1rem;
	flex: 2;
}

.premium-container {
    padding: var(--spacingGapXL)
}

.premium-grid {
    display: grid;
    width: 100%;
    gap: var(--spacingGap);
    grid-template-columns: repeat(auto-fit, minmax(min(400px, 100%), 1fr));
}

table.table-function-overview {
	width:100%;
	padding: var(--spacingGap);
}
table.table-function-overview tbody {
	display: flex;
	flex-direction: column;
    border: 1px solid rgb(0 0 0 / 5%);
    border-radius: 1rem;
}
table.table-function-overview tr {
	display: flex;
	align-items: center;
    gap: 1rem;
}
table.table-function-overview tr:nth-of-type(2n +1) {
	background-color: var(--lighterGray)
}
table.table-function-overview tr:first-of-type {
	background-color: var(--lightGray);
    border-start-start-radius: 1rem;
    border-start-end-radius: 1rem;
}
table.table-function-overview tr:last-of-type {
    border-end-start-radius: 1rem;
    border-end-end-radius: 1rem;
}
table.table-function-overview tbody tr th:first-of-type, table.table-function-overview tbody tr td:first-of-type {
	flex: 3;
	display: flex;
	justify-content: start;
}
table.table-function-overview th, table.table-function-overview td {
	flex: 1;
	display: flex;
    justify-content: center;
	padding: 1rem;
}

#klaro {
    font-family: "Roboto", sans-serif;
    font-size: 1rem;

    .cookie-modal {
        z-index: 9999;
    }

    .cookie-modal a {
        color: var(--green);
    }

    .cm-header h1 {
        font-size: 1.5rem;
    }

    .cm-header p {
        font-size: 1rem;
    }

    .cm-modal {
        background-color: var(--darkestGray);
        border-radius: var(--borderRadius);
        padding: 1rem;
    }

    .cookie-notice p, .cm-modal h1, .cm-modal p {
        color: var(--lightGray);
    }

    .cm-required, .cm-list-title {
        color: var(--lightGray);
    }

    .cm-btn {
        height: 36px;
        background: var(--lighterGray);
        color: var(--darkestGray);
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: var(--borderRadius);
        padding-left: 16px;
        padding-right: 16px;
    }

    .cm-btn-accept-all {
        background: var(--green);
        color: var(--lightestGray);
    }

    .slider {
        box-shadow: none;
    }

    .cm-list-input:not(:checked) + .cm-list-label .slider:before {
        background-color: var(--gray)
    }

    .cm-list-input:checked + .cm-list-label .slider {
        background-color: var(--green);
    }

    .cm-list-description, .purposes {
        color: var(--lightGray) !important;
    }

    @media (max-width: 600px) {
        .cm-footer-buttons {
            flex-flow: column-reverse;
            gap: 16px;
        }
    }
}

/* ######## */
/* BLOG-OVERVIEW */
/* ######## */

.blogoverview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));

    max-width: 60rem;
    gap: 2rem;
}

@media (min-width: 683px) and (max-width: 800px) {
    .container-footer-content:has(.blogoverview) + .footer {
        position: fixed;
        bottom: 0;
    }
}

.blogoverview-date {
    color: darkgray;
    font-size: .9rem;
}

.blogoverview a {
    color: var(--darkGray);
    font-size: initial;
    font-style: initial;
    font-weight: initial;
    line-height: initial;
    display: flex;
    flex-direction: column;
    gap: .25rem;
}

.youtubevideo {
    width: 100%;
    height: 400px;
}

.blogoverview p {
    color: gray;
    font-family: Roboto,sans-serif;
    font-size: 1.1rem !important;
    margin-top: .5rem;
}

.blogoverview img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin-bottom: 0.25rem;
}

/* ######## */
/* BLOGPOST */
/* ######## */

main.blogpost {
    font-family: Arial, sans-serif;
    max-width: 50rem;
    margin: 0 auto;
}

@media (max-width: 800px) {
    main.blogpost {
        margin-top: 5rem;
    }
}

@media (min-width: 683px) and (max-width: 800px) {
    .container-footer-content:has(.blogpost) + .footer {
        position: fixed;
        bottom: 0;
    }
}

.blogpost h1, .blogpost h2, .blogpost p {
    margin-top: 10px;
    margin-bottom: 10px;
    max-width: 100%;
}

.blogpost a {
    color: #128b3e;
}

.blogpost h1 {
    font-size: 1.8rem!important;
    margin-bottom: 5px;
}

.blogpost h2 {
    font-size: 1.5rem!important;
    margin-top: 25px;
}

.blogpost h3 {
    font-size: 1.2rem !important;
    margin-top: 20px;
}

.blogpost p, .blogpost li {
    font-size: 1.2rem!important;
    color: var(--darkGray);
    line-height: 140%;
    text-align: left;
    width: 100%;
    hyphens: auto;
}

.blogpost ul, .blogpost ol {
    padding-left: 20px;
    padding-top: 10px;
}

.blogpost ul li {
    list-style: disc;
}

.blogpost img {
    max-width: 100%;
    height: auto;
}

.blogpost.date {
    text-align: left;
    font-size: 0.9rem;
    margin-top: 5px;
    margin-bottom: 25px;
}

.blogpost-cta {
    margin-top: 15px;
    margin-bottom: 15px;
}

input[type=radio] {
	opacity: 0;
	position: absolute;
}

input[type=radio] + label {
	flex: 1;
	height: 50px;
	background-color: white;
	border: 1px solid var(--lightGray);
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 500;
	padding: 1rem;
}

.radio-group {
	display: flex;
	flex-direction: row;
	position: absolute;
	bottom: 18%;
	left: 50%;
	transform: translateX(-50%);
}
@media (max-width: 800px) {
   .radio-group {
	bottom: -5%;
	left: 50%;
	transform: translateX(-50%);
}
}

.radio-group label {
	cursor: pointer;
}

input[type=radio]:focus + label, input[type=radio]:checked + label {
	background-color: var(--green);
	color: white;
	border: 1px solid var(--green);
}

.coming-soon {
    color: #d9d9d9;
    font-size: 1.5rem;
}

.coming-soon span {
    border: 1px solid darkgrey;
    border-radius: 25px;
    padding: 5px;
}

  