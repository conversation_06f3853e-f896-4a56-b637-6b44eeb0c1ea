document.addEventListener('DOMContentLoaded', function() {
    var pathname = window.location.pathname; // Extrahiert den Pfad der URL

    if (pathname.includes('blog')) {
        enableMenuItem('.nav-blog');
    } else if (pathname.includes('index')) {
        enableMenuItem('.nav-smartenergy');
        enableMenuItem('.nav-products');
    } else if (pathname.includes('products-smartexpose')) {
        enableMenuItem('.nav-smartexpose');
        enableMenuItem('.nav-products');
    } else if (pathname.includes('about-us')) {
        enableMenuItem('.nav-aboutus');
    } else if (pathname.includes('faq')) {
        enableMenuItem('.nav-faq');
    } else if (pathname.includes('prices')) {
        enableMenuItem('.nav-prices');
    } else if (pathname.includes('news')) {
        enableMenuItem('.nav-news');
    } else if (pathname.endsWith('/')) {
        enableMenuItem('.nav-smartenergy');
        enableMenuItem('.nav-products');
    }
});

function enableMenuItem(selector) {
    Array.from(document.querySelectorAll(selector)).forEach(function(element) {
        element.classList.add('active');
    });
}
