{% extends "base.html" %}

{% block header %}
  <header class="md-header" data-md-component="header">
    <nav class="md-header__inner md-grid" aria-label="{{ lang.t('header.title') }}">

      {# Link to home - Corrected to point to site_url #}
      <a
        href="{{ config.site_url | url }}"
        title="{{ config.site_name | e }}"
        class="md-header__button md-logo"
        aria-label="{{ config.site_name }}"
        data-md-component="logo"
      >
        {% include "partials/logo.html" %} {# Include the actual logo content #}
      </a>

      {# Header title #}
      <div class="md-header__title" data-md-component="header-title">
        <div class="md-header__ellipsis">
          <div class="md-header__topic">
            <span class="md-ellipsis">
              {{ config.site_name }}
            </span>
          </div>
          <div class="md-header__topic" data-md-component="header-topic">
            <span class="md-ellipsis">
              {% if page.meta and page.meta.title %}
                {{ page.meta.title }}
              {% else %}
                {{ page.title }}
              {% endif %}
            </span>
          </div>
        </div>
      </div>

      {# Include search if enabled #}
      {% if "search" in config.plugins %}
        <label class="md-header__button md-icon" for="__search">
          {% include ".icons/material/magnify.svg" %}
        </label>
        {% include "partials/search.html" %}
      {% endif %}

      {# Include repository link if enabled #}
      {% if config.repo_url %}
        <div class="md-header__source">
          {% include "partials/source.html" %}
        </div>
      {% endif %}

      {# Include palette toggle if enabled #}
      {% if config.theme.palette and not config.theme.palette is mapping %}
        {% include "partials/palette.html" %}
      {% endif %}

    </nav>

    {# Include tabs if enabled #}
    {% if config.theme.features and "navigation.tabs" in config.theme.features %}
      {% if nav | selectattr("active") | list | length > 1 or "navigation.tabs.sticky" in config.theme.features %}
         {% include "partials/tabs.html" %}
      {% endif %}
    {% endif %}
  </header>
{% endblock %}
