---
description: "Schritt-für-Schritt-Anleitung zur Bearbeitung von Gebäudemodellen und Optimierung für den Evebi-Export."
---

# Gebäudemodelle bearbeiten und für Evebi-Export optimieren: Schritt-für-Schritt-Anleitung

1\. **In doorbit Studio in der 2D oder 3D Ansicht** mit einem Blick an den rotgefärbten Wänden erkennen, welche Räume noch bearbeitet werden müssen.

![](attachments/3D_Vorschau_pruefen.jpeg){.styled-image}

2\. **Wandverschiebmodus aktivieren:** Die 2D Ansicht Öffnen und in den Wandverschiebemodus wechseln.

![](attachments/Wandverschiebemodus_aktivieren.jpeg){.styled-image}

3\. **Wände anpassen:** Ziehe rote Punkte mit gedrückter Maustaste an die richtige Position.

![](attachments/stack_animation_waende_anpassen.webp){.styled-image}

4\. **<PERSON>ßenwandstärken im Evebi-Modus pflegen**, damit die Netto- und Bruttomaße richtig in EVEX Format exportiert werden. Mit Klick auf "Außenwandstärken überschreiben" kann die Stärke gleich für mehrere Außenwände übernommen werden. (1)
{ .annotate }

 1.  :woman_raising_hand: Die in doorbit Studio gepflegten Wandstärken werden bei der Bestimmung der Bruttomaße nicht herangezogen, sondern nur die im Evebi-Modus gepflegten Außenwandstärken.

![](attachments/Aussenwandstaerke_evebi-modus.png){.styled-image}

5\. **Objekt exportieren:** Nachdem alle Anpassungen vorgenommen worden sind, kann die EVEX Datei exportiert werden. (1)
{ .annotate }

 1.  :woman_raising_hand: Alternativ kann auch eine IFC Datei zwecks Import in das ECAD/CASCADOS exportiert werden, um ggf. komplexe Gebäude dort weiter zu bearbeiten.

![](attachments/export.png){.styled-image}

6\. **Datei mit Evebi öffnen**: Von Zonen bis Teilflächen ist alles enthalten. 

![](attachments/evebi.png){.styled-image}