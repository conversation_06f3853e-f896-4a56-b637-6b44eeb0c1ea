---
description: "Übersicht der Workflows in doorbit, einschließlich Datenerfassung, Gebäudemodell-Integration und Datenvorschlägen."
---

# Workflows

Workflows stehen sowohl in der App als auch mobil oder am Desktop über den Browser zur Verfügung und benötigen keine spezielle Hardware.

Workflows haben 3 zentrale Funktionen:

1. Erfassung zusätzlicher Informationen zum Objekt bzw. Projekt, beispielsweise Kundenwünsche
2. Übertragung von Informationen direkt ins Gebäudemodell (aktuell nur für [U-Wert Assistenten](../doorbit-dokumentation/schritt-fr-schritt-anleitungen/u-wert-assistent.md)) oder für den Export (aktuell nur die Angabe zur Dichtheit des Gebäudes bei Geschossdetails)
3. Bereitstellung von Datenvorschlägen aus dem Gebäudemodell oder öffentlichen Datenquellen wie dem Zensus. Diese Vorschläge werden durch ein entsprechendes Symbol ![Vorschlagssymbol](./attachments/image%20(9)-20241209-093536.png){.styled-image}
 gekennzeichnet.

Aktuell verfügbare Workflows:

- **Basisdaten:** Status, Projekttitel und Gebäudeart
- **Lage:** Automatische GPS-Erkennung oder manuelle Bestimmung
- **U-Wert Assistent:** Basierend auf Empfehlungen aus dem [Bundesanzeiger :material-open-in-new:](https://www.bundesanzeiger.de/pub/publication/qzQUGd8A3unSCCbVMcf?0){:target="_blank"}
- **Keller und Heizung**: Details zu Stellplatz, Heizungsart, Baujahr und Leistung der Heizungsanlage
- **Geschossdetails**: Infoabfrage zu Klimaanlage, Dichtheit des Gebäudes, Lüftung, Solaranlage, etc.
- **Dachgeschoss:** Angaben zu Dämmungsstärke, Dachsparrenstärke, Kniestock, Spitzboden
- **Erneuerbare Energien:** Informationen zur Anlage, einschließlich Art, Leistung, Fläche, Neigung, Himmelsrichtung und Batteriekapazität
- **Sonstige Infos:** Verbrauchsdaten hochladen, Nutzungsverhalten, Kundenwünsche, allgemeine Notizen.