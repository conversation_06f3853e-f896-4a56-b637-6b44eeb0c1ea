---
description: "Erklärung der Beziehung zwischen Teilflächen und Bauteilen in Evebi sowie der Export- und Importprozesse."
---
draft: true
# Export zu gbXML / Import zu Evebi

### Beziehung zwischen Teilflächen und Bauteilen in Evebi

Doorbit arbeitet zur flexiblen Anpassbarkeit der Teilflächen nur mit Teilflächen. Jede Innenwand zum Beispiel, die auf eine Außenwand trifft, erzeugt an der Schnittstelle 2 Teilflächen.

![image-20241213-142648.png](./attachments/image-20241213-142648.png){.styled-image}

Evebi unterscheidet zwischen Teilflächen (T1 - T7) und Bauteilen (N, O, S, W).  
Mehrere Teilflächen werden logisch zu einem Bauteil zusammengefasst, wenn mehrere Teilflächen dieselben Werte für diese Eigenschaften aufweisen:

- Himmelsausrichtung
- Konstruktion

Diese Z<PERSON>mmenfassung ist vorteilhaft, damit etwa in einem erstellten ISFP Printdokument lediglich von wenigen Außenwänden die Rede ist (Etwa “Die Nordwand mit U-Wert 1.5”). Würde Evebi ebenfalls nur mit Teilflächen arbeiten, so würde stets mit einer großen Menge von eigens benannten Wänden (Etwa “Außenwand Nord von Raum1” und “Außenwand Nord von Raum3”, usw.) gearbeitet werden.

## Zustandekommen von Teilflächenbezeichnungen in Evebi

Doorbit liefert für alle Teilflächen eine lautsprechende Bezeichnung, etwa:

- Außenwand (N) im Raum 1 - EG für *T1*
- Außenwand (N) im Raum 3 - EG für *T2*

Die Bauteilermittlung in Evebi fasst T1 und T2 nun zu einem Bauteil zusammen. Nun erhalten *T1* und *T2* in Evebi unter “*Teilflächen*” die Bezeichnung des **Bauteils**. Die unterschiedlichen Bezeichnungen der beiden Bauteile von Doorbit gehen nach unseren Erkenntnissen verloren, bzw. nutzt Evebi die im GbXML zuerst gelesene <Name> Attribut des <Surface> für alle Teilflächen desselben Bauteils.

#### Hintergrund

Solange zwei im GbXML vorkommende Teilflächen einen Azimuth von 0.0 (Nord) aufweisen, fasst Evebi diese beiden Teilflächen zu einer zusammen und nutzt den Namen der zuerst aus dem XML geparsten Teilfläche für beide Teilflächen.

Sobald man Azimuth von 0.0 auf etwa 10.0 ändert, erkennt Evebi, dass es sich bei dieser Teilfläche nicht mehr um eine perfekte Nordwand handelt und erzeugt ein *neues Bauteil.* Sodann erhält dieses Bauteil dann auch den Namen der angelieferten Teilfläche.

Evebi löst durch diese Vereinfachung ein Dilemma. Da GbXml bautechnisch gesehen nur Teilflächen anliefert und keine Bauteile, muss Evebi sich entscheiden: Entweder es übernimmt die Namen der Teilflächen aus dem GbXml in der Ansicht “Teilflächen”, dann stellt sich aber die Frage wie das Bauteil benannt werden soll, da es für dieses ja keinen Bezeichner im GbXML gibt.

Oder Evebi verwendet die erstbeste Teilflächenbezeichnung sowohl für die Bauteilbezeichnung, als auch für alle Teilflächen. Evebi hat sich für diese Lösung entschieden, zugunsten von Konsistenz aber zulasten der Flexibilität in der Bezeichnung der Teilflächen.
<!--
### Mögliche Lösungen

1. Allgemeinere Bezeichnung von Teilflächen in Doorbit

Statt den Raumbezug in die Teilfläche mit einzuschließen, müsste der Name deutlich vereinfacht werden. Es darf weder der Raumbezug, noch die Stockwerksbezeichnung im Namen vorkommen. Lediglich ein Name, wie “Außenwand (N)” wäre erlaubt.

1. Änderung der Namenslogik in Evebi

Doorbit und Evebi könnten im Rahmen einer Kooperation gemeinsam an einer Lösung arbeiten. So könnte Doorbit sowohl einen Namen für das entstehende Bauteil, als auch die Namen der Teilflächen liefern, sodass Evebi sowohl die Teilflächenbezeichner von Doorbit in die Evebi “Teilflächen” Ansicht übernehmen könnte, als auch dem entstehenden Bauteil einen lautsprechenden Namen zu geben. Beispiel:

T1: “Außenwand (N) im Bad”  
T2: “Außenwand (N) im Wohnzimmer”  
Bauteil: Außenwand (N) 
-->