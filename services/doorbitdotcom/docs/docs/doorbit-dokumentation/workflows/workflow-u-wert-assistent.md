---
description: "Anleitung zur Nutzung des U-Wert Assistenten zur Ermittlung der energetischen Qualität von Bauteilen."
---

# Workflow: U-Wert Assistent

Der U-Wert Assistent kann optional durchgeführt werden. Dieser ermöglicht auf Basis der im [Bundesanzeiger](https://www.bundesanzeiger.de/pub/publication/qzQUGd8A3unSCCbVMcf?0){.external} veröffentlichten “Bekanntmachung der Regeln zur Datenaufnahme und Datenverwendung im Wohngebäudebestand” vom 08.10.2020 die vereinfachte Ermittlung der energetischen Qualität bestehender Bauteile.

Der U-Wert Assistent ermöglicht die vereinfache Ermittlung von U-Werten auf Basis einiger weniger Angaben und bietet somit die Grundlage für die erste Orientierung. Die vorgeschlagenen U-Werte können selbstverständlich überschrieben werden.

Für folgende Bauteile ist der U-Wert Assistent einsetzbar:

- Fenster
- Außentüren
- Außenwände
- Unterste Geschossböden
- Oberste Geschossdecke

Vorgeschlagene Werte erkennt man am KI-Symbol: ![image (9)-20241209-093536.png](./attachments/image%20(9)-20241209-093536.png){.styled-image}

Die U-Werte aus dem U-Wert Assistenten werden automatisch bis auf “Oberste Geschossdecke” in das doorbit Studio übernommen und sind auch im Evebi sowie IFC Export enthalten. Die U-Werte können auch weiterhin im doorbit Studio im Nachgang zum U-Wert Assistenten jederzeit an jedem Bauteil einzeln im Bedarfsfall angepasst werden. 

Beispiel für exportiertes Objekt inkl. U-Werte in Evebi:

![image (8)-20241209-093427.png](./attachments/image%20(8)-20241209-093427.png){.styled-image}

Beispiel für exportiertes Objekt inkl. U-Werte über IFC in Hottgenroth:

![image (7)-20241209-093422.png](./attachments/image%20(7)-20241209-093422.png){.styled-image}

Eine Schritt-für-Schritt Klickanleitung findet sich [hier](../../doorbit-dokumentation/schritt-fr-schritt-anleitungen/u-wert-assistent.md).