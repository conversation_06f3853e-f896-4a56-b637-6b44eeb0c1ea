---
description: "Anleitung zur Bearbeitung und Anpassung von Wänden in doorbit Studio, einschließlich polygonaler Wände."
---

# Wände

:material-creation:{.icons} Innen- und Außenwände werden automatisch erkannt. <PERSON><PERSON><PERSON> diese einen gemeinsamen Raum bilden, wird auch der Raum automatisch erkannt.

:material-wall:{.iconsred} Rot gefärbte Wände zeigen an, dass diese Wände an mindestens einem Ende nicht mit einem anderen Element verbunden sind. Das ist ein Indiz dafür, dass ggf. dort die Wände nachbearbeitet werden müssen.

:material-cursor-move:{.icons} Im **doorbit Studio** kann man in der 2D-Ansicht:

- **Wände verschieben, in der Größe ändern und verbinden**: Bei Auswahl des Wandverschiebemodus werden grüne (verbunden) und rote (nicht verbundene) Verbindungspunkte anzeigt. 
!!! note "Es stehen 2 verschiedene Verschiebe-Optionen zur Verfügung:"
    - :material-set-all:{.icons} **Gemeinsam verschieben:** alle verbundenen Punkte und damit Wände, werden mit verschoben bzw. in der Größe verändert
    - :material-set-none:{.icons} **Frei verschieben:** der selektierte Punkt bzw. die selektierte Wand wird losgelöst von anderen Verbindungspunkten und damit ohne Auswirkungen auf das restliche Gebäudemodell verschoben
- **Neue Wände zeichnen**: Ergänzen Sie fehlende Wände im Gebäudemodell.


:material-vector-polygon-variant:{.icons} Wandmaße und polygonale Wände können sowohl in der 2D- als auch 3D Ansicht geändert werden:

- **Wandmaße** wie Breite, Höhe und Stärke **ändern**. Wandstärken sowie -höhen können auf einzelne Wände, Stockwerke oder das gesamte Gebäude mit einem Klick übertragen werden.
- Über die **Form**-Auswahl können rechteckige Wände in polygonale und runde Wände geändert werden.

![Formanpassung und Wandstärken sowie Wandhöhen übertragen](../attachments/wandstaerken_aendern.png){.styled-image}