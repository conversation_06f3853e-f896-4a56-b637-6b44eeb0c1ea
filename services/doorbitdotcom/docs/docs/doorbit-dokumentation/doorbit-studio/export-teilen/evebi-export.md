---
description: "<PERSON><PERSON><PERSON><PERSON>, wie Sie Daten aus Doorbit zu Evebi exportieren und welche Informationen im Export enthalten sind."
---

# Evebi Export

Doorbit ermöglich den Export der meisten in doorbit erhobenen und automatisch ermittelten Daten zu Evebi, einem Produkt von [Envysis](https://www.envisys.de/startseite/){: .external}. Diese Seite erklärt den Exportvorgang und die wichtigsten Konzepte.

## **Export durchführen**

Um das Gebäude in Evebi zu exportieren:

1. Zum doorbit Studio navigieren
2. Oben recht den Menüpunkt “Export” anklicken
3. Evebi-Export auswählen
4. Die erzeugte Datei hat die Endung .evex und kann direkt in Evebi geöffnet werden

![](../attachments/Evebi Export/image.png){.styled-image}

## **Im Export enthaltene Daten**

Grundsätzlich ist Doorbit bestrebt, möglichst viele der in Doorbit erhobenen Daten zu Evebi zu exportieren. Aktuell werden folgende Gebäudeinformationen exportiert.

**Allgemeine Daten:**

- Projekttitel
- Cover Foto und Gebäudefotos werden unter “Allgemeine Daten” in Evebi angezeigt
- Fotos von Schwachstellen werden im ISFP unter “Zustand” angezeigt
- Die Objekt- und Kundenadresse entspricht der Projektadresse in doorbit
- Die Planeradresse inkl. Firmenlogo (entspricht den Daten, die im doorbit-Profil hinterlegt sind)
- Angabe zur Dichtheit des Gebäudes (diese Angabe kann im Workflow **Geschossdetails** vorgenommen werden)
- Baujahr wie im U-Wert Assistenten ausgewählt
- Gebäudeposition (freistehend, einseitig/zweiseitig angebaut)
- Lage (innerorts, außerorts)

**Technische Daten:**

- Komplette Gebäudestruktur  inkl. “Wohnen”-Zone, Räume (auch Raumbuch Pro), Konstruktionen, Bauteilen und Teilflächen mit Netto und Bruttogrößen
- Raumhöhen, Stockwerkshöhen, NGF, BGF aller Räume
- Offsets für jedes Bauteil
- Maße von Öffnungen inkl. Brüstungshöhen bei Fenstern
- Automatische Raumtemperaturen sowie Luftverbundangaben in Abhängigkeit der Raumkategorie (entspricht der Raumnutzung)
- Wärmeübergänge für Wände, Fenster und Decken/Böden

## So erzeugt doorbit Konstruktionen, Bauteile und Teilflächen

### Hierarchie der Bauteile

Evebi verwendet drei Hauptkategorien, die aufeinander aufbauen:

1. Konstruktion (oberste Ebene)
2. Bauteil (mittlere Ebene)
3. Teilfläche (unterste Ebene)

Wichtige Regeln:

![image.png](../attachments/Evebi Export/image%201.png){.styled-image}

1. Jede Teilfläche (gelb) ist einem Bauteil (orange) zugewiesen. Mehrere Teilflächen können zu einem  Bauteil gehören.
2. Jedes Bauteil hat genau eine Konstruktion.
3. Mehrere Bauteile können dieselbe Konstruktion haben.

Diese Gesetzmäßigkeiten hält doorbit beim Evebi Export ein.

### Konstruktionen

Doorbit erstellt Konstruktionen automatisch basierend auf:

**Für Wände**

- Außenwand / Innenwand
- Wandstärke
- U-Wert

**Für Türen, Fenster und Öffnungen**

- In Außenwand / In Innenwand
- U-Wert
- Länge & Höhe des Bauteils

**Für Böden und Decken**

- U-Wert

Alternativ kann dieses Verhalten durch die  manuelle “Konstruktionauswahl” im doorbit Studio im Bedarfsfall am Bauteil ergänzt werden.

### Konstruktionsauswahl in doorbit Studio

Standardmäßig erzeugt doorbit pro unterschiedlichem U-Wert eine neue Konstruktion in Evebi.
Es gibt Fälle, in denen eine spezielle Wand eine eigene Konstruktion benötigt, z. B. weil genau eine Wand im Gebäude nachträglich gedämmt wurde. Um für diese Wand eine eigene Konstruktion zu erzeugen, kann entweder direkt der U-Wert an dieser Wand gepflegt oder stattdessen direkt eine eigene Konstruktion für diese Wand ausgewählt werden. Hierfür kann der Wand (oder mehreren Wänden) eine beliebige Konstruktion wie folgt zugeordnet werden.

![](../attachments/Evebi Export/image%202.png){.styled-image}

Um dieser speziellen Wand eine eigene Konstruktion geben zu können, muss eine selbstgewählte Konstruktionsgruppe zwischen A und G gewählt werden. Sodann wird für diese Wand eine eigene Konstruktion und ein neues Bauteil erstellt, welches die neue Konstruktion zugewiesen bekommt.

### Teilflächen und Bauteile

Jede Wand, die mit einer anderen Wand verbunden ist, ist eine Teilfläche in Evebi. Teilflächen entstehen automatisch an Wandverbindungen (Flächen die zwischen zwei Verbindungspunkten liegen) wie das untere Beispiel illustriert:

![](../attachments/Evebi Export/image%203.png){.styled-image}

Doorbit gruppiert Teilflächen zu Bauteilen nach folgenden Kriterien zusammen:

**Für Wände**

- Stockwerk
- Innenwand/Außenwand
- Befindet sich in Beheizt/Unbeheizter Zone
- Himmelsrichtung (N, O, S, W + NO, SO, SW, NW)
- U-Wert
- Wandstärke
- Ermittelter Wärmeübergang ([was ist das?](#ermittlung-der-warmeubergange))

**Für Türen, Fenster und Öffnungen**

- Stockwerk
- Innenwand/Außenwand
- Befindet sich in Beheizt/Unbeheizter Zone
- Himmelsrichtung (N, O, S, W + NO, SO, SW, NW)
- U-Wert
- Länge & Breite
- Ermittelter Wärmeübergang ([was ist das?](#ermittlung-der-warmeubergange))

**Für Böden und Decken**

- Stockwerk
- Befindet sich in Beheizt/Unbeheizter Zone
- U-Wert
- Ermittelter Wärmeübergang ([was ist das?](#ermittlung-der-warmeubergange))

💡Zur Erstellung eines eigenen, neuen Bauteils von z. B. einer ganz bestimmten Wand, kann die Funktion [Konstruktionsauswahl](#konstruktionsauswahl-in-doorbit-studio) verwendet werden. Da jedem Bauteil eine Konstruktion zugewiesen ist, erzeugt eine neue Konstruktion auch automatisch ein neues Bauteil.

## Ermittlung der Wärmeübergänge

In Evebi werden an Bauteilen die Wärmeübergänge über die Auswahl “grenzt an” gepflegt:

![](../attachments/Evebi Export//image%204.png){.styled-image}

Doorbit nimmt die Bestimmung der Wärmeübergänge automatisch wie folgt vor.

💡
**Wichtig**: Für eine optimale Berechnung sind folgende Angaben entscheidend:

1. Ist der Raum beheizt oder nicht und um welche Raumkategorie es sich handelt (entspricht der Raumnutzung)
2. Stockswerksart (Vollgeschoss, Kellergeschoss, Dachgeschoss)
3. Pflege von Zwischenwänden bei Reihenhäusern
4. Baujahr zur Ermittlung, ob Perimeterdämmung vorhanden ist ja/nein

Die Ermittlung unterstützt eine Vielzahl von Fällen sowohl für Wände, als auch für Bodenplatten.

**Beispiel:**

![](../attachments/Evebi Export/image%205.png){.styled-image}

Durch die dunkle Färbung erkennbar: Es handelt sich um ein Kellergeschoss.

1. Schwarze Wände. Wärmeübergang ist “Kellerwand gegen Erdreich”. (Bei Vollgeschoss: Außenluft)
2. Innenwand zwischen Hobbyraum und Garage. Wärmeübergang ist von beheizt zu unbeheizt. In Evebi lautet die Bezeichnung “Wand zu unbeheiztem Keller”.

**Fälle, die doorbit unterscheidet:**

**Für Wände**

- Kelleraußenwand gegen Erdreich
- Außenwand gegen Luft
- Zwischenwände gelten als “Beheizt zu Beheizt”
- Innenwand zu unbeheiztem Keller mit und ohne Perimeterdämmung
- Innenwand zu unbeheiztem Raum
- Innenwand zu niedrig beheiztem Raum
- Innenwand zu beheiztem Raum (Beheizt zu Beheizt)

**Für Böden**

- Kellerfußboden zu Erdreich
- Beheizter Keller unter EG Bodenplattensegment
- Unbeheizter Keller unter EG (mit/ohne Perimeterdämmung)
- Bodenplatte zu Erdreich (ohne Keller)
- Bodenplatte gegen beheiztem Raum (Beheizt zu Beheizt)

**Spezialfall “Wand zu unbeheiztem Keller”**

Bei Gebäuden ab Baujahr 1995 geht doorbit automatisch von einer vorhandenen Perimeterdämmung aus.

