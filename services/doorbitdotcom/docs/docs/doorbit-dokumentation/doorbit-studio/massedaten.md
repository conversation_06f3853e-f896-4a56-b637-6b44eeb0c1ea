---
description: "Detaillierte Beschreibung der Massedatenberechnung für Gebäudemodelle, einsch<PERSON>ßlich Flächen, Volumen und spezifischer Parameter für Räume, Wände, Geschosse und Gebäude."
---

# Massedaten für Gebäudemodelle

Dieses Dokument beschreibt die Massedaten für verschiedene Elemente in Gebäudemodellen. Es defini<PERSON>, wie Flächen, Volumen und andere Maße für Räume, Wände, Geschosse und Gebäude berechnet werden.

## Allgemeine Berechnungsmethoden

### Brutto- und Nettowerte
- **Bruttowerte** bezeichnen die Gesamtflächen oder -längen von Bauteilen ohne Abzug von Öffnungen.
- **Nettowerte** entsprechen den Bruttowerten abzüglich aller Öffnungen wie Türen und Fenster.

### Innenwandflächen
- Bei der Berechnung von Innenwandflächen in Bezug auf das Gebäude oder Stockwerk werden **beide Seiten** der Wand berücksichtigt.
- Bei der Berechnung von Raumflächen wird nur die Seite der Innenwand berücksichtigt, die **im dem Raum sichtbar** ist.

### Außenwandstärken und Bodenplatten-/Deckenhöhen im Evebi-Modus wirken sich auf Bruttoaußenflächen aus
- Bei den Bruttoaußenflächen werden die im Evebi-Modus gepflegten Außenwandstärken sowie Bodenplatten- und Deckenhöhe berücksichtigt.
- Die Bodenplatten- und Deckenhöhen werden beim Scannen automatisch ermittelt und beeinflussen die Geschosshöhe (Stockwershöhe).
- Diese Werte können im Stockwerksmenü jederzeit nachträglich angepasst werden.
- Standardmäßig ist eine Außenwandstärke von 18 cm für alle Außenwände im Evebimodus hinterlegt.
- Die Außenhöhe der Wände wird berechnet als: Innenhöhe + Plattenhöhe.
- Die im Evebi-Modus eingestellte Außenwandstärke und Plattenhöhe haben direkte Auswirkungen auf die Bruttowerte der Außenwände:
  - Bruttolänge = In doorbit-Modus angezeigte Länge + im Evebi-Modus gepflegte Außenwandstärke (bei orthogonal verbundenen Wänden)
  - Die Außenwandstärke wird zur Wandlänge addiert, wenn die Wand mit einer anderen orthogonalen Wand verbunden ist (90 Grad ± 22,5 Grad).
  - Bei als Zwischenwand markierten Außenwänden wird nur eine halbe Wandstärke zur Bestimmung der Außenlänge verwendet.

### BGF-Berechnung
- Die Brutto-Grundfläche (BGF) bei Bodenplatten wird vereinfacht berechnet: 
  - BGF = Netto-Grundfläche (NGF) × 1,2

## Massedaten nach Elementtyp

### 1. Raumdaten
Räume sind die grundlegenden Einheiten innerhalb eines Gebäudes und haben folgende Massedaten:

| Parameter | Beschreibung |
|-----------|-------------|
| Höhe | Lichte Höhe des Raums in m |
| NGF | Netto-Grundfläche des Raums in m² |
| NRI | Raumvolumen in m³ |
| BGF | Brutto-Grundfläche des Geschosses in m² (NGF × 1,2) |
| BRI | Brutto-Rauminhalt des Geschosses in m³ (BGF × durchschnittliche Raumhöhe) |
| Raumumfang | Umfang des Raums von Innen in m |
| Innenfläche (netto) | Gesamtfläche der Innenwände im Raum in m² (nur sichtbare Seiten) abzgl. Öffnungen, Türen und Fenster |
| Innenfläche (brutto) | Gesamtfläche der Innenwände im Raum in m² (nur sichtbare Seiten) inkl. Öffnungen, Türen und Fenster |
| Außenfläche (netto) | Gesamtfläche der im Raum sich befindenden Außenwände in m² abzügl. Öffnungen, Türen und Fenster und Hinzurechung der im Evebimodus gepflegten Außenwandstärken und Boden- sowie Deckenplattenhöhen|
| Außenfläche (brutto)  | Enstpricht der Außenfläche (netto) jedoch inkl. Öffnungen, Türen und Fenster |
| Fenster | Anzahl aller Fenster im Raum |
| Fensterfläche | Summe aller Fensterflächen des Raumes in m² | 	
| Türen gesamt | Anzahl aller Türen im Raum | 
| Außentüren | Anzahl aller Außentüren im Raum| 
| Türfläche	| Summen aller Türflächen des Raumes in m² | 
| Außentürfläche | Summme aller Außentürflächen des Raumens in m² | 

### 2. Außenwanddaten

| Parameter | Beschreibung |
|-----------|-------------|
| Länge | Innenlänge der Außenwand in m (Außenlänge inkl. Wandstärke bei orthogonalen Verbindungen kann im Evebimodus eingesehen werden) |
| Höhe | Innenhöhe der Außenwand in m  (Außenhöhe inkl. Boden- und Deckenplattenhöhen kann im Evebimodus eingesehen werden)|
| Innenfläche (netto) | Gesamtfläche der Außenwand in m² von Innen abzügl. Öffnungen, Türen und Fenster|
| Innenfläche (brutto) | Gesamtfläche der Außenwand in m² von Innen inkl. Öffnungen, Türen und Fenster |
| Außenfläche (netto) | Gesamtfläche der Außenwand von Außen in m² abzügl. Öffnungen, Türen und Fenster und Hinzurechung der im Evebimodus gepflegten Außenwandstärken und Boden- sowie Deckenplattenhöhen|
| Außenfläche (brutto)  | Enstpricht der Außenfläche (netto) jedoch inkl. Öffnungen, Türen und Fenster |

### 3. Innenwanddaten
Innenwände trennen Räume innerhalb des Gebäudes:

| Parameter | Beschreibung |
|-----------|-------------|
| Länge | Länge der Innenwand in m |
| Höhe | Höhe der Innenwand in m |
| Stärke | Stärke/Dicke der Innenwand in m |
| Innenfläche (netto) | Gesamtfläche der Innenwand in m² (beide Seiten) abzügl. Öffnungen, Türen und Fenster |
| Innenfläche (brutto) | Gesamtfläche der Innenwand in m² (beide Seiten) inkl. Öffnungen, Türen und Fenster |


### 4. Geschossdaten
Geschosse/Stockwerke sind horizontale Ebenen des Gebäudes:

| Parameter | Beschreibung |
|-----------|-------------|
| Geschossbezeichnung | Identifikation des Geschosses |
| NGF | Netto-Grundfläche des Geschosses in m² |
| NRI | Netto-Rauminhalt des Geschosses in m³ (NGF x durchschnittliche Innenhöhe des Stockwerks) |
| BGF | Brutto-Grundfläche des Geschosses in m² (NGF × 1,2) |
| BRI | Brutto-Rauminhalt des Geschosses in m³ (BGF × Stockwerkshöhe) |
| Stockwerkshöhe | Geschosshöhe in m (durchschnittlliche Innenhöhe + Deckenplattenhöhe) |
| Stockwerksumfang | Umfang der Bodenplatte in m. Die Wandstärke der Außenwände im doorbit Modus wird berücksichtigt|
| Innenwandfläche | Summe aller Innenwände im Geschoss in m² (beide Seiten) |
| Außenwandfläche | Summe aller Außenwände im Geschoss in m² |

### 5. Gebäudedaten
Das Gesamtgebäude umfasst alle Geschosse und Elemente:

| Parameter | Beschreibung |
|-----------|-------------|
| NGF | Netto-Grundfläche des Gebäudes in m² (Summe NGF über alle Stockwerke)|
| NRI | Netto-Rauminhalt des Gebäudes in m³(Summe NRI über alle Stockwerke ) |
| BGF | Brutto-Grundfläche des Gebäudes in m² (NGF × 1,2)|
| BRI | Brutto-Rauminhalt des Gebäudes in m³ (BGF × Stockwerkshöhe) |
| Technikfläche (TF) | NGF aller Räume, den die Raumkategorie Heizungsraum zugeordnet worden ist |
| Verkehrsfläche (VF) | NGF aller Räume, den die Raumkategorie Flur/Treppenhaus zugeordnet worden ist |
| Anzahl Geschosse | Gesamtzahl der Geschosse |
| Anzahl Räume | Gesamtzahl der Räume |
| Innenfläche (netto) | Summe aller Innenwände im Gebäude in m² (beide Seiten) abzgl. Öffnungen, Türen und Fenster |
| Innenfläche (brutto) | Summe aller Innenwände im Gebäude in m² (beide Seiten) inkl. Öffnungen, Türen und Fenster |
| Außenfläche (netto) | Summe aller Außenwandflächen von Außen in m² abzügl. Öffnungen, Türen und Fenster und Hinzurechung der im Evebimodus gepflegten Außenwandstärken und Boden- sowie Deckenplattenhöhen |
| Außenfläche (brutto)  | Enstpricht Außenfläche (netto) jedoch inkl. Öffnungen, Türen und Fenster |

## Evebi-Modus Anpassungen

Der Evebi-Modus ermöglicht präzise Anpassungen der Massedaten:

1. **Außenwandstärke anpassen**:
    - Standardwert: 18 cm
    - Kann auf jeden beliebigen Wert geändert werden
    - Hat direkte Auswirkung auf die Bruttowerte der Außenwände und damit auf BRI

2. **Bodenplatten- und Deckenhöhe anpassen**:
    - Werte werden beim Scannen automatisch ermittelt
    - Können jederzeit im Stockwerksmenü geändert werden
    - Beeinflussen ebenfalls die Bruttowerte der Außenwände und Geschosshöhen

3. **Auswirkungen auf Bruttowerte**:
    - Die im Evebi-Modus eingestellten Werte beeinflussen direkt die Berechnung der Bruttowerte für Außenwandflächen, Geschosshöhe und BRI
    - Formel: Bruttolänge = doorbit-Modus Länge + Evebi-Modus Außenwandstärke (bei orthogonalen Verbindungen)
    - Bruttoflächen werden entsprechend mit den angepassten Werten neu berechnet

