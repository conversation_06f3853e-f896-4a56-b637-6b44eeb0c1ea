---
description: "Einführung in doorbit Studio, eine webbasierte CAD-Anwendung zur Bearbeitung und Erweiterung von Gebäudemodellen."
---

# doorbit Studio

doorbit Studio ist eine webbasierte CAD-Anwendung ([https://doorbit.com/login/](https://doorbit.com/login/)). Die Basis bildet immer das mit der doorbit App erstellte Gebäudemodell. Darin enthaltene Elemente (Wände, Öffnungen, Bodenplatten, Räume, Notizen etc.) können bearbeitet und neue hinzugefügt werden.

## doorbit Studio als PWA installieren

### Was ist eine PWA?

Eine Progressive Web App (PWA) ist eine Webanwendung, die wie eine native App auf Ihrem Gerät funktioniert. doorbit als PWA bietet Ihnen die Bequemlichkeit einer installierten Anwendung, ohne den Umweg über den Browser zu gehen.

### Vorteile einer PWA-Installation

- **Schnellerer Zugriff**: Direkter Start über Ihr Startmenü oder den Homescreen
- **Optimierte Benutzeroberfläche**: Vollbildansicht ohne Browser-Elemente

### Installation auf Ihrem Gerät

Die Installation von doorbit als PWA ist einfach und schnell:

1. Öffnen Sie doorbit in Ihrem Browser (Chrome, Edge, Brave, Safari)
2. Suchen Sie das Installations-Symbol in der Adressleiste Ihres Browsers 
   ![PWA Installationssymbol](doorbit-studio/attachments/PWA_Browser.png)
3. Klicken Sie auf das Symbol, woraufhin sich ein Installationsdialog öffnet
   ![PWA installieren](doorbit-studio/attachments/PWA_Browser_install.png)
4. Bestätigen Sie mit "Installieren"
5. doorbit ist nun als eigenständige Anwendung auf Ihrem Gerät verfügbar

Nach der Installation können Sie doorbit wie jede andere App über Ihr Startmenü, Desktop oder den App-Bildschirm Ihres Mobilgeräts starten.