// docs/javascripts/external-links.js
document.addEventListener('DOMContentLoaded', function() {
    // Alle Links mit der Klasse 'external' auswählen
    const externalLinks = document.querySelectorAll('a.external');
    
    externalLinks.forEach(link => {
        // Target _blank hinzufügen
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
        
        // Material Design Icon als SVG einfügen
        const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        iconSvg.setAttribute('viewBox', '0 0 24 24');
        iconSvg.classList.add('external-link-icon');
        
        const iconPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        iconPath.setAttribute('d', 'M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z');
        
        iconSvg.appendChild(iconPath);
        link.appendChild(iconSvg);
    });
});