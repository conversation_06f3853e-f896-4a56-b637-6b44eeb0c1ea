---
categories: 
  - Release Notes
date: 2024-06-17
tags:
  - Release Notes
--- 
# Juni Update

**CAD & IFC**

Es wurde die erste funktionale Version von Doorbit Studio veröffentlicht 🎉. Diese Version beinhaltet folgende Highlights:

- Pflege verschiedener Informationen an allen Bauteilen möglich (Wände, Fenster, Türen, Bodenplatten)
- Anzeige von Massedaten an Bauteilen, Gebäude und Stockwerken
- Mehrfachauswahl von Wänden per Strg + <PERSON><PERSON> mögli<PERSON>, markierte Wände sind farblich hervorgehoben
- Farbliche Unterscheidung von Außenwänden (dunkelgrau) und Zwischenwänden (Mittelreihenhaus)
- Pflege der Wandstärke für Außenwände im Modell (Version 1 - gleiche Stärke für alle Außenwände)
- Fenster (V1) werden grün markiert mit Anzeige der Fensterfläche und Pflege des Baujahr-Ranges für U-Wert
- Türen (V1) werden blau markiert mit Anzeige der Türfläche
- Unterstützung für nicht-rechteckige Bodenplatten beim IFC-Export
- Automatische Erweiterung der Bodenplatte bei Änderung der Wandstärke
- Performance-Optimierung im 3D-Modell

Berechnete Massedaten auf Gebäude- und Stockwerksebene:

- Netto-Raumfläche, Netto-Rauminhalt
- Wand-Flächen (innen/außen, netto/brutto)
- Anzahl, Flächen und Maße von Fenstern und Türen
- Stockwerkshöhe und -umfang

**Postprocessing-Features:**

- Technische Postprocessing-Pipeline für automatisierte Tasks an Gebäuden aufgebaut
- Erstes Feature - Bündige Ausrichtung von Stockwerken aufeinander

**Plattform:**

- Objekte können nun Status haben (konfigurierbare Statusliste)
- Objekte lassen sich filtern und durchsuchen
- Verbesserte Ladezeiten im Dashboard
- Suche im Dashboard nach Adresse und Namen
- Anzeige eines benutzerdefinierten Identifiers am Objekt
- Support für EVEBI (Energieberatersoftware) Gebäudeimport über ECAD

**App:**

- Pflege von konfigurierbaren Daten am Raum
- Eigene Vergabe von Raumtyp und Raumname