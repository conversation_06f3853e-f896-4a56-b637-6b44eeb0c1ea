.styled-image {
    border: 1px solid #ccc;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
[data-md-color-scheme="doorbit"] {
    --md-primary-fg-color:        #343951;
    --md-primary-fg-color--light: #F05B29;
    --md-primary-fg-color--dark:  #343951;

    /* Background colors */
    --md-default-bg-color:        #F3F5F8;
    --md-footer-bg-color:         #343951;
    --md-footer-bg-color--dark:   #343951;
    
    /* Accent colors */
    --md-accent-fg-color:         #FF3E02;
    --md-accent-fg-color--transparent: #FF3E0210;
    --md-accent-bg-color:         #ECFDF2;
    --md-accent-bg-color--light:  #ECFDF2;

    /* Text colors */
    --md-typeset-color:           #343951;
    --md-typeset-a-color:         #F05B29;
    --md-typeset-h1-color:        #2A2E3F;
    --md-typeset-h2-color:        #2A2E3F;
    --md-typeset-h3-color:        #2A2E3F;
    --md-typeset-h4-color:        #2A2E3F;
    
    /* Code blocks */
    --md-code-bg-color:          #2A2E3F;
    --md-code-fg-color:          #F8FAFC;
    
    /* Tables */
    --md-typeset-table-color:    #848BA5;
    
    /* Navigation */
    --md-nav-bg-color:           #43E77F;
    --md-nav-link-color:         #F05B29;      /* Regular links */ 
    --md-nav-link-hover-color:   #2A2E3F;     /* Hover state */
    --md-nav-link-active-color:  #2A2E3F;    /* Active/current page */

    /* Tag */
    .md-tag {
        background-color: #ECFDF2;
        border: 1px solid #43E77F;
        color: #005D3A;
    }
    .icons {
        color: #F05B29;
    }
    .iconsred {
        color: #ff0000;
    }
}
[data-md-color-scheme="slate"] {
    --md-hue: 210; 
    
    /* Primary colors */
    --md-primary-fg-color:        #2A2E3F;
    --md-primary-fg-color--light: #43E77F;
    --md-primary-fg-color--dark:  #848BA5;
    
    /* Background colors */
    --md-default-bg-color:        #343951;
    --md-footer-bg-color:         #2A2E3F;
    --md-footer-bg-color--dark:   #2A2E3F;
    
    /* Accent colors */
    --md-accent-fg-color:         #3DFC84;
    --md-accent-fg-color--transparent: #3DFC8410;
    --md-accent-bg-color:         #2A2E3F;
    --md-accent-bg-color--light:  #2A2E3F;

    /* Text colors */
    --md-typeset-color:           #E0E2E8;
    --md-typeset-a-color:         #43E77F;
    --md-typeset-h1-color:        #F8FAFC;
    --md-typeset-h2-color:        #F8FAFC;
    --md-typeset-h3-color:        #F8FAFC;
    --md-typeset-h4-color:        #F8FAFC;
    
    /* Code blocks */
    --md-code-bg-color:          #2A2E3F;
    --md-code-fg-color:          #ffffff;
    
    /* Tables */
    --md-typeset-table-color:    #848BA5;
    
    /* Navigation */
    --md-nav-bg-color:           #2A2E3F;
    --md-nav-link-color:         #F8FAFC;           /* Regular links */
    --md-nav-link-hover-color:   #43E77F;     /* Hover state */
    --md-nav-link-active-color:  #43E77F;    /* Active/current page */

    /* Tags */
    .md-tag {
        background-color: #002814;
        border: 1px solid #005D3A;
        color: #43E77F;
    }
    .icons {
        color: #43E77F;
    }
    .iconsred {
        color: #ff0000;
    }
}

/* Details/Summary Styling */
.md-typeset details {
    border-radius: 4px;
    border: 1px solid #76D8FF;
    background-color: #F1FBFF;
}

.md-typeset summary {
    cursor: pointer;
    padding: 1rem 0;
    color: #08577D;
    font-weight: bold;
}

/* Details/Summary Styling for tip type */
.md-typeset details.tip {
    border-color: #43E77F;
    background-color: #ECFDF2;
}

.md-typeset details.tip summary {
    color: #005D3A;
}

/* Details/Summary Styling for warning type */
.md-typeset details.warning {
    border-color: #FFBABA;
    background-color: #FFF8F8;
}

.md-typeset details.warning summary {
    color: #952B2B;
}

/* Dark mode styling */
[data-md-color-scheme="slate"] .md-typeset details {
    border-color: #08577D;
    background-color: #001428;
}

[data-md-color-scheme="slate"] .md-typeset summary {
    color: #76D8FF;
}

/* Dark mode styling for tip type */
[data-md-color-scheme="slate"] .md-typeset details.tip {
    border-color: #005D3A;
    background-color: #002814;
}

[data-md-color-scheme="slate"] .md-typeset details.tip summary {
    color: #43E77F;
}

/* Dark mode styling for warning type */
[data-md-color-scheme="slate"] .md-typeset details.warning {
    border-color: #FFA500;
    background-color: #4B2E00;
}

[data-md-color-scheme="slate"] .md-typeset details.warning summary {
    color: #FFA500;
}

.md-nav__item--section>.md-nav {
    margin-left: 0!important;
}

.md-sidebar__scrollwrap {
    overflow-y: initial!important;
}

/* docs/stylesheets/custom.css */

/* Anpassung der Icon-Größe in der Navigation */
.md-nav__link .md-icon {
    font-size: 0.8rem;
    margin-right: 0.3rem;
}

/* Anpassung der Icon-Farbe */
.md-icon svg {
    fill: currentColor;
}

/* Hover-Effekt für Icons */
.md-nav__link:hover .md-icon {
    color: var(--md-accent-fg-color);
}

/* Icon-Größe in der Hauptnavigation */
.md-header__button.md-icon {
    margin: 0.2rem;
    padding: 0.2rem;
}

/* Styling für externe Links */
.external-link-icon {
    width: 0.75rem;
    height: 0.75rem;
    display: inline-block;
    margin-left: 0.2rem;
    vertical-align: middle;
    fill: currentColor;
}

/* Neu */
.header-email {
    margin-left: 1rem;
    font-size: 1.5rem;
    color: var(--md-typeset-a-color);
    text-decoration: none;
    /* Bei Bedarf: weitere Positionierungsregeln, z. B. */
    /* position: relative; top: 0.2rem; */
}

.header-email:hover {
    color: var(--md-accent-fg-color);
}
