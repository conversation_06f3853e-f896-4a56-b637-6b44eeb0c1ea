---
description: "Übersicht über die doorbit Dokumentation und ihre Hauptkomponenten."
---

# doorbit Dokumentation

Die doorbit Plattform besteht aus drei Hauptkomponenten:

1. [**doorbit App**](./doorbit-dokumentation/doorbit-app.md): Erstellen Sie ein 3D-Gebäudemodell direkt vor Ort und erfassen Sie weitere Daten über Workflows. Wichtig: Für die Erstellung des 3D Modells mittels Scanfunktion wird ein iPhone Pro (ab Version 12) oder iPad Pro mit LiDAR Sensor benötigt.
2. [**doorbit Studio**](./doorbit-dokumentation/doorbit-studio.md): Bearbeiten Sie Ihr Gebäudemodell bequem im Webbrowser oder auf dem Tablet. Sie können CAD-Funktionen nutzen und Änderungen vornehmen - ganz ohne Apple Gerät. doorbit Studio läuft in allen gängigen Browsern, besonders gut in Chrome.
3. [**Workflows**](./doorbit-dokumentation/workflows.md): Nutzen Sie vorgefertigte Arbeitsabläufe – sowohl mobil als auch am Desktop – für eine effiziente und strukturierte Datenerfassung.

Das untere Schaubild zeigt, wie alles zusammenspielt: Die doorbit App speichert automatisch alle erfassten Objekte im Gebäudemodell. Diese können Sie dann in doorbit Studio weiter bearbeiten. Die hellgrün markierten Bereiche zeigen die Daten, die Sie im IFC- und EVEX-Format exportieren können. Teilen Sie Ihre vollständigen Projektdaten ganz einfach über einen Weblink mit anderen. Der Link ist nicht öffentlich auffindbar - nur Personen mit dem direkten Link können die Daten einsehen, aber nicht verändern.

![](assets/images/doorbit_Plattform_dbt_colors_20250210_white_background.png)