site_name: Dokumentation und Release Notes
site_dir: ../dist/docs
site_url: https://doorbit.com/de/
site_description: Dokumentation und Release Notes für die Lösungen von doorbit. Dokumentation zum doorbit Studio, doorbit App und Workflows sind hier zu finden.

extra:
  generator: false  # Removes the "Made with Material for MkDocs" notice


theme:
  logo: assets/images/dbt_logo_white.svg
  favicon: assets/images/dbt_logo_icon.svg
  language: de
  name: material
  custom_dir: overrides
  features:
    - navigation.instant
    - navigation.instant.progress
    - navigation.tracking
    - navigation.tabs
    - toc.follow
    - navigation.sections
    - navigation.tabs.sticky
    - navigation.top
    - search.suggest
    - search.highlight
    - search.share
    - navigation.indexes
    - content.tooltips
    - navigation.footer
    - header.autohide
    - announce.dismiss
    
  palette: 
    - media: "(prefers-color-scheme: light)"
      scheme: doorbit
      toggle:
        icon: material/weather-night
        name: We<PERSON><PERSON> zu dunklem Modus
      primary: custom
      accent: custom
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle: 
        icon: material/weather-sunny
        name: <PERSON><PERSON><PERSON> zu hellem Modus
      primary: custom
      accent: custom

plugins:
  - search
  - tags
  - exclude:
      glob:
        - "**/_*.md"   # Alle Dateien, die mit einem Unterstrich beginnen, werden ausgeschlossen.
        - "**/*?q=*"   # URLs mit dem Parameter ?q= ausschließen.
  - blog:
      blog_dir: blog
      blog_toc: true
      archive: true  # Disable default archive
      post_url_format: "{slug}"
      archive_name: Archiv
      categories: false
      archive_date_format: yyyy
      archive_url_date_format: yyyy
      archive_toc: true   
  - mkdocs-video:
      is_video: True
      video_controls: True
      video_autoplay: True
      video_loop: True
  - meta-descriptions

nav:
  - Dokumentation:
    - Übersicht: index.md
    - doorbit App:
      - Installation: doorbit-dokumentation/doorbit-app.md
      - Wichtigte Tipps & Tricks: doorbit-dokumentation/doorbit-app/die-wichtigsten-tipps-und-hinweise-beim-scannen.md
      - Scan-Vorgang: doorbit-dokumentation/doorbit-app/scan-vorgang.md
      - Notizen und Fotos erfassen: doorbit-dokumentation/doorbit-app/App_Notizen.md
    - doorbit Studio: 
      - Was ist doorbit Studio: doorbit-dokumentation/doorbit-studio.md
      - Dashboard: doorbit-dokumentation/doorbit-studio/dashboard.md
      - Bearbeitungswerkzeuge: doorbit-dokumentation/doorbit-studio/werkzeuge.md
      - Gebäude: 
        - Stockwerke: doorbit-dokumentation/doorbit-studio/gebude/stockwerke.md
        - Räume: doorbit-dokumentation/doorbit-studio/gebude/raeume.md 
        - Wände: doorbit-dokumentation/doorbit-studio/gebude/waende.md
        - Öffnungen: doorbit-dokumentation/doorbit-studio/gebude/oeffnungen.md
        - Dachflächen: doorbit-dokumentation/doorbit-studio/gebude/dachflaechen.md
        - Was nicht funktioniert: doorbit-dokumentation/doorbit-studio/gebude/noch-fehlende-elemente.md
      - Massedaten: doorbit-dokumentation/doorbit-studio/massedaten.md
      - Export & Teilen:
        - ECAD / Cascados IFC Export: doorbit-dokumentation/doorbit-studio/export-teilen/ecad-cascados-ifc-export.md
        - Evebi Export: doorbit-dokumentation/doorbit-studio/export-teilen/evebi-export.md
        - IFC Export: doorbit-dokumentation/doorbit-studio/export-teilen/ifc_export.md 
        - Weblink: doorbit-dokumentation/doorbit-studio/export-teilen/weblink.md
    - Workflows: 
      - Was sind Workflows: doorbit-dokumentation/workflows.md
      - U-Wert Assistent: doorbit-dokumentation/workflows/workflow-u-wert-assistent.md
  - Schritt-für-Schritt Anleitungen: 
    - Wie können Raumangaben nach dem Scan geändert werden: doorbit-dokumentation/schritt-fr-schritt-anleitungen/wie-knnen-whrend-des-scan-vorgangs-gemachte-raumangaben-nachtrglich-gendert-werden.md
    - Wie modelliert man polygonale Wände?: doorbit-dokumentation/schritt-fr-schritt-anleitungen/wie-modelliert-man-aus-rechteckigen-wnden-polygonale-wnde.md
    - U-Wert Assistent: doorbit-dokumentation/schritt-fr-schritt-anleitungen/u-wert-assistent.md
    - Gebäudemodelle bearbeiten und für Evebi-Export optimieren: doorbit-dokumentation/schritt-fr-schritt-anleitungen/gebudemodelle-bearbeiten-und-fr-evebi-export-optimieren.md
    - Verwendung von Tastaturkürzeln: doorbit-dokumentation/schritt-fr-schritt-anleitungen/verwendung-von-tastaturkrzeln-und-maustasten-innerhalb-von-doorbit-studio.md
  - Release Notes:
    - Aktuelle Release Notes: blog/index.md

extra_css:
  - assets/css/custom.css

extra_javascript:
  - https://kit.fontawesome.com/43e1c488b6.js
  - js/external-links.js  
  - js/header-email.js

markdown_extensions:
  - pymdownx.arithmatex
  - pymdownx.superfences
  - pymdownx.inlinehilite
  - pymdownx.caret
  - pymdownx.mark
  - pymdownx.tilde
  - pymdownx.smartsymbols
  - pymdownx.tasklist
  - pymdownx.emoji
  - pymdownx.keys
  - pymdownx.betterem
  - pymdownx.magiclink
  - pymdownx.highlight
  - pymdownx.details
  - pymdownx.snippets
  - markdown.extensions.admonition
  - attr_list  # Ermöglicht target="_blank" Attribute
  - md_in_html
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      options:
        custom_icons:
          - overrides/.icons
  - toc:
      permalink: false
