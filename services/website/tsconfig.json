{"compilerOptions": {"baseUrl": ".", "target": "ESNext", "types": ["vite/client"], "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "strictNullChecks": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["node_modules"]}