import vue from '@vitejs/plugin-vue';
import vuetify, {transformAssetUrls} from 'vite-plugin-vuetify';
import {defineConfig, loadEnv} from 'vite';
import {fileURLToPath} from 'url'
import {URL} from "node:url";
import {VitePWA} from "vite-plugin-pwa";
import copy from "rollup-plugin-copy";
import visualizer from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
// https://vitejs.dev/config/#using-environment-variables-in-config
export default defineConfig(({command, mode}) => {
    const env = loadEnv(mode, process.cwd(), '')

    return {
        css: {
            preprocessorOptions: {
                scss: {
                    api: "modern-compiler"
                }
            }
        },
        build: {
            cssMinify: env.VITE_IS_DEVELOPMENT !== 'true',
            minify: env.VITE_IS_DEVELOPMENT !== 'true',
            sourcemap: env.VITE_IS_DEVELOPMENT === 'true',
            rollupOptions: {
                external: ['assets/development'], //alles unter diesem pfad wird ausgeschlossen im build
                output: {
                    assetFileNames: (preRenderedChunk => {
                        //No Hashing for web-ifc.wasm
                        if (preRenderedChunk.name === 'web-ifc.wasm') {
                            return 'assets/web-ifc.wasm'
                        }
                        return "assets/[name]-[hash][extname]"
                    }),
                    //Vorsicht! Hier kann es schnell zu üblen Dateigrößen kommen. Nach jeder Änderung Dateigrößen prüfen! Ausgewogenheit ist das Ziel.
                    // manualChunks: (id) => {
                    //     // ### IFC ###
                    //     if (id.includes('/node_modules/web-ifc/web-ifc-api.js')) {
                    //         return 'ifc1'
                    //     }
                    //     if (id.includes('/node_modules/web-ifc/')) {
                    //         return 'ifc2'
                    //     }
                    //     if (id.includes("/src/adapter/ifc/")) {
                    //         return "ifc3"
                    //     }
                    //
                    //     // ### THREE ###
                    //     if (id.includes("/node_modules/three-csg-ts/")) {
                    //         return "three1"
                    //     }
                    //     if (id.includes("/node_modules/three/examples/")) {
                    //         return "three2"
                    //     }
                    //     if (id.includes("/node_modules/three/build/three.cjs")) {
                    //         return "three3"
                    //     }
                    //     if (id.includes("/node_modules/three/build/")) {
                    //         return "three4"
                    //     }
                    //     if (id.includes("/node_modules/three/src/")) {
                    //         return "three5"
                    //     }
                    //     if (id.includes("/node_modules/three/")) {
                    //         return "three6"
                    //     }
                    //     if (id.includes("/src/adapter/three/")) {
                    //         return "three7"
                    //     }
                    //
                    //     // ### LEAFLET ###
                    //     if (id.includes("/node_modules/@turf/")) {
                    //         return "leaflet1"
                    //     }
                    //     if (id.includes("/node_modules/leaflet/dist/images/")) {
                    //         return "leaflet2"
                    //     }
                    //     if (id.includes("/node_modules/leaflet/dist/leaflet.js")) {
                    //         return "leaflet3"
                    //     }
                    //     if (id.includes("/node_modules/leaflet/dist/")) {
                    //         return "leaflet4"
                    //     }
                    //     if (id.includes("/node_modules/leaflet/src/")) {
                    //         return "leaflet5"
                    //     }
                    //     if (id.includes("/node_modules/leaflet/")) {
                    //         return "leaflet6"
                    //     }
                    //     if (id.includes("/src/adapter/leaflet/components/")) {
                    //         return "leaflet7"
                    //     }
                    //     if (id.includes("/src/adapter/leaflet/")) {
                    //         return "leaflet8"
                    //     }
                    //
                    //     // ### CHARTJS ###
                    //     if (id.includes("/node_modules/chart.js/")) {
                    //         return "chart"
                    //     }
                    //
                    //     // ### VUETIFY ###
                    //     if (id.includes("/node_modules/vuetify/lib/components/")) {
                    //         return "vuetify1"
                    //     }
                    //     if (id.includes("/node_modules/vuetify/")) {
                    //         return "vuetify2"
                    //     }
                    //     if (id.includes("/src/adapter/vuetify/")) {
                    //         return "vuetify3"
                    //     }
                    //
                    //     // ### KLARO ###
                    //     if (id.includes("/node_modules/klaro/")) {
                    //         return "klaro"
                    //     }
                    //
                    //     // ### GRAPHQL ###
                    //     if (id.includes("/node_modules/graphql/")) {
                    //         return "graphql1"
                    //     }
                    //     if (id.includes("/src/adapter/graphql/apollo-links/")) {
                    //         return "graphql2"
                    //     }
                    //     if (id.includes("/src/adapter/graphql/fragments/")) {
                    //         return "graphql3"
                    //     }
                    //     if (id.includes("/src/adapter/graphql/generated/")) {
                    //         return "graphql4"
                    //     }
                    //     if (id.includes("/src/adapter/graphql/")) {
                    //         return "graphql5"
                    //     }
                    //
                    //     // ### ANIMEJS ###
                    //     if (id.includes("/node_modules/animejs/")) {
                    //         return "anime"
                    //     }
                    //
                    //     // ### KEYCLOAK ###
                    //     if (id.includes("/node_modules/keycloak-js/")) {
                    //         return "keycloak"
                    //     }
                    //
                    //     // ### VUE ROUTER ###
                    //     if (id.includes("/node_modules/vue-router/")) {
                    //         return "router"
                    //     }
                    //
                    //     // ### MDI ###
                    //     if (id.includes("/node_modules/@mdi/js/mdi.js")) {
                    //         return "mdi1"
                    //     }
                    //     if (id.includes("/node_modules/@mdi/js/")) {
                    //         return "mdi2"
                    //     }
                    //     if (id.includes("/node_modules/@mdi/svg/")) {
                    //         return "mdi3"
                    //     }
                    //     if (id.includes("/node_modules/@mdi/")) {
                    //         return "mdi4"
                    //     }
                    //
                    //     // ### APOLLO ###
                    //     if (id.includes("/node_modules/@apollo/")) {
                    //         return "apollo"
                    //     }
                    // }
                }
            }
        },
        plugins: [
            visualizer(),
            // asyncComponentPlugin({
            //     include: ['**/*.vue', '**/*.js', '**/*.ts'],
            //     exclude: ['node_modules', 'dist'],
            // }),
            vue({
                template: {
                    transformAssetUrls,
                }
            }),
            // https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vite-plugin
            vuetify({
                autoImport: true,
            }),
            VitePWA({
                injectRegister: false,
                devOptions: {
                    enabled: env.VITE_DEBUG_WORKBOX === 'true',
                },
                manifest: {
                    theme_color: '#2A2E3F',
                    background_color: '#F8FAFC',
                    display: 'standalone',
                    short_name: 'doorbit',
                    name: 'doorbit Portal',
                    description: "Reimagine real estate - mit KI gestützten Immobilienlösungen. Erledigen Sie Aufgaben in Rekordzeit mit den neusten Technologien von doorbit. Jetzt ausprobieren.",
                    categories: ['business', 'productivity'],
                    start_url: '/account/details/',
                    scope: '/',
                    icons: [
                        {
                            "src": "/assets/android-chrome-192x192.png",
                            "sizes": "192x192",
                            "type": "image/png"
                        },
                        {
                            "src": "/assets/android-chrome-512x512.png",
                            "sizes": "512x512",
                            "type": "image/png"
                        }
                    ],
                    screenshots: [
                        {
                            "src": "/assets/screenshots/mobile-1.PNG",
                            "sizes": "1170x2532",
                            "type": "image/png",
                            "form_factor": "narrow"
                        },
                        {
                            "src": "/assets/screenshots/mobile-2.PNG",
                            "sizes": "1170x2532",
                            "type": "image/png",
                            "form_factor": "narrow"
                        },
                        {
                            "src": "/assets/screenshots/mobile-3.PNG",
                            "sizes": "1170x2532",
                            "type": "image/png",
                            "form_factor": "narrow"
                        },
                        {
                            "src": "/assets/screenshots/mobile-4.PNG",
                            "sizes": "1170x2532",
                            "type": "image/png",
                            "form_factor": "narrow"
                        },
                        {
                            "src": "/assets/screenshots/large-1.png",
                            "sizes": "1198x902",
                            "type": "image/png",
                            "form_factor": "wide"
                        },
                        {
                            "src": "/assets/screenshots/large-2.png",
                            "sizes": "1198x902",
                            "type": "image/png",
                            "form_factor": "wide"
                        }, {
                            "src": "/assets/screenshots/large-3.png",
                            "sizes": "1198x902",
                            "type": "image/png",
                            "form_factor": "wide"
                        }, {
                            "src": "/assets/screenshots/large-4.png",
                            "sizes": "1198x902",
                            "type": "image/png",
                            "form_factor": "wide"
                        }, {
                            "src": "/assets/screenshots/large-5.png",
                            "sizes": "1198x902",
                            "type": "image/png",
                            "form_factor": "wide"
                        },
                    ]
                },
                workbox: {
                    sourcemap: true,
                    //Mac: use the command "find . -type f | sed -n 's/.*\.\([^.]*\)$/\1/p' | sort | uniq | tr '\n' ', ' | sed 's/, $/\n/'" to get all file extensions in the dist folder
                    globPatterns: ['**/*.{PNG,css,html,js,json,map,png,svg,webmanifest,webp,woff,woff2,xml}'],
                    globIgnores: ['**/android-chrome-*.png'],
                    maximumFileSizeToCacheInBytes: 20 * 1024 * 1024, // 10 MB
                    navigateFallbackDenylist: [
                        /^\/de\// // This is our static website at doorbit.com/de/. Do not handle via SW!
                    ],
                    runtimeCaching: [
                        {
                            // Cache all photos seen from flow-configs.doorbit.com
                            urlPattern: /^https:\/\/flow-configs\.doorbit\.com\/.*\.(?:png|jpg|jpeg|svg|gif|webp)$/,
                            handler: 'CacheFirst',
                            options: {
                                cacheName: 'flow-configs-image-cache',
                            }
                        },
                        {
                            // Cache photos seen from img-integ.doorbit.com and img.doorbit.com with "_mini" or "_th" in the name
                            // Be careful to not cache images from OSM for instance since this would blow up the cache.
                            urlPattern: /^https:\/\/(img-integ|img)\.doorbit\.com\/.*_(mini|th)\.(?:png|jpg|jpeg|svg|gif|webp)$/,
                            handler: 'CacheFirst',
                            options: {
                                cacheName: 'doorbit-special-image-cache',
                            }
                        },
                        {
                            urlPattern: /^\/de\//,
                            handler: 'NetworkOnly' // doorbit.com/de/ immer aus dem Netzwerk holen, kein SW Caching!
                        }
                    ],
                }
            }),
            copy({
                targets: [
                    //https://developer.apple.com/documentation/xcode/supporting-associated-domains
                    {src: env.VITE_APPLE_APP_SITE_ASSOCIATION_FILE!, dest: 'dist/.well-known', rename: 'apple-app-site-association'},
                ],
                hook: 'writeBundle',
            })
        ],
        define: {
            'process.env': {},
        },
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            },
            extensions: [
                '.js',
                '.json',
                '.jsx',
                '.mjs',
                '.ts',
                '.tsx',
                '.vue',
            ],
        },
        server: {
            port: 8087,
            headers: {
                "Content-Security-Policy": `default-src 'self' localhost:*; script-src 'self' *.hs-scripts.com *.usemessages.com https://js.stripe.com https://www.googletagmanager.com/gtag/js https://www.googletagmanager.com/gtm.js 'sha256-B2NrGAwTpQy/1AqWEBqkLVe7+FVFNvF44mDvWTRrKOw=' 'sha256-kWCc5c/SYsOvsZg6nOa2i0cDop+w+iAPv8+WtKmn2Ww=' 'sha256-Lq61U4jJNEGu4nXf3jYlJDSHho7XYSQQWaAQG1MyUko=' 'sha256-1e5RR2OpHhuX2h0Bat19DsNTmqbo4M3T1pqfeTXCHaA=' 'sha256-/AO8vAagk08SqUGxY96ci/dGyTDsuoetPOJYMn7sc+E=' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self'; connect-src 'self' *.hubspot.com wss: ws: localhost:* ***************:* https://auth-integ.doorbit.com https://flow-configs.doorbit.com https://region1.google-analytics.com https://solar.googleapis.com; font-src 'self' data:; frame-src 'self' *.hubspot.com https://js.stripe.com https://auth-integ.doorbit.com https://share-eu1.hsforms.com ${env.VITE_PROJECT_CONFIGURATOR_URL}; img-src 'self' localhost:* blob: https://img-integ.doorbit.com data: https://*.doorbit.com blob: https://server.arcgisonline.com data: https://*.arcgisonline.com; manifest-src 'self' https://doorbit.cloudflareaccess.com; media-src 'self'; worker-src 'self';`

                // COMMENT IN to test removal of 'unsafe-eval'.
                // Every new library could use "eval" or "Function('string')". As this is deemed super unsafe,
                // such libraries must not be used anymore. See README.md#unsafe-eval for more information.
                // "Content-Security-Policy": "default-src 'self' localhost:*; script-src 'self' https://www.googletagmanager.com/gtag/js https://www.googletagmanager.com/gtm.js 'sha256-B2NrGAwTpQy/1AqWEBqkLVe7+FVFNvF44mDvWTRrKOw=' 'sha256-kWCc5c/SYsOvsZg6nOa2i0cDop+w+iAPv8+WtKmn2Ww=' 'sha256-Lq61U4jJNEGu4nXf3jYlJDSHho7XYSQQWaAQG1MyUko=' ; style-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self'; connect-src 'self' localhost:* https://auth-integ.doorbit.com https://region1.google-analytics.com; font-src 'self' data:; frame-src 'self' https://auth-integ.doorbit.com; img-src 'self' localhost:* https://img-integ.doorbit.com data: https://*.doorbit.com; manifest-src 'self' https://doorbit.cloudflareaccess.com; media-src 'self'; worker-src 'none';"
            },
        },
    }
});
