<template>
    <t-renderer-webgl :cursor="cursor"
                      :needs-image-download="false"
                      :raycasters="raycasters"
                      :redraw-counter="redrawCounter"
                      :show-signature="false"
                      :tone-mapping="LinearToneMapping"
                      :tone-mapping-exposure="1"
                      renderer-id="wallEditor">
        <t-scene :background-color="0xe0e2e8"
                 :show-grid="debug"
                 :show-poles="debug">
            <t-orthographic-camera :far="20"
                                   :fit-to-camera-padding="embedded ? 0 : undefined"
                                   :near="1"
                                   :position="cameraPosition"
                                   :target="cameraTarget"
                                   :zoom="100">
                <t-orbit-controls :disable-pan="embedded"
                                  :disable-rotate="!debug"
                                  :disable-zoom="embedded"/>
            </t-orthographic-camera>

            <t-group :fit-to-camera-key="fitToCameraRefreshCounter"
                     fit-to-camera
                     hide-until-first-fit-to-camera>
                <t-group :transformation="centerTransformation">
                    <t-mesh-raw :mesh="planeMesh"/>
                    <t-mesh-raw v-if="!embedded"
                                :mesh="cameraPlaneMesh"/>
                </t-group>
                <t-mesh-raw :mesh="wallMesh"/>
            </t-group>

            <t-mesh v-for="leftNeighborWallConfig in leftNeighborWallConfigs"
                    :key="leftNeighborWallConfig.size"
                    :render-order="1"
                    :transformation="leftNeighborWallConfig.transformation">
                <t-plane-geometry :height="leftNeighborWallConfig.size"
                                  :width="leftNeighborWallConfig.size"/>
                <t-mesh-material-raw :material="wallMaterialNeighbor"/>
            </t-mesh>

            <t-mesh v-for="rightNeighborWallConfig in rightNeighborWallConfigs"
                    :key="rightNeighborWallConfig.size"
                    :render-order="1"
                    :transformation="rightNeighborWallConfig.transformation">
                <t-plane-geometry :height="rightNeighborWallConfig.size"
                                  :width="rightNeighborWallConfig.size"/>
                <t-mesh-material-raw :material="wallMaterialNeighbor"/>
            </t-mesh>

            <template v-if="!embedded">
                <t-mesh-raw v-for="point in points"
                            :key="point.id"
                            :mesh="point.mesh"
                            :raycaster-ids="point.raycasterIds"
                            :raycaster-object="point"
                            add-to-scene/>

                <t-mesh v-for="(point, index) in points"
                        :key="point.id"
                        :render-order="4"
                        :transformation="point.textTransformation.value"
                        add-to-scene
                        is-billboard>
                    <t-text-geometry :font="THREE_BUILDING_FONT"
                                     :size="0.05"
                                     :text="`${index+1}`"/>

                    <t-mesh-basic-material :color="point.isFixed ? 0xFFFFFF : 0x000000"
                                           :depth-test="false"
                                           transparent/>
                </t-mesh>

                <t-mesh v-for="(edgeTransformation, index) in edgeTextTransformations"
                        :key="index"
                        :render-order="5"
                        :transformation="edgeTransformation"
                        add-to-scene
                        is-billboard>
                    <t-text-geometry :font="THREE_BUILDING_FONT"
                                     :size="0.04"
                                     :text="edgeTexts[index]"/>

                    <t-mesh-basic-material :color="0xFF0000"
                                           :depth-test="false"
                                           transparent/>
                </t-mesh>

                <t-mesh v-for="(angleTransformation, index) in angleTextTransformations"
                        :key="index"
                        :render-order="2.9"
                        :transformation="angleTransformation"
                        add-to-scene
                        is-billboard>
                    <t-text-geometry :font="THREE_BUILDING_FONT"
                                     :size="0.04"
                                     :text="angleTexts[index]"/>

                    <t-mesh-basic-material :color="0xFFFFFF"
                                           :depth-test="false"
                                           transparent/>
                </t-mesh>

                <t-mesh :render-order="1"
                        :transformation="axisTextXTransformation"
                        add-to-scene>
                    <t-text-geometry :font="THREE_BUILDING_FONT"
                                     :size="0.05"
                                     :text="axisTextX"/>
                    <t-mesh-basic-material :color="0x000000"
                                           :depth-test="false"/>
                </t-mesh>

                <t-mesh :render-order="1"
                        :transformation="axisTextYTransformation"
                        add-to-scene>
                    <t-text-geometry :font="THREE_BUILDING_FONT"
                                     :size="0.05"
                                     :text="axisTextY"/>
                    <t-mesh-basic-material :color="0x000000"
                                           :depth-test="false"/>
                </t-mesh>

                <t-lines2 :color="0xFF0000"
                          :depth-test="false"
                          :render-order="100"
                          :transformation="cursorTransformationHorizontal"
                          :vertices="cursorLineHorizontalVertices"
                          :visible="!draggableRaycaster.isDragging.value && !hoverRaycaster.isHovering.value && !tAreVectors3Equal(cursorDefaultPosition, cursorPosition, BUILDING_EPSILON)"
                          add-to-scene/>
                <t-lines2 :color="0xFF0000"
                          :depth-test="false"
                          :render-order="100"
                          :transformation="cursorTransformationVertical"
                          :vertices="cursorLineVerticalVertices"
                          :visible="!draggableRaycaster.isDragging.value && !hoverRaycaster.isHovering.value && !tAreVectors3Equal(cursorDefaultPosition, cursorPosition, BUILDING_EPSILON)"
                          add-to-scene/>

                <t-lines2 v-for="(snapLine, index) in snapLines"
                          :key="snapLine.id"
                          :material="snapLineMaterial"
                          :render-order="2.5"
                          :transformation="snapLineTransformations[index]"
                          :vertices="snapLineVertices"
                          :visible="isSnapLineVisible(snapLine)"
                          add-to-scene/>
            </template>
        </t-scene>
    </t-renderer-webgl>
</template>

<script lang="ts"
        setup>
    import {Box2, CircleGeometry, Color, FrontSide, LinearToneMapping, MathUtils, Matrix4, Mesh, MeshBasicMaterial, PlaneGeometry, Shape, ShapeGeometry, Vector2, Vector3} from "three";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import TOrthographicCamera from "@/adapter/three/components/camera/t-orthographic-camera.vue";
    import {TTrackingRaycaster} from "@/adapter/three/raycasting/tracking/TTrackingRaycaster";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {computed, EffectScope, effectScope, onMounted, onUnmounted, ref, ShallowRef, shallowRef, watch} from "vue";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import {TDraggableRaycaster} from "@/adapter/three/raycasting/draggable/TDraggableRaycaster";
    import {createDraggableEmitterAndConsumer, TDraggableRaycasterEmitterOrConsumer, TDraggableRelated} from "@/adapter/three/raycasting/draggable/TDraggableRaycasterEmitterOrConsumer";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TTextGeometry from "@/adapter/three/components/geometry/t-text-geometry.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {THoverRaycaster} from "@/adapter/three/raycasting/hover/THoverRaycaster";
    import {createHoverEmitterAndConsumer, THoverRaycasterEmitterAndConsumer, THoverRelated} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {TSnappingManager, TSnappingResult} from "@/adapter/three/snapping/TSnappingManager";
    import {TSnapLine} from "@/adapter/three/snapping/TSnapLine";
    import {emptySet} from "@/utility/set";
    import TLines2 from "@/adapter/three/components/object/t-lines2.vue";
    import {LineMaterial} from "three/addons/lines/LineMaterial.js";
    import {tAreVectors2Equal, tAreVectors3Equal, tDestroyMesh, tVectors2ToBox2} from "@/adapter/three/three-utility";
    import {areNumbersEqual} from "@/utility/number";
    import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import TMeshMaterialRaw from "@/adapter/three/components/material/t-mesh-material-raw.vue";
    import {BUILDING_EPSILON, BUILDING_WALL_MIN_HEIGHT} from "@/components/listing/building/building";
    import {loadThreeBuildingFont, THREE_BUILDING_FONT} from "@/components/listing/three-building-resources";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";

    const props = withDefaults(defineProps<{
        neighborHeightsLeft: ReadonlySet<number>,
        neighborHeightsRight: ReadonlySet<number>,
        embedded?: boolean
    }>(), {
        embedded: false
    })

    const resetCounter = shallowRef<number>(0)
    defineExpose({
        getVertices: () => {
            const vs = points.value.map(p => new Vector2(p.mesh.position.x, -p.mesh.position.z))
            if (vs.length > 0) {
                vs.push(vs[0].clone())
            }
            return vs
        },
        resetVertices: () => {
            ++resetCounter.value
        }
    })

    const vertices = defineModel<readonly Vector2[]>("vertices", {
        required: true
    })

    const redrawCounter = shallowRef<number>(0)
    watch(vertices, () => {
        ++redrawCounter.value
    }, {
        deep: true
    })

    const transformedVertices = computed<readonly Vector2[]>({
        get: () => {
            const vs = vertices.value.map(v => new Vector2(v.x, -v.y))
            if (vs.length > 0 && tAreVectors2Equal(vs[0], vs[vs.length - 1], BUILDING_EPSILON)) {
                return vs.slice(0, vs.length - 1)
            }
            return vs
        },
        set: newValue => {
            const vs = newValue.map(v => new Vector2(v.x, -v.y));
            if (vs.length > 0) {
                vs.push(vs[0].clone())
            }
            vertices.value = vs
        }
    })

    const debug = false
    const {n} = useI18n()
    const lastSnappingResult = shallowRef<Optional<TSnappingResult<null, SnapLineTemplate>>>(null)

    const wallBox = computed<Box2>(() => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        resetCounter.value //trigger reset

        return tVectors2ToBox2(vertices.value)
    })

    const size = computed<Vector2>(() => wallBox.value.getSize(new Vector2()))
    const center = computed<Vector2>(() => wallBox.value.getCenter(new Vector2()))
    const centerTransformation = computed<Matrix4>(() => new Matrix4().makeTranslation(0, 0, -center.value.y))
    const wallWidth = computed<number>(() => size.value.x)
    const wallHeight = computed<number>(() => size.value.y)

    const filteredNeighborHeights = computed<ReadonlySet<number>>(() => new Set([...props.neighborHeightsLeft, ...props.neighborHeightsRight]))

    const cameraPosition = new Vector3(0, 10, 0)
    const cameraTarget = new Vector3(0, -1, 0)

    const wallShapeRefreshCounter = shallowRef<number>(0)

    const trackingRaycaster = new TTrackingRaycaster(1000)
    const draggableRaycaster = new TDraggableRaycaster()
    const hoverRaycaster = new THoverRaycaster()
    const raycasters = computed<readonly TRaycaster[]>(() => props.embedded ? [] : [
        trackingRaycaster,
        draggableRaycaster,
        hoverRaycaster,
    ])
    const snappingManager = new TSnappingManager<null, SnapLineTemplate>(
        BUILDING_EPSILON,
        0.01, //1cm
        10, //10m
    )
    snappingManager.snapGridSize.value = 0.01
    snappingManager.snapLineRange.value = 0.05

    watch(draggableRaycaster.isDragging, isDragging => {
        trackingRaycaster.isEnabled = !isDragging
        hoverRaycaster.isEnabled = !isDragging
        snappingManager.autoRefresh = !isDragging

        snappingManager.forceRefresh() //vor dem dragging ist klar, aber nach dem dragging müssen auch die linien erneuert werden, die sonst nicht ausgelöst werden würden
    })

    const cursor = computed<string>(() => {
        if (props.embedded) {
            return 'default'
        }
        if (draggableRaycaster.isDragging.value) {
            return 'grabbing'
        }
        if (hoverRaycaster.isHovering.value) {
            return 'grab'
        }
        return 'pointer'
    })

    onMounted(() => {
        ++resetCounter.value

        loadThreeBuildingFont()
    })

    onUnmounted(() => {
        pointMap.value.clear()

        clearFitToCameraTimeout()
        clearPoints(true)

        trackingRaycaster.destroy()
        draggableRaycaster.destroy()
        hoverRaycaster.destroy()
        snappingManager.destroy()

        snapLineMaterial.dispose()

        tDestroyMesh(planeMesh, true, true)
        tDestroyMesh(cameraPlaneMesh, true, true)
        tDestroyMesh(wallMesh, true, true)

        wallMaterialNeighbor.dispose()
    })

    //#################
    //### AXIS TEXT ###
    //#################
    const axisTextOffset = 0.2 //20cm

    function calculateAxisText(value: number): string {
        const number = n(value * 100, 'integer')
        const unit = 'cm'//TODO: t('enums.listingFieldUnit.CENTIMETER') //no font support, fallback to "cm"
        return `${number} ${unit}`
    }

    const axisTextX = computed<Optional<string>>(() => calculateAxisText(wallWidth.value))
    const axisTextY = computed<Optional<string>>(() => calculateAxisText(wallHeight.value))
    const axisTextXTransformation = computed<Matrix4>(() => {
        const translation = new Matrix4().makeTranslation(
            0,
            0,
            wallHeight.value / 2 + axisTextOffset - center.value.y,
        )
        const rotationX = new Matrix4().makeRotationX(-Math.PI / 2)
        return translation.multiply(rotationX)
    })
    const axisTextYTransformation = computed<Matrix4>(() => {
        const translation = new Matrix4().makeTranslation(
            -wallWidth.value / 2 - axisTextOffset,
            0,
            -center.value.y,
        )
        const rotationX = new Matrix4().makeRotationX(-Math.PI / 2)
        const rotationZ = new Matrix4().makeRotationZ(Math.PI / 2)
        return translation.multiply(rotationX).multiply(rotationZ)
    })

    //##############
    //### CURSOR ###
    //##############
    const cursorSize = 0.1 //1cm
    const cursorDefaultPosition = new Vector3(-1000, 0, -1000)
    const cursorLineHorizontalVertices = [
        new Vector3(-cursorSize, 0, 0),
        new Vector3(cursorSize, 0, 0),
    ]
    const cursorLineVerticalVertices = [
        new Vector3(0, 0, -cursorSize),
        new Vector3(0, 0, cursorSize),
    ]
    const cursorPosition = ref<Vector3>(cursorDefaultPosition.clone())
    const cursorTransformationHorizontal = computed<Matrix4>(() => new Matrix4().makeTranslation(cursorPosition.value))
    const cursorTransformationVertical = computed<Matrix4>(() => new Matrix4().makeTranslation(cursorPosition.value))

    watch(trackingRaycaster.moveUpdateCounter, () => {
        const positionXZ = trackingRaycaster.intersectionWorldPositionXZ.clone()

        clampToPlaneAndSnap(positionXZ, emptySet(), emptySet(), false)

        cursorPosition.value.set(positionXZ.x, 0, positionXZ.y)
    })

    //#############
    //### PLANES ###
    //#############
    const fitToCameraRefreshCounter = shallowRef(0)
    const planeMaterial = new MeshBasicMaterial({
        color: 0xE8E8E8,
        depthTest: false,
    });
    const planeMesh = new Mesh();
    planeMesh.geometry = new PlaneGeometry(wallWidth.value, wallHeight.value)
    planeMesh.material = planeMaterial
    planeMesh.rotation.x = -Math.PI / 2
    planeMesh.renderOrder = 1

    const cameraPlaneWidth = computed<number>(() => wallWidth.value + axisTextOffset * 2.5)
    const cameraPlaneHeight = computed<number>(() => wallHeight.value + axisTextOffset * 2.5)
    const cameraPlaneMesh = new Mesh();
    cameraPlaneMesh.geometry = new PlaneGeometry(cameraPlaneWidth.value, cameraPlaneHeight.value)
    cameraPlaneMesh.rotation.x = -Math.PI / 2
    cameraPlaneMesh.visible = false

    function clampToPlaneAndSnap(
        positionXZ: Vector2,
        ignoredSnapPointIds: ReadonlySet<string>,
        ignoredSnapLineIds: ReadonlySet<string>,
        isFixed: boolean,
    ) {
        const snappingResult = snappingManager.snapPositionXZ(
            positionXZ,
            ignoredSnapPointIds,
            ignoredSnapLineIds,
        )
        positionXZ.copy(snappingResult.snappedPositionXZ)

        lastSnappingResult.value = snappingResult

        if (isFixed) {
            return
        }

        const box = wallBox.value
        const size = box.getSize(new Vector2())
        const center = box.getCenter(new Vector2())

        const minX = center.x - size.x / 2
        const maxX = center.x + size.x / 2
        const minY = -center.y - size.y / 2
        const maxY = -center.y + size.y / 2 - BUILDING_WALL_MIN_HEIGHT //distance from the bottom

        positionXZ.x = Math.min(maxX, Math.max(minX, positionXZ.x))
        positionXZ.y = Math.min(maxY, Math.max(minY, positionXZ.y))
    }

    //##############
    //### POINTS ###
    //##############
    let pointIdCounter = 0
    const pointSize = 0.08 //8cm

    type Point = {
        readonly id: string
        readonly isFixed: boolean
        readonly mesh: Mesh
        readonly raycasterIds: ReadonlySet<string>
        readonly textTransformation: ShallowRef<Matrix4>
        readonly snapLines: readonly TSnapLine<SnapLineTemplate>[]
        readonly snapLineIds: ReadonlySet<string>
    } & TDraggableRelated<TDraggableRaycasterEmitterOrConsumer<null>>
        & THoverRelated<THoverRaycasterEmitterAndConsumer<null>>

    const points = shallowRef<readonly Point[]>([])
    const pointMap = computed<Map<string, Point>>(oldPointMap => {
        oldPointMap?.clear()

        const map = new Map<string, Point>()
        for (const point of points.value) {
            map.set(point.id, point)
        }
        return map
    })

    function destroyPoint(point: Point) {
        tDestroyMesh(point.mesh, true, true)
    }

    let pointsReactivityScope: Optional<EffectScope> = effectScope(true)

    function clearPoints(unmounted: boolean = false) {
        pointsReactivityScope?.stop()
        if (unmounted) {
            pointsReactivityScope = null
        } else {
            pointsReactivityScope = effectScope(true)
        }

        snappingManager.removeAllSnapLines()
        snappingManager.forceRefresh()

        for (const point of points.value) {
            destroyPoint(point)
        }

        points.value = []
    }

    function isYOnBaseline(y: number): boolean {
        const vertices = transformedVertices.value
        const minY = vertices.length <= 0 ? 0 : Math.max(...vertices.map(v => v.y)) //wir müssen hier max statt min nehmen, weil die vertices invertiert sind in z richtung (durch die ignorierte rotation über x, die überall angewendet wird)
        return areNumbersEqual(minY, y, BUILDING_EPSILON); //wenn min, dann liegt der Punkt auf der Grundlinie der Wand
    }

    watch([transformedVertices, resetCounter], ([vertices]) => {
        clearPoints()

        for (const vertex of vertices) {
            const isFixed = isYOnBaseline(vertex.y)
            addPoint(vertex, true, isFixed)
        }
    }, {
        immediate: true
    })

    watch(trackingRaycaster.leftClickUpdateCounter, () => {
        draggableRaycaster.reset()

        for (const point of points.value) {
            if (point.hover.isHovered.value) {
                return
            }
        }

        const newPosition = trackingRaycaster.intersectionWorldPositionXZ.clone()
        clampToPlaneAndSnap(newPosition, emptySet(), emptySet(), false)
        addPoint(newPosition, false)
    })

    watch(trackingRaycaster.rightClickUpdateCounter, () => {
        draggableRaycaster.reset()

        const ps = points.value

        if (ps.length <= 3) { //es muss immer 3 punkte geben
            return
        }

        for (let i = 0; i < ps.length; ++i) {
            const point = ps[i]
            if (point.hover.isHovered.value && !point.isFixed) {
                for (const snapLine of point.snapLines) {
                    snappingManager.removeSnapLineById(snapLine.id)
                }

                points.value = points.value.filter(p => p.id !== point.id)

                destroyPoint(point)
                break
            }
        }

        checkPointOrder()
    })

    /**
     * Prüft, ob nur noch 2 Punkte übrig sind. Ist das der Fall, wird geprüft, ob der erste Punkt links vom zweiten Punkt liegt.
     * Ist das nicht der Fall, werden die Punkte getauscht. Dieser Schritt ist wichtig, da sonst diverse Probleme entstehen,
     * wenn die Reihenfolge der Vertices nicht im Uhrzeigersinn vorliegt.
     */
    function checkPointOrder() {
        const ps = points.value
        if (ps.length !== 2) {
            return
        }
        const a = ps[0].mesh.position
        const az = a.z //z enthält in diesem Fall die Y-Koordinate des Vertex
        if (!isYOnBaseline(az)) {
            return
        }
        const b = ps[1].mesh.position
        const bz = b.z //z enthält in diesem Fall die Y-Koordinate des Vertex
        if (!isYOnBaseline(bz)) {
            return
        }
        const ax = a.x
        const bx = b.x

        if (ax > bx) {
            points.value = [ps[1], ps[0]]
        }
    }

    function createSnapLines(pointId: string, positionXZ: Vector2): readonly TSnapLine<SnapLineTemplate>[] {
        return [
            {
                id: `${pointId}-horizontal`,
                object: {
                    point1Id: pointId
                },
                normalizedDirectionXZ: new Vector2(1, 0),
                positionXZ: positionXZ.clone(),
                groupId: pointId,
            },
            {
                id: `${pointId}-vertical`,
                object: {
                    point1Id: pointId
                },
                normalizedDirectionXZ: new Vector2(0, 1),
                positionXZ: positionXZ.clone(),
                groupId: pointId,
            }
        ]
    }

    const pointColor = 0xf45446
    const pointColorFixed = 0x952B2B
    const pointColorHovered = 0xFFFFFF
    const pointColorDragged = 0xFF0000

    function addPoint(position: Vector2, addAtEnd: boolean, isFixed: boolean = false) {
        const geometry = new CircleGeometry(pointSize, 32);
        const color = isFixed ? pointColorFixed : pointColor
        const material = new MeshBasicMaterial({
            color,
            depthTest: false,
            transparent: true,
        });
        const mesh = new Mesh(geometry, material);
        mesh.rotation.x = -Math.PI / 2
        mesh.position.x = position.x
        mesh.position.z = position.y
        mesh.renderOrder = 3

        const ps = points.value
        const id = String(++pointIdCounter)
        const draggable = createDraggableEmitterAndConsumer(id, null)
        const hover = createHoverEmitterAndConsumer(id, null)
        const snapLines = isFixed ? [] : createSnapLines(id, position)

        const point: Point = {
            id,
            isFixed,
            mesh,
            raycasterIds: new Set(isFixed
                ? []
                : [
                    draggableRaycaster.id,
                    hoverRaycaster.id
                ]
            ),
            draggable,
            hover,
            textTransformation: shallowRef(new Matrix4().makeTranslation(mesh.position)),
            snapLines,
            snapLineIds: new Set(snapLines.map(l => l.id))
        }

        if (addAtEnd) {
            points.value = [...ps, point]
        } else {
            const index = calculateIndexForNewPoint(point)
            points.value = [...ps.slice(0, index), point, ...ps.slice(index)]
        }

        pointsReactivityScope?.run(() => {
            watch(draggable.positionXZ, positionXZ => {
                if (positionXZ === null) {
                    return
                }
                updatePointPosition(point, positionXZ)
            })

            watch(draggable.isDragged, isDragged => {
                material.color = new Color(isDragged ? pointColorDragged : color)
            })

            watch(hover.isHovered, isHovered => {
                material.color = new Color(isHovered ? pointColorHovered : color)
            })
        })

        for (const snapLine of point.snapLines) {
            snappingManager.addSnapLine(snapLine)
        }
    }

    // noinspection OverlyComplexFunctionJS
    function calculateIndexForNewPoint(point: Point): number {
        type PointWithDistance = readonly [Point, number, number, number]

        const ps = points.value
        const size = ps.length
        if (size < 2) {
            return 0
        }
        if (size === 2) {
            return 1
        }
        const a = point.mesh.position
        const ax = a.x
        const az = a.z

        const pointDistances: readonly PointWithDistance[] = ps.map((p, i) => {
            const b = p.mesh.position
            const bx = b.x
            const bz = b.z

            const distanceABX = Math.abs(ax - bx)
            const distanceABZ = Math.abs(az - bz)

            return [
                p,
                i,
                distanceABX,
                distanceABZ,
            ]
        })
        const filteredPointDistances: readonly PointWithDistance[] = pointDistances.filter(p => !p[0].isFixed)

        const sortedPointDistances: readonly PointWithDistance[] = filteredPointDistances.toSorted((a, b) => {
            const [pointA, aIndex, aDisX, aDisZ] = a
            const [pointB, bIndex, bDisX, bDisZ] = b

            if (areNumbersEqual(aDisX, bDisX, BUILDING_EPSILON)) {
                return aDisZ < bDisZ ? -1 : 1
            }
            return aDisX < bDisX ? -1 : 1
        })

        const index = sortedPointDistances[0][1]
        const lastIndex = size - 1

        const b = ps[index].mesh.position
        const bx = b.x
        const bz = b.z

        if (areNumbersEqual(ax, bx, BUILDING_EPSILON)) {
            if (areNumbersEqual(ax, -wallWidth.value / 2, BUILDING_EPSILON)) {
                if (az < bz) {
                    return index + 1 > lastIndex ? 0 : index + 1
                }
                return index
            }
            if (az < bz) {
                return index
            }
            return index + 1 > lastIndex ? 0 : index + 1
        }

        if (ax < bx) {
            return index
        }
        return index + 1 > lastIndex ? 0 : index + 1
    }

    function updatePointPosition(point: Point, positionXZ: Vector2, clampAndSnapPosition: boolean = true) {
        const clampedPositionXZ = positionXZ.clone()

        if (clampAndSnapPosition) {
            const ignoredSnapLineIds = new Set([...point.snapLineIds, ...pointIdToPointLineSnapLineIds.value.get(point.id)!])
            clampToPlaneAndSnap(clampedPositionXZ, emptySet(), ignoredSnapLineIds, point.isFixed)
        }

        point.mesh.position.x = clampedPositionXZ.x
        point.mesh.position.z = clampedPositionXZ.y

        point.textTransformation.value = new Matrix4().makeTranslation(point.mesh.position)

        for (const snapLine of point.snapLines) {
            snappingManager.updatePositionXZAndDirectionXZForLine(
                snapLine.id,
                clampedPositionXZ,
                snapLine.normalizedDirectionXZ,
                false,
            )
        }

        const map = pointMap.value
        for (const pointLineSnapLine of pointIdToPointLineSnapLines.value.get(point.id)!) {
            const a = map.get(pointLineSnapLine.object.point1Id!)!
            const b = map.get(pointLineSnapLine.object.point2Id!)!

            const [positionXZ, normalizedDirectionXZ] = calculatePositionXZAndNormalizedDirectionXZForPoints(a, b)

            snappingManager.updatePositionXZAndDirectionXZForLine(
                pointLineSnapLine.id,
                positionXZ,
                normalizedDirectionXZ,
                false,
            )
        }

        ++wallShapeRefreshCounter.value
    }

    //############
    //### WALL ###
    //############
    const edgeTextOffset = -0.1 //-10cm

    const wallMaterial = new MeshBasicMaterial({
        color: 0x444A69,
        depthTest: false,
    });
    const wallMaterialNeighbor = wallMaterial.clone()
    wallMaterialNeighbor.opacity = 0.1
    wallMaterialNeighbor.transparent = true

    const wallMesh = new Mesh()
    wallMesh.material = wallMaterial
    wallMesh.rotation.x = -Math.PI / 2
    wallMesh.renderOrder = 2

    watch([points, wallShapeRefreshCounter], ([points, wallShapeRefreshCounter]) => {
        const shape = new Shape(points.map(p => new Vector2(p.mesh.position.x, -p.mesh.position.z)))

        wallMesh.geometry.dispose()
        wallMesh.geometry = new ShapeGeometry(shape)
    }, {
        immediate: true
    })

    //### EDGE TEXT ###
    function calculateEdgeText(value: number): string {
        const number = n(value * 100, 'integer')
        const unit = 'cm'//TODO: t('enums.listingFieldUnit.CENTIMETER') //no font support, fallback to "cm"
        return `${number} ${unit}`
    }

    const edgeTexts = computed<readonly string[]>(() => {
        // noinspection BadExpressionStatementJS
        wallShapeRefreshCounter.value //trigger refresh

        const ps = points.value
        const edgeTexts: string[] = []
        for (let i = 0; i < ps.length; ++i) {
            const a = ps[i].mesh.position
            const b = ps[i === ps.length - 1 ? 0 : i + 1].mesh.position

            const distance = a.distanceTo(b)
            edgeTexts.push(calculateEdgeText(distance))
        }
        return edgeTexts
    })
    const edgeTextTransformations = computed<readonly Matrix4[]>(() => {
        // noinspection BadExpressionStatementJS
        wallShapeRefreshCounter.value //trigger refresh

        const ps = points.value
        const edgeTransformations: Matrix4[] = []
        for (let i = 0; i < ps.length; ++i) {
            const a = ps[i].mesh.position
            const b = ps[i === ps.length - 1 ? 0 : i + 1].mesh.position

            //calculate normal
            const direction = b.clone().sub(a).normalize()
            const normal = new Vector3(-direction.z, 0, direction.x)
            const center = a.clone().add(b).divideScalar(2).add(normal.clone().multiplyScalar(edgeTextOffset))

            const translation = new Matrix4().makeTranslation(center.x, 0, center.z)
            edgeTransformations.push(translation)
        }
        return edgeTransformations
    })

    //##############
    //### ANGLES ###
    //##############
    const angleTextOffset = 0.25 //25cm

    function calculateAngleText(value: number): string {
        const unit = '°'//TODO: t('enums.listingFieldUnit.DEGREE')
        const valueString = n(value, 'decimal')

        // if (value > 90) {
        //     const moduloValue = value % 90
        //     const moduloValueString = n(moduloValue, 'decimal')
        //
        //     return `${moduloValueString} ${unit} / ${valueString} ${unit}`
        // }

        return `${valueString} ${unit}`
    }

    const angles = computed<readonly number[]>(() => {
        // noinspection BadExpressionStatementJS
        wallShapeRefreshCounter.value //trigger refresh

        const ps = points.value
        const angles: number[] = []
        for (let i = 0; i < ps.length; ++i) {
            const a = ps[i === 0 ? ps.length - 1 : i - 1].mesh.position
            const b = ps[i].mesh.position
            const c = ps[i === ps.length - 1 ? 0 : i + 1].mesh.position

            const ba = a.clone().sub(b).normalize()
            const bc = c.clone().sub(b).normalize()

            // Calculate the dot product to get the angle between the two vectors
            const dotProduct = ba.dot(bc)
            let angle = Math.acos(dotProduct)

            // Calculate the cross product to determine the direction (clockwise/counterclockwise)
            const crossProduct = new Vector3().crossVectors(ba, bc)

            // Use the sign of the cross product's y-component (or another axis based on your coordinate system)
            // to determine if the angle is clockwise or counterclockwise
            if (crossProduct.y < 0) {
                // If the cross product's y is negative, it means the angle is greater than 180 degrees
                angle = 2 * Math.PI - angle
            }

            let angleInDegrees = MathUtils.radToDeg(angle)
            angleInDegrees = isNaN(angleInDegrees) ? -1 : angleInDegrees
            angleInDegrees = Math.round(angleInDegrees)

            angles.push(angleInDegrees)
        }
        return angles
    })
    const angleTexts = computed<readonly string[]>(() => angles.value.map(calculateAngleText))
    const angleTextTransformations = computed<readonly Matrix4[]>(() => {
        // noinspection BadExpressionStatementJS
        wallShapeRefreshCounter.value //trigger refresh

        const ps = points.value
        const angleTransformations: Matrix4[] = []
        for (let i = 0; i < ps.length; ++i) {
            const a = ps[i === 0 ? ps.length - 1 : i - 1].mesh.position
            const b = ps[i].mesh.position
            const c = ps[i === ps.length - 1 ? 0 : i + 1].mesh.position

            const ba = a.clone().sub(b).normalize()
            const bc = c.clone().sub(b).normalize()

            // Calculate the normal to the plane of the angle
            const normal = new Vector3().crossVectors(ba, bc).normalize()

            // Use the normal to ensure the text is placed consistently on the correct side
            const centerBAandBC = ba.clone().add(bc).normalize().multiplyScalar(angleTextOffset)

            // Check the dot product of the normal to ensure the text doesn't flip
            const dotProduct = normal.dot(new Vector3(0, 1, 0))
            const adjustedCenterBAandBC = dotProduct >= 0 ? centerBAandBC : centerBAandBC.negate()

            // Compute the center point for placing the text
            const center = b.clone().add(adjustedCenterBAandBC)

            // Apply the translation to move the text to the computed center position
            const translation = new Matrix4().makeTranslation(center.x, 0, center.z)
            angleTransformations.push(translation)
        }
        return angleTransformations
    })

    //##################
    //### SNAP LINES ###
    //##################
    const snapLineMaterial = new LineMaterial({
        side: FrontSide,
        depthTest: false,
        worldUnits: true,
        linewidth: 0.01,
        opacity: 1,
        dashed: true,
        dashScale: 10,
        dashSize: 0.5,
        gapSize: 0.5,
        color: 0xB46251,
    })

    type SnapLineTemplate = {
        point1Id?: string
        point2Id?: string
    }

    const halfWidthSnapLine: TSnapLine<SnapLineTemplate> = {
        id: 'halfWidth',
        object: {},
        normalizedDirectionXZ: new Vector2(0, 1),
        positionXZ: new Vector2(0, 0),
        groupId: 'halfWidth',
    }
    const halfHeightSnapLine: TSnapLine<SnapLineTemplate> = {
        id: 'halfHeight',
        object: {},
        normalizedDirectionXZ: new Vector2(1, 0),
        positionXZ: new Vector2(0, 0),
        groupId: 'halfHeight',
    }
    snappingManager.addSnapLine(halfWidthSnapLine)
    snappingManager.addSnapLine(halfHeightSnapLine)

    const neighborSnapLines = computed<readonly TSnapLine<SnapLineTemplate>[]>(() => {
        const wHeight = wallHeight.value
        const halfWallHeight = wHeight / 2
        const centerY = center.value.y

        const snapLines: TSnapLine<SnapLineTemplate>[] = []

        for (const height of filteredNeighborHeights.value) {
            const snapLine: TSnapLine<SnapLineTemplate> = {
                id: `neighbor-${height}`,
                object: {},
                normalizedDirectionXZ: new Vector2(1, 0),
                positionXZ: new Vector2(
                    0,
                    (height - halfWallHeight + centerY) * -1, //inverted z
                ),
                groupId: 'neighbor',
            }

            snapLines.push(snapLine)
        }
        return snapLines
    })

    /**
     * @return [positionXZ, normalizedDirectionXZ]
     */
    function calculatePositionXZAndNormalizedDirectionXZForPoints(point1: Point, point2: Point): [Vector2, Vector2] {
        const a = point1.mesh.position
        const b = point2.mesh.position

        const direction = b.clone().sub(a).normalize()
        const center = a.clone().add(b).divideScalar(2)

        return [
            new Vector2(center.x, center.z),
            new Vector2(direction.x, direction.z)
        ]
    }

    const pointLineSnapLines = computed<readonly TSnapLine<SnapLineTemplate>[]>(() => {
        const snapLines: TSnapLine<SnapLineTemplate>[] = []

        for (let i = 0; i < points.value.length; ++i) {
            const a = points.value[i]
            const b = points.value[i === points.value.length - 1 ? 0 : i + 1]

            const [positionXZ, normalizedDirectionXZ] = calculatePositionXZAndNormalizedDirectionXZForPoints(a, b)

            snapLines.push({
                id: `pointLine-${i}`,
                object: {
                    point1Id: a.id,
                    point2Id: b.id
                },
                normalizedDirectionXZ,
                positionXZ,
                groupId: `pointLine-${i}`,
            })
        }

        return snapLines
    })

    const pointIdToPointLineSnapLines = computed<ReadonlyMap<string, readonly TSnapLine<SnapLineTemplate>[]>>(() => {
        const map = new Map<string, readonly TSnapLine<SnapLineTemplate>[]>()
        for (const point of points.value) {
            const snapLines = pointLineSnapLines.value.filter(sl => sl.object.point1Id === point.id || sl.object.point2Id === point.id)
            map.set(point.id, snapLines)
        }
        return map
    })

    const pointIdToPointLineSnapLineIds = computed<ReadonlyMap<string, ReadonlySet<string>>>(() => {
        const map = new Map<string, ReadonlySet<string>>()
        for (const [pointId, snapLines] of pointIdToPointLineSnapLines.value) {
            map.set(pointId, new Set(snapLines.map(sl => sl.id)))
        }
        return map
    })

    watch(neighborSnapLines, (newSnapLines, oldSnapLines) => {
        if (oldSnapLines !== undefined) {
            for (const snapLine of oldSnapLines) {
                snappingManager.removeSnapLineById(snapLine.id)
            }
        }
        for (const snapLine of newSnapLines) {
            snappingManager.addSnapLine(snapLine)
        }
    }, {
        immediate: true
    })

    watch(pointLineSnapLines, (newSnapLines, oldSnapLines) => {
        if (oldSnapLines !== undefined) {
            for (const snapLine of oldSnapLines) {
                snappingManager.removeSnapLineById(snapLine.id)
            }
        }
        for (const snapLine of newSnapLines) {
            snappingManager.addSnapLine(snapLine)
        }
    }, {
        immediate: true
    })

    const snapLines = computed<readonly TSnapLine<SnapLineTemplate>[]>(() => {
        const snapLines: TSnapLine<SnapLineTemplate>[] = [
            halfWidthSnapLine,
            halfHeightSnapLine,
            ...neighborSnapLines.value,
            ...pointLineSnapLines.value,
        ]
        for (const point of points.value) {
            snapLines.push(...point.snapLines)
        }
        return snapLines
    })
    const snapLineLength = 200
    const halfSnapLineLength = snapLineLength / 2
    const snapLineVertices: readonly Vector3[] = [
        new Vector3(
            -halfSnapLineLength,
            0,
            0,
        ),
        new Vector3(
            halfSnapLineLength,
            0,
            0,
        ),
    ]
    const snapLineTransformations = computed<readonly Matrix4[]>(() => {
        // noinspection BadExpressionStatementJS
        wallShapeRefreshCounter.value //trigger refresh

        const snapLineTransformations: Matrix4[] = []

        for (const snapLine of snapLines.value) {
            const translationMatrix = new Matrix4().makeTranslation(
                snapLine.positionXZ.x,
                0,
                snapLine.positionXZ.y,
            )

            const normalizedDirectionXZ = snapLine.normalizedDirectionXZ
            const theta = Math.atan2(normalizedDirectionXZ.y, normalizedDirectionXZ.x);
            const rotationMatrix = new Matrix4().makeRotationY(-theta)
            snapLineTransformations.push(translationMatrix.multiply(rotationMatrix))
        }

        return snapLineTransformations
    })

    function isSnapLineVisible(snapLine: TSnapLine<SnapLineTemplate>): boolean {
        const snappingResult = lastSnappingResult.value
        if (snappingResult === null) {
            return false
        }
        switch (snappingResult.type) {
            case "LINE":
                return snappingResult.snapLine.id === snapLine.id
            case "LINE_INTERSECTION_POINT":
                return snappingResult.intersectionPoint.line1.id === snapLine.id
                    || snappingResult.intersectionPoint.line2.id === snapLine.id
            default:
                return false
        }
    }

    //########################
    //### NEIGHBOR HEIGHTS ###
    //########################
    type NeighborWallConfig = {
        readonly transformation: Matrix4
        readonly size: number
    }

    const leftNeighborWallConfigs = computed<readonly NeighborWallConfig[]>(() => {
        const configs: NeighborWallConfig[] = []

        const wHeight = wallHeight.value

        const halfWallWidth = wallWidth.value / 2
        const halfWallHeight = wHeight / 2
        const centerY = center.value.y

        for (const height of props.neighborHeightsLeft) {
            const size = height
            const halfSize = size / 2

            const translation = new Matrix4().makeTranslation(
                -halfWallWidth - halfSize,
                0,
                (-halfWallHeight + halfSize + centerY) * -1 //inverted z
            )
            const rotation = new Matrix4().makeRotationX(-Math.PI / 2)
            const transformation = translation.multiply(rotation)

            configs.push({
                transformation,
                size
            })
        }
        return configs
    })

    const rightNeighborWallConfigs = computed<readonly NeighborWallConfig[]>(() => {
        const configs: NeighborWallConfig[] = []

        const halfWallWidth = wallWidth.value / 2
        const halfWallHeight = wallHeight.value / 2
        const centerY = center.value.y

        for (const height of props.neighborHeightsRight) {
            const size = height
            const halfSize = size / 2

            const translation = new Matrix4().makeTranslation(
                halfWallWidth + halfSize,
                0,
                (-halfWallHeight + halfSize + centerY) * -1 //inverted z
            )
            const rotation = new Matrix4().makeRotationX(-Math.PI / 2)
            const transformation = translation.multiply(rotation)

            configs.push({
                transformation,
                size
            })
        }
        return configs
    })

    //#############
    //### OTHER ###
    //#############
    let fitToCameraTimeoutId: Optional<number> = null

    function clearFitToCameraTimeout() {
        if (fitToCameraTimeoutId !== null) {
            clearTimeout(fitToCameraTimeoutId)
            fitToCameraTimeoutId = null
        }
    }

    watch([wallWidth, wallHeight], ([width, height]) => {
        for (const point of points.value) {
            updatePointPosition(
                point,
                new Vector2(point.mesh.position.x, point.mesh.position.z)
            )
        }

        planeMesh.geometry.dispose()
        cameraPlaneMesh.geometry.dispose()

        planeMesh.geometry = new PlaneGeometry(width, height)
        cameraPlaneMesh.geometry = new PlaneGeometry(cameraPlaneWidth.value, cameraPlaneHeight.value)

        //wir müssen hier warten, weil ein parent basierend auf einer neuen breite oder höhe den canvas frame entsprechend anders darstellen könnte. dann braucht das rendering einen kurzen moment.
        //erst nach der neuen canvas größe sollten wir fitToCamera auslösen
        clearFitToCameraTimeout()
        fitToCameraTimeoutId = setTimeout(() => {
            ++fitToCameraRefreshCounter.value
        }, 200)
    }, {
        immediate: true
    })
</script>

<style scoped>
</style>