<template>
    <div class="h-100 w-100">
        <d-wall-editor v-model:vertices="vertices"
                       :neighbor-heights-left="neighborHeightsLeft"
                       :neighbor-heights-right="neighborHeightsRight"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {Vector2} from "three";
    import DWallEditor from "@/components/wall-editor/d-wall-editor.vue";
    import {shallowRef} from "vue";

    const width = 4
    const height = 2
    const halfWidth = width / 2

    const vertices = shallowRef<readonly Vector2[]>([
        new Vector2(-halfWidth, -height * 4 / 5),
        new Vector2(0, height * 1 / 5),
        new Vector2(halfWidth, -height * 4 / 5),
    ])

    const neighborHeightsLeft = new Set([height, height / 2, height / 4 * 3, height / 3])
    const neighborHeightsRight = new Set([height, height / 5, height / 5 * 4, 4])
</script>

<style scoped>
</style>