<template>
    <v-container fluid>
        <v-row>
            <v-col>
                <d-h1>{{ t('nativeApp.rooms.title') }}</d-h1>
            </v-col>
        </v-row>
        <v-row dense>
            <v-col>
                <d-list rounded="lg">
                    <template v-for="(room, index) in building?.rooms ?? []"
                              :key="room.id">
                        <d-divider v-if="index > 0"/>
                        <d-list-item :title="nameOfRoom(index, room)"
                                     @click="onRoomClick(room)">
                            <template #append>
                                <d-confirmation-wrapper @onconfirm="onRoomDeleted(room)">
                                    <template #default="{props}">
                                        <d-btn :icon="mdiDelete"
                                               type="default"
                                               v-bind="props"
                                               variant="text"/>
                                    </template>
                                </d-confirmation-wrapper>
                                <d-icon :icon="mdiChevronRight"
                                        type="default"/>
                            </template>
                        </d-list-item>
                    </template>
                </d-list>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {onMounted, onUnmounted, shallowRef} from "vue";
    import {ListingBuildingScanInput, ListingBuildingScanRoomInput} from "@/adapter/graphql/generated/graphql";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {mdiChevronRight, mdiDelete} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import DH1 from "@/adapter/vuetify/theme/components/text/headline/d-h1.vue";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import {roomName} from "@/components/listing/building/building";

    const building = shallowRef<Optional<ListingBuildingScanInput>>(null)

    const {t, n} = useI18n()

    function nameOfRoom(index: number, room: ListingBuildingScanRoomInput): string {
        return roomName(room, index, t, n)
    }

    const nativeAppMethod = "nativeAppBuildingScan"

    onMounted(() => {
        createNativeAppMethod(nativeAppMethod, (newBuilding: ListingBuildingScanInput) => {
            building.value = newBuilding
        })
        NATIVE_APP_SERVICE?.onBuildingScanReady()
    })

    onUnmounted(() => {
        destroyNativeAppMethod(nativeAppMethod)
    })

    function onRoomClick(room: ListingBuildingScanRoomInput) {
        NATIVE_APP_SERVICE?.onBuildingScanRoomClicked(room.id)
    }

    function onRoomDeleted(room: ListingBuildingScanRoomInput) {
        NATIVE_APP_SERVICE?.onBuildingScanRoomDeleted(room.id)
    }
</script>

<style scoped>
</style>