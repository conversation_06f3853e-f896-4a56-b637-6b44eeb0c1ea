<template>
    <div v-show="false">
        <d-fc-ui-element-custom-scanner v-if="customUIElement"
                                        :custom-element="customUIElement"
                                        :listing-building-token="listingBuildingToken"
                                        is-app-clips-mode
                                        @on-scan-done="onScanDone"/>
    </div>

    <v-container v-if="isScanDone"
                 class="h-100"
                 fluid>
        <v-row align="center"
               class="h-100"
               justify="center">
            <v-col cols="auto">
                <d-alert v-if="hasScanError"
                         :text="t('iOSAppClip.error')"
                         type="error"/>

                <d-alert v-else
                         :text="t('iOSAppClip.success')"
                         type="success"/>
            </v-col>
        </v-row>
    </v-container>

    <d-loading v-else/>
</template>

<script lang="ts"
        setup>
    import DFcUiElementCustomScanner from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner.vue";
    import {ListingContext} from "@/model/listing/ListingContext";
    import {computed, provide, ref, toRef, watch} from "vue";
    import {FCFlowConfigIdInjection, FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingEnvironmentInjection, LListingIdInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import {Optional} from "@/model/Optional";
    import {CustomUiElement, DListingLoadFlowConfigQueryVariables, DListingLoadListingQueryVariables, FlowConfig, Listing, useDListingLoadFlowConfigLazyQuery, useDListingLoadListingQuery} from "@/adapter/graphql/generated/graphql";
    import {findCustomUIElementById} from "@/components/flow-config/use-flow-config";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
    import DLoading from "@/components/fragment/d-loading.vue";
    import {createEmptyFlowData} from "@/model/listing/FlowData";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {useI18n} from "vue-i18n";
    import {createFlowConfigField} from "@/service/flow-config/flow-config-service";
    import {createListingField} from "@/service/native-app/native-app-current-listing";

    const props = defineProps<{
        listingId: string
        customUiElementId: string
        listingBuildingToken: string
    }>()

    const loadListingVariables = computed<DListingLoadListingQueryVariables>(() => ({
        id: props.listingId,
    }))

    const {t} = useI18n()

    const {
        result: loadListingResult,
        onError: onLoadListingError,
        //TODO: loading + error
    } = useDListingLoadListingQuery(loadListingVariables, {
        fetchPolicy: "no-cache"
    })

    const rawListing = computed<Optional<Listing>>(() => loadListingResult.value?.listing as (Listing | undefined) ?? null)
    const listing = createListingField(toRef(() => props.listingId), rawListing)
    const flowConfigId = computed<Optional<string>>(() => listing.value?.flowConfigId ?? null)

    const loadFlowConfigVariables = computed<DListingLoadFlowConfigQueryVariables>(() => ({
        id: flowConfigId.value ?? "",
    }))

    onLoadListingError(error => {
        console.warn("Error while loading listing.", error)
    });

    const {
        result: loadFlowConfigResult,
        load: loadFlowConfig,
        refetch: refetchFlowConfig,
    } = useDListingLoadFlowConfigLazyQuery(loadFlowConfigVariables, {
        fetchPolicy: "no-cache"
    });

    const rawFlowConfig = computed<Optional<FlowConfig>>(() => loadFlowConfigResult.value?.flowConfig as (FlowConfig | undefined) ?? null)
    const flowConfig = createFlowConfigField(flowConfigId, rawFlowConfig)

    watch(flowConfigId, async flowConfigId => {
        if (flowConfigId !== null) {
            try {
                const loadResult = await loadFlowConfig()
                if (loadResult === false) {
                    await refetchFlowConfig()
                }
            } catch (e) {
                console.warn("Error while loading flow config.", e)
            }
        }
    }, {
        immediate: true
    })

    provide(LListingInjection, toRef(() => listing.value!))
    provide(LListingIdInjection, toRef(() => props.listingId))

    provide(FCFlowConfigInjection, toRef(() => flowConfig.value!))
    provide(FCFlowConfigIdInjection, toRef(() => flowConfig.value!.id))

    provide(LFlowDataInjection, toRef(createEmptyFlowData()))

    const customUIElement = computed<Optional<CustomUiElement>>(() => findCustomUIElementById(flowConfig.value, props.customUiElementId))

    const context: ListingContext = "EDIT"
    provide(LListingContextInjection, toRef(context))

    const env = computed<ListingEnvironment>(() => ({
        context: context,
        layout: {
            fill: false,
            position: "DEFAULT"
        }
    }))
    provide(LListingEnvironmentInjection, env)

    provide(LArrayIndexInjection, toRef(null))

    const hasScanError = ref<boolean>(false)
    const isScanDone = ref<boolean>(false)

    function onScanDone(wasSuccessful: boolean) {
        isScanDone.value = true
        hasScanError.value = !wasSuccessful
    }
</script>

<style scoped>

</style>