<template>
    <d-listing-subflow-building-component v-if="listing !== null && flowConfig !== null && customUIElement !== null"
                                          :component-ids="selectedComponentIds"
                                          :custom-ui-element="customUIElement"
                                          embedded
                                          subflow-type="POI"
                                          target="BUILDING_SCAN_POINT_OF_INTEREST"/>
</template>

<script lang="ts"
        setup>
    import DListingSubflowBuildingComponent from "@/components/listing/subflow/building-component/d-listing-subflow-building-component.vue";
    import {computed, onMounted, onUnmounted, provide, shallowRef, toRef, watch} from "vue";
    import {Optional} from "@/model/Optional";
    import {CustomUiElement, DListingLoadFlowConfigQueryVariables, DListingLoadListingQueryVariables, FlowConfig, Listing, useDListingLoadFlowConfigLazyQuery, useDListingLoadListingLazyQuery} from "@/adapter/graphql/generated/graphql";
    import {LArrayIndexInjection, LListingContextInjection, LListingEnvironmentInjection, LListingIdInjection, LListingInjection, LSuggestionsInjection} from "@/components/listing/ListingInjectionKeys";
    import {CONFIG} from "@/config";
    import {createEmptyFlowData} from "@/model/listing/FlowData";
    import {findCustomUIElementById, needsRefresh} from "@/components/flow-config/use-flow-config";
    import {FCFlowConfigIdInjection, FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
    import {ListingContext} from "@/model/listing/ListingContext";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {createFlowConfigField} from "@/service/flow-config/flow-config-service";
    import {createListingField} from "@/service/native-app/native-app-current-listing";

    type NativeAppPoiDataConfig = {
        listingId: string
        customUIElementId: string
        pointOfInterestId: string
    }
    const config = shallowRef<Optional<NativeAppPoiDataConfig>>(null)

    const listingId = computed<Optional<string>>(() => config.value?.listingId ?? null)
    const customUIElementId = computed<Optional<string>>(() => config.value?.customUIElementId ?? null)
    const selectedComponentIds = computed<readonly string[]>(() => config.value === null ? [] : [config.value.pointOfInterestId])
    const context = shallowRef<ListingContext>("EDIT") //static

    watch(listingId, async listingId => {
        if (listingId === null) {
            return
        }
        const loadListingVariables: DListingLoadListingQueryVariables = {
            id: listingId
        }
        try {
            const loadResult = await loadListing(null, loadListingVariables)
            if (loadResult === false) {
                await refetchListing(loadListingVariables)
            }
        } catch (e) {
            console.warn("Error while loading listing.")
        }
    })

    //#########################################################################################################
    //THIS AREA SHOULD BE SIMILAR TO d-listing.vue >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    //#########################################################################################################

    const {
        result: loadListingResult,
        onError: onLoadListingError,
        load: loadListing,
        refetch: refetchListing,
        //TODO: loading + error
    } = useDListingLoadListingLazyQuery()

    const rawListing = computed<Optional<Listing>>(() => loadListingResult.value?.listing as (Listing | undefined) ?? null)
    const listing = createListingField(listingId, rawListing)
    const flowConfigId = computed<Optional<string>>(() => listing.value?.flowConfigId ?? null)

    const loadFlowConfigVariables = computed<DListingLoadFlowConfigQueryVariables>(() => ({
        id: flowConfigId.value ?? "",
    }))

    onLoadListingError(error => {
        console.warn("Error while loading listing.", error)
    });

    const {
        result: loadFlowConfigResult,
        load: loadFlowConfig,
        refetch: refetchFlowConfig,
    } = useDListingLoadFlowConfigLazyQuery(loadFlowConfigVariables)

    const rawFlowConfig = computed<Optional<FlowConfig>>(() => loadFlowConfigResult.value?.flowConfig as (FlowConfig | undefined) ?? null)
    const flowConfig = createFlowConfigField(flowConfigId, rawFlowConfig)

    watch(flowConfigId, async flowConfigId => {
        if (flowConfigId === null) {
            return
        }
        try {
            const loadResult = await loadFlowConfig()
            if (loadResult === false) {
                await refetchFlowConfig()
            }
        } catch (e) {
            console.warn("Error while loading flow config.", e)
        }
    }, {
        immediate: true
    })

    function resetTimeout() {
        if (timeoutId !== null) {
            clearTimeout(timeoutId)
            timeoutId = null
        }
    }

    const nativeAppMethod = "nativeAppPoiDataConfig"
    let timeoutId: Optional<number> = null

    onMounted(() => {
        createNativeAppMethod(nativeAppMethod, (newConfig: NativeAppPoiDataConfig) => {
            config.value = newConfig
        })
        NATIVE_APP_SERVICE?.onPointOfInterestDataConfigReady()

        resetTimeout()
        timeoutId = setTimeout(() => {
            if (flowConfig.value !== null) {
                if (needsRefresh(flowConfig.value)) {
                    refetchFlowConfig()
                }
            }
        }, CONFIG.APOLLO_REFETCH_FLOW_CONFIG_AFTER_LOAD_MS);
    })

    onUnmounted(() => {
        destroyNativeAppMethod(nativeAppMethod)

        resetTimeout()
    })

    provide(LListingInjection, toRef(() => listing.value!))
    provide(LListingIdInjection, toRef(() => listingId.value!))

    provide(FCFlowConfigInjection, toRef(() => flowConfig.value!))
    provide(FCFlowConfigIdInjection, toRef(() => flowConfig.value!.id))

    //#########################################################################################################
    //THIS AREA SHOULD BE SIMILAR TO d-listing.vue <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    //#########################################################################################################

    //#########################################################################################################
    //THIS AREA SHOULD BE SIMILAR TO d-listing-subflow.vue >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    //#########################################################################################################

    const customUIElement = computed<Optional<CustomUiElement>>(() => customUIElementId.value === null ? null : findCustomUIElementById(flowConfig.value, customUIElementId.value))

    provide(LListingContextInjection, context)

    const env = computed<ListingEnvironment>(() => ({
        context: context.value,
        layout: {
            fill: false,
            position: "DEFAULT"
        }
    }))
    provide(LListingEnvironmentInjection, env)

    const suggestions = createEmptyFlowData()
    provide(LSuggestionsInjection, toRef(suggestions))

    provide(LArrayIndexInjection, toRef(null))

    //#########################################################################################################
    //THIS AREA SHOULD BE SIMILAR TO d-listing-subflow.vue <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
    //#########################################################################################################
</script>

<style scoped>
</style>