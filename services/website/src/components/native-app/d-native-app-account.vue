<template>
    <d-account-page-layout :title="t('nativeApp.bottomNavigation.account')">
        <v-container fluid>
            <v-row>
                <v-col>
                    <d-card rounded="lg">
                        <d-list no-background>
                            <template v-for="(item, index) in accountItems"
                                      :key="index">
                                <d-divider v-if="index > 0"/>
                                <d-list-item :class="item.class ?? ''"
                                             :title="item.label"
                                             :to="item.route"
                                             @click="() => item.action?.()"/>
                            </template>
                        </d-list>
                    </d-card>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <d-card rounded="lg">
                        <d-list no-background>
                            <template v-for="(item, index) in settingsItems"
                                      :key="index">
                                <d-divider v-if="index > 0"/>
                                <d-list-item :class="item.class ?? ''"
                                             :title="item.label"
                                             :to="item.route"
                                             @click="() => item.action?.()"/>
                            </template>
                        </d-list>
                    </d-card>
                </v-col>
            </v-row>

            <d-build-info-row/>
        </v-container>
    </d-account-page-layout>
</template>

<script lang="ts"
        setup>
    import {RouteLocationRaw} from "vue-router";
    import {useI18n} from "vue-i18n";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import DBuildInfoRow from "@/components/fragment/d-build-info-row.vue";
    import DAccountPageLayout from "@/components/account/d-account-page-layout.vue";

    const {t} = useI18n()

    type NavigationItem = {
        label: string;
        class?: string
        route?: RouteLocationRaw & { name: string }
        action?: () => void
    }

    const homeRedirectURL = `${window.location.origin}/nativeApp/account/`

    const accountItems: NavigationItem[] = [
        {
            label: t('nativeApp.account.details'),
            route: {
                name: 'accountDetails'
            }
        },
        {
            label: t('nativeApp.account.logout'),
            class: 'errorItem',
            route: {
                name: 'logout',
                query: {
                    redirectURL: homeRedirectURL
                }
            }
        }
    ];

    const settingsItems: NavigationItem[] = [
        {
            label: t('nativeApp.settings.tac'),
            route: {
                name: 'termsAndConditions'
            }
        },
        {
            label: t('nativeApp.settings.privacy'),
            route: {
                name: 'privacyPolicy'
            }
        },
        {
            label: t('nativeApp.settings.imprint'),
            route: {
                name: 'imprint'
            }
        },
        {
            label: t('nativeApp.settings.app'),
            action: () => {
                NATIVE_APP_SERVICE?.onOpenAppSettings()
            }
        },
    ];
</script>

<style scoped>
    .errorItem :deep(.v-list-item-title) {
        color: rgb(var(--v-theme-d-text-error));
    }
</style>