<template>
    <v-container class="h-100">
        <v-row align-content="center"
               class="h-100"
               justify="center">
            <v-col class="text-center"
                   cols="auto">
                <d-h1 class="mb-2">{{ t('components.notFound.title') }}</d-h1>
                <div class="text-body-1 mb-4">{{ t('components.notFound.description') }}</div>
                <!-- ACHTUNG: nativ darf niemals auf iwas zeigen, was nicht mit LDP edit oder account zu tun hat -->
                <d-btn :prepend-icon="mdiAccount"
                       :text="t('components.notFound.backToAccount')"
                       :to="{ name: 'account' }"
                       type="primary"/>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import {mdiAccount} from "@mdi/js";
    import DH1 from "@/adapter/vuetify/theme/components/text/headline/d-h1.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {updatePageTitle} from "@/utility/page-title";

    const {t} = useI18n()

    updatePageTitle(t, t('components.notFound.title'))
</script>

<style scoped>
</style>