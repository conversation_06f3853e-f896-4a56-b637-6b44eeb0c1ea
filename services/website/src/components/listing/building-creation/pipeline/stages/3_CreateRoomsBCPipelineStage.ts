import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, ConstructionPartInput, FloorInput, ListingBuildingScan, ListingBuildingScanRoom, RoomInput} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {createConstructionPartFromSurface, createEmptyShapeRepresentationInput, createRoomShapeRepresentation, matrix4ToTransformationMatrixArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {v4 as uuidv4} from 'uuid';
import {EnsureDefined, mapFlowConfigFieldValueToInput} from "@/adapter/graphql/mapper/graphql-mapper";
import {Matrix4} from "three";
import {createNewRoomCustomData, isRoomValidForRecognition} from "@/components/listing/building/building";
import {mergeFieldValues} from "@/adapter/graphql/mapper/buildinginput-to-building-mapper";

type RoomCreationResult = {
    room: EnsureDefined<RoomInput>
    surfaceWallIdToWallId: ReadonlyMap<string, string>
    notAssignedWallSurfaceIds: string[]
    slopedWallSurfaceIds: string[]
}

export const CreateRoomsBCPipelineStage = new class extends BuildingCreationPipelineStage {
    constructor() {
        super('CREATE_ROOMS');
    }

    async process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>> {
        const building = buildingInput!

        const floorLevels = building.floors.map(f => f.level)
        if (floorLevels.length <= 0) {
            return building
        }

        const maxFloorLevel = Math.max(...floorLevels)

        for (let i = 0; i < building.floors.length; ++i) {
            const floor = building.floors[i]
            const scannedRoomsOfFloor = buildingScan.rooms.filter(room => room.floorLevel === floor.level)
            building.floors[i] = this.createRoomsForFloor(floor, floor.level === maxFloorLevel, scannedRoomsOfFloor)
        }

        return building
    }

    private createRoomsForFloor(floor: EnsureDefined<FloorInput>, isLastFloor: boolean, scannedRooms: readonly ListingBuildingScanRoom[]): EnsureDefined<FloorInput> {
        const rooms: EnsureDefined<RoomInput>[] = []

        for (const scannedRoom of scannedRooms) {
            if (!isRoomValidForRecognition(scannedRoom)) {
                continue
            }
            const roomCreationResult = this.createRoom(scannedRoom, isLastFloor)
            if (roomCreationResult === null) {
                continue
            }
            rooms.push(roomCreationResult.room)
        }

        floor.unrecognizedRooms = rooms

        return floor
    }

    private createRoom(scannedRoom: ListingBuildingScanRoom, isOnLastFloor: boolean): Optional<RoomCreationResult> {
        const surfaceWallIdToWallId = new Map<string, string>()
        const notAssignedWallSurfaceIds: string[] = []
        const slopedWallSurfaceIds: string[] = []

        const roomShapeRepresentation = createRoomShapeRepresentation(scannedRoom) //wird in Stage CREATE_ROOM_SHAPES_AND_SLABS neu erstellt
        if (roomShapeRepresentation === null) {
            return null
        }

        const [shapeRepresentation, roomTransformation] = roomShapeRepresentation
        const inverseRoomTransformation = roomTransformation.clone().invert()

        const floorSlab = this.createRoomFloorSlab(scannedRoom, inverseRoomTransformation) //wird in Stage CREATE_ROOM_SHAPES_AND_SLABS neu erstellt
        const ceilingSlab = isOnLastFloor
            ? null
            : this.createRoomCeilingSlab(scannedRoom, inverseRoomTransformation) //wird in Stage CREATE_ROOM_SHAPES_AND_SLABS neu erstellt

        const room: EnsureDefined<RoomInput> = {
            id: scannedRoom.id,
            displayId: null,
            roomNumber: null,
            customData: mergeFieldValues(createNewRoomCustomData(), scannedRoom.customData).map(mapFlowConfigFieldValueToInput),
            shapeRepresentation,
            floorSlab,
            floorSlabSegments: [],
            ceilingSlab,
            ceilingSlabSegments: [],
            wallIds: [],
            furniture: [],
        }

        return {
            room,
            surfaceWallIdToWallId,
            notAssignedWallSurfaceIds,
            slopedWallSurfaceIds
        }
    }

    private createRoomFloorSlab(scannedRoom: ListingBuildingScanRoom, inverseRoomTransformation: Matrix4): EnsureDefined<ConstructionPartInput> {
        const floorSurfaces = scannedRoom.surfaces.filter(surface => surface.category === 'FLOOR')

        if (floorSurfaces.length <= 0) {
            return {
                id: uuidv4(),
                displayId: null,
                foreignRoomId: null,
                type: "FLOOR",
                customData: [],
                shapeRepresentation: createEmptyShapeRepresentationInput()
            }
        }

        const floorSlab = createConstructionPartFromSurface(floorSurfaces[0], true)

        const floorSurfaceTransformation = transformationMatrixOfShapeRepresentation(floorSlab.shapeRepresentation)
        const newFloorSurfaceTransformation = inverseRoomTransformation.clone().multiply(floorSurfaceTransformation)

        floorSlab.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newFloorSurfaceTransformation)

        return floorSlab
    }

    private createRoomCeilingSlab(scannedRoom: ListingBuildingScanRoom, inverseRoomTransformation: Matrix4): EnsureDefined<ConstructionPartInput> {
        const floorSurfaces = scannedRoom.surfaces.filter(surface => surface.category === 'FLOOR')

        if (floorSurfaces.length <= 0) {
            return {
                id: uuidv4(),
                displayId: null,
                foreignRoomId: null,
                type: "CEILING",
                customData: [],
                shapeRepresentation: createEmptyShapeRepresentationInput()
            }
        }

        const ceilingSlab = createConstructionPartFromSurface(floorSurfaces[0], true, "CEILING")

        const ceilingSurfaceTransformation = transformationMatrixOfShapeRepresentation(ceilingSlab.shapeRepresentation)
        const newCeilingSurfaceTransformation = inverseRoomTransformation.clone().multiply(ceilingSurfaceTransformation)

        ceilingSlab.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newCeilingSurfaceTransformation)

        return ceilingSlab
    }
}