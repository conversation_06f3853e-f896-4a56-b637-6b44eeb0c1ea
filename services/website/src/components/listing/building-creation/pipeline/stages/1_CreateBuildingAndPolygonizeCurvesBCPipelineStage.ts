import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, BuildingPointOfInterestInput, ConstructionPartInput, FloorInput, FurnitureInput, ListingBuildingScan, ListingBuildingScanObject, ListingBuildingScanPointOfInterest, ListingBuildingScanSurface, WallInput} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {v4 as uuidv4} from 'uuid';
import {createConstructionPartFromSurface, createEmptyShapeRepresentationInput, createShapeRepresentationFromObject, createShapeRepresentationFromSurface, matrix4ToTransformationMatrixArray, surfacesOfFloorLevel, transformationMatrixOfShapeRepresentation, validateSurface, wallSurfacesOfFloorLevel} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {EnsureDefined, mapFlowConfigFieldValueToInput} from "@/adapter/graphql/mapper/graphql-mapper";
import {BULDING_WALL_THICKNESS_DEFAULT_EVEBI, createNewWallInputCustomData, thicknessOfShape} from "@/components/listing/building/building";

export const CreateBuildingBCPipelineStage = new class extends BuildingCreationPipelineStage {
    constructor() {
        super('CREATE_BUILDING');
    }

    async process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>> {
        if (!buildingScan.wasRawDataValid) {
            console.warn('Building scan data is invalid') //TODO: anders machen?
        }

        const floors = this.createFloors(buildingScan)
        const pointsOfInterest = this.createPointsOfInterest(buildingScan)

        return {
            // Customizable
            customData: [],

            // Representable
            shapeRepresentation: createEmptyShapeRepresentationInput(), //wird in Stage CREATE_BUILDING_SHAPE erstellt

            // Building
            id: buildingScan.id,
            displayId: null,
            floors,
            pointsOfInterest,
            rotationYCorrectionAngle: 0, //wird in Stage CREATE_BUILDING_SHAPE erstellt
        }
    }

    private createFloors(buildingScan: ListingBuildingScan): EnsureDefined<FloorInput>[] {
        const floorLevels = buildingScan.floorLevels
        if (floorLevels.length <= 0) {
            return []
        }

        const maxFloorLevel = Math.max(...floorLevels)

        const floors: EnsureDefined<FloorInput>[] = []

        for (const floorLevel of floorLevels) {
            const floor = this.createFloor(floorLevel, buildingScan, floorLevel === maxFloorLevel)
            floors.push(floor)
        }

        return floors
    }

    private createFloor(floorLevel: number, buildingScan: ListingBuildingScan, isLast: boolean): EnsureDefined<FloorInput> {
        const walls = this.createWalls(floorLevel, buildingScan)
        const floorSlab = this.createFloorSlabFloorFromBuildingScan(floorLevel, buildingScan)
        const ceilingSlab = isLast ? null : this.createFloorSlabCeilingFromBuildingScan(floorLevel, buildingScan)
        const furniture = this.createFurnitureList(floorLevel, buildingScan)

        const shapeRepresentation = createEmptyShapeRepresentationInput() //wird in Stage CREATE_FLOOR_SHAPES erstellt

        return {
            // Customizable
            customData: [],

            // Representable
            shapeRepresentation,

            // Floor
            id: uuidv4(),
            displayId: null,
            level: floorLevel,
            rooms: [], //wird in Stage CREATE_ROOMS erstellt
            unrecognizedRooms: [], //wird in Stage CREATE_ROOMS erstellt
            pointsOfInterest: [],
            walls,
            floorSlab,
            ceilingSlab,
            furniture,
            roofAreas: [],
        }
    }

    private createWalls(floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<WallInput>[] {
        const wallSurfaces = wallSurfacesOfFloorLevel(floorLevel, buildingScan)

        const walls = []

        for (const wallSurface of wallSurfaces) {
            const wall = this.createWall(wallSurface, floorLevel, buildingScan)
            walls.push(wall)
        }

        return walls
    }

    private createWall(wallSurface: ListingBuildingScanSurface, floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<WallInput> {
        validateSurface(wallSurface)

        const openings = this.createOpenings(wallSurface, floorLevel, buildingScan)
        const shapeRepresentation = createShapeRepresentationFromSurface(wallSurface, false)

        const thickness = thicknessOfShape(shapeRepresentation)
        const sizeAdjustmentZ = BULDING_WALL_THICKNESS_DEFAULT_EVEBI - thickness

        return {
            // Customizable
            customData: createNewWallInputCustomData(),

            // Representable
            shapeRepresentation,

            // Wall
            id: wallSurface.id,
            displayId: null,
            openings,
            roomIds: [],
            isExterior: false,
            isPartition: false,
            isIntermediate: false,
            sizeAdjustmentX: 0,
            sizeAdjustmentY: 0,
            sizeAdjustmentZ,
            sizeAdjustmentUserX: 0,
            sizeAdjustmentUserXRelatedWallIds: [],
            sizeAdjustmentUserY: 0,
        }
    }

    private createOpenings(wallSurface: ListingBuildingScanSurface, floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<ConstructionPartInput>[] {
        const surfaces = surfacesOfFloorLevel(floorLevel, buildingScan)
        const children = surfaces.filter(surface =>
            surface.parentId === wallSurface.id &&
            surface.wasRawBoundingBoxValid &&
            surface.wasRawPolygonValid &&
            surface.wasRawTransformationValid
        )

        const windows = children.filter(child => child.category === 'WINDOW')
        const doors = children.filter(child => child.category === 'DOOR')
        const openings = children.filter(child => child.category === 'OPENING')

        const constructionParts: EnsureDefined<ConstructionPartInput>[] = []

        for (const window of windows) {
            const constructionPart = createConstructionPartFromSurface(window, false)
            constructionParts.push(constructionPart)
        }

        for (const door of doors) {
            const constructionPart = createConstructionPartFromSurface(door, false)
            constructionParts.push(constructionPart)
        }

        for (const opening of openings) {
            const constructionPart = createConstructionPartFromSurface(opening, false)
            constructionParts.push(constructionPart)
        }

        return constructionParts
    }

    private createFloorSlabFloorFromBuildingScan(floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<ConstructionPartInput> {
        const surfaces = surfacesOfFloorLevel(floorLevel, buildingScan)
        const floors = surfaces.filter(surface => surface.category === 'FLOOR')

        if (floors.length <= 0) {
            return {
                id: uuidv4(),
                displayId: null,
                foreignRoomId: null,
                type: "FLOOR",
                customData: [],
                shapeRepresentation: createEmptyShapeRepresentationInput()
            }
        }

        return createConstructionPartFromSurface(floors[0], true)
    }

    private createFloorSlabCeilingFromBuildingScan(floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<ConstructionPartInput> {
        const surfaces = surfacesOfFloorLevel(floorLevel, buildingScan)
        const floors = surfaces.filter(surface => surface.category === 'FLOOR')

        if (floors.length <= 0) {
            return {
                id: uuidv4(),
                displayId: null,
                foreignRoomId: null,
                type: "CEILING",
                customData: [],
                shapeRepresentation: createEmptyShapeRepresentationInput()
            }
        }

        const constructionPart = createConstructionPartFromSurface(floors[0], true, "CEILING")
        const transformationMatrix = transformationMatrixOfShapeRepresentation(constructionPart.shapeRepresentation)
        const inverseTransformationMatrix = transformationMatrix.clone().invert()
        constructionPart.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(inverseTransformationMatrix)

        return constructionPart
    }

    private createFurnitureList(floorLevel: number, buildingScan: ListingBuildingScan): EnsureDefined<FurnitureInput>[] {
        const furniture: EnsureDefined<FurnitureInput>[] = []

        for (const object of buildingScan.objects) {
            if (object.floorLevel === floorLevel) {
                furniture.push(this.createFurniture(object))
            }
        }

        return furniture
    }

    private createFurniture(object: ListingBuildingScanObject): EnsureDefined<FurnitureInput> {
        const shapeRepresentation = createShapeRepresentationFromObject(object);

        return {
            id: object.id,
            displayId: null,
            type: object.category,
            customData: [],
            shapeRepresentation: shapeRepresentation
        }
    }

    private createPointsOfInterest(buildingScan: ListingBuildingScan): EnsureDefined<BuildingPointOfInterestInput>[] {
        const pois = buildingScan.pointsOfInterest ?? []

        const pointsOfInterest: EnsureDefined<BuildingPointOfInterestInput>[] = []

        for (const poi of pois) {
            const pointOfInterest = this.createPointOfInterest(poi)
            pointsOfInterest.push(pointOfInterest)
        }

        return pointsOfInterest
    }

    private createPointOfInterest(poi: ListingBuildingScanPointOfInterest): EnsureDefined<BuildingPointOfInterestInput> {
        return {
            id: poi.id,
            displayId: null,
            customData: poi.customData.map(mapFlowConfigFieldValueToInput),
            transformationMatrix: poi.transformation
        }
    }
}