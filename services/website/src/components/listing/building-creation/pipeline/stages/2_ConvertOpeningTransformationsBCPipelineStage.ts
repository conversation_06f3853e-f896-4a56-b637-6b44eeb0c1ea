import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, ConstructionPartInput, FloorInput, ListingBuildingScan, WallInput} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {matrix4ToTransformationMatrixArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {Matrix4} from "three";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";

export const ConvertOpeningTransformationsBCPipelineStage = new class extends BuildingCreationPipelineStage {
    constructor() {
        super('CONVERT_OPENING_TRANSFORMATIONS');
    }

    async process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>> {
        const building = buildingInput!

        for (let i = 0; i < building.floors.length; ++i) {
            building.floors[i] = this.convertFloor(building.floors[i])
        }
        return building
    }

    private convertFloor(floor: EnsureDefined<FloorInput>): EnsureDefined<FloorInput> {
        for (let i = 0; i < floor.walls.length; ++i) {
            floor.walls[i] = this.convertWall(floor.walls[i])
        }
        return floor
    }

    private convertWall(wall: EnsureDefined<WallInput>): EnsureDefined<WallInput> {
        const wallTransformation = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)
        const inverseWallTransformation = wallTransformation.invert()

        for (let i = 0; i < wall.openings.length; ++i) {
            wall.openings[i] = this.convertOpening(wall.openings[i], inverseWallTransformation)
        }
        return wall
    }

    private convertOpening(opening: EnsureDefined<ConstructionPartInput>, inverseWallTransformation: Matrix4): EnsureDefined<ConstructionPartInput> {
        const openingTransformation = transformationMatrixOfShapeRepresentation(opening.shapeRepresentation)
        const newTransformation = inverseWallTransformation.clone().multiply(openingTransformation)

        opening.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        return opening
    }
}