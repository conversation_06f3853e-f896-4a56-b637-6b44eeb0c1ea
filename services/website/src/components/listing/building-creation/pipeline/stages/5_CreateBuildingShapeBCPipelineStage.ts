import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, ListingBuildingScan} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {Box3, MathUtils, Matrix4, <PERSON><PERSON>, Vector3} from "three";
import {createIdentityMatrix, matrix4ToTransformationMatrixArray, shapeToBufferedGeometry_000, transformationMatrixOfArray, transformationMatrixOfShapeRepresentation, wallSurfacesOfFloorLevel} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {calculateCamerasOfPointOfInterest, overwriteCameraTransformationMatrixOfPointOfInterestInput, transformationOfShape, transformationOfShapeRepresentation} from "@/components/listing/building/building";
import {tDeskewAngle3D, tDestroyMesh, tOverrideTransformation} from "@/adapter/three/three-utility";
import {calculateDeskew3DLineSegments} from "@/components/listing/building-scan/building-scan";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";


export const CreateBuildingShapeBCPipelineStage = new class extends BuildingCreationPipelineStage {
    constructor() {
        super('CREATE_BUILDING_SHAPE');
    }

    // noinspection FunctionTooLongJS
    async process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>> {
        const building = buildingInput!

        // noinspection LocalVariableNamingConventionJS
        const meshes_000_G = building.floors.map(floor => {
            const transformation = transformationOfShapeRepresentation(floor.shapeRepresentation)
            const shapeTransformation = transformationOfShape(floor.shapeRepresentation)
            const geometry = shapeToBufferedGeometry_000(floor.shapeRepresentation, false, false)

            const mesh = new Mesh(geometry)
            const meshTransformation = transformation.clone().multiply(shapeTransformation)

            tOverrideTransformation(mesh, meshTransformation)

            mesh.updateMatrix()
            mesh.updateMatrixWorld(true)

            return mesh
        })

        const boundingBox = new Box3();
        // noinspection LocalVariableNamingConventionJS
        for (const mesh_000_G of meshes_000_G) {
            boundingBox.expandByObject(mesh_000_G, true);
        }

        // noinspection LocalVariableNamingConventionJS
        for (const mesh_000_G of meshes_000_G) {
            tDestroyMesh(mesh_000_G, true, true)
        }

        const center = boundingBox.getCenter(new Vector3())
        const size = boundingBox.getSize(new Vector3())

        const buildingTransformationMatrix = new Matrix4().makeTranslation(center.x, center.y, center.z) //für die kinder

        const deskewAngle = this.calculateDeskewAngleInRadians(buildingScan)

        let angle: number
        if (deskewAngle === null) {
            angle = 0
        } else {
            angle = MathUtils.radToDeg(deskewAngle)
        }

        let transformationMatrix: number[][]
        if (angle === 0) {
            transformationMatrix = createIdentityMatrix()
        } else {
            const matrix = new Matrix4().makeRotationY(MathUtils.degToRad(angle));
            transformationMatrix = matrix4ToTransformationMatrixArray(matrix)
        }

        building.shapeRepresentation = {
            transformationMatrix: transformationMatrix,
            box: {
                width: size.x,
                height: size.y,
                depth: size.z,
            },
            polygon: null,
            ring: null,
        }

        let newAngle = buildingScan.rotationYCorrectionAngle - angle
        //TODO: modulo wäre besser
        if (newAngle < 0) {
            newAngle += 360
        } else if (newAngle >= 360) {
            newAngle -= 360
        }
        building.rotationYCorrectionAngle = newAngle

        const inverseBuildingTransformationMatrix = buildingTransformationMatrix.clone().invert()

        for (const floor of building.floors) {
            const floorShapeRepresentation = floor.shapeRepresentation
            const transformationMatrix = transformationMatrixOfShapeRepresentation(floorShapeRepresentation)
            const newTransformationMatrix = inverseBuildingTransformationMatrix.clone().multiply(transformationMatrix)

            floorShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformationMatrix)
        }

        for (const poi of building.pointsOfInterest ?? []) {
            const transformationMatrix = transformationMatrixOfArray(poi.transformationMatrix)
            const newTransformationMatrix = inverseBuildingTransformationMatrix.clone().multiply(transformationMatrix)

            poi.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformationMatrix)

            const cameras = calculateCamerasOfPointOfInterest(poi)
            for (const camera of cameras) {
                const cameraTransformationMatrix = camera.transformationMatrix
                const newCameraTransformationMatrix = inverseBuildingTransformationMatrix.clone().multiply(cameraTransformationMatrix)

                overwriteCameraTransformationMatrixOfPointOfInterestInput(poi, camera.arrayIndex, newCameraTransformationMatrix)
            }
        }

        return building
    }

    private calculateDeskewAngleInRadians(buildingScan: ListingBuildingScan): Optional<number> {
        const wallSurfaces = wallSurfacesOfFloorLevel(0, buildingScan)
        const lines = calculateDeskew3DLineSegments(wallSurfaces)
        return tDeskewAngle3D(lines) ?? 0
    }
}