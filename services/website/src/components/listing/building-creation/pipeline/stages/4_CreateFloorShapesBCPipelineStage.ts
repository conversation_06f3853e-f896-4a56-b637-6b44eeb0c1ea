import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, ConstructionPartInput, FloorInput, FurnitureInput, ListingBuildingScan, RoomInput, ShapeRepresentationInput, WallInput} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {Matrix4} from "three";
import {matrix4ToTransformationMatrixArray, transformationMatrixOfArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, BUILDING_FLOOR_SLAB_THICKNESS_MIN, calculateCamerasOfPointOfInterest, heightOfShape, overwriteCameraTransformationMatrixOfPointOfInterestInput} from "@/components/listing/building/building";
import {calculateFloorLevelToPoiMapFromBuilding} from "@/components/listing/building-scan/building-scan";
import {tDecomposeMatrix, tRoundRotationOfTransformationTo90DegreeSteps} from "@/adapter/three/three-utility";
import {toRaw} from "vue";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";


export const CreateFloorShapesBCPipelineStage = new class extends BuildingCreationPipelineStage {
    constructor() {
        super('CREATE_FLOOR_SHAPES');
    }

    // noinspection FunctionTooLongJS
    async process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>> {
        const building = buildingInput!

        const floorLevelToPoiMap = calculateFloorLevelToPoiMapFromBuilding(building)

        //FLOOR SHAPES + TRANSFORMATIONS
        for (let i = 0; i < building.floors.length; ++i) {
            const currentFloor = building.floors[i]
            const [floorShapeRepresentation, floorTransformationMatrix] = this.createFloorShapeRepresentation(currentFloor, buildingScan)
            const inverseFloorTransformationMatrix = floorTransformationMatrix.clone().invert()

            currentFloor.shapeRepresentation = floorShapeRepresentation

            currentFloor.floorSlab = this.updateTransformation(currentFloor.floorSlab, inverseFloorTransformationMatrix)
            currentFloor.rooms = currentFloor.rooms.map(room => this.updateTransformation(room, inverseFloorTransformationMatrix))
            currentFloor.unrecognizedRooms = (currentFloor.unrecognizedRooms as (null | undefined | EnsureDefined<RoomInput>[]))?.map(room => this.updateTransformation(room, inverseFloorTransformationMatrix)) ?? null
            currentFloor.walls = currentFloor.walls.map(wall => this.updateTransformation(wall, inverseFloorTransformationMatrix))
            currentFloor.furniture = (currentFloor.furniture as (null | undefined | EnsureDefined<FurnitureInput>[]))?.map(furniture => this.updateTransformation(furniture, inverseFloorTransformationMatrix)) ?? null

            const pois = floorLevelToPoiMap.get(currentFloor.level) ?? []
            for (const poi of pois) {
                const oldTransformationMatrix = transformationMatrixOfArray(poi.transformationMatrix)
                const newTransformationMatrix = inverseFloorTransformationMatrix.clone().multiply(oldTransformationMatrix)

                poi.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformationMatrix)

                const cameras = calculateCamerasOfPointOfInterest(poi)
                for (const camera of cameras) {
                    const cameraTransformationMatrix = camera.transformationMatrix
                    const newCameraTransformationMatrix = inverseFloorTransformationMatrix.clone().multiply(cameraTransformationMatrix)

                    overwriteCameraTransformationMatrixOfPointOfInterestInput(poi, camera.arrayIndex, newCameraTransformationMatrix)
                }

                if (currentFloor.pointsOfInterest === undefined || currentFloor.pointsOfInterest === null) {
                    currentFloor.pointsOfInterest = [poi]
                } else {
                    currentFloor.pointsOfInterest.push(poi)
                }
                building.pointsOfInterest = building.pointsOfInterest?.filter(p => p.id !== poi.id) ?? []
            }
        }

        //SLAB HEIGHTS
        for (let i = building.floors.length - 1; i >= 0; --i) {
            const currentFloor = building.floors[i];
            const previousFloor = i > 0 ? building.floors[i - 1] : null;
            building.floors[i] = this.updateFloorSlabHeight(currentFloor, previousFloor);
        }

        return building
    }

    private updateTransformation<T extends EnsureDefined<ConstructionPartInput | RoomInput | WallInput | FurnitureInput>>(component: T, inverseFloorTransformationMatrix: Matrix4): T {
        const componentShapeRepresentation = component.shapeRepresentation
        const transformationMatrix = transformationMatrixOfShapeRepresentation(componentShapeRepresentation)
        const newTransformationMatrix = inverseFloorTransformationMatrix.clone().multiply(transformationMatrix)

        componentShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformationMatrix)

        return component
    }

    /**
     * @return [floorShapeRepresentation, floorTransformationMatrix]
     */
    private createFloorShapeRepresentation(currentFloor: EnsureDefined<FloorInput>, buildingScan: ListingBuildingScan): [EnsureDefined<ShapeRepresentationInput>, Matrix4] {
        const maxWallHeight = currentFloor.walls.reduce((maxHeight, wall) => Math.max(maxHeight, heightOfShape(wall.shapeRepresentation)), 0)

        const floorSlabShapeRepresentation: EnsureDefined<ShapeRepresentationInput> = JSON.parse(JSON.stringify(toRaw(currentFloor.floorSlab.shapeRepresentation)))

        if (floorSlabShapeRepresentation.box) {
            floorSlabShapeRepresentation.box.height = maxWallHeight
        } else if (floorSlabShapeRepresentation.polygon) {
            floorSlabShapeRepresentation.polygon.extrusion = maxWallHeight
        } else if (floorSlabShapeRepresentation.ring) {
            floorSlabShapeRepresentation.ring.extrusion = maxWallHeight
        }

        const floorSlabTransformationMatrix = transformationMatrixOfShapeRepresentation(floorSlabShapeRepresentation)
        const centerTranslationMatrix = new Matrix4().makeTranslation(0, maxWallHeight / 2, 0)
        const floorTransformationMatrix = floorSlabTransformationMatrix.clone().multiply(centerTranslationMatrix)
        const correctedFloorTransformationMatrix = tRoundRotationOfTransformationTo90DegreeSteps(floorTransformationMatrix)
        const floorTransformationMatrixArray = matrix4ToTransformationMatrixArray(correctedFloorTransformationMatrix)

        const floorShapeRepresentation: EnsureDefined<ShapeRepresentationInput> = {
            ...floorSlabShapeRepresentation,
            transformationMatrix: floorTransformationMatrixArray
        }

        return [floorShapeRepresentation, correctedFloorTransformationMatrix]
    }

    // noinspection FunctionTooLongJS,OverlyComplexFunctionJS
    private updateFloorSlabHeight(floor: EnsureDefined<FloorInput>, previousFloor: Optional<FloorInput>): EnsureDefined<FloorInput> {
        const previousFloorHeight = previousFloor === null ? 0 : heightOfShape(previousFloor.shapeRepresentation)
        const previousFloorTransformationMatrix = previousFloor === null ? new Matrix4().identity() : transformationMatrixOfShapeRepresentation(previousFloor.shapeRepresentation)
        const [previousFloorCenter] = tDecomposeMatrix(previousFloorTransformationMatrix)

        const floorHeight = heightOfShape(floor.shapeRepresentation)
        const floorTransformationMatrix = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
        const [floorCenter] = tDecomposeMatrix(floorTransformationMatrix)

        const floorSlabFloorHeight = Math.max(BUILDING_FLOOR_SLAB_THICKNESS_MIN, (floorCenter.y - floorHeight / 2) - (previousFloorCenter.y + previousFloorHeight / 2))
        const floorSlabCeilingHeight = BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT

        const floorSlabFloorTransformationMatrix = new Matrix4().makeTranslation(0, -(floorHeight + floorSlabFloorHeight) / 2, 0)
        const floorSlabCeilingTransformationMatrix = new Matrix4().makeTranslation(0, (floorHeight + floorSlabCeilingHeight) / 2, 0)

        if (floor.floorSlab.shapeRepresentation.box) {
            floor.floorSlab.shapeRepresentation.box.height = floorSlabFloorHeight
        } else if (floor.floorSlab.shapeRepresentation.polygon) {
            floor.floorSlab.shapeRepresentation.polygon.extrusion = floorSlabFloorHeight
        } else if (floor.floorSlab.shapeRepresentation.ring) {
            floor.floorSlab.shapeRepresentation.ring.extrusion = floorSlabFloorHeight
        }
        floor.floorSlab.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(floorSlabFloorTransformationMatrix)

        if (floor.ceilingSlab) {
            if (floor.ceilingSlab.shapeRepresentation.box) {
                floor.ceilingSlab.shapeRepresentation.box.height = floorSlabCeilingHeight
            } else if (floor.ceilingSlab.shapeRepresentation.polygon) {
                floor.ceilingSlab.shapeRepresentation.polygon.extrusion = floorSlabCeilingHeight
            } else if (floor.ceilingSlab.shapeRepresentation.ring) {
                floor.ceilingSlab.shapeRepresentation.ring.extrusion = floorSlabCeilingHeight
            }
            floor.ceilingSlab.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(floorSlabCeilingTransformationMatrix)
        }

        return floor
    }
}