import * as THREE from "three";
import {BoxGeometry, BufferGeometry, ExtrudeGeometry, MathUtils, Matrix4, Mesh, Vector2, Vector3} from "three";
import {Optional} from "@/model/Optional";
import {chunkify} from "@/utility/arrays";
import {BUILDING_EPSILON, createNewWallOpeningCustomData, isExtrudable3D, vector2DToVector2} from "@/components/listing/building/building";
import {BoxInput, ConstructionPartInput, Extrudable, ListingBuildingScan, ListingBuildingScanObject, ListingBuildingScanRoom, ListingBuildingScanRoomInput, ListingBuildingScanSurface, PolygonInput, RingInput, Shape, ShapeRepresentation, ShapeRepresentationInput, Vector2DInput, Vector3DInput} from "@/adapter/graphql/generated/graphql";
import {transformationMatrixOfSurface} from "@/components/listing/building-scan/building-scan";
import {toRaw} from "vue";
import {tCenterOfVectors2, tDest<PERSON><PERSON>esh, tPolygon3DToBufferGeometry_000, tVectors2ToCentralPoint} from "@/adapter/three/three-utility";
import {csgGenerateGeometryWithHoles_000} from "@/adapter/csg/csg-utils";
import {EnsureDefined, mapFlowConfigFieldValueToInput} from "@/adapter/graphql/mapper/graphql-mapper";
import {WallOpeningTypeValues} from "@/model/building/WallOpeningType";

export function createIdentityMatrix(): number[][] {
    return [
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [0, 0, 0, 1]
    ]
}

export function createConstructionPartFromSurface(surface: ListingBuildingScanSurface, ignoreThickness: boolean, forcedCategory?: string): EnsureDefined<ConstructionPartInput> {
    validateSurface(surface)

    const shapeRepresentation = createShapeRepresentationFromSurface(surface, ignoreThickness)

    const type = forcedCategory ?? surface.category;
    return {
        // Customizable
        customData: WallOpeningTypeValues.includes(type) ? createNewWallOpeningCustomData(type).map(mapFlowConfigFieldValueToInput) : [],

        // Representable
        shapeRepresentation,

        // ConstructionPart
        id: surface.id,
        displayId: null,
        type,
        foreignRoomId: null,
    }
}

export function createEmptyShapeRepresentationInput(): EnsureDefined<ShapeRepresentationInput> {
    return {
        transformationMatrix: createIdentityMatrix(),
        box: {
            width: 0,
            height: 0,
            depth: 0,
        },
        polygon: null,
        ring: null,
    }
}

export function matrix4ToTransformationMatrixArray(matrix: Matrix4): number[][] {
    return chunkify(matrix.toArray(), 4)
}

export function createShapeRepresentationFromSurface(surface: ListingBuildingScanSurface, ignoreThickness: boolean): EnsureDefined<ShapeRepresentationInput> {
    //for testing
    // return {
    //     transformationMatrix: surface.transformation,
    //     box: createBoxFromSurface(surface, ignoreThickness)
    // }

    const hasRing = surface.arc != null
    const hasPolygon = !hasRing && surface.polygon.length > 0

    const box = !hasPolygon && !hasRing ? createBoxFromSurface(surface, ignoreThickness) : null
    const polygon = hasPolygon ? createPolygonFromSurface(surface, ignoreThickness)! : null
    const ring = hasRing ? createRingFromSurface(surface, ignoreThickness)! : null

    let transformationMatrix = surface.transformation
    if (ring !== null) {
        const center = ring.center
        const centerMatrix = new Matrix4().makeTranslation(center.x, 0, center.y)

        const oldTransformation = transformationMatrixOfSurface(surface)
        const newTransformation = oldTransformation.clone().multiply(centerMatrix)
        transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        ring.center = {
            x: 0,
            y: 0
        }
    }

    return {
        transformationMatrix,

        box,
        polygon,
        ring,
    }
}

export function createShapeRepresentationFromObject(object: ListingBuildingScanObject): EnsureDefined<ShapeRepresentationInput> {
    const width = object.boundingBox[0]
    const height = object.boundingBox[1]
    const depth = object.boundingBox[2]

    return {
        transformationMatrix: object.transformation,

        box: {
            width,
            height,
            depth,
        },
        polygon: null,
        ring: null,
    }
}

export function createBoxFromSurface(source: ListingBuildingScanSurface, ignoreThickness: boolean): EnsureDefined<BoxInput> {
    const width = source.boundingBox[0]
    const height = source.boundingBox[1]
    const depth = source.boundingBox[2]

    //FLOOR
    if (source.category === "FLOOR") {
        return {
            width,
            height: ignoreThickness ? 0 : height,
            depth
        }
    }

    //OTHERS
    return {
        width,
        height,
        depth: ignoreThickness ? 0 : depth,
    }
}

function createDefaultExtrudableFromSurface(surface: ListingBuildingScanSurface, ignoreThickness: boolean): Extrudable {
    const extrusion = ignoreThickness ? 0 : surface.boundingBox[2]
    const extrusionDirection: EnsureDefined<Vector3DInput> = {
        x: 0,
        y: 0,
        z: 1,
    }

    return {
        extrusion,
        extrusionDirection,
    }
}

export function createPolygonFromSurface(surface: ListingBuildingScanSurface, ignoreThickness: boolean): Optional<EnsureDefined<PolygonInput>> {
    const polygon = surface.polygon;

    if (polygon.length <= 0) {
        return null
    }

    const vertices = createVectors3D(polygon)

    //complete polygon if necessary
    if (vertices.length > 0) {
        const first = vertices[0]
        const last = vertices[vertices.length - 1]
        if (first.x !== last.x || first.y !== last.y || first.z !== last.z) {
            vertices.push(first)
        }
    }

    const basePolygon: EnsureDefined<PolygonInput> = {
        ...createDefaultExtrudableFromSurface(surface, ignoreThickness),

        vertices,
        holes: [],
    }

    switch (surface.category) {
        case 'FLOOR':
            return {
                ...basePolygon,

                extrusion: ignoreThickness ? 0 : surface.boundingBox[1],
                extrusionDirection: {
                    x: 0,
                    y: 1,
                    z: 0,
                },
            }

        default:
            return basePolygon
    }
}

function createRingFromSurface(surface: ListingBuildingScanSurface, ignoreThickness: boolean): Optional<EnsureDefined<RingInput>> {
    const arc = surface.arc

    if (arc == null) {
        return null
    }

    const center = createVector2D(arc.center)

    const baseRing: RingInput = {
        ...createDefaultExtrudableFromSurface(surface, false),
        size: ignoreThickness ? 0 : surface.boundingBox[1],

        radius: arc.radius,
        startAngle: arc.startAngle,
        endAngle: arc.endAngle,
        center,
    }

    switch (surface.category) {
        case 'WALL':
        case 'WINDOW':
        case 'DOOR':
        case 'OPENING':
            return {
                ...baseRing,

                extrusion: surface.boundingBox[1],
                extrusionDirection: {
                    x: 0,
                    y: 1,
                    z: 0,
                },
                size: ignoreThickness ? 0 : surface.boundingBox[2],
            }

        default:
            return baseRing
    }
}

export function createVectors3D(points: number[][]): EnsureDefined<Vector3DInput>[] {
    return points.map(createVector3D)
}

export function createVector3D(point: number[]): EnsureDefined<Vector3DInput> {
    return {
        x: point[0],
        y: point[1],
        z: point[2],
    }
}

export function createVector2D(point: number[]): EnsureDefined<Vector2DInput> {
    return {
        x: point[0],
        y: point[1],
    }
}

export function surfacesOfFloorLevel(floorLevel: number, buildingScan: ListingBuildingScan): ListingBuildingScanSurface[] {
    return buildingScan.surfaces.filter(surface => surface.floorLevel === floorLevel)
}

export function wallSurfacesOfFloorLevel(floorLevel: number, buildingScan: ListingBuildingScan): ListingBuildingScanSurface[] {
    const surfaces = surfacesOfFloorLevel(floorLevel, buildingScan)
    return surfaces.filter(surface => surface.category === 'WALL')
}

export function validateSurface(surface: ListingBuildingScanSurface): void {
    if (!surface.wasRawBoundingBoxValid) {
        console.warn(`Bounding box of surface ${surface.id} is invalid`) //TODO: anders machen?
    }
    if (!surface.wasRawTransformationValid) {
        console.warn(`Transformation of surface ${surface.id} is invalid`) //TODO: anders machen?
    }
    if (!surface.wasRawPolygonValid) {
        console.warn(`Polygon of surface ${surface.id} is invalid`) //TODO: anders machen?
    }
}

export function transformationMatrixOfShapeRepresentation(shapeRepresentation: ShapeRepresentation | ShapeRepresentationInput): Matrix4 {
    return transformationMatrixOfArray(shapeRepresentation.transformationMatrix)
}

export function transformationMatrixOfArray(transformationMatrix: number[][]): Matrix4 {
    return new Matrix4().fromArray(transformationMatrix.flat())
}

export function shapeToBoxInput(shape: ShapeRepresentationInput | Shape): Optional<BoxInput> {
    const rawShape = toRaw(shape)
    if ("__typename" in rawShape && rawShape.__typename === "Box") {
        return rawShape
    }
    if ("box" in rawShape) {
        return rawShape.box === undefined ? null : rawShape.box
    }
    return null
}

export function shapeToPolygonInput(shape: ShapeRepresentationInput | Shape): Optional<PolygonInput> {
    const rawShape = toRaw(shape)
    if ("__typename" in rawShape && rawShape.__typename === "Polygon") {
        return {
            ...rawShape,
            holes: rawShape.holes?.map(hole => ({vertices: hole})) ?? [] //TODO: später optional fallback entfernen
        }
    }
    if ("polygon" in rawShape) {
        return rawShape.polygon === undefined ? null : rawShape.polygon
    }
    return null
}

export function shapeToRingInput(shape: ShapeRepresentationInput | Shape): Optional<RingInput> {
    const rawShape = toRaw(shape)
    if ("__typename" in rawShape && rawShape.__typename === "Ring") {
        return rawShape
    }
    if ("ring" in rawShape) {
        return rawShape.ring === undefined ? null : rawShape.ring
    }
    return null
}

// noinspection FunctionNamingConventionJS
function boxToBufferedGeometry_000(
    box: BoxInput,
    useSegmentation: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number,
): BufferGeometry {
    const width = Math.max(fallbackSize, box.width);
    const height = forcedHeight === undefined ? Math.max(fallbackSize, box.height) : forcedHeight
    const depth = forcedThickness === undefined ? Math.max(fallbackSize, box.depth) : forcedThickness;

    const widthSegments = useSegmentation ? sizeSegmentation(width) : 1;
    const heightSegments = useSegmentation ? sizeSegmentation(height) : 1;
    const depthSegments = useSegmentation ? sizeSegmentation(depth) : 1;

    const geometry = new BoxGeometry(
        width,
        height,
        depth,
        widthSegments,
        heightSegments,
        depthSegments
    )

    geometry.computeBoundingBox()
    geometry.computeBoundingSphere()
    geometry.computeVertexNormals()

    return geometry
}

// noinspection FunctionNamingConventionJS
function polygonToBufferedGeometry_000(
    shape: ShapeRepresentationInput | Shape,
    polygon: PolygonInput,
    useSegmentation: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number //TODO: <<<<<<<<<<<<<<<<<<<<< not supported yet
): BufferGeometry {
    if (isExtrudable3D(polygon)) {
        return polygon3DToBufferedGeometry_000(
            shape,
            polygon,
            useSegmentation,
            fallbackSize,
            forcedThickness,
            forcedHeight,
        )
    }
    return polygon2DToBufferedGeometry_000(
        shape,
        polygon,
        useSegmentation,
        fallbackSize,
        forcedThickness,
        forcedHeight,
    )
}

// noinspection FunctionNamingConventionJS
function polygon2DToBufferedGeometry_000(
    shape: ShapeRepresentationInput | Shape,
    polygon: PolygonInput,
    useSegmentation: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number //TODO: <<<<<<<<<<<<<<<<<<<<< not supported yet
): BufferGeometry {
    const extrusion = forcedThickness === undefined ? Math.max(fallbackSize, polygon.extrusion) : forcedThickness
    const extrusionSegments = useSegmentation ? sizeSegmentation(extrusion) : 1;

    //TODO: POLYGONE FEINER UNTERTEILEN, WENN SEGMENTATION AKTIV IST <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    const [threeShape] = shapeToThreeShape(shape, fallbackSize, forcedThickness)

    // noinspection LocalVariableNamingConventionJS
    const geometry_000 = new ExtrudeGeometry(threeShape, {
        depth: extrusion,
        bevelEnabled: false,
        steps: extrusionSegments,
    })

    geometry_000.computeBoundingBox()
    geometry_000.computeBoundingSphere()
    geometry_000.computeVertexNormals()

    if (polygon.holes === undefined || polygon.holes === null || polygon.holes.length <= 0) {
        return geometry_000
    }

    // noinspection LocalVariableNamingConventionJS
    const mesh_000_GM = new Mesh(geometry_000)

    // noinspection LocalVariableNamingConventionJS
    const holeMeshes_000_GM = polygon.holes.map(hole => {
        const polygonVectors = hole.vertices.map(vector2DToVector2)
        const threeShape = new THREE.Shape(polygonVectors.length <= 0 ? undefined : polygonVectors)

        const geometry = new ExtrudeGeometry(threeShape, {
            depth: extrusion,
            bevelEnabled: false,
            steps: extrusionSegments,
        })

        geometry.computeBoundingBox()
        geometry.computeBoundingSphere()
        geometry.computeVertexNormals()

        return new Mesh(geometry)
    })

    // noinspection LocalVariableNamingConventionJS
    const geometryWithHoles_000 = csgGenerateGeometryWithHoles_000(mesh_000_GM, holeMeshes_000_GM)

    tDestroyMesh(mesh_000_GM, true, true)
    holeMeshes_000_GM.forEach(holeMesh => tDestroyMesh(holeMesh, true, true))

    return geometryWithHoles_000
}

// noinspection FunctionNamingConventionJS
function polygon3DToBufferedGeometry_000(
    shape: ShapeRepresentationInput | Shape,
    polygon: PolygonInput,
    useSegmentation: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number //TODO: <<<<<<<<<<<<<<<<<<<<< not supported yet
): BufferGeometry {
    const vertices = polygon.vertices.map(v => new Vector3(v.x, v.y, v.z))

    // noinspection LocalVariableNamingConventionJS
    const geometry_000 = tPolygon3DToBufferGeometry_000(vertices)

    geometry_000.computeBoundingBox()
    geometry_000.computeBoundingSphere()
    //geometry_000.computeVertexNormals() //is already part of tPolygon3DToBufferGeometry_000

    return geometry_000
}

// noinspection FunctionNamingConventionJS
function ringToBufferedGeometry_000(
    shape: ShapeRepresentationInput | Shape,
    ring: RingInput,
    useSegmentation: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number
): BufferGeometry {
    const height = forcedHeight === undefined ? Math.max(fallbackSize, ring.extrusion) : forcedHeight
    const heightSegments = useSegmentation ? sizeSegmentation(height) : 1;
    const [threeShape, curveSegments] = shapeToThreeShape(shape, fallbackSize, forcedThickness)

    const geometry = new ExtrudeGeometry(threeShape, {
        depth: height,
        bevelEnabled: false,
        curveSegments, //diese segmentierung sollte immer genutzt werden
        steps: heightSegments
    })

    geometry.computeBoundingBox()
    geometry.computeBoundingSphere()
    geometry.computeVertexNormals()

    return geometry
}

// noinspection OverlyComplexFunctionJS,FunctionTooLongJS,FunctionNamingConventionJS
export function shapeToBufferedGeometry_000(
    shape: ShapeRepresentationInput | Shape,
    useSegmentation: boolean,
    polygonToBox: boolean,
    fallbackSize: number = 0,
    forcedThickness?: number,
    forcedHeight?: number,
): BufferGeometry {
    //BOX
    const box = shapeToBoxInput(shape)
    if (box) {
        return boxToBufferedGeometry_000(
            box,
            useSegmentation,
            fallbackSize,
            forcedThickness,
            forcedHeight,
        )
    }

    //POLYGON
    const polygon = shapeToPolygonInput(shape)
    if (polygon) {
        if (polygonToBox) {
            const xs = polygon.vertices.map(v => v.x)
            const xMin = xs.length <= 0 ? 0 : Math.min(...xs)
            const xMax = xs.length <= 0 ? 0 : Math.max(...xs)
            const width = xMax - xMin

            const ys = polygon.vertices.map(v => v.y)
            const yMin = ys.length <= 0 ? 0 : Math.min(...ys)
            const yMax = ys.length <= 0 ? 0 : Math.max(...ys)
            const height = yMax - yMin

            const box: BoxInput = {
                width,
                height,
                depth: polygon.extrusion
            }
            return boxToBufferedGeometry_000(
                box,
                useSegmentation,
                fallbackSize,
                forcedThickness,
                forcedHeight,
            )
        }

        return polygonToBufferedGeometry_000(
            shape,
            polygon,
            useSegmentation,
            fallbackSize,
            forcedThickness,
            forcedHeight,
        )
    }

    //RING
    const ring = shapeToRingInput(shape)
    if (ring) {
        return ringToBufferedGeometry_000(
            shape,
            ring,
            useSegmentation,
            fallbackSize,
            forcedThickness,
            forcedHeight,
        )
    }

    throw new Error(`Unsupported shape: ${JSON.stringify(shape)}`)
}

function sizeSegmentation(sizeInMeter: number): number {
    return Math.ceil(sizeInMeter / 0.1) //0.1m = 10cm
}

/**
 * @param radiusInMeter
 * @param thicknessInMeter
 * @param thetaAngle in degrees
 */
export function ringSizeSegments(radiusInMeter: number, thicknessInMeter: number, thetaAngle: number): number {
    const outerRadiusInMeter = radiusInMeter + thicknessInMeter / 2
    const thetaAngleInRadians = MathUtils.degToRad(thetaAngle)
    const thetaLengthInMeter = outerRadiusInMeter * thetaAngleInRadians

    return sizeSegmentation(thetaLengthInMeter)
}

// noinspection FunctionTooLongJS
/**
 * @return [threeShape, curveSegments]
 */
export function shapeToThreeShape(shape: ShapeRepresentationInput | Shape, fallbackSize: number = 0, forcedThickness?: number): [THREE.Shape, number] {
    //BOX
    const box = shapeToBoxInput(shape)
    if (box) {
        const width = Math.max(fallbackSize, box.width);
        const height = Math.max(fallbackSize, box.height);

        const threeShape = new THREE.Shape()
        threeShape.moveTo(0, 0)
        threeShape.lineTo(width, 0)
        threeShape.lineTo(width, height)
        threeShape.lineTo(0, height)
        threeShape.lineTo(0, 0)

        return [threeShape, -1]
    }

    //POLYGON
    const polygon = shapeToPolygonInput(shape)
    if (polygon) {
        const polygonVectors = polygon.vertices.map(vector2DToVector2)
        const threeShape = new THREE.Shape(polygonVectors.length <= 0 ? undefined : polygonVectors)

        return [threeShape, -1]
    }

    //RING
    const ring = shapeToRingInput(shape)
    if (ring) {
        const size = forcedThickness === undefined ? Math.max(fallbackSize, ring.size) : forcedThickness

        const startAngle = ring.startAngle //in degrees
        const endAngle = ring.endAngle //in degrees

        const thetaAngle = endAngle - startAngle //in degrees

        const thetaStart = MathUtils.degToRad(startAngle) //in radians
        const thetaLength = MathUtils.degToRad(thetaAngle)  //in radians

        const radius = ring.radius
        const innerRadius = radius - size / 2
        const outerRadius = radius + size / 2

        const threeShape = new THREE.Shape()
        threeShape.absarc(0, 0, innerRadius, thetaStart, thetaStart + thetaLength, false);
        threeShape.absarc(0, 0, outerRadius, thetaStart + thetaLength, thetaStart, true);

        const curveSegments = ringSizeSegments(radius, size, thetaAngle);
        return [threeShape, curveSegments]
    }

    throw new Error(`Unsupported shape: ${JSON.stringify(shape)}`)
}

// noinspection FunctionTooLongJS
/**
 * @return [shapeRepresentation, roomTransformation]
 */
export function createRoomShapeRepresentation(scannedRoom: ListingBuildingScanRoom | ListingBuildingScanRoomInput): Optional<[EnsureDefined<ShapeRepresentationInput>, Matrix4]> {
    const floorSurfaces = scannedRoom.surfaces.filter(surface => surface.category === 'FLOOR')

    if (floorSurfaces.length <= 0) {
        console.warn(`No floor surface found for room ${scannedRoom.id}`)
        return null
    }

    const firstFloorSurface = floorSurfaces[0]
    const walls = scannedRoom.surfaces.filter(s => s.category === "WALL");
    const ceilingHeight = walls
        .map(s => s.boundingBox[1])
        .reduce((a, b) => Math.max(a, b), 0)

    const firstFloorShapeRepresentation = createShapeRepresentationFromSurface(firstFloorSurface, true)

    let center = new Vector2()

    if (firstFloorShapeRepresentation.box) {
        firstFloorShapeRepresentation.box.height = ceilingHeight
    } else if (firstFloorShapeRepresentation.polygon) {
        const rawVertices2D = firstFloorShapeRepresentation.polygon.vertices.map(v => new Vector2(v.x, -v.y))
        center = tVectors2ToCentralPoint(rawVertices2D, [], BUILDING_EPSILON) ?? tCenterOfVectors2(rawVertices2D)

        const rotationMatrix = new Matrix4().makeRotationY(Math.PI)
        const scaleMatrix = new Matrix4().makeScale(-1, 1, 1) // "y" is inverted
        const vertexTransformation = rotationMatrix.multiply(scaleMatrix)

        const vertices2D = rawVertices2D.map(v => v.clone().sub(center))
        const vertices3D = vertices2D.map(v => new Vector3(v.x, 0, v.y))
        const transformedVertices3D = vertices3D.map(v => v.applyMatrix4(vertexTransformation))
        const transformedVertices2D = transformedVertices3D.map(v => new Vector2(v.x, v.z))

        firstFloorShapeRepresentation.polygon.extrusion = ceilingHeight
        firstFloorShapeRepresentation.polygon.vertices = transformedVertices2D.map(v => ({x: v.x, y: v.y, z: 0}))
    } else if (firstFloorShapeRepresentation.ring) {
        firstFloorShapeRepresentation.ring.extrusion = ceilingHeight
    }

    const floorSurfaceTransformationMatrix = transformationMatrixOfSurface(firstFloorSurface)
    const centerTranslationMatrix = new Matrix4().makeTranslation(center.x, ceilingHeight / 2, center.y)
    const roomTransformationMatrix = floorSurfaceTransformationMatrix.clone().multiply(centerTranslationMatrix)
    const roomTransformationMatrixArray = matrix4ToTransformationMatrixArray(roomTransformationMatrix)

    const roomShapeRepresentation: EnsureDefined<ShapeRepresentationInput> = {
        ...firstFloorShapeRepresentation,
        transformationMatrix: roomTransformationMatrixArray
    }

    return [roomShapeRepresentation, roomTransformationMatrix]
}