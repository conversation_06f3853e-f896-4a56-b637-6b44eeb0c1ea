import {BuildingInput, ListingBuildingScan} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {BuildingCreationPipelineStageId} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStageId";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";

export abstract class BuildingCreationPipelineStage {
    protected constructor(public readonly id: BuildingCreationPipelineStageId) {
    }

    abstract process(buildingScan: ListingBuildingScan, buildingInput: Optional<EnsureDefined<BuildingInput>>): Promise<EnsureDefined<BuildingInput>>
}