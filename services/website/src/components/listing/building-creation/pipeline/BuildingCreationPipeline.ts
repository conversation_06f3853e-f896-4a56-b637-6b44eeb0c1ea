import {BuildingCreationPipelineStage} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStage";
import {BuildingInput, ListingBuildingScan} from "@/adapter/graphql/generated/graphql";
import {BuildingCreationPipelineStageId} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStageId";
import {Optional} from "@/model/Optional";
import {CreateBuildingBCPipelineStage} from "@/components/listing/building-creation/pipeline/stages/1_CreateBuildingAndPolygonizeCurvesBCPipelineStage";
import {ConvertOpeningTransformationsBCPipelineStage} from "@/components/listing/building-creation/pipeline/stages/2_ConvertOpeningTransformationsBCPipelineStage";
import {CreateRoomsBCPipelineStage} from "@/components/listing/building-creation/pipeline/stages/3_CreateRoomsBCPipelineStage";
import {CreateFloorShapesBCPipelineStage} from "@/components/listing/building-creation/pipeline/stages/4_CreateFloorShapesBCPipelineStage";
import {CreateBuildingShapeBCPipelineStage} from "@/components/listing/building-creation/pipeline/stages/5_CreateBuildingShapeBCPipelineStage";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";

export const BuildingCreationPipeline = new class {
    private stages: readonly BuildingCreationPipelineStage[] = [
        CreateBuildingBCPipelineStage,
        ConvertOpeningTransformationsBCPipelineStage,
        CreateRoomsBCPipelineStage,
        CreateFloorShapesBCPipelineStage,
        CreateBuildingShapeBCPipelineStage,
    ]

    async process(buildingScan: ListingBuildingScan, stageIds: readonly BuildingCreationPipelineStageId[] = this.stages.map(s => s.id)): Promise<Optional<EnsureDefined<BuildingInput>>> {
        let building: Optional<EnsureDefined<BuildingInput>> = null

        for (const stage of this.stages) {
            if (!stageIds.includes(stage.id)) {
                console.log(`Skipping stage ${stage.id}`)
                continue
            }
            console.log(`Processing stage ${stage.id}`)
            try {
                building = await stage.process(buildingScan, building)
            } catch (e) {
                console.error(e)
                return null
            }
        }

        return building
    }
}