query dListingBuildingCreationPlaygroundLoadListing(
    $id: String!
) {
    listing(
        id: $id
    ) {
        id
        flowConfigId
        building {
            ...allOfBuilding
        }
    }
}

query dListingBuildingCreationPlaygroundLoadFlowConfig(
    $id: String!
) {
    flowConfig(
        id: $id
    ) {
        ...allOfFlowConfig
    }
}

query dListingBuildingCreationPlaygroundLoadBuildingScan(
    $listingId: String!
) {
    listingBuildingScan(
        listingId: $listingId
    ) {
        ...allOfListingBuildingScan
    }
}

mutation dListingBuildingCreationPlaygroundDeleteBuilding(
    $listingId: String!
) {
    deleteListingBuilding(
        listingId: $listingId
    )
}

mutation dListingBuildingCreationPlaygroundDeleteBuildingScan(
    $listingId: String!
) {
    deleteListingBuildingScan(
        listingId: $listingId
    )
}

mutation dListingBuildingCreationPlaygroundSaveBuilding(
    $listingBuildingData: ListingBuildingDataInput!
) {
    saveListingBuilding(
        listingBuildingData: $listingBuildingData
    ) {
        ...allOfBuilding
    }
}

mutation dListingBuildingCreationPlaygroundSaveBuildingScan(
    $listingBuildingScanData: ListingBuildingScanDataInput!
) {
    saveListingBuildingScan(
        listingBuildingScanData: $listingBuildingScanData
    )
}