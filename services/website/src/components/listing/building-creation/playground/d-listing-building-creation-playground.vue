<template>
    <v-container class="h-100"
                 fluid>
        <v-row justify="center">
            <v-col cols="3">
                <d-card class="pa-4">
                    <d-text-field v-model="listingId"
                                  label="Listing ID"/>
                </d-card>
            </v-col>
        </v-row>

        <v-row>
            <v-col class="text-center"
                   cols="6">
                <d-h1>BUILDING SCAN</d-h1>
            </v-col>
            <v-col class="text-center"
                   cols="6">
                <d-h1>BUILDING</d-h1>
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="6">
                <d-card>
                    <d-toolbar class="px-3">
                        <d-btn :loading="buildingScanLoading"
                               type="secondary"
                               @click="refreshBuildingScan">Refresh: {{ buildingScanRenderKey }}
                        </d-btn>

                        <v-spacer/>

                        <d-confirmation-wrapper @onconfirm="onCompleteBuildingScan">
                            <template #default="{props}">
                                <d-btn :loading="saveBuildingScanLoading"
                                       class="me-4"
                                       type="tertiary"
                                       v-bind="props">Toggle completion
                                </d-btn>
                            </template>
                        </d-confirmation-wrapper>

                        <d-chip v-if="buildingScan"
                                :type="buildingScan.isComplete ? 'success' : 'warning'">{{ buildingScan.isComplete ? 'completed' : 'not completed' }}
                        </d-chip>

                        <d-confirmation-wrapper v-if="buildingScan"
                                                @onconfirm="onDeleteBuildingScan">
                            <template #default="{props}">
                                <d-btn :icon="mdiDelete"
                                       :loading="deleteBuildingScanLoading"
                                       type="default"
                                       v-bind="props"
                                       variant="text"/>
                            </template>
                        </d-confirmation-wrapper>
                    </d-toolbar>
                </d-card>
            </v-col>

            <v-col cols="6">
                <d-card>
                    <d-toolbar class="px-3">
                        <d-text-field v-model="customUIElementId"
                                      label="Custom UI Element ID"/>

                        <v-spacer/>

                        <d-btn :loading="listingLoading"
                               type="secondary"
                               @click="refreshBuilding">Refresh: {{ buildingRenderKey }}
                        </d-btn>

                        <d-confirmation-wrapper v-if="building"
                                                @onconfirm="onDeleteBuilding">
                            <template #default="{props}">
                                <d-btn :icon="mdiDelete"
                                       :loading="deleteBuildingLoading"
                                       type="default"
                                       v-bind="props"
                                       variant="text"/>
                            </template>
                        </d-confirmation-wrapper>
                    </d-toolbar>
                </d-card>
            </v-col>
        </v-row>

        <v-row class="renderers">
            <v-col cols="6">
                <d-listing-building-scan-renderer-default v-if="buildingScan"
                                                          :key="buildingScanRenderKey"
                                                          :show-signature="false"
                                                          debug
                                                          initialize-with3d
                                                          is-embedded
                                                          show-full-screen-button/>
            </v-col>
            <v-col cols="6">
                <d-listing-building v-if="building && flowConfig"
                                    :key="buildingRenderKey"
                                    :custom-ui-element-id="customUIElementId ?? ''"
                                    :show-signature="false"
                                    context="VIEW"
                                    initialize-with3d
                                    is-embedded
                                    show-full-screen-button/>
            </v-col>
        </v-row>

        <v-row>
            <v-col class="log"
                   cols="6">
                <pre>
                    {{ buildingScan }}
                </pre>
            </v-col>

            <v-col class="log"
                   cols="6">
                <pre>
                    {{ building }}
                </pre>
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="12">
                <d-card>
                    <v-list v-model:selected="selectedPipelineStageIds"
                            class="bg-transparent"
                            lines="one"
                            select-strategy="classic">
                        <v-list-item v-for="(pipelineStageId, index) in BuildingCreationPipelineStageIdValues"
                                     :key="index"
                                     :value="pipelineStageId"
                                     density="compact">
                            <template #prepend="{ isActive }">
                                <v-list-item-action start>
                                    <v-checkbox-btn :model-value="isActive"
                                                    density="compact"/>
                                </v-list-item-action>
                            </template>

                            <v-list-item-title>{{ pipelineStageId }}</v-list-item-title>
                        </v-list-item>
                    </v-list>

                    <template #actions>
                        <v-layout class="justify-space-between align-center pa-2 pt-0">
                            <d-btn disabled
                                   type="tertiary">! Create buildings for all listings !
                            </d-btn>

                            <d-btn :disabled="buildingScan?.isComplete !== true"
                                   :loading="isBuildingCreationLoading || saveBuildingLoading"
                                   size="large"
                                   type="primary"
                                   @click="onCreateBuilding">Create building
                            </d-btn>
                        </v-layout>
                    </template>
                </d-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import DListingBuilding from "@/components/listing/building/d-listing-building.vue";
    import {LFlowDataInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {computed, provide, ref, shallowRef, toRaw, toRef, watch} from "vue";
    import {createEmptyMutableFlowData, MutableFlowData} from "@/model/listing/FlowData";
    import {Building, DListingBuildingCreationPlaygroundLoadBuildingScanQueryVariables, DListingBuildingCreationPlaygroundLoadFlowConfigQueryVariables, DListingBuildingCreationPlaygroundLoadListingQueryVariables, FlowConfig, Listing, ListingBuildingScan, useDListingBuildingCreationPlaygroundDeleteBuildingMutation, useDListingBuildingCreationPlaygroundDeleteBuildingScanMutation, useDListingBuildingCreationPlaygroundLoadBuildingScanQuery, useDListingBuildingCreationPlaygroundLoadFlowConfigQuery, useDListingBuildingCreationPlaygroundLoadListingQuery, useDListingBuildingCreationPlaygroundSaveBuildingMutation, useDListingBuildingCreationPlaygroundSaveBuildingScanMutation} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import DH1 from "@/adapter/vuetify/theme/components/text/headline/d-h1.vue";
    import DToolbar from "@/adapter/vuetify/theme/components/d-toolbar.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import {mdiDelete} from "@mdi/js";
    import DChip from "@/adapter/vuetify/theme/components/chip/d-chip.vue";
    import {BuildingCreationPipeline} from "@/components/listing/building-creation/pipeline/BuildingCreationPipeline";
    import {BuildingCreationPipelineStageId, BuildingCreationPipelineStageIdValues} from "@/components/listing/building-creation/pipeline/BuildingCreationPipelineStageId";
    import DListingBuildingScanRendererDefault from "@/components/listing/building-scan/renderer/default/d-listing-building-scan-renderer-default.vue";
    import {FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {buildingInputToBuilding} from "@/adapter/graphql/mapper/buildinginput-to-building-mapper";
    import {createMutableBuildingFrom} from "@/components/listing/building/building";
    import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
    import {BuildingRendererEmpty} from "@/components/listing/building/renderer/BuildingRendererEmpty";
    import {createFlowConfigField} from "@/service/flow-config/flow-config-service";
    import {deepCopyListingBuildingScanInput} from "@/adapter/graphql/mapper/buildingscaninput-to-buildingscaninput-mapper";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";

    const listingId = shallowRef<Optional<string>>("49677210891")//12023757185, "0a158efe-150b-4429-986f-9d0ad3368248")//"94941116272")
    const customUIElementId = shallowRef<Optional<string>>("63968487-fcb7-49d8-964c-a908a7f9edb1")

    const flowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay
    const buildingRenderKey = shallowRef<number>(0)
    const buildingScanRenderKey = shallowRef<number>(0)

    provide(LListingIdInjection, toRef(() => listingId.value ?? ''))
    provide(LFlowDataInjection, flowData)

    const selectedPipelineStageIds = shallowRef<readonly BuildingCreationPipelineStageId[]>(BuildingCreationPipelineStageIdValues)
    const listingQueryVariables = computed<DListingBuildingCreationPlaygroundLoadListingQueryVariables>(() => ({
        id: listingId.value ?? ""
    }))
    const {
        result: listingResult,
        restart: restartListingQuery,
        loading: listingLoading,
        error: listingQueryError,
    } = useDListingBuildingCreationPlaygroundLoadListingQuery(listingQueryVariables)
    const listing = computed<Optional<Listing>>(() => listingResult.value?.listing as (Listing | undefined) ?? null)

    const flowConfigQueryVariables = computed<DListingBuildingCreationPlaygroundLoadFlowConfigQueryVariables>(() => ({
        id: listing.value?.flowConfigId ?? ""
    }))
    const {
        result: flowConfigResult,
        //loading: areFlowConfigsLoading, //TODO: loading
        //TODO: error
    } = useDListingBuildingCreationPlaygroundLoadFlowConfigQuery(flowConfigQueryVariables)

    const rawFlowConfig = computed<Optional<FlowConfig>>(() => flowConfigResult.value?.flowConfig as (FlowConfig | undefined) ?? null)
    const flowConfig = createFlowConfigField(toRef(() => listing.value?.flowConfigId ?? null), rawFlowConfig)

    provide(FCFlowConfigInjection, toRef(() => flowConfig.value!))

    const buildingScanQueryVariables = computed<DListingBuildingCreationPlaygroundLoadBuildingScanQueryVariables>(() => ({
        listingId: listingId.value ?? ""
    }))
    const {
        result: buildingScanQueryResult,
        error: buildingScanQueryError,
        loading: buildingScanLoading,
        restart: restartBuildingScanQuery
    } = useDListingBuildingCreationPlaygroundLoadBuildingScanQuery(buildingScanQueryVariables)

    const building = computed<Optional<Building>>(() => listingQueryError.value == null ? (listing.value?.building as Building ?? null) : null)
    const buildingScan = computed<Optional<ListingBuildingScan>>(() => buildingScanQueryError.value == null ? (buildingScanQueryResult.value?.listingBuildingScan as ListingBuildingScan ?? null) : null)

    const {
        mutate: deleteBuilding,
        loading: deleteBuildingLoading
    } = useDListingBuildingCreationPlaygroundDeleteBuildingMutation()
    const {
        mutate: deleteBuildingScan,
        loading: deleteBuildingScanLoading
    } = useDListingBuildingCreationPlaygroundDeleteBuildingScanMutation()
    const {
        mutate: saveBuilding,
        loading: saveBuildingLoading
    } = useDListingBuildingCreationPlaygroundSaveBuildingMutation()
    const {
        mutate: saveBuildingScan,
        loading: saveBuildingScanLoading
    } = useDListingBuildingCreationPlaygroundSaveBuildingScanMutation()

    function refreshBuilding() {
        restartListingQuery()
    }

    function refreshBuildingScan() {
        restartBuildingScanQuery()
    }

    async function onDeleteBuilding() {
        const id = listingId.value
        if (id === null || id === '') {
            return
        }
        await deleteBuilding({
            listingId: id
        }, {
            update: (cache, result) => {
                if (result.data?.deleteListingBuilding === true) {
                    cache.modify({
                        id: cache.identify({__typename: "Listing", id}),
                        fields: {
                            building(): Optional<EnsureDefined<Building>> {
                                return null
                            }
                        }
                    })
                }
            }
        });
        refreshBuilding()
    }

    async function onDeleteBuildingScan() {
        const id = listingId.value
        if (id === null || id === '') {
            return
        }
        await deleteBuildingScan({
            listingId: id
        })
        refreshBuildingScan()
    }

    async function onCompleteBuildingScan() {
        const id = listingId.value
        if (id === null || id === '') {
            return
        }

        const newBuildingScan = deepCopyListingBuildingScanInput(buildingScan.value!)
        const newCompletionState = !newBuildingScan.isComplete
        newBuildingScan.isComplete = newCompletionState

        const result = await saveBuildingScan({
            listingBuildingScanData: {
                listingId: id,
                buildingScan: newBuildingScan
            }
        }, {
            update: (cache, result) => {
                if (result.data?.saveListingBuildingScan === true) {
                    cache.modify({
                        id: cache.identify({__typename: "ListingBuildingScan", id: newBuildingScan.id}),
                        fields: {
                            isComplete(): boolean {
                                return newCompletionState
                            }
                        }
                    })
                }
            }
        })

        if (result?.data?.saveListingBuildingScan !== true) {
            console.error("Building save failed")
            return
        }

        refreshBuildingScan()
    }

    watch([listing, customUIElementId], () => {
        ++buildingRenderKey.value
    })
    watch(buildingScanQueryResult, () => {
        ++buildingScanRenderKey.value
    })

    const isBuildingCreationLoading = shallowRef<boolean>(false)

    async function createBuilding() {
        console.log("Creating building …")

        const bScan = toRaw(buildingScan.value!)
        const stageIds = toRaw(selectedPipelineStageIds.value)

        const newBuilding = await BuildingCreationPipeline.process(bScan, stageIds)

        if (newBuilding === null) {
            console.error("Building creation failed")
            return
        }

        const lId = listingId.value ?? ""

        try {
            const result = await saveBuilding({
                listingBuildingData: {
                    listingId: lId,
                    building: newBuilding
                }
            }, {
                update(cache, response) {
                    const building = response.data?.saveListingBuilding as (undefined | EnsureDefined<Building>) ?? buildingInputToBuilding(newBuilding, false)
                    cache.modify({
                        id: cache.identify({
                            __typename: "Listing",
                            id: lId
                        }),
                        fields: {
                            building(): Optional<EnsureDefined<Building>> {
                                return building
                            },
                            hasBuilding(): boolean {
                                return true
                            },
                        }
                    });
                }
            });

            const saveListingBuilding = result?.data?.saveListingBuilding as undefined | EnsureDefined<Building>
            if (saveListingBuilding === undefined) {
                console.warn("Building save failed")
            }

            const savedBuilding = saveListingBuilding ?? buildingInputToBuilding(newBuilding, false)

            const mutableBuilding = createMutableBuildingFrom(savedBuilding)
            const emptyRenderer = new BuildingRendererEmpty(lId, toRef(() => mutableBuilding))

            await BuildingPipelineBuilder
                .create("PostBuildingCreationPipeline", emptyRenderer)
                .doAll(false) //currently false, deskewing floors makes more broken
                .build()
                .execute()

            emptyRenderer.destroy()

            refreshBuilding()
        } catch (e) {
            console.warn("Error when saving building.", e)
        }
    }

    async function onCreateBuilding() {
        isBuildingCreationLoading.value = true
        try {
            await createBuilding()
        } finally {
            isBuildingCreationLoading.value = false
        }
    }
</script>

<style scoped>
    .renderers {
        height: 400px;
    }

    .log {
        height: 250px;
        overflow-y: auto;
        background-color: black;
        color: white;
    }
</style>