query dListingLoadListing (
    $id: String!
) {
    listing(
        id: $id
    ) {
        id
        isEditable
        hasMainImage #für visibility checks
        fieldValues {
            ...allOfFlowConfigFieldValue
        }
        hasBuildingScan
        hasBuilding
        isBuildingScanComplete
        buildingScanId
        building {
            ...allOfBuilding
        }
        flowConfigId
        images {
            ...allOfListingImage
        }
    }
}

query dListingLoadFlowConfig (
    $id: String!
) {
    flowConfig(
        id: $id
    ) {
        ...allOfFlowConfig
    }
}