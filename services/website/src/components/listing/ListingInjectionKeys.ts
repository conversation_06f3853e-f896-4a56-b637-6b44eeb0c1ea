import {InjectionKey, Ref} from "vue";
import {FlowConfigPage, Listing} from "@/adapter/graphql/generated/graphql";
import {FlowData, MutableFlowData} from "@/model/listing/FlowData";
import {ListingContext} from "@/model/listing/ListingContext";
import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
import {Optional} from "@/model/Optional";

export const LListingInjection: InjectionKey<Readonly<Ref<Listing>>> = Symbol('lListing')
export const LListingIdInjection: InjectionKey<Readonly<Ref<string>>> = Symbol('lListingId')
export const LMutableFlowDataInjection: InjectionKey<Ref<MutableFlowData>> = Symbol('lMutableFlowData')
export const LFlowDataInjection: InjectionKey<Readonly<Ref<FlowData>>> = Symbol('lFlowData')
export const LSelectedPageInjection: InjectionKey<Readonly<Ref<Optional<FlowConfigPage>>>> = Symbol('lSelectedPage')
export const LSuggestionsInjection: InjectionKey<Readonly<Ref<FlowData>>> = Symbol('lSuggestions')
export const LListingContextInjection: InjectionKey<Readonly<Ref<ListingContext>>> = Symbol('lListingContext')
export const LListingEnvironmentInjection: InjectionKey<Readonly<Ref<ListingEnvironment>>> = Symbol('lListingEnvironment')
export const LArrayIndexInjection: InjectionKey<Readonly<Ref<Optional<number>>>> = Symbol('lArrayIndex')
