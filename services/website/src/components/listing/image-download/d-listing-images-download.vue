<template>
    <div class="sessionStatusWrapper pa-4">
        <div class="sessionStatus">
            <d-progress-circular v-if="isLoading"
                                 :size="mdAndUp ? 100 : 75"
                                 class="mb-10"
                                 indeterminate
                                 width="6"/>
            <d-h2 v-if="mdAndUp">
                {{ t('components.listing.imagesDownload.yourDownloadWillStartShortly') }}
            </d-h2>
            <d-h5 v-else>
                {{ t('components.listing.imagesDownload.yourDownloadWillStartShortly') }}
            </d-h5>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {useDListingImagesDownloadMutation} from "@/adapter/graphql/generated/graphql";
    import {inject, onMounted, shallowRef} from "vue";
    import {useRouter} from "vue-router";
    import {useI18n} from "vue-i18n";
    import DProgressCircular from "@/adapter/vuetify/theme/components/progress/d-progress-circular.vue";
    import DH5 from "@/adapter/vuetify/theme/components/text/headline/d-h5.vue";
    import DH2 from "@/adapter/vuetify/theme/components/text/headline/d-h2.vue";
    import {useDisplay} from "vuetify";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const listingId = inject(LListingIdInjection)!

    const router = useRouter()
    const {t} = useI18n()
    const {mdAndUp} = useDisplay()
    const isLoading = shallowRef<boolean>(true)

    onMounted(() => {
        useDListingImagesDownloadMutation()
            .mutate({
                listingId: listingId.value
            })
            .then((result) => {
                const url = result?.data?.generateListingImageZipArchiveUrl

                if (url) {
                    const filename = url.substring(url.lastIndexOf('/') + 1)
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', filename);
                    document.body.appendChild(link);
                    link.click();

                    setTimeout(() => {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        router.back()
                    }, 100);
                }
            })
            .finally(() => {
                isLoading.value = false
            })
    })
</script>

<style scoped>
    .sessionStatusWrapper {
        display: flex;
        width: 100%;
        height: 100%;
        justify-items: center;
        align-items: center;
        flex-direction: row;
    }

    .sessionStatus {
        flex: 1 1 auto;
        text-align: center;
    }
</style>