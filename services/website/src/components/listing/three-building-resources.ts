import {T_FONT_LOADER, T_LOADING_MANAGER, T_OBJ_LOADER} from "@/adapter/three/three-utility";
import {shallowRef} from "vue";
import fontURL from "@/assets/3d/font-roboto-regular.json?url";
import {Optional} from "@/model/Optional";
import {Mesh, MeshBasicMaterial, Object3D} from "three";
import iPhoneObj from "@/assets/3d/iphone.obj?url";
import {Font} from "three/addons/loaders/FontLoader.js";

export const THREE_BUILDING_RESOURCES_PROGRESS = shallowRef<number>(0)
export const THREE_BUILDING_RESOURCES_IS_LOADING = shallowRef<boolean>(false)

T_LOADING_MANAGER.onLoad = () => {
    THREE_BUILDING_RESOURCES_IS_LOADING.value = true
}

T_LOADING_MANAGER.onProgress = (url, loaded, total) => {
    THREE_BUILDING_RESOURCES_PROGRESS.value = loaded / total
}

T_LOADING_MANAGER.onError = (url) => {
    console.warn(`Failed to load ${url}`);
}

export const THREE_BUILDING_FONT = shallowRef<Optional<Font>>(null)
export const THREE_BUILDING_IPHONE = shallowRef<Optional<Object3D>>(null)

const IPHONE_MATERIAL = new MeshBasicMaterial({
    color: 0x000000,
})

let wasFontLoaded = false
let wasIPhoneLoaded = false

export function loadThreeBuildingFont() {
    if (wasFontLoaded) {
        return
    }
    wasFontLoaded = true

    T_FONT_LOADER.load(fontURL, loadedFont => {
        THREE_BUILDING_FONT.value = loadedFont
    })
}

export function loadThreeBuildingIPhone() {
    if (wasIPhoneLoaded) {
        return
    }
    wasIPhoneLoaded = true

    T_OBJ_LOADER.load(
        iPhoneObj,
        object => {
            object.traverse(child => {
                if (child instanceof Mesh) {
                    child.material = IPHONE_MATERIAL
                }
            })

            THREE_BUILDING_IPHONE.value = object
        },
    )
}