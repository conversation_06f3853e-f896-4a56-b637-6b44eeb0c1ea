<template>
    <v-tabs v-model="selectedPageId"
            :show-arrows="false"
            :stacked="pages.some(p => p.icon != null)"
            center-active
            class="w-100 h-100"
            fixed-tabs>
        <v-tab v-for="page in pages"
               :key="page.id"
               :rounded="false"
               :value="page.id"
               selected-class="selectedTab">
            <d-icon v-if="page.icon"
                    :icon="flowConfigIconSupplier(page.icon) ?? mdiHelp"
                    :size="24"
                    type="default"/>

            <div class="text-subtitle-2">
                {{ flowConfigTranslator(page.titleShort) }}
            </div>
        </v-tab>
    </v-tabs>
</template>

<script lang="ts"
        setup>
    import {FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {Optional} from "@/model/Optional";
    import {mdiHelp} from "@mdi/js";

    defineProps<{
        pages: readonly FlowConfigPage[]
    }>()

    const {
        flowConfigTranslator,
        flowConfigIconSupplier
    } = useFlowConfig()

    const selectedPageId = defineModel<Optional<string>>('modelValue', {
        required: true,
    })
</script>

<style scoped>
    .selectedTab {
        background-color: rgb(var(--v-theme-d-background-default));
        color: rgb(var(--v-theme-d-text-title)) !important;
    }

    :deep(button) {
        max-width: unset !important;
    }

    :deep(.v-btn--icon.v-btn--density-default) {
        width: auto;
    }
</style>