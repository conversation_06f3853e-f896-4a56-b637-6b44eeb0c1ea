<template>
    <d-size-wrapper v-slot="{height: wrapperHeight}">
        <div :style="{'height': `${wrapperHeight}px`}"
             class="wrapper">
            <header v-if="$slots.header"
                    class="header">
                <slot name="header"/>
            </header>

            <div class="contentWrapper">
                <main class="content">
                    <d-size-wrapper v-slot="{height: contentHeight}">
                        <!-- disabled => kein swipe support. sorgt bei manchen seiten für probleme -->
                        <v-window v-model="selectedPageId"
                                  :style="{'height': `${contentHeight}px`}"
                                  class="w-100"
                                  disabled>
                            <v-window-item v-for="page in pages"
                                           :key="page.id"
                                           :value="page.id"
                                           class="h-100 w-100">
                                <slot v-if="$slots.page"
                                      :page="page"
                                      name="page"/>
                                <d-fc-page :page="page"/>
                            </v-window-item>
                        </v-window>
                    </d-size-wrapper>

                    <slot/>
                </main>

                <footer v-if="$slots.footer"
                        class="footer">
                    <slot name="footer"/>
                </footer>

                <div class="menu">
                    <v-navigation-drawer v-model="isNavigationDrawerVisible"
                                         absolute
                                         disable-resize-watcher
                                         scrim>
                        <d-list :selected-values="[selectedPageId]">
                            <d-list-item v-for="page in pages"
                                         :key="page.id"
                                         :title="flowConfigTranslator(page.titleShort)"
                                         :value="page.id"
                                         @click="onPageSelected(page)"/>
                        </d-list>
                    </v-navigation-drawer>
                </div>
            </div>
        </div>
    </d-size-wrapper>
</template>

<script lang="ts"
        setup>
    import DSizeWrapper from "@/components/fragment/d-size-wrapper.vue";
    import {FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {serializeFlowConfigPageForRoute, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {watch} from "vue";
    import DFcPage from "@/components/flow-config/page/d-fc-page.vue";
    import {useRoute, useRouter} from "vue-router";
    import {Optional} from "@/model/Optional";

    const props = defineProps<{
        pages: readonly FlowConfigPage[]
    }>()

    const router = useRouter()
    const route = useRoute()

    const isNavigationDrawerVisible = defineModel<boolean>('isNavigationDrawerVisible', {
        required: true
    })

    const {flowConfigTranslator} = useFlowConfig()

    const selectedPageId = defineModel<Optional<string>>('selectedPageIndex', {
        required: true,
    })

    function onPageSelected(page: FlowConfigPage) {
        selectedPageId.value = page.id
    }

    watch(selectedPageId, selectedPageId => {
        isNavigationDrawerVisible.value = false

        if (selectedPageId === null) {
            return
        }

        const pages = props.pages;
        if (pages.length <= 0) {
            return
        }

        const page = pages.find(p => p.id === selectedPageId)
        if (page === undefined) {
            console.warn('Could not find page with id', selectedPageId)
            return
        }

        if (route.hash === "") {
            router.replace({
                hash: `#${serializeFlowConfigPageForRoute(page)}`
            })
        } else {
            router.push({
                hash: `#${serializeFlowConfigPageForRoute(page)}`
            })
        }
    }, {
        immediate: true
    })
</script>

<style scoped>
    .wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .header {
        flex: 1 1 auto;
        width: 100%;
        z-index: 1001;
    }

    .contentWrapper {
        position: relative;
        flex: 1 1 auto;
        width: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .content {
        width: 100%;
        flex: 1 1 auto;
        overflow: hidden;
        position: relative; /*for the default slot and possible absolute positions of children*/
    }

    :deep(.v-window) {
        overflow-y: auto;
    }

    .footer {
        flex: 1 1 auto;
        width: 100%;
        z-index: 1001;
    }

    .menu {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1001;
    }

    .menu :deep(nav) {
        top: 0 !important;
        height: 100% !important;
    }

    .menu :deep(.v-navigation-drawer__scrim) {
        pointer-events: auto;
    }
</style>