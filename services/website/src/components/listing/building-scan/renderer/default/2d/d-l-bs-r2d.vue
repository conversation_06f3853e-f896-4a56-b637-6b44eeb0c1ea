<template>
    <t-renderer-webgl ref="renderer"
                      :needs-image-download="needsImageDownload"
                      :renderer-id="id"
                      :show-signature="showSignature"
                      :show-stats="debug"
                      :signature-position="signaturePosition"
                      :tone-mapping="LinearToneMapping"
                      :tone-mapping-exposure="1">
        <t-scene :background-color="0xe0e2e8">
            <!-- TODO: culling feintunen -->
            <t-orthographic-camera :far="debug ? 200 : 20"
                                   :near="1"
                                   :position="cameraPosition"
                                   :target="cameraTarget"
                                   :zoom="30">
                <t-orbit-controls :disable-rotate="!debug"/>
            </t-orthographic-camera>

            <d-l-b-r-compass v-if="false"
                             :bounding-box="boundingBox"/>

            <t-group v-if="listingBuildingScan !== null"
                     v-model:bounding-box="boundingBox"
                     :transformation="new Matrix4().makeRotationY(MathUtils.degToRad(listingBuildingScan.rotationYCorrectionAngle))"
                     fit-to-camera
                     hide-until-first-fit-to-camera>
                <template v-for="surface in validSurfaces"
                          :key="surface.id">
                    <d-l-bs-r2d-surface
                        :objects="listingBuildingScan?.objects ?? []"
                        :surface="surface"
                        :surfaces="validSurfaces"/>
                </template>

                <template v-for="object in listingBuildingScan.objects"
                          :key="object.id">
                    <!-- TODO: Treppen sind erstmal ausgebaut. Solange die Scan-Genauigkeit weiterhin "schlecht" bleibt, werden stattdessen Icons dargestellt. -->
                    <d-l-bs-r2d-stairs
                        v-if="false && object.category === 'STAIRS'"
                        :object="object"/>
                    <d-l-bs-r2d-object
                        v-else-if="showFurniture && object.category !== 'STAIRS'"
                        :object="object"/>
                </template>

                <template v-for="(stairLocation2D, index) in stairLocations2D"
                          :key="index">
                    <d-l-bs-r2d-stairs-icon :stair-location="stairLocation2D"/>
                </template>

                <template v-for="(room, index) in listingBuildingScan.rooms"
                          :key="room.id">
                    <d-l-bs-r2d-room-text :room="room"
                                          :room-index="index"/>
                </template>
            </t-group>
        </t-scene>
    </t-renderer-webgl>
</template>

<script lang="ts"
        setup>
    import {Box3, LinearToneMapping, MathUtils, Matrix4, Vector3} from "three";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import {computed, inject, onUnmounted, provide, ref, shallowRef, toRef, watch} from "vue";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Optional} from "@/model/Optional";
    import TOrthographicCamera from "@/adapter/three/components/camera/t-orthographic-camera.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_STAIRS_ICON, LBSR2DDeskewAngleInjection} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {StairClusterElement} from "@/components/listing/building-scan/StairClusterElement";
    import {StairCluster} from "@/components/listing/building-scan/StairCluster";
    import {StairLocation} from "@/components/listing/building-scan/StairLocation";
    import {StairLocation2D} from "@/components/listing/building-scan/StairLocation2D";
    import {calculateStairClusterElements_000_G, calculateStairClusters, calculateStairLocations, DBuildingScanShowFurnitureInjection} from "@/components/listing/building-scan/building-scan";
    import DLBsR2dSurface from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-surface.vue";
    import DLBsR2dStairs from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-stairs.vue";
    import DLBsR2dObject from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-object.vue";
    import DLBsR2dStairsIcon from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-stairs-icon.vue";
    import DLBsR2dRoomText from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-room-text.vue";
    import {DListingBuildingScanRendererTwoDQueryVariables, ListingBuildingScan, ListingBuildingScanSurface, useDListingBuildingScanRendererTwoDQuery} from "@/adapter/graphql/generated/graphql";
    import {emptySet} from "@/utility/set";
    import {tDestroyMesh} from "@/adapter/three/three-utility";
    import DLBRCompass from "@/components/listing/building/renderer/d-l-b-r-compass.vue";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";

    const props = defineProps<{
        rendererId: string
        showText?: boolean
        debug?: boolean
        needsImageDownload: boolean
        reloadKey: number
        showSignature: boolean
        signaturePosition?: "START" | "END"
    }>()

    const id = `${props.rendererId}-buildingScan2D`

    provide(LBSR2DDeskewAngleInjection, toRef(0))

    const validSurfaces = computed<readonly ListingBuildingScanSurface[]>(() => (listingBuildingScan.value?.surfaces ?? []).filter(surface => surface.wasRawTransformationValid && surface.wasRawPolygonValid && surface.wasRawBoundingBoxValid))

    const listingId = inject(LListingIdInjection)!
    const showFurniture = inject(DBuildingScanShowFurnitureInjection)!
    const boundingBox = ref<Box3>(new Box3()) //ref okay

    const queryVariables = computed<DListingBuildingScanRendererTwoDQueryVariables>(() => ({
        listingId: listingId.value
    }))

    const renderer = shallowRef<Optional<typeof TRendererWebgl>>(null)
    defineExpose({
        generateJPEG: () => renderer.value?.generateJPEG(),
    })

    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
        refetch
    } = useDListingBuildingScanRendererTwoDQuery(queryVariables);

    watch(() => props.reloadKey, () => refetch())

    const listingBuildingScan = computed<Optional<ListingBuildingScan>>(() => queryResult?.value?.listingBuildingScan as ListingBuildingScan ?? null);

    //don't use new instances as parameters directly, otherwise they will be rerender everytime one of the parent components change
    const cameraPosition = new Vector3(0, 10, 0) //10
    const cameraTarget = new Vector3(0, -1, 0) //-1

    const availableFloorLevels = computed<ReadonlySet<number>>(() => {
        const building = listingBuildingScan.value
        if (building === null) {
            return emptySet()
        }
        return new Set(building.floorLevels)
    })

    onUnmounted(() => {
        for (const stairMeshClusterElement of stairMeshClusterElements_000_G.value) {
            tDestroyMesh(stairMeshClusterElement.mesh, true, false)
        }
    })

    // noinspection LocalVariableNamingConventionJS
    const stairMeshClusterElements_000_G = computed<readonly StairClusterElement[]>(oldStairMeshClusterElements => {
        if (oldStairMeshClusterElements !== undefined) {
            for (const stairMeshClusterElement of oldStairMeshClusterElements) {
                tDestroyMesh(stairMeshClusterElement.mesh, true, false)
            }
        }

        const objects = listingBuildingScan.value?.objects ?? []
        const stairs = objects.filter(o => o.category === 'STAIRS')//.filter(o => o.floorLevel !== 1) //for testing listing 739978663 (integ)
        return calculateStairClusterElements_000_G(stairs, availableFloorLevels.value)
    })
    const stairClusters = computed<readonly StairCluster[]>(() => calculateStairClusters(stairMeshClusterElements_000_G.value))
    const stairLocations = computed<readonly StairLocation[]>(() => calculateStairLocations(stairClusters.value, availableFloorLevels.value))
    const stairLocations2D = computed<readonly StairLocation2D[]>(() => {
        return stairLocations.value.map(stairLocation => {
            stairLocation.mesh.geometry.computeBoundingBox()

            const center = new Vector3()
            stairLocation.mesh.geometry.boundingBox?.getCenter(center)

            const centerInWorld = stairLocation.mesh.localToWorld(center)

            const translationMatrix = new Matrix4().makeTranslation(
                centerInWorld.x,
                centerInWorld.y + BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_STAIRS_ICON,
                centerInWorld.z
            )

            const transformationMatrix = translationMatrix

            return {
                ...stairLocation,
                iconTransformation: transformationMatrix
            }
        })
    })
</script>

<style scoped>
</style>