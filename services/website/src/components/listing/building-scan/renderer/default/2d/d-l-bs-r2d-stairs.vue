<template>
    <!--    &lt;!&ndash; DEBUG &ndash;&gt;-->
    <!--    <t-mesh v-if="false"-->
    <!--            :render-order="BUILDING_SCAN_2D_Z_INDEX_OBJECT"-->
    <!--            :transformation="transformation"-->
    <!--            reset-y>-->
    <!--        <t-plane-geometry :height="depth"-->
    <!--                          :width="width"-->
    <!--                          rotate-x/>-->

    <!--        <t-mesh-basic-material :color="0xFF00FF"-->
    <!--                               :depth-test="false"/>-->
    <!--    </t-mesh>-->

    <!--    &lt;!&ndash; STAIR STEPS &ndash;&gt;-->
    <!--    &lt;!&ndash; steps + 1 for the last step line &ndash;&gt;-->
    <!--    <t-mesh v-for="(step, index) in steps + 1"-->
    <!--            :key="index"-->
    <!--            :render-order="BUILDING_SCAN_2D_Z_INDEX_STAIRS_STEP"-->
    <!--            :transformation="stepTransformations[index]"-->
    <!--            reset-y>-->
    <!--        <t-plane-geometry :height="width"-->
    <!--                          :width="BUILDING_SCAN_2D_STAIRS_STEP_LINE_THICKNESS"/>-->

    <!--        <t-mesh-basic-material :color="color"-->
    <!--                               :depth-test="false"/>-->
    <!--    </t-mesh>-->

    <!--    &lt;!&ndash; STAIR LEFT SIDE &ndash;&gt;-->
    <!--    <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_STAIRS_SIDES"-->
    <!--            :transformation="leftSideTransformation"-->
    <!--            reset-y>-->
    <!--        <t-plane-geometry :height="depth"-->
    <!--                          :width="BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS"/>-->

    <!--        <t-mesh-basic-material :color="color"-->
    <!--                               :depth-test="false"/>-->
    <!--    </t-mesh>-->

    <!--    &lt;!&ndash; STAIR RIGHT SIDE &ndash;&gt;-->
    <!--    <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_STAIRS_SIDES"-->
    <!--            :transformation="rightSideTransformation"-->
    <!--            reset-y>-->
    <!--        <t-plane-geometry :height="depth"-->
    <!--                          :width="BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS"/>-->

    <!--        <t-mesh-basic-material :color="color"-->
    <!--                               :depth-test="false"/>-->
    <!--    </t-mesh>-->
</template>

<script lang="ts"
        setup>
    // import TMesh from "@/adapter/three/components/t-mesh.vue";
    // import {ListingBuildingScanObject} from "@/adapter/graphql/generated/graphql";
    // import {computed, toRef} from "vue";
    // import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    // import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    // import {useLocalStorage} from "@/service/local-storage/local-storage";
    // import {tStepsOfStairOfHeight} from "@/adapter/three/three-utility";
    // import {Matrix4} from "three";
    // import {MathUtils} from "three";
    // import {useRoomDataObject} from "@/components/listing/building-scan/building-scan";
    // import {BUILDING_SCAN_2D_COLOR_WALL_DARK, BUILDING_SCAN_2D_COLOR_WALL_LIGHT, BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS, BUILDING_SCAN_2D_STAIRS_STEP_LINE_THICKNESS, BUILDING_SCAN_2D_Z_INDEX_OBJECT, BUILDING_SCAN_2D_Z_INDEX_STAIRS_SIDES, BUILDING_SCAN_2D_Z_INDEX_STAIRS_STEP} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    //
    // const props = defineProps<{
    //     object: ListingBuildingScanObject
    // }>()
    //
    // const {
    //     width,
    //     height,
    //     depth,
    //     transformation
    // } = useRoomDataObject(toRef(() => props.object))
    //
    // const {theme} = useLocalStorage()
    //
    // const steps = computed<number>(() => {
    //     return tStepsOfStairOfHeight(height.value)
    // })
    // const color = computed<number>(() => {
    //     return theme.value === 'dark' ? BUILDING_SCAN_2D_COLOR_WALL_DARK : BUILDING_SCAN_2D_COLOR_WALL_LIGHT
    // })
    //
    // const leftSideTransformation = computed<Matrix4>(() => {
    //     const translation = new Matrix4().makeTranslation(
    //         (-width.value / 2) + (BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS / 2),
    //         0,
    //         0
    //     )
    //     const rotationX = new Matrix4().makeRotationX(MathUtils.degToRad(-90))
    //     return transformation.value.clone()
    //         .multiply(translation)
    //         .multiply(rotationX)
    // })
    //
    // const rightSideTransformation = computed<Matrix4>(() => {
    //     const translation = new Matrix4().makeTranslation(
    //         (width.value / 2) - (BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS / 2),
    //         0,
    //         0
    //     )
    //     const rotationX = new Matrix4().makeRotationX(MathUtils.degToRad(-90))
    //     return transformation.value.clone()
    //         .multiply(translation)
    //         .multiply(rotationX)
    // })
    //
    // const stepTransformations = computed<Matrix4[]>(() => {
    //     const stairDepth = depth.value - BUILDING_SCAN_2D_STAIRS_STEP_LINE_THICKNESS
    //     const stairSteps = steps.value
    //
    //     const stepDepth = stairDepth / stairSteps
    //
    //     const stepTransformations: Matrix4[] = []
    //
    //     for (let i = 0; i <= stairSteps; ++i) { // <= for the last step line
    //         const translation = new Matrix4().makeTranslation(
    //             0,
    //             0,
    //             i * stepDepth - stairDepth / 2
    //         )
    //         const rotationX = new Matrix4().makeRotationX(MathUtils.degToRad(-90))
    //         const rotationY = new Matrix4().makeRotationY(MathUtils.degToRad(90))
    //         const stepTransformation = transformation.value.clone()
    //             .multiply(translation)
    //             .multiply(rotationY)
    //             .multiply(rotationX)
    //         stepTransformations.push(stepTransformation)
    //     }
    //
    //     return stepTransformations
    // })
</script>

<style scoped>

</style>