<template>
    <!-- FLOOR -->
    <t-mesh :scalar="BUILDING_SCAN_3D_FLOOR_SCALAR"
            :transformation="transformation"
            :visible="showSlabs && isVisible"
            cast-shadow
            receive-shadow>
        <t-plane-geometry v-if="polygonVectors.length <= 0"
                          :height="depth"
                          :width="width"/>
        <t-shape-geometry v-else
                          :points="polygonVectors"/>

        <t-mesh-basic-material :color="0xC4C9D6"
                               :side="DoubleSide"/>
    </t-mesh>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanObject, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {BUILDING_SCAN_3D_FLOOR_SCALAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {DBuildingScanShowSlabsInjection, DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import TShapeGeometry from "@/adapter/three/components/geometry/t-shape-geometry.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import {DoubleSide, MathUtils, Matrix4} from "three";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";

    const props = defineProps<{
        floor: ListingBuildingScanSurface
        objects: readonly ListingBuildingScanObject[]
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.floor.floorLevel))

    const showSlabs = inject(DBuildingScanShowSlabsInjection)!

    const {
        width,
        depth,
        transformation: rawTransformation,
        polygonVectors,
        destroyRoomDataSurface,
    } = useRoomDataSurface(toRef(() => props.floor), toRef(null))

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const transformation = computed<Matrix4>(() => {
        const rotationXMatrix = new Matrix4().makeRotationX(MathUtils.degToRad(-90))
        return rawTransformation.value.clone().multiply(rotationXMatrix)
    })
</script>

<style scoped>
</style>