<template>
    <d-l-bs-r2d-surface-wall v-if="surface.category === 'WALL'"
                             :surfaces="surfaces"
                             :wall="surface"/>
    <d-l-bs-r2d-surface-window v-else-if="surface.category === 'WINDOW'"
                               :window="surface"/>
    <d-l-bs-r2d-surface-door v-else-if="surface.category === 'DOOR'"
                             :door="surface"/>
    <d-l-bs-r2d-surface-floor v-else-if="surface.category === 'FLOOR'"
                              :floor="surface"
                              :objects="objects"/>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanObject, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import DLBsR2dSurfaceWall from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-surface-wall.vue";
    import DLBsR2dSurfaceWindow from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-surface-window.vue";
    import DLBsR2dSurfaceDoor from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-surface-door.vue";
    import DLBsR2dSurfaceFloor from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-surface-floor.vue";

    defineProps<{
        surface: ListingBuildingScanSurface
        surfaces: readonly ListingBuildingScanSurface[]
        objects: readonly ListingBuildingScanObject[]
    }>()
</script>

<style scoped>

</style>