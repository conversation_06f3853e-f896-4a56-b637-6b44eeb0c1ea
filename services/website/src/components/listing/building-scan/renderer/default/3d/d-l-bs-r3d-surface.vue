<template>
    <d-l-bs-r3d-surface-wall v-if="surface.category === 'WALL'"
                             :focused-room-mesh="focusedRoomMesh"
                             :occlusion-culling-raycaster="occlusionCullingRaycaster"
                             :surfaces="surfaces"
                             :wall="surface"/>
    <template v-if="showOpenings">
        <d-l-bs-r3d-surface-window v-if="surface.category === 'WINDOW'"
                                   :focused-room-mesh="focusedRoomMesh"
                                   :window="surface"/>
        <d-l-bs-r3d-surface-door v-else-if="surface.category === 'DOOR'"
                                 :door="surface"
                                 :focused-room-mesh="focusedRoomMesh"/>
    </template>

    <d-l-bs-r3d-surface-floor v-if="surface.category === 'FLOOR'"
                              :floor="surface"
                              :objects="objects"/>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanObject, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import DLBsR3dSurfaceWall from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface-wall.vue";
    import DLBsR3dSurfaceFloor from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface-floor.vue";
    import DLBsR3dSurfaceWindow from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface-window.vue";
    import {Optional} from "@/model/Optional";
    import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
    import DLBsR3dSurfaceDoor from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface-door.vue";
    import {Mesh} from "three";

    defineProps<{
        surface: ListingBuildingScanSurface
        surfaces: readonly ListingBuildingScanSurface[]
        objects: readonly ListingBuildingScanObject[]
        occlusionCullingRaycaster: Optional<TOcclusionCullingRaycaster<ListingBuildingScanSurface>>
        focusedRoomMesh: Optional<Mesh>
        showOpenings: boolean
    }>()
</script>

<style scoped>
</style>