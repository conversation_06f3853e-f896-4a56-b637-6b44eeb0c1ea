<template>
    <!-- WALL -->
    <t-mesh :key="renderKey"
            :render-order="BUILDING_SCAN_2D_Z_INDEX_WALL"
            :transformation="transformation"
            :visible="isVisible"
            reset-y>
        <t-ring-geometry-with-depth v-if="wall.arc != null"
                                    :center-x="wall.arc.center[0]"
                                    :center-z="wall.arc.center[1]"
                                    :depth="depth"
                                    :end-angle="wall.arc.endAngle"
                                    :height="0.001"
                                    :inner-radius="wall.arc.radius - depth / 2"
                                    :outer-radius="wall.arc.radius + depth / 2"
                                    :start-angle="wall.arc.startAngle"/>
        <!-- TODO: ist die höhe wirklich notwending um holes einzufügen? ohne ging bisher nicht -->
        <t-box-geometry v-else
                        :depth="depth"
                        :height="0.001"
                        :width="width"/>

        <t-mesh-basic-material :color="BUILDING_SCAN_2D_COLOR_WALL"
                               :depth-test="false"/>

        <!-- HOLES -->
        <t-mesh v-for="holeSurface in holeSurfaces"
                :key="holeSurface.id"
                :transformation="holeTransformation(holeSurface)"
                hole
                reset-y>
            <!-- einfache breite reicht auch, aber sicher ist sicher, sonst entsteht kein loch -->
            <!-- bei der height geht auch 0, aber sicher ist sicher, sonst entsteht kein loch -->
            <t-ring-geometry-with-depth v-if="holeSurface.arc != null"
                                        :center-x="holeSurface.arc.center[0]"
                                        :center-z="holeSurface.arc.center[1]"
                                        :depth="depth * 2"
                                        :end-angle="holeSurface.arc.endAngle"
                                        :height="1"
                                        :inner-radius="holeSurface.arc.radius - depth"
                                        :outer-radius="holeSurface.arc.radius + depth"
                                        :start-angle="holeSurface.arc.startAngle"/>

            <!-- einfache breite reicht auch, aber 2x sicher ist sicher, sonst entsteht kein loch -->
            <!-- bei der height geht auch 0, aber sicher ist sicher, sonst entsteht kein loch -->
            <t-box-geometry
                v-else
                :depth="depth * 2"
                :height="1"
                :width="holeSurface.boundingBox[0]"/>

            <t-mesh-basic-material :color="0xFF00FF"
                                   :depth-test="false"/>
        </t-mesh>
    </t-mesh>
</template>

<script lang="ts"
        setup>
    import {MathUtils, Matrix4} from "three";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, shallowRef, toRef, watch} from "vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import TRingGeometryWithDepth from "@/adapter/three/components/geometry/t-ring-geometry-with-depth.vue";
    import {BUILDING_SCAN_2D_COLOR_WALL, BUILDING_SCAN_2D_DEFAULT_THICKNESS, BUILDING_SCAN_2D_Z_INDEX_WALL} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, transformationMatrixOfSurface, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";

    const props = defineProps<{
        wall: ListingBuildingScanSurface
        surfaces: readonly ListingBuildingScanSurface[]
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.wall.floorLevel))

    const {
        width,
        depth: rawDepth,
        transformation: rawTransformation,
        destroyRoomDataSurface,
    } = useRoomDataSurface(toRef(() => props.wall), toRef(null))

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const depth = computed<number>(() => Math.max(BUILDING_SCAN_2D_DEFAULT_THICKNESS, rawDepth.value))

    const transformation = computed<Matrix4>(() => {
        if (props.wall.arc == null) {
            return rawTransformation.value.clone()
        }

        const center = props.wall.arc.center

        const translation = new Matrix4().makeTranslation(center[0], 0, center[1])

        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));

        return rawTransformation.value.clone().multiply(translation).multiply(rotationMatrixX)
    })

    //Sollte sich die Wall ändern (update), müssen die holeSurfaces (und damit die wall) nicht neu gerendert werden, wenn die id gleich geblieben ist
    const wallId = computed<string>(() => props.wall.id)

    const holeSurfaces = computed<ListingBuildingScanSurface[]>(() => {
        return props.surfaces.filter(surface => surface.parentId === wallId.value)
    })

    const renderKey = shallowRef<number>(0)
    watch([() => props.wall, holeSurfaces], () => {
        ++renderKey.value
    }, {
        deep: true
    })

    function holeTransform(holeSurface: ListingBuildingScanSurface) {
        const holeTransformationMatrix = transformationMatrixOfSurface(holeSurface)

        if (holeSurface.arc == null) {
            return holeTransformationMatrix.clone()
        }

        const center = holeSurface.arc.center

        const translation = new Matrix4().makeTranslation(center[0], 0, center[1])

        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));

        return holeTransformationMatrix.clone().multiply(translation).multiply(rotationMatrixX)
    }

    function holeTransformation(holeSurface: ListingBuildingScanSurface): Matrix4 {
        const holeTransformationMatrix = holeTransform(holeSurface)
        return transformation.value.clone().invert().multiply(holeTransformationMatrix)
    }
</script>

<style scoped>
</style>