<template>
    <t-renderer-webgl ref="renderer"
                      :effects="effects"
                      :needs-image-download="needsImageDownload"
                      :raycasters="raycasters"
                      :renderer-id="id"
                      :show-signature="showSignature"
                      :show-stats="debug"
                      :signature-position="signaturePosition"
                      :tone-mapping="ACESFilmicToneMapping"
                      :tone-mapping-exposure="1"
                      shadow-map>
        <t-scene theme-background>
            <t-perspective-camera :far="BUILDING_SCAN_3D_CAMERA_FAR"
                                  :position="cameraPosition"
                                  :target="cameraTarget">
                <t-orbit-controls/>
            </t-perspective-camera>

            <d-l-b-r-compass v-if="false"
                             :bounding-box="boundingBox"/>

            <t-ambient-light :intensity="1.25"/>

            <t-sky v-if="showSky"
                   :daytime="isDark ? 'NIGHT' : 'DAY'"
                   :offset-degrees="0"/>

            <t-group v-if="listingBuildingScan !== null"
                     v-model:bounding-box="boundingBox"
                     :transformation="new Matrix4().makeRotationY(MathUtils.degToRad(listingBuildingScan.rotationYCorrectionAngle))"
                     fit-to-camera
                     hide-until-first-fit-to-camera>
                <template v-for="surface in validSurfaces"
                          :key="surface.id">
                    <d-l-bs-r3d-surface :focused-room-mesh="null"
                                        :objects="listingBuildingScan.objects"
                                        :occlusion-culling-raycaster="occlusionCullingRaycaster"
                                        :surface="surface"
                                        :surfaces="validSurfaces"
                                        show-openings/>
                </template>

                <template v-for="object in listingBuildingScan.objects"
                          :key="object.id">
                    <d-l-bs-r3d-stairs v-if="!DEBUG_STAIR_CLUSTERS && object.category === 'STAIRS'"
                                       :object="object"/>
                    <!-- TODO: visibile steuern, nicht so -->
                    <d-l-bs-r3d-object v-if="(DEBUG_STAIR_CLUSTERS && object.category === 'STAIRS') || (!DEBUG_STAIR_CLUSTERS && object.category !== 'STAIRS')"
                                       :object="object"/>
                </template>

                <d-l-bs-r3d-point-of-interest v-for="poi in listingBuildingScan.pointsOfInterest"
                                              :key="poi.id"
                                              :building="listingBuildingScan"
                                              :point-of-interest="poi"/>

                <template v-if="showText">
                    <template v-for="(room, index) in listingBuildingScan.rooms"
                              :key="room.id">
                        <d-l-bs-r3d-room-text :room="room"
                                              :room-index="index"/>
                    </template>
                </template>

                <template v-if="DEBUG_STAIR_CLUSTERS">
                    <t-mesh-raw v-for="(stairCluster, index) in stairClusters"
                                :key="index"
                                :mesh="stairCluster.mesh">
                        <t-mesh-standard-material :color="tRandomColor(String(index * 1000))"
                                                  :metalness="0"
                                                  :opacity="0.5"
                                                  :roughness="0.5"
                                                  transparent/>
                    </t-mesh-raw>
                </template>

                <template v-if="DEBUG_OCCLUSION_CULLING">
                    <t-mesh v-for="(occlusionCullingRaySourceBoundingBoxTransformation, index) in occlusionCullingRaySourceBoundingBoxTransformations"
                            :key="index"
                            :render-order="BUILDING_SCAN_3D_RENDER_ORDER_DEBUG"
                            :transformation="occlusionCullingRaySourceBoundingBoxTransformation">
                        <t-box-geometry :depth="0.1"
                                        :height="0.1"
                                        :width="0.1"/>
                        <t-mesh-basic-material :color="0xFF00FF"
                                               :depth-test="false"
                                               transparent/>
                    </t-mesh>
                </template>

                <!--                <template v-if="true">-->
                <!--                    <t-mesh-raw v-for="(renderPlane, index) in renderPlanes"-->
                <!--                                :key="index"-->
                <!--                                :mesh="renderPlane">-->
                <!--                        <t-mesh-standard-material :color="0x00ffff"-->
                <!--                                                  :depth-test="false"-->
                <!--                                                  :opacity="0.25"-->
                <!--                                                  transparent/>-->
                <!--                    </t-mesh-raw>-->
                <!--                </template>-->

                <!--                <t-mesh-raw v-for="(renderMesh, index) in renderMeshes"-->
                <!--                            :key="index"-->
                <!--                            :mesh="renderMesh">-->
                <!--                    <t-mesh-standard-material :color="0xff00ff"-->
                <!--                                              :depth-test="false"-->
                <!--                                              transparent-->
                <!--                                              wireframe/>-->
                <!--                </t-mesh-raw>-->
            </t-group>
        </t-scene>
    </t-renderer-webgl>
</template>

<script lang="ts"
        setup>
    import {ACESFilmicToneMapping, Box3, BoxGeometry, MathUtils, Matrix4, Mesh, Vector3} from "three";
    import TPerspectiveCamera from "@/adapter/three/components/camera/t-perspective-camera.vue";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import {computed, inject, onUnmounted, ref, shallowRef, watch, watchEffect} from "vue";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Optional} from "@/model/Optional";
    import TSky from "@/adapter/three/components/object/t-sky.vue";
    import {tDestroyMesh, tRandomColor, tVectorsToTranslationMatrices} from "@/adapter/three/three-utility";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";
    import {TEffect} from "@/adapter/three/TEffect";
    import {GTAOEffect} from "@/adapter/three/postprocessing/GTAOEffect";
    import TMeshStandardMaterial from "@/adapter/three/components/material/t-mesh-standard-material.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {BUILDING_SCAN_3D_CAMERA_FAR, BUILDING_SCAN_3D_RENDER_ORDER_DEBUG} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {calculateStairClusterElements_000_G, calculateStairClusters, calculateStairLocations, centerVectorOfSection, DBuildingScanVisibleFloorLevelsInjection, DEBUG_STAIR_CLUSTERS} from "@/components/listing/building-scan/building-scan";
    import {StairClusterElement} from "@/components/listing/building-scan/StairClusterElement";
    import {StairCluster} from "@/components/listing/building-scan/StairCluster";
    import {StairLocation} from "@/components/listing/building-scan/StairLocation";
    import DLBsR3dSurface from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface.vue";
    import DLBsR3dStairs from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-stairs.vue";
    import DLBsR3dObject from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-object.vue";
    import DLBsR3dRoomText from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-room-text.vue";
    import {DListingBuildingScanRendererThreeDQueryVariables, ListingBuildingScan, ListingBuildingScanSurface, useDListingBuildingScanRendererThreeDQuery} from "@/adapter/graphql/generated/graphql";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
    import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
    import {emptySet} from "@/utility/set";
    import {useColorMode} from "@vueuse/core";
    import TAmbientLight from "@/adapter/three/components/light/t-ambient-light.vue";
    import DLBsR3dPointOfInterest from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-point-of-interest.vue";
    import DLBRCompass from "@/components/listing/building/renderer/d-l-b-r-compass.vue";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";
    // import testjson from "@/components/listing/building-model/testdata.json?raw";
    // import rawMeshesFileURL from "@/components/listing/building-model/arMeshAnchors.bin?url";

    const DEBUG_OCCLUSION_CULLING = false

    const props = defineProps<{
        rendererId: string,
        showText: boolean
        showSky: boolean
        debug?: boolean
        needsImageDownload: boolean
        reloadKey: number
        showSignature: boolean
        signaturePosition?: "START" | "END"
    }>()

    const id = `${props.rendererId}-buildingScan3D`

    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')
    const listingId = inject(LListingIdInjection)!
    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!

    const validSurfaces = computed<readonly ListingBuildingScanSurface[]>(() => (listingBuildingScan.value?.surfaces ?? []).filter(surface => surface.wasRawTransformationValid && surface.wasRawPolygonValid && surface.wasRawBoundingBoxValid))

    const boundingBox = ref<Box3>(new Box3()) //ref okay
    const renderer = shallowRef<Optional<typeof TRendererWebgl>>(null)

    const variables = computed<DListingBuildingScanRendererThreeDQueryVariables>(() => ({
        listingId: listingId.value
    }))

    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
        refetch
    } = useDListingBuildingScanRendererThreeDQuery(variables);

    watch(() => props.reloadKey, () => refetch())

    // const meshes = computed<readonly ListingBuildingMesh[]>(() => listing.value?.building?.meshes ?? [])
    // const planes = computed<readonly ListingBuildingPlane[]>(() => listing.value?.building?.planes ?? [])
    const listingBuildingScan = computed<Optional<ListingBuildingScan>>(() => queryResult?.value?.listingBuildingScan as ListingBuildingScan ?? null);

    // const rawMeshes = ref<readonly number[]>([])

    // async function loadRawMeshes() {
    //     const response = await fetch(rawMeshesFileURL);
    //
    //     if (!response.ok) {
    //         throw new Error(`Failed to fetch raw meshes: ${response.statusText}`);
    //     }
    //
    //     const buffer = await response.arrayBuffer();
    //     const dataView = new DataView(buffer);
    //     const intArray: number[] = [];
    //
    //     // read 4 bytes at a time assuming each integer is stored as 4 bytes
    //     for (let i = 0; i < buffer.byteLength; i += 4) {
    //         intArray.push(dataView.getInt32(i, true));
    //     }
    //
    //     rawMeshes.value = intArray
    // }

    // onMounted(() => {
    //     loadRawMeshes()
    // })

    // const renderMeshes = computed<readonly Mesh[]>(() => {
    //     const data = rawMeshes.value
    //
    //     if (data.length <= 0) {
    //         return []
    //     }
    //
    //     console.log("RAW MESHES", data)
    //
    //     const meshes: Mesh[] = []
    //     let i = 0
    //
    //     // const randomGenerator = createRandomGenerator("1")
    //
    //     while (i < data.length) {
    //         console.log("")
    //
    //         //VERTICES + NORMALS
    //         const vertexCount = data[i++]
    //         const vertices: number[] = []
    //         const normals: number[] = []
    //
    //         if (vertexCount <= 0) {
    //             console.warn("Wrong vertex count", vertexCount)
    //             return []
    //         }
    //
    //         console.log("V+N COUNT", vertexCount, data.slice(i, i + vertexCount * 6))
    //
    //         for (let j = 0; j < vertexCount; ++j) {
    //             const dataIndex = i + j * 6
    //
    //             const v1 = data[dataIndex] / 100
    //             const v2 = data[dataIndex + 1] / 100
    //             const v3 = data[dataIndex + 2] / 100
    //             const n1 = data[dataIndex + 3] / 100
    //             const n2 = data[dataIndex + 4] / 100
    //             const n3 = data[dataIndex + 5] / 100
    //
    //             vertices.push(v1, v2, v3)
    //             normals.push(n1, n2, n3)
    //         }
    //         i += vertexCount * 6
    //
    //         console.log("VERTICES", vertices)
    //         console.log("NORMALS", normals)
    //
    //         //FACES + CLASSIFICATION
    //         const faceCount = data[i++]
    //         const faces: number[] = []
    //         const classifications: number[] = []
    //
    //         if (faceCount <= 0) {
    //             console.warn("Wrong face count", faceCount)
    //             return []
    //         }
    //
    //         console.log("F+C COUNT", faceCount, data.slice(i, i + faceCount * 4))
    //
    //         for (let j = 0; j < faceCount; ++j) {
    //             const dataIndex = i + j * 4
    //
    //             const f1 = data[dataIndex]
    //             const f2 = data[dataIndex + 1]
    //             const f3 = data[dataIndex + 2]
    //             const classification = data[dataIndex + 3]
    //
    //             faces.push(f1, f2, f3)
    //             classifications.push(classification)
    //         }
    //         i += faceCount * 4
    //
    //         console.log("FACES", faces)
    //         console.log("CLASSIFICATIONS", classifications)
    //
    //         //MESH
    //         const geometry = new BufferGeometry()
    //         geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(vertices), 3))
    //         geometry.setAttribute('normal', new THREE.BufferAttribute(new Float32Array(normals), 3))
    //         geometry.setIndex(faces)//new THREE.BufferAttribute(new Uint32Array(faces), 3))
    //         const mesh = new THREE.Mesh(geometry)
    //         mesh.userData.classifications = classifications
    //
    //         // const randomTranslation = new Matrix4().makeTranslation(
    //         //     randomFloat(randomGenerator, -20, 20),
    //         //     0,
    //         //     randomFloat(randomGenerator, -20, 20)
    //         // )
    //         // mesh.applyMatrix4(randomTranslation)
    //
    //         meshes.push(mesh)
    //     }
    //
    //     console.log("CHECK", i === data.length, i, data.length)
    //
    //     return meshes
    // })
    //
    // const renderPlanes = computed<readonly Mesh[]>(() => planes.value.map(plane => {
    //     const baseTransformationMatrix = new Matrix4().fromArray(plane.transformation.flat())
    //     const center = plane.center
    //     const width = plane.width
    //     const height = plane.height
    //     const rotationOnYAxis = plane.rotation
    //
    //     const centerTranslationMatrix = new Matrix4().makeTranslation(center[0], center[1], center[2])
    //     const rotationMatrix = new Matrix4().makeRotationY(rotationOnYAxis)
    //     const transformationMatrix = baseTransformationMatrix.multiply(centerTranslationMatrix).multiply(rotationMatrix)
    //
    //     const geometry = new BoxGeometry(width, 0.1, height)
    //     const mesh = new Mesh(geometry)
    //     mesh.applyMatrix4(transformationMatrix)
    //     return mesh
    // }))

    const effects: TEffect[] = [
        new GTAOEffect(4),
        // new FXAAEffect()
    ]

    onUnmounted(() => {
        for (const effect of effects) {
            effect.destroy()
        }

        for (const raycaster of raycasters) {
            raycaster.destroy()
        }

        removeOldRaySourceMeshes()

        for (const stairMeshClusterElement of stairMeshClusterElements_000_G.value) {
            tDestroyMesh(stairMeshClusterElement.mesh, true, false)
        }
    })

    //don't use new instances as parameters directly, otherwise they will be rerender everytime one of the parent components change
    const cameraPosition = new Vector3(0, 7, 7)
    const cameraTarget = new Vector3(0, 0, 0)

    defineExpose({
        generateJPEG: () => renderer.value?.generateJPEG(),
    })

    const availableFloorLevels = computed<ReadonlySet<number>>(() => {
        const building = listingBuildingScan.value
        if (building === null) {
            return emptySet()
        }
        return new Set(building.floorLevels)
    })

    // noinspection LocalVariableNamingConventionJS
    const stairMeshClusterElements_000_G = computed<readonly StairClusterElement[]>(oldStairMeshClusterElements => {
        if (oldStairMeshClusterElements !== undefined) {
            for (const stairMeshClusterElement of oldStairMeshClusterElements) {
                tDestroyMesh(stairMeshClusterElement.mesh, true, false)
            }
        }

        if (!DEBUG_STAIR_CLUSTERS) {
            return []
        }
        const objects = listingBuildingScan.value?.objects ?? []
        const stairs = objects.filter(o => o.category === 'STAIRS')//.filter(o => o.floorLevel !== 1) //for testing listing 739978663 (integ)
        return calculateStairClusterElements_000_G(stairs, availableFloorLevels.value)
    })
    const stairClusters = computed<readonly StairCluster[]>(() => calculateStairClusters(stairMeshClusterElements_000_G.value))
    const stairLocations = computed<readonly StairLocation[]>(() => calculateStairLocations(stairClusters.value, availableFloorLevels.value))

    if (DEBUG_STAIR_CLUSTERS) {
        watchEffect(() => {
            console.log("STAIR CLUSTERS", stairClusters.value.map(c => [...c.clusterEffectedFloorLevels].toSorted()))
            console.log("STAIR DISPLAY", stairClusters.value.map(c => [...c.displayEffectedFloorLevels].toSorted()))
            console.log("STAIR LOCATIONS", stairLocations.value)
        })
    }

    const occlusionCullingRaycaster = new TOcclusionCullingRaycaster<ListingBuildingScanSurface>()
    // noinspection LocalVariableNamingConventionJS
    const occlusionCullingRaySourceBoundingBoxTransformations = computed<Matrix4[]>(() => {
        const floorLevels = visibleFloorLevels.value
        const rooms = listingBuildingScan.value?.rooms?.filter(r => floorLevels.has(r.floorLevel)) ?? []
        const sections = rooms.flatMap(r => r.sections ?? []) //Die Building-Sections sind iwi kacke, die sind meist höher und nicht im Raumzentrum (Raum-Sections sind besser)
        const sectionCenterVectors = sections.map(section => centerVectorOfSection(section))
        return tVectorsToTranslationMatrices(sectionCenterVectors)
    })

    function removeOldRaySourceMeshes() {
        for (const raySourceMeshes of occlusionCullingRaycaster.raySourceMeshes) {
            tDestroyMesh(raySourceMeshes, true, true)
        }
    }

    watch(occlusionCullingRaySourceBoundingBoxTransformations, transformations => {
        removeOldRaySourceMeshes()

        occlusionCullingRaycaster.raySourceMeshes = transformations.map(transformation => {
            const mesh = new Mesh(new BoxGeometry())
            mesh.applyMatrix4(transformation)
            return mesh
        })
    }, {
        deep: true
    })

    const raycasters: readonly TRaycaster[] = [occlusionCullingRaycaster]
</script>

<style scoped>
</style>