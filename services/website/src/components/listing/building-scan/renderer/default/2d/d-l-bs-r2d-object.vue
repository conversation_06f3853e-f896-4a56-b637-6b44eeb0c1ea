<template>
    <t-group :visible="isVisible">
        <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_OBJECT"
                :transformation="transformation"
                reset-y>
            <t-plane-geometry :height="depth"
                              :width="width"
                              rotate-x/>

            <t-mesh-basic-material :color="BUILDING_SCAN_2D_COLOR_FURNITURE"
                                   :depth-test="false"/>
        </t-mesh>
        <!-- TODO: parent beziehung -->

        <t-lines :color="BUILDING_SCAN_2D_COLOR_FURNITURE_OUTLINE"
                 :depth-test="false"
                 :render-order="BUILDING_SCAN_2D_Z_INDEX_OBJECT_BORDER"
                 :transformation="transformation"
                 :vertices="lineVertices"
                 reset-y
                 type="LINE_LOOP"/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScanObject} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, toRef} from "vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {Vector3} from "three";
    import TLines from "@/adapter/three/components/object/t-lines.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import {BUILDING_SCAN_2D_COLOR_FURNITURE, BUILDING_SCAN_2D_COLOR_FURNITURE_OUTLINE, BUILDING_SCAN_2D_Z_INDEX_OBJECT, BUILDING_SCAN_2D_Z_INDEX_OBJECT_BORDER} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataObject} from "@/components/listing/building-scan/building-scan";
    import TGroup from "@/adapter/three/components/object/t-group.vue";

    const props = defineProps<{
        object: ListingBuildingScanObject
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.object.floorLevel))

    const {
        width,
        depth,
        transformation,
    } = useRoomDataObject(toRef(() => props.object))

    const lineVertices = computed<Vector3[]>(() => {
        const x = width.value / 2 //halfWidth
        const y = 0
        const z = depth.value / 2 //halfDepth

        const left = -x
        const right = x
        const top = z
        const bottom = -z

        const leftTop = new Vector3(left, y, top)
        const rightTop = new Vector3(right, y, top)
        const leftBottom = new Vector3(left, y, bottom)
        const rightBottom = new Vector3(right, y, bottom)

        return [
            leftTop,
            rightTop,
            rightBottom,
            leftBottom,
        ]
    })
</script>

<style scoped>

</style>