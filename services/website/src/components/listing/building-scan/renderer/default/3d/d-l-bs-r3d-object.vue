<template>
    <t-mesh :scalar="BUILDING_SCAN_3D_OBJECT_SCALAR"
            :transformation="transformation"
            :visible="showFurniture && isVisible"
            cast-shadow
            receive-shadow>
        <t-box-geometry :depth="depth"
                        :height="height"
                        :width="width"/>

        <t-mesh-standard-material :color="0x0095D0"
                                  :metalness="0"
                                  :opacity="DEBUG_STAIR_CLUSTERS ? 0.5 : undefined"
                                  :roughness="0.6"
                                  :transparent="DEBUG_STAIR_CLUSTERS"/>
    </t-mesh>

    <!-- TODO: parent beziehung -->
</template>

<script lang="ts"
        setup>
    import TMeshStandardMaterial from "@/adapter/three/components/material/t-mesh-standard-material.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import {ListingBuildingScanObject} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, toRef} from "vue";
    import {BUILDING_SCAN_3D_OBJECT_SCALAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {DBuildingScanShowFurnitureInjection, DBuildingScanVisibleFloorLevelsInjection, DEBUG_STAIR_CLUSTERS, useRoomDataObject} from "@/components/listing/building-scan/building-scan";

    const props = defineProps<{
        object: ListingBuildingScanObject
    }>()

    const showFurniture = inject(DBuildingScanShowFurnitureInjection)!
    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!

    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.object.floorLevel))

    const {
        width,
        height,
        depth,
        transformation
    } = useRoomDataObject(toRef(() => props.object))
</script>

<style scoped>

</style>