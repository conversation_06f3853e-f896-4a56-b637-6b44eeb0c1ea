<template>
    <t-group :visible="isVisible">
        <!-- WINDOW -->
        <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_WINDOW"
                :transformation="transformation"
                reset-y>
            <t-plane-geometry :height="depth"
                              :width="width"/>

            <t-mesh-basic-material :color="0xffffff"
                                   :depth-test="false"/>
        </t-mesh>

        <t-lines :color="BUILDING_SCAN_2D_COLOR_WALL"
                 :depth-test="false"
                 :render-order="BUILDING_SCAN_2D_Z_INDEX_WINDOW_LINES"
                 :transformation="transformation"
                 :vertices="lineVertices"
                 reset-y
                 type="LINE_LOOP"/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {MathUtils, Matrix4, Shape, Vector3} from "three";
    import TLines from "@/adapter/three/components/object/t-lines.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import {BUILDING_SCAN_2D_COLOR_WALL, BUILDING_SCAN_2D_DEFAULT_THICKNESS, BUILDING_SCAN_2D_Z_INDEX_WINDOW, BUILDING_SCAN_2D_Z_INDEX_WINDOW_LINES} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import TGroup from "@/adapter/three/components/object/t-group.vue";

    const props = defineProps<{
        window: ListingBuildingScanSurface
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.window.floorLevel))

    const {
        width,
        depth: rawDepth,
        transformation: rawTransformation,
        destroyRoomDataSurface,
    } = useRoomDataSurface(toRef(() => props.window), toRef(null))

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const depth = computed<number>(() => Math.max(BUILDING_SCAN_2D_DEFAULT_THICKNESS, rawDepth.value))

    const transformation = computed<Matrix4>(() => {
        if (props.window.arc == null) {
            return rawTransformation.value.clone()
        }

        const center = props.window.arc.center

        const translation = new Matrix4().makeTranslation(center[0], 0, center[1])

        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));

        return rawTransformation.value.clone().multiply(rotationMatrixX).multiply(translation)
    })

    const lineVertices = computed<Vector3[]>(() => {
        const arc = props.window.arc

        if (arc == null) {
            const x = width.value / 2 //halfWidth
            const y = 0
            const z = depth.value / 2 //halfDepth

            const left = -x
            const right = x
            const top = z
            const bottom = -z
            const center = 0

            const leftTop = new Vector3(left, y, top)
            const rightTop = new Vector3(right, y, top)
            const rightCenter = new Vector3(right, y, center)
            const leftCenter = new Vector3(left, y, center)
            const leftBottom = new Vector3(left, y, bottom)
            const rightBottom = new Vector3(right, y, bottom)

            return [
                leftTop,
                rightTop,
                rightCenter,
                leftCenter,
                leftBottom,
                rightBottom,
                rightCenter,
                leftCenter
            ]
        }

        const centerX = arc.center[0]
        const centerZ = arc.center[1]
        const endAngle = arc.endAngle
        const innerRadius = arc.radius - depth.value / 2
        const centerRadius = arc.radius
        const outerRadius = arc.radius + depth.value / 2
        const startAngle = arc.startAngle

        const curveTop = new Shape()
        curveTop.absarc(
            centerX,
            centerZ,
            outerRadius,
            MathUtils.degToRad(startAngle),
            MathUtils.degToRad(endAngle),
            false
        )

        const curveCenter = new Shape()
        curveCenter.absarc(
            centerX,
            centerZ,
            centerRadius,
            MathUtils.degToRad(startAngle),
            MathUtils.degToRad(endAngle),
            false
        )

        const curveBottom = new Shape()
        curveBottom.absarc(
            centerX,
            centerZ,
            innerRadius,
            MathUtils.degToRad(startAngle),
            MathUtils.degToRad(endAngle),
            false
        )

        const curveTopPoints = curveTop.getPoints()
        const curveCenterPoints = curveCenter.getPoints().reverse()
        const curveBottomPoints = curveBottom.getPoints()

        const vertices = [
            ...curveTopPoints,
            ...curveCenterPoints,
            ...curveBottomPoints,
            curveCenterPoints[0],
            ...curveCenterPoints, //TODO: doppelter mittelweg eig. total dumm … ohne LINE_LOOP geht das anders
            curveTopPoints[0]
        ]

        return vertices.map(vertex => new Vector3(vertex.x, 0, vertex.y))
    })
</script>

<style scoped>
</style>