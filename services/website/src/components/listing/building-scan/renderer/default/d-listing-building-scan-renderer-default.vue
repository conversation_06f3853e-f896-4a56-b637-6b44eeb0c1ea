<template>
    <div class="contentWrapper"> <!-- TODO: das muss anders gelöst werden -->
        <d-listing-detail-toolbar v-if="!isEmbedded"
                                  class="px-2">
            <d-listing-button-back :route="{
                                        name: 'viewListing',
                                        params: {
                                            id: listingId,
                                        },
                                   }"
                                   :text="t('components.listing.buildingModel.backToListingButton')"/>

            <v-spacer/>

            <v-slide-x-reverse-transition>
                <div v-if="is3DModeEnabled">
                    <d-btn v-if="smAndDown"
                           :icon="slabsIcon"
                           :title="t('components.listing.buildingModel.visibilityMenu.items.slabs')"
                           type="default"
                           variant="text"
                           @click="toggleSlabs"/>
                    <d-btn v-else
                           :prepend-icon="slabsIcon"
                           :text="t('components.listing.buildingModel.visibilityMenu.items.slabs')"
                           type="default"
                           variant="text"
                           @click="toggleSlabs"/>
                </div>
            </v-slide-x-reverse-transition>

            <d-btn v-if="smAndDown"
                   :icon="furnitureIcon"
                   :title="t('components.listing.buildingModel.visibilityMenu.items.furniture')"
                   type="default"
                   variant="text"
                   @click="toggleFurniture"/>
            <d-btn v-else
                   :prepend-icon="furnitureIcon"
                   :text="t('components.listing.buildingModel.visibilityMenu.items.furniture')"
                   type="default"
                   variant="text"
                   @click="toggleFurniture"/>

            <div class="px-3">
                <d-btn-toggle v-model="is3DModeEnabled"
                              :items="renderTypeItems"
                              mandatory/>
            </div>

            <!-- TODO: select theme -->
            <v-select v-if="floorLevels.size > 1"
                      v-model="selectedFloorLevelsInput"
                      :items="sortedFloorLevels"
                      :label="t('components.listing.buildingModel.floorLevels')"
                      :multiple="is3DModeEnabled"
                      class="mx-2"
                      style="flex-grow: 0.1;"/>
        </d-listing-detail-toolbar>

        <div :class="{isEmbedded}"
             class="modelWrapper">
            <v-layout v-if="isEmbedded && (!hideControls || showFullScreenButton)"
                      :class="{hideControls, 'pa-2 rounded-xl elevation-2': !hideControls}"
                      class="controlsOverlay position-absolute align-center ma-4 overflow-visible">
                <template v-if="!hideControls">
                    <v-slide-x-reverse-transition>
                        <div v-if="is3DModeEnabled">
                            <d-btn :icon="slabsIcon"
                                   :title="t('components.listing.buildingModel.visibilityMenu.items.slabs')"
                                   type="default"
                                   variant="text"
                                   @click="toggleSlabs"/>
                        </div>
                    </v-slide-x-reverse-transition>

                    <d-btn :icon="furnitureIcon"
                           :title="t('components.listing.buildingModel.visibilityMenu.items.furniture')"
                           type="default"
                           variant="text"
                           @click="toggleFurniture"/>

                    <div class="ms-2 me-2">
                        <d-btn-toggle v-model="is3DModeEnabled"
                                      :items="renderTypeItems"
                                      mandatory/>
                    </div>

                    <!-- TODO: select theme -->
                    <v-select v-if="floorLevels.size > 1"
                              v-model="selectedFloorLevelsInput"
                              :items="sortedFloorLevels"
                              :label="t('components.listing.buildingModel.floorLevels')"
                              :multiple="is3DModeEnabled"
                              class="me-5 ms-1"/>
                </template>
                <d-btn v-if="showFullScreenButton"
                       :class="{'mx-1': !hideControls}"
                       :icon="mdiFullscreen"
                       :size="isEmbedded && !hideControls ? 'default' : 'large'"
                       :to="{
                          name: 'listingBuildingScan',
                          params: {
                            id: listingId,
                          }
                       }"
                       :variant="hideControls ? 'elevated' : undefined"
                       type="tertiary"/>
            </v-layout>
            <d-l-bs-r3d v-if="is3DModeEnabled"
                        ref="renderer3D"
                        :needs-image-download="false"
                        :reload-key="0"
                        :show-signature="showSignature"
                        :signature-position="isEmbedded ? 'START' : undefined"
                        renderer-id="listing"
                        show-sky
                        show-text/>
            <d-l-bs-r2d v-else
                        ref="renderer2D"
                        :debug="debug"
                        :needs-image-download="false"
                        :reload-key="0"
                        :show-signature="showSignature"
                        :signature-position="isEmbedded ? 'START' : undefined"
                        renderer-id="listing"
                        show-text/>
        </div>
    </div>
    <!-- TODO: loading indicator? -->
</template>

<script lang="ts"
        setup>
    import {computed, inject, provide, ref, shallowRef, watchEffect} from "vue";
    import {useDisplay} from "vuetify";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {mdiCube, mdiCubeOff, mdiFullscreen, mdiLayers, mdiLayersOff, mdiVideo2d, mdiVideo3d} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DBtnToggle from "@/adapter/vuetify/theme/components/button/d-btn-toggle.vue";
    import DListingDetailToolbar from "@/components/listing/fragments/d-listing-detail-toolbar.vue";
    import DListingButtonBack from "@/components/listing/fragments/d-listing-button-back.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {DBuildingScanShowFurnitureInjection, DBuildingScanShowSlabsInjection, DBuildingScanVisibleFloorLevelsInjection, DEBUG_STAIR_CLUSTERS} from "@/components/listing/building-scan/building-scan";
    import {DListingBuildingScanRendererDefaultQueryVariables, ListingBuildingScan, useDListingBuildingScanRendererDefaultQuery} from "@/adapter/graphql/generated/graphql";
    import DLBsR3d from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d.vue";
    import DLBsR2d from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d.vue";
    import {emptySet} from "@/utility/set";
    import {DBtnSize} from "@/adapter/vuetify/theme/components/button/d-btn";
    import {DBtnToggleItem} from "@/adapter/vuetify/theme/components/button/d-btn-toggle";

    const props = withDefaults(defineProps<{
        showSignature?: boolean
        isEmbedded?: boolean
        hideControls?: boolean
        initializeWith3d?: boolean
        showFullScreenButton?: boolean
        fullScreenButtonSize?: DBtnSize
        debug?: boolean
    }>(), {
        showSignature: true,
        isEmbedded: false,
        hideControls: false,
        initializeWith3d: false,
        showFullScreenButton: false,
        fullScreenButtonSize: undefined,
        debug: false
    })

    const listingId = inject(LListingIdInjection)!

    const renderTypeItems: readonly DBtnToggleItem<boolean>[] = [{
        icon: mdiVideo2d,
        value: false,
    }, {
        icon: mdiVideo3d,
        value: true,
    }]

    const {smAndDown, xs} = useDisplay();
    const {t} = useI18n()

    const is3DModeEnabled = shallowRef<boolean>(props.initializeWith3d)
    const showFurniture = shallowRef<boolean>(DEBUG_STAIR_CLUSTERS)
    const showSlabs = shallowRef<boolean>(!DEBUG_STAIR_CLUSTERS)

    const selectedFloorLevelsInput = ref<number[] | number>(DEBUG_STAIR_CLUSTERS ? [0, 1, 2, 3, 4, 5] : [0]) //ref okay
    const selectedFloorLevels = computed<ReadonlySet<number>>(() => {
        const fLevels = floorLevels.value
        if (fLevels.size <= 1) {
            return fLevels
        }

        const selectedFloorLevels = selectedFloorLevelsInput.value

        if (Array.isArray(selectedFloorLevels)) {
            const validSelectedFloorLevels = selectedFloorLevels.filter(floorLevel => fLevels.has(floorLevel))

            if (is3DModeEnabled.value) {
                return new Set(validSelectedFloorLevels)
            }
            const selectedFloorLevel = validSelectedFloorLevels.length <= 0 ? 0 : validSelectedFloorLevels[0]
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            selectedFloorLevelsInput.value = selectedFloorLevel //TODO: <<<<<<<<<<<<<<<<< anders machen
            return new Set([selectedFloorLevel])
        }

        const validSelectedFloorLevel = fLevels.has(selectedFloorLevels) ? selectedFloorLevels : 0
        return new Set([validSelectedFloorLevel])
    })

    const sortedFloorLevels = computed<number[]>(() => {
        return [...floorLevels.value].sort((a, b) => a - b)
    })

    provide(DBuildingScanShowFurnitureInjection, showFurniture)
    provide(DBuildingScanShowSlabsInjection, showSlabs)
    provide(DBuildingScanVisibleFloorLevelsInjection, selectedFloorLevels)

    const renderer3D = shallowRef<Optional<typeof DLBsR3d>>(null)
    const renderer2D = shallowRef<Optional<typeof DLBsR2d>>(null)

    watchEffect(() => {
        if (!is3DModeEnabled.value) {
            showSlabs.value = true
        }
    })

    function toggleFurniture() {
        showFurniture.value = !showFurniture.value
    }

    function toggleSlabs() {
        showSlabs.value = !showSlabs.value
    }

    const queryVariables = computed<DListingBuildingScanRendererDefaultQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
    } = useDListingBuildingScanRendererDefaultQuery(queryVariables);

    const listingBuildingScan = computed<Optional<ListingBuildingScan>>(() => queryResult?.value?.listingBuildingScan as ListingBuildingScan ?? null); //TODO: view nur betreten, wenn building vorhanden

    const furnitureIcon = computed<string>(() => showFurniture.value ? mdiCube : mdiCubeOff)
    const slabsIcon = computed<string>(() => showSlabs.value ? mdiLayers : mdiLayersOff)

    const floorLevels = computed<ReadonlySet<number>>(() => {
        const b = listingBuildingScan.value
        if (b === null) {
            return emptySet()
        }
        return new Set(b.floorLevels)
    })
</script>

<style scoped>
    .contentWrapper {
        position: relative;
        height: 100%;
        width: 100%;
    }

    .modelWrapper {
        width: 100%;
        height: 100%;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
    }

    .modelWrapper.isEmbedded {
        padding-top: 0;
    }

    .controlsOverlay {
        right: 0;
        z-index: 1004;
    }

    .controlsOverlay.hideControls {
        top: 0;
    }

    .controlsOverlay:not(.hideControls) {
        background: rgb(var(--v-theme-on-surface-variant));
        bottom: 0;
    }
</style>