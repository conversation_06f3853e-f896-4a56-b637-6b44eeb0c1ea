<template>
    <t-group :transformation="textGroupTransformation"
             :visible="isVisible"
             is-billboard>
        <t-mesh :render-order="1000"
                :transformation="squareMeterTextTransformation">
            <t-text-geometry :depth="0"
                             :font="THREE_BUILDING_FONT"
                             :size="FONT_SIZE"
                             :text="n(squareMeters, 'squareMeters') + ' m²'"/>
            <!-- TODO: number formatter mit quadratmeter -->

            <t-mesh-basic-material :color="BUILDING_SCAN_3D_COLOR_TEXT"
                                   :depth-test="false"
                                   transparent/>
        </t-mesh>

        <t-mesh :render-order="1000"
                :transformation="labelTransformation">
            <t-text-geometry :depth="0"
                             :font="THREE_BUILDING_FONT"
                             :size="FONT_SIZE"
                             :text="label"/>

            <t-mesh-basic-material :color="BUILDING_SCAN_3D_COLOR_TEXT"
                                   :depth-test="false"
                                   transparent/>
        </t-mesh>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanRoom, ListingBuildingScanRoomInput, ListingBuildingScanSection} from "@/adapter/graphql/generated/graphql";
    import {Matrix4, Vector3} from "three";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {computed, inject, onMounted, toRef} from "vue";
    import {Optional} from "@/model/Optional";
    import TTextGeometry from "@/adapter/three/components/geometry/t-text-geometry.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {useI18n} from "vue-i18n";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {BUILDING_SCAN_3D_COLOR_TEXT} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {centerVectorOfSection, DBuildingScanVisibleFloorLevelsInjection, useRoomData} from "@/components/listing/building-scan/building-scan";
    import {useRoomName} from "@/components/listing/building/building";
    import {loadThreeBuildingFont, THREE_BUILDING_FONT} from "@/components/listing/three-building-resources";

    //TODO: refactoring notwendig, überschnneidungen zwischen 2d/3d

    const FONT_SIZE = 0.15

    const props = defineProps<{
        room: ListingBuildingScanRoom | ListingBuildingScanRoomInput
        roomIndex: number
    }>()

    onMounted(() => {
        loadThreeBuildingFont()
    })

    const propsRoom = toRef(() => props.room)
    const propsRoomIndex = toRef(() => props.roomIndex)
    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.room.floorLevel))

    const {
        squareMeters
    } = useRoomData(toRef(() => props.room))

    const {t, n} = useI18n()
    const label = useRoomName(propsRoom, propsRoomIndex, t, n)

    const mainSection = computed<Optional<ListingBuildingScanSection>>(() => props.room.sections.length <= 0 ? null : props.room.sections[0]) //TODO: das muss noch besser werden, dafür gibt es auch ein ticke)
    const textPosition = computed<Vector3>(() => {
        if (mainSection.value === null) {
            return new Vector3()
        }
        return centerVectorOfSection(mainSection.value!)
    })

    const textGroupTransformation = computed<Matrix4>(() => {
        const position = textPosition.value

        const translationMatrix = new Matrix4().makeTranslation(
            position.x,
            position.y,
            position.z
        )

        // const rotationMatrix = new Matrix4().makeRotationY(-deskewRadians.value)

        return translationMatrix//.multiply(rotationMatrix)
    })

    const squareMeterTextTransformation = computed<Matrix4>(() => {
        return new Matrix4().makeTranslation(
            0,
            -FONT_SIZE,
            0
        )
    })

    const labelTransformation = computed<Matrix4>(() => {
        return new Matrix4().makeTranslation(
            0,
            FONT_SIZE,
            0
        )
    })
</script>

<style scoped>
</style>