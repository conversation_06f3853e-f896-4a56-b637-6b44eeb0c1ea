<template>
    <t-mesh :transformation="transformation"
            :visible="isVisible"
            cast-shadow
            receive-shadow>
        <t-sphere-geometry :radius="0.125"/>

        <t-mesh-basic-material :color="0xFF0000"/>
    </t-mesh>

    <!-- TODO: parent beziehung -->
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScan, ListingBuildingScanInput, ListingBuildingScanPointOfInterest} from "@/adapter/graphql/generated/graphql";
    import {computed, inject} from "vue";
    import {Matrix4} from "three";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {transformationMatrixOfArray} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import TSphereGeometry from "@/adapter/three/components/geometry/t-sphere-geometry.vue";
    import {calculateFloorLevelToPoiMapFromBuildingScan, DBuildingScanVisibleFloorLevelsInjection} from "@/components/listing/building-scan/building-scan";

    const props = defineProps<{
        building: ListingBuildingScan | ListingBuildingScanInput,
        pointOfInterest: ListingBuildingScanPointOfInterest
    }>()

    const transformation = computed<Matrix4>(() => transformationMatrixOfArray(props.pointOfInterest.transformation))

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => {
        const floorLevelToPoiMap = calculateFloorLevelToPoiMapFromBuildingScan(props.building)
        const entry = floorLevelToPoiMap.entries().find(([floorLevel, pois]) => pois.some(poi => poi.id === props.pointOfInterest.id))
        return entry !== undefined && visibleFloorLevels.value.has(entry[0])
    })
</script>

<style scoped>

</style>