<template>
    <!-- WALL -->
    <t-mesh :key="renderKey"
            :raycaster-ids="raycasterIds"
            :raycaster-object="wallWithHoles"
            :render-order="isIntersectingWithFocusedRoomMesh ? 102 : undefined"
            :scalar="BUILDING_SCAN_3D_WALL_SCALAR + (isOcclusionCulled ? -0.001 : 0)"
            :transformation="transformation"
            :visible="isVisible"
            cast-shadow
            receive-shadow>
        <t-ring-geometry-with-depth v-if="wall.arc != null"
                                    :center-x="wall.arc.center[0]"
                                    :center-z="wall.arc.center[1]"
                                    :depth="depth"
                                    :end-angle="wall.arc.endAngle"
                                    :height="wall.boundingBox[1]"
                                    :inner-radius="wall.arc.radius - depth / 2"
                                    :outer-radius="wall.arc.radius + depth / 2"
                                    :start-angle="wall.arc.startAngle"/>
        <t-shape-geometry-with-depth v-else-if="polygonVectors.length > 0"
                                     :depth="depth"
                                     :points="polygonVectors"/>
        <t-box-geometry v-else
                        :depth="depth"
                        :height="height"
                        :width="width"/>

        <t-mesh-material-raw :material="isOcclusionCulled ? BUILDING_SCAN_3D_MATERIAL_WALL_CLIPPED : (isIntersectingWithFocusedRoomMesh ? BUILDING_SCAN_3D_MATERIAL_WALL_FOCUSED : BUILDING_SCAN_3D_MATERIAL_WALL_DEFAULT)"/>

        <!-- HOLES -->
        <t-mesh v-for="holeSurface in holeSurfaces"
                :key="holeSurface.id"
                :transformation="holeTransformation(holeSurface)"
                hole>
            <!-- einfache breite reicht auch, aber sicher ist sicher, sonst entsteht kein loch -->
            <t-ring-geometry-with-depth v-if="holeSurface.arc != null"
                                        :center-x="holeSurface.arc.center[0]"
                                        :center-z="holeSurface.arc.center[1]"
                                        :depth="depth * 2"
                                        :end-angle="holeSurface.arc.endAngle"
                                        :height="holeSurface.boundingBox[1]"
                                        :inner-radius="holeSurface.arc.radius - depth"
                                        :outer-radius="holeSurface.arc.radius + depth"
                                        :start-angle="holeSurface.arc.startAngle"/>

            <!-- einfache breite reicht auch, aber sicher ist sicher, sonst entsteht kein loch -->
            <t-box-geometry v-else
                            :depth="depth * 2"
                            :height="holeSurface.boundingBox[1]"
                            :width="holeSurface.boundingBox[0]"/>

            <t-mesh-standard-material :color="0xFF00FF"
                                      :opacity="0.5"
                                      transparent/>
        </t-mesh>
    </t-mesh>

    <!-- OCCLUSION CULLING -->
    <t-mesh :key="renderKey"
            :raycaster-ids="raycasterIds"
            :raycaster-object="wallWithoutHoles"
            :scalar="BUILDING_SCAN_3D_WALL_SCALAR"
            :transformation="transformation"
            :visible="false">
        <t-ring-geometry-with-depth v-if="wall.arc != null"
                                    :center-x="wall.arc.center[0]"
                                    :center-z="wall.arc.center[1]"
                                    :depth="depth"
                                    :end-angle="wall.arc.endAngle"
                                    :height="wall.boundingBox[1]"
                                    :inner-radius="wall.arc.radius - depth / 2"
                                    :outer-radius="wall.arc.radius + depth / 2"
                                    :start-angle="wall.arc.startAngle"/>
        <t-shape-geometry-with-depth v-else-if="polygonVectors.length > 0"
                                     :depth="depth"
                                     :points="polygonVectors"/>
        <t-box-geometry v-else
                        :depth="depth"
                        :height="height"
                        :width="width"/>
    </t-mesh>

    <t-mesh-raw v-if="isIntersectingWithFocusedRoomMesh && BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM"
                :mesh="intersectionMesh"/>
</template>

<script lang="ts"
        setup>
    import {MathUtils, Matrix4, Mesh} from "three";
    import TMeshStandardMaterial from "@/adapter/three/components/material/t-mesh-standard-material.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, shallowRef, toRef, watch} from "vue";
    import {BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM, BUILDING_SCAN_3D_DEFAULT_THICKNESS, BUILDING_SCAN_3D_MATERIAL_WALL_CLIPPED, BUILDING_SCAN_3D_MATERIAL_WALL_DEFAULT, BUILDING_SCAN_3D_MATERIAL_WALL_FOCUSED, BUILDING_SCAN_3D_WALL_SCALAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, transformationMatrixOfSurface, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import TRingGeometryWithDepth from "@/adapter/three/components/geometry/t-ring-geometry-with-depth.vue";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import TShapeGeometryWithDepth from "@/adapter/three/components/geometry/t-shape-geometry-with-depth.vue";
    import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
    import {Optional} from "@/model/Optional";
    import {createOcclusionCullingConsumer, createOcclusionCullingEmitter, TOcclusionCullingRaycasterConsumer, TOcclusionCullingRaycasterEmitter, TOcclusionCullingRelated} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycasterEmitterOrConsumer";
    import TMeshMaterialRaw from "@/adapter/three/components/material/t-mesh-material-raw.vue";
    import {emptySet} from "@/utility/set";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";

    const props = defineProps<{
        wall: ListingBuildingScanSurface
        surfaces: readonly ListingBuildingScanSurface[]
        occlusionCullingRaycaster: Optional<TOcclusionCullingRaycaster<ListingBuildingScanSurface>>
        focusedRoomMesh: Optional<Mesh>
    }>()

    const idWithHoles = computed<string>(() => `${props.wall.id}-with-holes`)
    const idWithoutHoles = computed<string>(() => `${props.wall.id}-without-holes`)

    const wallWithHoles = computed<ListingBuildingScanSurface & TOcclusionCullingRelated<TOcclusionCullingRaycasterConsumer<ListingBuildingScanSurface>>>(() => {
        const id = idWithHoles.value
        const noHolesId = idWithoutHoles.value

        return {
            ...props.wall,
            occlusionCulling: createOcclusionCullingConsumer(id, props.wall, [noHolesId]),
        }
    })

    const wallWithoutHoles = computed<ListingBuildingScanSurface & TOcclusionCullingRelated<TOcclusionCullingRaycasterEmitter<ListingBuildingScanSurface>>>(() => {
        const id = idWithoutHoles.value

        return {
            ...props.wall,
            occlusionCulling: createOcclusionCullingEmitter(id, props.wall),
        }
    })

    const isOcclusionCulled = computed<boolean>(() => wallWithHoles.value.occlusionCulling.isOcclusionCulled.value && !isIntersectingWithFocusedRoomMesh.value)

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.wall.floorLevel))

    const {
        width,
        height,
        depth: rawDepth,
        transformation: rawTransformation,
        polygonVectors,
        intersectionMesh,
        isIntersectingWithFocusedRoomMesh,
        destroyRoomDataSurface,
    } = useRoomDataSurface(
        toRef(() => props.wall),
        toRef(() => props.focusedRoomMesh)
    )

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const depth = computed<number>(() => Math.max(BUILDING_SCAN_3D_DEFAULT_THICKNESS, rawDepth.value))

    const transformation = computed<Matrix4>(() => {
        if (props.wall.arc == null) {
            return rawTransformation.value.clone()
        }

        const center = props.wall.arc.center

        const translation = new Matrix4().makeTranslation(center[0], -height.value / 2, center[1])

        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));

        return rawTransformation.value.clone().multiply(translation).multiply(rotationMatrixX)
    })

    //Sollte sich die Wall ändern (update), müssen die holeSurfaces (und damit die wall) nicht neu gerendert werden, wenn die id gleich geblieben ist
    const wallId = computed<string>(() => props.wall.id)

    const holeSurfaces = computed<readonly ListingBuildingScanSurface[]>(() => {
        return props.surfaces.filter(surface => surface.parentId === wallId.value)
    })

    const renderKey = shallowRef<number>(0)
    watch([() => props.wall, holeSurfaces], () => { //wir müssen die wand neu zeichnen, wegen der geometrie der löcher, sobald sich etwas daran ändert
        ++renderKey.value
    })

    function holeTransform(holeSurface: ListingBuildingScanSurface) {
        const holeTransformationMatrix = transformationMatrixOfSurface(holeSurface)

        if (holeSurface.arc == null) {
            return holeTransformationMatrix.clone()
        }

        const center = holeSurface.arc.center
        const holeHeight = holeSurface.boundingBox[1]

        const translation = new Matrix4().makeTranslation(center[0], -holeHeight / 2, center[1])

        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));

        return holeTransformationMatrix.clone().multiply(translation).multiply(rotationMatrixX)
    }

    function holeTransformation(holeSurface: ListingBuildingScanSurface): Matrix4 {
        const holeTransformationMatrix = holeTransform(holeSurface)
        return transformation.value.clone().invert().multiply(holeTransformationMatrix)
    }

    const raycasterIds = computed<ReadonlySet<string>>(() => props.occlusionCullingRaycaster === null ? emptySet() : new Set([props.occlusionCullingRaycaster.id]))
</script>

<style scoped>
</style>