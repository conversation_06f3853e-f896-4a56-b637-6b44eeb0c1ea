<template>
    <t-group :visible="isVisible">
        <!-- DOOR -->
        <t-lines :color="BUILDING_SCAN_2D_COLOR_WALL"
                 :depth-test="false"
                 :render-order="BUILDING_SCAN_2D_Z_INDEX_DOOR_LINES"
                 :transformation="lineTransformation"
                 :vertices="lineCircleVertices"
                 reset-y
                 type="LINE"/>

        <t-lines :color="BUILDING_SCAN_2D_COLOR_WALL"
                 :depth-test="false"
                 :render-order="BUILDING_SCAN_2D_Z_INDEX_DOOR_LINES"
                 :transformation="lineTransformation"
                 :vertices="lineVertices"
                 reset-y
                 type="LINE"/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {EllipseCurve, MathUtils, Matrix4, Vector3} from "three";
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import TLines from "@/adapter/three/components/object/t-lines.vue";
    import {BUILDING_SCAN_2D_COLOR_WALL, BUILDING_SCAN_2D_Z_INDEX_DOOR_LINES} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import TGroup from "@/adapter/three/components/object/t-group.vue";

    const props = defineProps<{
        door: ListingBuildingScanSurface
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.door.floorLevel))

    const {
        width,
        transformation,
        destroyRoomDataSurface,
    } = useRoomDataSurface(toRef(() => props.door), toRef(null))

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const lineTransformation = computed<Matrix4>(() => {
        const translationMatrix = new Matrix4().makeTranslation(
            -width.value / 2,
            0,
            0,
        )

        return transformation.value.clone().multiply(translationMatrix)
    })

    const lineCircleVertices = computed<Vector3[]>(() => {
        const divisions = 50;
        const size = width.value
        const degree = 90
        const degreeInRadians = MathUtils.degToRad(degree)

        const curve = new EllipseCurve(
            0,
            0,
            size,
            size,
            0,
            degreeInRadians,
            false,
            0
        );

        return curve
            .getPoints(divisions)
            .map(point => new Vector3(point.x, 0, point.y))
    })

    const lineVertices = computed<Vector3[]>(() => {
        const size = width.value

        return [
            new Vector3(0, 0, 0),
            new Vector3(0, 0, size),
        ]
    })
</script>

<style scoped>
</style>