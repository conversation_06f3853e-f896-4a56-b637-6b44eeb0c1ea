import {InjectionKey, Ref} from "vue";

export const BUILDING_SCAN_2D_DEFAULT_THICKNESS = 0.11 //in meters (11cm), sollte kleiner als der Default-<PERSON><PERSON> von <PERSON> sein (11,81cm)

export const BUILDING_SCAN_2D_STAIRS_STEP_LINE_THICKNESS = 0.015 //in meters (1,5 cm)
export const BUILDING_SCAN_2D_STAIRS_SIDE_LINE_THICKNESS = 0.03 //in meters (3 cm)
export const BUILDING_SCAN_2D_STAIRS_ICON_SIZE = 0.5 //in meters (50 cm)

export const BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_TEXT = 0.2
export const BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_STAIRS_ICON = 0.1

export const BUILDING_SCAN_2D_COLOR_FURNITURE = 0x0095D0
export const BUILDING_SCAN_2D_COLOR_FURNITURE_OUTLINE = 0x08577D
export const BUILDING_SCAN_2D_COLOR_WALL = 0x444A69
export const BUILDING_SCAN_2D_COLOR_TEXT = 0x000000
export const BUILDING_SCAN_2D_COLOR_FLOOR = 0xE8E8E8

export const BUILDING_SCAN_2D_Z_INDEX_FLOOR = 1
export const BUILDING_SCAN_2D_Z_INDEX_ROOM_FLOOR = 2
export const BUILDING_SCAN_2D_Z_INDEX_OBJECT = 3
export const BUILDING_SCAN_2D_Z_INDEX_OBJECT_BORDER = 4
export const BUILDING_SCAN_2D_Z_INDEX_STAIRS_STEP = 5
export const BUILDING_SCAN_2D_Z_INDEX_STAIRS_SIDES = 6
export const BUILDING_SCAN_2D_Z_INDEX_WALL = 7
export const BUILDING_SCAN_2D_Z_INDEX_WINDOW = 8
export const BUILDING_SCAN_2D_Z_INDEX_WINDOW_LINES = 9
export const BUILDING_SCAN_2D_Z_INDEX_DOOR = 10
export const BUILDING_SCAN_2D_Z_INDEX_DOOR_LINES = 11
export const BUILDING_SCAN_2D_Z_INDEX_SELECTED_DOOR = 12
export const BUILDING_SCAN_2D_Z_INDEX_SELECTED_OPENING = 13
export const BUILDING_SCAN_2D_Z_INDEX_STAIRS_ICON = 14
export const BUILDING_SCAN_2D_Z_INDEX_SECTION_TEXT = 15
export const BUILDING_SCAN_2D_Z_INDEX_SELECTION = 16
export const BUILDING_SCAN_2D_Z_INDEX_HOVER_LINES = 17
export const BUILDING_SCAN_2D_Z_INDEX_HOVER_TEXT = 18

export const LBSR2DDeskewAngleInjection: InjectionKey<Readonly<Ref<number>>> = Symbol('lBSR2DDeskewAngle')
