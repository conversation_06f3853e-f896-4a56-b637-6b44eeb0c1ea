<template>
    <t-mesh :scalar="BUILDING_SCAN_3D_OBJECT_SCALAR"
            :transformation="transformation"
            :visible="isVisible"
            cast-shadow
            receive-shadow>
        <t-stairs-geometry :depth="depth"
                           :height="height"
                           :width="width"/>

        <t-mesh-standard-material :color="0x848BA5"
                                  :metalness="0"
                                  :roughness="0.5"/>
    </t-mesh>
</template>

<script lang="ts"
        setup>
    import TMeshStandardMaterial from "@/adapter/three/components/material/t-mesh-standard-material.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import {ListingBuildingScanObject} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, toRef} from "vue";
    import TStairsGeometry from "@/adapter/three/components/geometry/t-stairs-geometry.vue";
    import {BUILDING_SCAN_3D_OBJECT_SCALAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataObject} from "@/components/listing/building-scan/building-scan";

    const props = defineProps<{
        object: ListingBuildingScanObject
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.object.floorLevel))

    const {
        width,
        height,
        depth,
        transformation
    } = useRoomDataObject(toRef(() => props.object))
</script>

<style scoped>

</style>