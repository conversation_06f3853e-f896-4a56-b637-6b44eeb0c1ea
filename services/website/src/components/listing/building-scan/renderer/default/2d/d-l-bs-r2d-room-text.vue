<template>
    <t-group :transformation="textGroupTransformation"
             :visible="isVisible"
             is-billboard>
        <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_SECTION_TEXT"
                :transformation="squareMeterTextTransformation">
            <t-text-geometry :depth="0"
                             :font="THREE_BUILDING_FONT"
                             :size="FONT_SIZE"
                             :text="n(squareMeters, 'squareMeters') + ' m²'"/>
            <!-- TODO: number formatter mit quadratmeter -->

            <t-mesh-basic-material :color="BUILDING_SCAN_2D_COLOR_TEXT"/>
        </t-mesh>

        <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_SECTION_TEXT"
                :transformation="labelTransformation">
            <t-text-geometry :depth="0"
                             :font="THREE_BUILDING_FONT"
                             :size="FONT_SIZE"
                             :text="label"/>

            <t-mesh-basic-material :color="BUILDING_SCAN_2D_COLOR_TEXT"/>
        </t-mesh>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanRoom, ListingBuildingScanSection} from "@/adapter/graphql/generated/graphql";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TTextGeometry from "@/adapter/three/components/geometry/t-text-geometry.vue";
    import {computed, inject, onMounted, toRef} from "vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {Optional} from "@/model/Optional";
    import {Matrix4, Vector3} from "three";
    import {useI18n} from "vue-i18n";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {centerVectorOfSection, DBuildingScanVisibleFloorLevelsInjection, useRoomData} from "@/components/listing/building-scan/building-scan";
    import {BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_TEXT, BUILDING_SCAN_2D_COLOR_TEXT, BUILDING_SCAN_2D_Z_INDEX_SECTION_TEXT, LBSR2DDeskewAngleInjection} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {useRoomName} from "@/components/listing/building/building";
    import {loadThreeBuildingFont, THREE_BUILDING_FONT} from "@/components/listing/three-building-resources";

    //TODO: refactoring notwendig, überschnneidungen zwischen 2d/3d

    const FONT_SIZE = 0.15

    const deskewRadians = inject(LBSR2DDeskewAngleInjection)!

    const props = defineProps<{
        room: ListingBuildingScanRoom
        roomIndex: number
    }>()

    const propsRoom = toRef(() => props.room)
    const propsRoomIndex = toRef(() => props.roomIndex)
    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.room.floorLevel))

    onMounted(() => {
        loadThreeBuildingFont()
    })

    const {
        squareMeters
    } = useRoomData(propsRoom)

    const mainSection = computed<Optional<ListingBuildingScanSection>>(() => props.room.sections.length <= 0 ? null : props.room.sections[0]) //TODO: das muss noch besser werden, dafür gibt es auch ein ticke)
    const textPosition = computed<Vector3>(() => {
        if (mainSection.value === null) {
            return new Vector3()
        }
        return centerVectorOfSection(mainSection.value!)
    })

    const {t, n} = useI18n()
    const label = useRoomName(propsRoom, propsRoomIndex, t, n)

    const textGroupTransformation = computed<Matrix4>(() => {
        const position = textPosition.value

        const translationMatrix = new Matrix4().makeTranslation(
            position.x,
            BUILDING_SCAN_2D_BILLBOARD_Y_FIGHTING_DISTANCE_TEXT,
            position.z
        )

        const rotationMatrix = new Matrix4().makeRotationY(-deskewRadians.value)
        return translationMatrix.multiply(rotationMatrix)
    })

    const squareMeterTextTransformation = computed<Matrix4>(() => {
        return new Matrix4().makeTranslation(
            0,
            -FONT_SIZE,
            0
        )
    })

    const labelTransformation = computed<Matrix4>(() => {
        return new Matrix4().makeTranslation(
            0,
            FONT_SIZE,
            0
        )
    })
</script>

<style scoped>
</style>