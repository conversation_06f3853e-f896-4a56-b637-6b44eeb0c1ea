<template>
    <t-mesh :render-order="isIntersectingWithFocusedRoomMesh ? 103 : undefined"
            :transformation="transformation"
            :visible="isVisible">
        <t-box-geometry :depth="depth"
                        :height="height"
                        :width="width"/>

        <t-mesh-basic-material :color="0x009F64"
                               :depth-test="isIntersectingWithFocusedRoomMesh ? false : undefined"
                               :transparent="isIntersectingWithFocusedRoomMesh"/>
    </t-mesh>

    <t-mesh-raw v-if="isIntersectingWithFocusedRoomMesh && BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM"
                :mesh="intersectionMesh"/>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import {BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM, BUILDING_SCAN_3D_DEFAULT_THICKNESS} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import {Optional} from "@/model/Optional";
    import {Mesh} from "three";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";

    const props = defineProps<{
        door: ListingBuildingScanSurface
        focusedRoomMesh: Optional<Mesh>
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.door.floorLevel))

    const {
        width,
        height,
        depth: rawDepth,
        transformation,
        intersectionMesh,
        isIntersectingWithFocusedRoomMesh,
        destroyRoomDataSurface,
    } = useRoomDataSurface(
        toRef(() => props.door),
        toRef(() => props.focusedRoomMesh)
    )

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const depth = computed<number>(() => Math.max(BUILDING_SCAN_3D_DEFAULT_THICKNESS, rawDepth.value))
</script>

<style scoped>

</style>