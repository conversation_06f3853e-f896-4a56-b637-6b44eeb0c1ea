<template>
    <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_FLOOR"
            :transformation="transformation"
            :visible="isVisible"
            reset-y>
        <t-plane-geometry v-if="polygonVectors.length <= 0"
                          :height="depth"
                          :width="width"/>
        <t-shape-geometry v-else
                          :points="polygonVectors"/>

        <t-mesh-basic-material :color="BUILDING_SCAN_2D_COLOR_FLOOR"
                               :depth-test="false"/>
    </t-mesh>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanObject, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import TShapeGeometry from "@/adapter/three/components/geometry/t-shape-geometry.vue";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {BUILDING_SCAN_2D_COLOR_FLOOR, BUILDING_SCAN_2D_Z_INDEX_FLOOR} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import {MathUtils, Matrix4} from "three";

    const props = defineProps<{
        floor: ListingBuildingScanSurface
        objects: readonly ListingBuildingScanObject[]
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.floor.floorLevel))

    const {
        width,
        depth,
        transformation: rawTransformation,
        polygonVectors,
        destroyRoomDataSurface,
    } = useRoomDataSurface(toRef(() => props.floor), toRef(null))

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const transformation = computed<Matrix4>(() => {
        const rotationXMatrix = new Matrix4().makeRotationX(MathUtils.degToRad(-90))
        return rawTransformation.value.clone().multiply(rotationXMatrix)
    })

    const stairsOfLowerFloorLevel = computed<ListingBuildingScanObject[]>(() => {
        return props.objects.filter(o => o.category === 'STAIRS' && o.floorLevel === props.floor.floorLevel - 1)
    })
</script>

<style scoped>
</style>