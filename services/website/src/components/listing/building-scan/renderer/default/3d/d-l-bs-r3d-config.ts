import {DoubleS<PERSON>, Matrix4, <PERSON><PERSON>, MeshBasicMaterial, MeshStandardMaterial, MeshStandardMaterialParameters} from "three";
import {createShapeRepresentationFromSurface, shapeToBufferedGeometry_000} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
import {transformationMatrixOfSurface} from "@/components/listing/building-scan/building-scan";
import {tDestroyMesh} from "@/adapter/three/three-utility";
import {csgCreateBrush_000_GM, csgCreateEvaluator} from "@/adapter/csg/csg-utils";
import {INTERSECTION} from "three-bvh-csg";

export const BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM = false

export const BUILDING_SCAN_3D_DEFAULT_THICKNESS = 0.01 //in meters (1 cm)

export const BUILDING_SCAN_3D_COLOR_TEXT = 0x000000 //black

export const BUILDING_SCAN_3D_CAMERA_FAR = 50

//ANTI Z-FIGHTING
export const BUILDING_SCAN_3D_FLOOR_SCALAR = 1
export const BUILDING_SCAN_3D_WALL_SCALAR = 1
export const BUILDING_SCAN_3D_OBJECT_SCALAR = 0.999

export const BUILDING_SCAN_3D_RENDER_ORDER_DEBUG = 10_000

const BUILDING_SCAN_3D_MATERIAL_WALL: MeshStandardMaterialParameters = {
    color: 0xFFFFFF,
    roughness: 0.5,
    metalness: 0,
    side: DoubleSide,
    transparent: true, //nochmal für prüfen, ob es für default notwendig ist
}
export const BUILDING_SCAN_3D_MATERIAL_WALL_DEFAULT = new MeshStandardMaterial({
    ...BUILDING_SCAN_3D_MATERIAL_WALL,
})
export const BUILDING_SCAN_3D_MATERIAL_WALL_FOCUSED = new MeshBasicMaterial({
    color: 0xFFCF2E,
    transparent: true,
    depthTest: false,
})
export const BUILDING_SCAN_3D_MATERIAL_WALL_CLIPPED = new MeshStandardMaterial({
    ...BUILDING_SCAN_3D_MATERIAL_WALL,
    opacity: 0.5
})

// noinspection FunctionNamingConventionJS
export function surfaceToTestMesh_000_GM(surface: ListingBuildingScanSurface): Mesh {
    const shape = createShapeRepresentationFromSurface(surface, false)
    const geometry = shapeToBufferedGeometry_000(shape, false, false, 0.1) //10cm
    const material = new MeshBasicMaterial({
        color: 0xFF0000,
        transparent: true,
        opacity: 0.5,
        depthTest: false
    })
    const mesh = new Mesh(geometry, material)
    const baseTransformation = transformationMatrixOfSurface(surface)
    const rotationMatrix = surface.category === "FLOOR" ? new Matrix4().makeRotationX(-Math.PI / 2) : new Matrix4().identity()
    const transformation = baseTransformation.multiply(rotationMatrix)
    switch (surface.category) {
        case "FLOOR":
            mesh.renderOrder = 1001
            break
        case "WALL":
            mesh.renderOrder = 1002
            break
        case "WINDOW":
            mesh.renderOrder = 1003
            break
        case "DOOR":
            mesh.renderOrder = 1004
            break
    }
    mesh.applyMatrix4(transformation)
    return mesh
}

export function areMeshesIntersecting(mesh1: Mesh, mesh2: Mesh): boolean {
    // noinspection LocalVariableNamingConventionJS
    const brush1_000_GM = csgCreateBrush_000_GM(mesh1)
    if (brush1_000_GM === null) {
        return false
    }
    // noinspection LocalVariableNamingConventionJS
    const brush2_000_GM = csgCreateBrush_000_GM(mesh2)
    if (brush2_000_GM === null) {
        tDestroyMesh(brush1_000_GM, true, true)
        return false
    }

    try {
        // noinspection LocalVariableNamingConventionJS
        const intersectionBrush_000_GM = csgCreateEvaluator().evaluate(brush1_000_GM, brush2_000_GM, INTERSECTION)

        const areMeshesIntersecting = intersectionBrush_000_GM.geometry.attributes.position.array.length > 0

        tDestroyMesh(intersectionBrush_000_GM, true, true)

        return areMeshesIntersecting
    } catch (e) {
        console.error("Error while checking if meshes are intersecting", e)
        return false
    } finally {
        tDestroyMesh(brush1_000_GM, true, true)
        tDestroyMesh(brush2_000_GM, true, true)
    }
}