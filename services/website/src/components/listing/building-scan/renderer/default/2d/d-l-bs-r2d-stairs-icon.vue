<template>
    <t-group :transformation="stairLocation.iconTransformation"
             :visible="isVisible"
             is-billboard>
        <t-mesh :render-order="BUILDING_SCAN_2D_Z_INDEX_STAIRS_ICON">
            <t-plane-geometry :height="BUILDING_SCAN_2D_STAIRS_ICON_SIZE"
                              :width="BUILDING_SCAN_2D_STAIRS_ICON_SIZE"/>

            <t-mesh-basic-material>
                <t-texture :texture-url="stairDirectionToTextureURL(stairLocation.direction)"/>
            </t-mesh-basic-material>
        </t-mesh>
    </t-group>
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TPlaneGeometry from "@/adapter/three/components/geometry/t-plane-geometry.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import TTexture from "@/adapter/three/components/t-texture.vue";
    import {BUILDING_SCAN_2D_STAIRS_ICON_SIZE, BUILDING_SCAN_2D_Z_INDEX_STAIRS_ICON} from "@/components/listing/building-scan/renderer/default/2d/d-l-bs-r2d-config";
    import {stairDirectionToTextureURL} from "@/components/listing/building-scan/StairDirection";
    import {StairLocation2D} from "@/components/listing/building-scan/StairLocation2D";
    import {computed, inject} from "vue";
    import {DBuildingScanVisibleFloorLevelsInjection} from "@/components/listing/building-scan/building-scan";

    const props = defineProps<{
        stairLocation: StairLocation2D
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.stairLocation.floorLevel))
</script>

<style scoped>
</style>