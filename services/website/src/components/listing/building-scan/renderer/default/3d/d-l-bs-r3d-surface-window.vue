<template>
    <!-- LIGHT -->
    <!--    <t-rect-area-light :color="BUILDING_LIGHT_DAYLIGHT"-->
    <!--                       :height="height"-->
    <!--                       :intensity="5"-->
    <!--                       :transformation="lightTransformation"-->
    <!--                       :visible="showLight && isVisible"-->
    <!--                       :width="width"/>-->

    <t-mesh :render-order="isIntersectingWithFocusedRoomMesh ? 104 : undefined"
            :transformation="transformation"
            :visible="isVisible">
        <t-box-geometry :depth="depth"
                        :height="height"
                        :width="width"/>

        <t-mesh-basic-material :color="0x0095D0"
                               :depth-test="isIntersectingWithFocusedRoomMesh ? false : undefined"
                               :opacity="0.75"
                               transparent/>
    </t-mesh>

    <t-mesh-raw v-if="isIntersectingWithFocusedRoomMesh && BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM"
                :mesh="intersectionMesh"/>
</template>

<script lang="ts"
        setup>
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {DBuildingScanVisibleFloorLevelsInjection, useRoomDataSurface} from "@/components/listing/building-scan/building-scan";
    import {BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM, BUILDING_SCAN_3D_DEFAULT_THICKNESS} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import {Optional} from "@/model/Optional";
    import {Mesh} from "three";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";

    // const LIGHT_WIDTH_OFFSET = 0.001

    const props = defineProps<{
        window: ListingBuildingScanSurface
        focusedRoomMesh: Optional<Mesh>
    }>()

    const visibleFloorLevels = inject(DBuildingScanVisibleFloorLevelsInjection)!
    const isVisible = computed<boolean>(() => visibleFloorLevels.value.has(props.window.floorLevel))

    // const showLight = inject(DBuildingScanShowLightInjection)!

    const {
        width,
        height,
        depth: rawDepth,
        transformation,
        intersectionMesh,
        isIntersectingWithFocusedRoomMesh,
        destroyRoomDataSurface,
    } = useRoomDataSurface(
        toRef(() => props.window),
        toRef(() => props.focusedRoomMesh)
    )

    onUnmounted(() => {
        destroyRoomDataSurface()
    })

    const depth = computed<number>(() => Math.max(BUILDING_SCAN_3D_DEFAULT_THICKNESS, rawDepth.value))

    // const lightTransformation = computed<Matrix4>(() => {
    //     const translation = new Matrix4().makeTranslation(0, 0, BUILDING_SCAN_3D_DEFAULT_THICKNESS / 2 + LIGHT_WIDTH_OFFSET)
    //     const rotation = new Matrix4().makeRotationY(MathUtils.degToRad(180))
    //     return transformation.value.clone().multiply(translation).multiply(rotation)
    // })
</script>

<style scoped>

</style>