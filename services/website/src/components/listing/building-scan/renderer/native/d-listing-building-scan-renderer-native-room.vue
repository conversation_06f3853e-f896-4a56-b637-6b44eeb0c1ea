<template>
    <div class="w-100 h-100">
        <d-l-bs-nr3d-room :render-state="renderState"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {onMounted, onUnmounted, shallowRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import DLBsNr3dRoom from "@/components/listing/building-scan/renderer/native/3d/d-l-bs-nr3d-room.vue";
    import {NativeAppRoomRenderState} from "@/components/listing/building-scan/renderer/native/NativeAppRoomRenderState";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";

    const renderState = shallowRef<Optional<NativeAppRoomRenderState>>(null)
    const nativeAppMethod = "renderNativeAppRoom"

    onMounted(() => {
        createNativeAppMethod(nativeAppMethod, (newRenderState: Optional<NativeAppRoomRenderState>) => {
            renderState.value = newRenderState
        })
        NATIVE_APP_SERVICE?.onRoomRendererIsReady()
    })

    onUnmounted(() => {
        destroyNativeAppMethod(nativeAppMethod)
    })
</script>

<style scoped>
</style>