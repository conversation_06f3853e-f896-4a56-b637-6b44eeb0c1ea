<template>
    <div class="w-100 h-100">
        <d-l-bs-nr3d-building :render-state="renderState"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {onMounted, onUnmounted, shallowRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {NativeAppBuildingRenderState} from "@/components/listing/building-scan/renderer/native/NativeAppBuildingRenderState";
    import DLBsNr3dBuilding from "@/components/listing/building-scan/renderer/native/3d/d-l-bs-nr3d-building.vue";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";

    const renderState = shallowRef<Optional<NativeAppBuildingRenderState>>(null)
    const nativeAppMethod = "renderNativeAppBuilding"

    onMounted(() => {
        createNativeAppMethod(nativeAppMethod, (newRenderState: Optional<NativeAppBuildingRenderState>) => {
            renderState.value = newRenderState
        })
        NATIVE_APP_SERVICE?.onBuildingRendererIsReady()
    })

    onUnmounted(() => {
        destroyNativeAppMethod(nativeAppMethod)
    })
</script>

<style scoped>
</style>