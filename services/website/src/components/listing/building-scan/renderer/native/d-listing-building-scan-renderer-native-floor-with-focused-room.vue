<template>
    <div class="w-100 h-100">
        <d-l-bs-nr3d-floor-with-focused-room :render-state="renderState"/>

        <d-snackbar v-model="isSnackbarVisible"
                    :text="t('nativeApp.renderer.floorWithFocusedRoom.newFloorMessage')"
                    location="top center"
                    type="info"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {onMounted, onUnmounted, shallowRef, watch} from "vue";
    import {Optional} from "@/model/Optional";
    import {NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import DLBsNr3dFloorWithFocusedRoom from "@/components/listing/building-scan/renderer/native/3d/d-l-bs-nr3d-floor-with-focused-room.vue";
    import {NativeAppFloorWithFocusedRoomRenderState} from "@/components/listing/building-scan/renderer/native/NativeAppFloorWithFocusedRoomRenderState";
    import DSnackbar from "@/adapter/vuetify/theme/components/snackbar/d-snackbar.vue";
    import {useI18n} from "vue-i18n";
    import {useDocumentVisibility} from "@vueuse/core";

    const renderState = shallowRef<Optional<NativeAppFloorWithFocusedRoomRenderState>>(null)
    const nativeAppMethod = "renderNativeAppFloorWithFocusedRoom"

    const {t} = useI18n()
    const isSnackbarVisible = shallowRef<boolean>(false)

    const visibility = useDocumentVisibility()
    watch(visibility, newVisibility => {
        if (newVisibility === 'hidden') {
            isSnackbarVisible.value = false
        }
    })

    onMounted(() => {
        createNativeAppMethod(nativeAppMethod, (newRenderState: Optional<NativeAppFloorWithFocusedRoomRenderState>) => {
            renderState.value = newRenderState
        })
        NATIVE_APP_SERVICE?.onFloorWithFocusedRoomRendererIsReady()
    })

    onUnmounted(() => {
        destroyNativeAppMethod(nativeAppMethod)
    })

    function isNewFloor(renderState: NativeAppFloorWithFocusedRoomRenderState): boolean {
        if (renderState.building.rooms.length < 2) { //es muss mindestens zwei räume geben. einen oben und einen unten
            return false
        }
        const room = renderState.building.rooms.find(room => room.id === renderState.focusedRoomId)
        if (room === undefined) {
            return false
        }
        return renderState.building.rooms.filter(r => r.floorLevel === room.floorLevel).length === 1
    }

    watch(renderState, renderState => {
        if (renderState === null) {
            isSnackbarVisible.value = false
            return
        }
        isSnackbarVisible.value = isNewFloor(renderState)
    }, {
        deep: true,
        immediate: true
    })
</script>

<style scoped>
</style>