<template>
    <t-renderer-webgl :effects="effects"
                      :needs-image-download="false"
                      :raycasters="raycasters"
                      :show-signature="false"
                      :tone-mapping="ACESFilmicToneMapping"
                      :tone-mapping-exposure="1"
                      fill-window
                      fit-to-scene-after-resize
                      renderer-id="nativeBuildingScanFloorWithFocusedRoom3D"
                      transparent-canvas>
        <t-scene>
            <t-ambient-light :intensity="1.25"/>

            <t-perspective-camera :far="BUILDING_SCAN_3D_CAMERA_FAR"
                                  :position="cameraPosition"
                                  :target="cameraTarget">
                <t-orbit-controls/>
            </t-perspective-camera>

            <t-group v-model:bounding-box="boundingBox"
                     fit-to-camera
                     hide-until-first-fit-to-camera>
                <template v-if="renderState">
                    <template v-for="surface in validSurfaces"
                              :key="surface.id">
                        <d-l-bs-r3d-surface :focused-room-mesh="focusedRoomMesh_000_M"
                                            :objects="renderState.building.objects"
                                            :occlusion-culling-raycaster="occlusionCullingRaycaster"
                                            :surface="surface"
                                            :surfaces="validSurfaces"
                                            show-openings/>
                    </template>

                    <template v-for="object in renderState.building.objects"
                              :key="object.id">
                        <d-l-bs-r3d-stairs v-if="object.category === 'STAIRS'"
                                           :object="object"/>
                        <!--<d-l-bs-r3d-object v-else
                                           :object="object"/>-->
                    </template>

                    <d-l-bs-r3d-point-of-interest v-for="poi in pois"
                                                  :key="poi.id"
                                                  :building="renderState.building"
                                                  :point-of-interest="poi"/>

                    <template v-for="(room, index) in renderState.building.rooms"
                              :key="room.id">
                        <d-l-bs-r3d-room-text :room="room"
                                              :room-index="index"/>
                    </template>
                </template>
            </t-group>

            <t-mesh-raw v-if="focusedRoomMesh_000_M && BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM"
                        :mesh="focusedRoomMesh_000_M"/>
        </t-scene>
    </t-renderer-webgl>
</template>

<script lang="ts"
        setup>
    import {ACESFilmicToneMapping, Box3, BoxGeometry, BufferGeometry, MathUtils, Matrix4, Mesh, MeshBasicMaterial, Vector3} from "three";
    import TPerspectiveCamera from "@/adapter/three/components/camera/t-perspective-camera.vue";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import {computed, onUnmounted, provide, ref, toRef, watch} from "vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Optional} from "@/model/Optional";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import {tDestroyMesh, tVectorsToTranslationMatrices} from "@/adapter/three/three-utility";
    import {TEffect} from "@/adapter/three/TEffect";
    import {GTAOEffect} from "@/adapter/three/postprocessing/GTAOEffect";
    import {centerVectorOfSection, DBuildingScanShowFurnitureInjection, DBuildingScanShowSlabsInjection, DBuildingScanVisibleFloorLevelsInjection} from "@/components/listing/building-scan/building-scan";
    import {BUILDING_SCAN_3D_CAMERA_FAR, BUILDING_SCAN_3D_DEBUG_FOCUSED_ROOM} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import DLBsR3dSurface from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface.vue";
    import DLBsR3dRoomText from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-room-text.vue";
    import {ListingBuildingScanInput, ListingBuildingScanPointOfInterest, ListingBuildingScanRoomInput, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
    import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
    import TAmbientLight from "@/adapter/three/components/light/t-ambient-light.vue";
    import {createRoomShapeRepresentation, shapeToBufferedGeometry_000, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import {heightOfShape} from "@/components/listing/building/building";
    import TMeshRaw from "@/adapter/three/components/t-mesh-raw.vue";
    import {NativeAppFloorWithFocusedRoomRenderState} from "@/components/listing/building-scan/renderer/native/NativeAppFloorWithFocusedRoomRenderState";
    import DLBsR3dPointOfInterest from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-point-of-interest.vue";
    import {convertListingBuildingPointOfInterestInput} from "@/adapter/graphql/mapper/buildingscaninput-to-buildingscan-mapper";
    import DLBsR3dStairs from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-stairs.vue";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";

    const props = defineProps<{
        renderState: Optional<NativeAppFloorWithFocusedRoomRenderState>
    }>()

    const pois = computed<readonly ListingBuildingScanPointOfInterest[]>(() => (props.renderState?.building?.pointsOfInterest ?? []).map(poi => convertListingBuildingPointOfInterestInput(poi, false)))
    const validSurfaces = computed<readonly ListingBuildingScanSurface[]>(() => (props.renderState?.building.surfaces ?? []).filter(surface => surface.wasRawTransformationValid && surface.wasRawPolygonValid && surface.wasRawBoundingBoxValid))

    const focusedRoom = computed<Optional<ListingBuildingScanRoomInput>>(() => {
        const renderState = props.renderState
        if (renderState === null) {
            return null
        }

        //Einzelne Räume oder weniger müssen nicht hervorgehoben werden
        if (renderState.building.rooms.length <= 1) {
            return null
        }

        //Raum ermitteln
        const room = renderState.building.rooms.find(room => room.id === renderState.focusedRoomId)
        if (room === undefined) {
            return null
        }

        //Einzelne Räume oder weniger eines Stockwerks müssen nicht hervorgehoben werden (außer, wenn das gesamte Gebäude angezeigt wird)
        if (renderState.building.rooms.filter(r => r.floorLevel === room.floorLevel).length <= 1 && !renderState.showBuilding) {
            return null
        }

        return room
    })

    // noinspection LocalVariableNamingConventionJS
    const focusedRoomBoundingGeometry_000 = computed<Optional<BufferGeometry>>(oldFocusedRoomBoundingGeometry => {
        oldFocusedRoomBoundingGeometry?.dispose()

        const room = focusedRoom.value
        if (room === null) {
            return null
        }
        const roomShapeRepresentation = createRoomShapeRepresentation(room);
        if (roomShapeRepresentation === null) {
            return null
        }
        const [shapeRepresentation] = roomShapeRepresentation
        return shapeToBufferedGeometry_000(shapeRepresentation, false, false, 1)
    })

    const focusedRoomTransformation = computed<Optional<Matrix4>>(() => {
        const room = focusedRoom.value
        if (room === null) {
            return null
        }
        const roomShapeRepresentation = createRoomShapeRepresentation(room);
        if (roomShapeRepresentation === null) {
            return null
        }
        const [shapeRepresentation] = roomShapeRepresentation
        const height = heightOfShape(shapeRepresentation)
        const baseTransformation = transformationMatrixOfShapeRepresentation(shapeRepresentation)
        const translation = new Matrix4().makeTranslation(
            0,
            -height / 2,
            0
        )
        const rotationMatrixX = new Matrix4().makeRotationX(MathUtils.degToRad(-90));
        return baseTransformation.clone().multiply(translation).multiply(rotationMatrixX)
    })

    // noinspection LocalVariableNamingConventionJS
    const focusedRoomMesh_000_M = computed<Optional<Mesh>>(oldFocusedRoomMesh => {
        if (oldFocusedRoomMesh !== undefined && oldFocusedRoomMesh !== null) {
            tDestroyMesh(oldFocusedRoomMesh, false, true)
        }

        const geometry = focusedRoomBoundingGeometry_000.value
        if (geometry === null) {
            return null
        }
        const transformation = focusedRoomTransformation.value
        if (transformation === null) {
            return null
        }
        const material = new MeshBasicMaterial({
            color: 0x00FF00,
            transparent: true,
            opacity: 0.75,
            depthTest: false
        })
        const mesh = new Mesh(geometry, material)
        mesh.renderOrder = 1000
        mesh.applyMatrix4(transformation)
        return mesh
    })

    function floorLevels(listingBuildingScan: ListingBuildingScanInput): Set<number> {
        const surfaceFloorLevels = validSurfaces.value.map(s => s.floorLevel)
        const objectFloorLevels = listingBuildingScan.objects.map(o => o.floorLevel)
        const sectionFloorLevels = listingBuildingScan.sections.map(s => s.floorLevel)

        return new Set([...surfaceFloorLevels, ...objectFloorLevels, ...sectionFloorLevels])
    }

    const visibleFloorLevels = computed<ReadonlySet<number>>(() => {
        const renderState = props.renderState
        if (renderState?.showBuilding !== true) {
            const fcsdRoom = focusedRoom.value
            if (fcsdRoom !== null) {
                return new Set([fcsdRoom.floorLevel])
            }

            if (renderState !== null && renderState.building.rooms.length > 0) {
                const room = renderState.building.rooms.find(room => room.id === renderState.focusedRoomId)
                if (room !== undefined && renderState.building.rooms.filter(r => r.floorLevel === room.floorLevel).length <= 1) {
                    return new Set([room.floorLevel])
                }
            }
        }
        return props.renderState ? floorLevels(props.renderState.building) : new Set()
    })

    const boundingBox = ref<Box3>(new Box3()) //ref okay

    provide(DBuildingScanShowFurnitureInjection, toRef(true))
    provide(DBuildingScanShowSlabsInjection, toRef(true))
    provide(DBuildingScanVisibleFloorLevelsInjection, visibleFloorLevels)

    //don't use new instances as parameters directly, otherwise they will be rerender everytime one of the parent components change
    const cameraPosition = new Vector3(0, 7, 7)
    const cameraTarget = new Vector3(0, 0, 0)

    const effects: TEffect[] = [
        new GTAOEffect(4),
        // new FXAAEffect()
    ]

    onUnmounted(() => {
        for (const effect of effects) {
            effect.destroy()
        }
        for (const raycaster of raycasters) {
            raycaster.destroy()
        }
        removeOldRaySourceMeshes()

        focusedRoomBoundingGeometry_000.value?.dispose()

        if (focusedRoomMesh_000_M.value !== null) {
            tDestroyMesh(focusedRoomMesh_000_M.value, false, true)
        }
    })

    const occlusionCullingRaycaster = new TOcclusionCullingRaycaster<ListingBuildingScanSurface>()
    // noinspection LocalVariableNamingConventionJS
    const occlusionCullingRaySourceBoundingBoxTransformations = computed<Matrix4[]>(() => {
        const floorLevels = visibleFloorLevels.value
        const rooms = props.renderState?.building?.rooms?.filter(r => floorLevels.has(r.floorLevel)) ?? []
        const sections = rooms.flatMap(r => r.sections ?? []) //Die Building-Sections sind iwi kacke, die sind meist höher und nicht im Raumzentrum (Raum-Sections sind besser)
        const sectionCenterVectors = sections.map(section => centerVectorOfSection(section))
        return tVectorsToTranslationMatrices(sectionCenterVectors)
    })

    function removeOldRaySourceMeshes() {
        for (const raySourceMeshes of occlusionCullingRaycaster.raySourceMeshes) {
            tDestroyMesh(raySourceMeshes, true, true)
        }
    }

    watch(occlusionCullingRaySourceBoundingBoxTransformations, transformations => {
        removeOldRaySourceMeshes()

        occlusionCullingRaycaster.raySourceMeshes = transformations.map(transformation => {
            const mesh = new Mesh(new BoxGeometry())
            mesh.applyMatrix4(transformation)
            return mesh
        })
    }, {
        deep: true
    })

    const raycasters: readonly TRaycaster[] = [occlusionCullingRaycaster]
</script>

<style scoped>
</style>