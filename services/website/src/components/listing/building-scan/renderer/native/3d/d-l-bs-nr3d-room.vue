<template>
    <t-renderer-webgl :effects="effects"
                      :needs-image-download="false"
                      :raycasters="raycasters"
                      :show-signature="false"
                      :tone-mapping="ACESFilmicToneMapping"
                      :tone-mapping-exposure="1"
                      fill-window
                      fit-to-scene-after-resize
                      renderer-id="nativeBuildingScanRoom3D"
                      transparent-canvas>
        <t-scene>
            <t-ambient-light :intensity="1.25"/>

            <t-perspective-camera :far="BUILDING_SCAN_3D_CAMERA_FAR"
                                  :position="cameraPosition"
                                  :target="cameraTarget">
                <t-orbit-controls/>
            </t-perspective-camera>

            <t-group v-model:bounding-box="boundingBox"
                     fit-to-camera
                     hide-until-first-fit-to-camera>
                <template v-if="renderState">
                    <template v-for="surface in validSurfaces"
                              :key="surface.id">
                        <d-l-bs-r3d-surface :focused-room-mesh="null"
                                            :objects="renderState.room.objects"
                                            :occlusion-culling-raycaster="occlusionCullingRaycaster"
                                            :show-openings="false"
                                            :surface="surface"
                                            :surfaces="validSurfaces"/>
                    </template>

                    <template v-for="object in renderState.room.objects"
                              :key="object.id">
                        <d-l-bs-r3d-stairs v-if="object.category === 'STAIRS'"
                                           :object="object"/>
                        <!--<d-l-bs-r3d-object v-else
                                           :object="object"/>-->
                    </template>

                    <d-l-bs-r3d-room-text v-if="!renderState.isLive"
                                          :room="renderState.room"
                                          :room-index="-1"/>
                </template>
            </t-group>
        </t-scene>
    </t-renderer-webgl>
</template>

<script lang="ts"
        setup>
    import {ACESFilmicToneMapping, Box3, BoxGeometry, Matrix4, Mesh, Vector3} from "three";
    import TPerspectiveCamera from "@/adapter/three/components/camera/t-perspective-camera.vue";
    import TScene from "@/adapter/three/components/t-scene.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Optional} from "@/model/Optional";
    import TOrbitControls from "@/adapter/three/components/camera/t-orbit-controls.vue";
    import {computed, onUnmounted, provide, ref, toRef, watch} from "vue";
    import {tDestroyMesh, tVectorsToTranslationMatrices} from "@/adapter/three/three-utility";
    import {TEffect} from "@/adapter/three/TEffect";
    import {GTAOEffect} from "@/adapter/three/postprocessing/GTAOEffect";
    import {BUILDING_SCAN_3D_CAMERA_FAR} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
    import {NativeAppRoomRenderState} from "@/components/listing/building-scan/renderer/native/NativeAppRoomRenderState";
    import {centerVectorOfSection, DBuildingScanShowFurnitureInjection, DBuildingScanShowSlabsInjection, DBuildingScanVisibleFloorLevelsInjection} from "@/components/listing/building-scan/building-scan";
    import DLBsR3dSurface from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-surface.vue";
    import DLBsR3dRoomText from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-room-text.vue";
    import {TOcclusionCullingRaycaster} from "@/adapter/three/raycasting/occlusion-culling/TOcclusionCullingRaycaster";
    import {TRaycaster} from "@/adapter/three/raycasting/TRaycaster";
    import TAmbientLight from "@/adapter/three/components/light/t-ambient-light.vue";
    import {ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
    import DLBsR3dStairs from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-stairs.vue";
    import TRendererWebgl from "@/adapter/three/components/t-renderer-webgl.vue";

    const props = defineProps<{
        renderState: Optional<NativeAppRoomRenderState>
    }>()

    const visibleFloorLevels = computed<ReadonlySet<number>>(() => props.renderState ? new Set([props.renderState.room.floorLevel]) : new Set())

    provide(DBuildingScanShowFurnitureInjection, toRef(true))
    provide(DBuildingScanShowSlabsInjection, toRef(true))
    provide(DBuildingScanVisibleFloorLevelsInjection, visibleFloorLevels)

    const boundingBox = ref<Box3>(new Box3()) //ref okay

    const effects: TEffect[] = [
        new GTAOEffect(4),
        // new FXAAEffect()
    ]

    onUnmounted(() => {
        for (const effect of effects) {
            effect.destroy()
        }
        for (const raycaster of raycasters) {
            raycaster.destroy()
        }
        removeOldRaySourceMeshes()
    })

    const validSurfaces = computed<readonly ListingBuildingScanSurface[]>(() => (props.renderState?.room.surfaces ?? []).filter(surface => surface.wasRawTransformationValid && surface.wasRawPolygonValid && surface.wasRawBoundingBoxValid))

    //don't use new instances as parameters directly, otherwise they will be rerender everytime one of the parent components change
    const cameraPosition = new Vector3(0, 7, 7)
    const cameraTarget = new Vector3(0, 0, 0)

    const occlusionCullingRaycaster = new TOcclusionCullingRaycaster<ListingBuildingScanSurface>()
    // noinspection LocalVariableNamingConventionJS
    const occlusionCullingRaySourceBoundingBoxTransformations = computed<Matrix4[]>(() => {
        const sections = props.renderState?.room?.sections ?? [] //Die Building-Sections sind iwi kacke, die sind meist höher und nicht im Raumzentrum (Raum-Sections sind besser)
        const sectionCenterVectors = sections.map(section => centerVectorOfSection(section))
        return tVectorsToTranslationMatrices(sectionCenterVectors)
    })

    function removeOldRaySourceMeshes() {
        for (const raySourceMeshes of occlusionCullingRaycaster.raySourceMeshes) {
            tDestroyMesh(raySourceMeshes, true, true)
        }
    }

    watch(occlusionCullingRaySourceBoundingBoxTransformations, transformations => {
        removeOldRaySourceMeshes()

        occlusionCullingRaycaster.raySourceMeshes = transformations.map(transformation => {
            const mesh = new Mesh(new BoxGeometry())
            mesh.applyMatrix4(transformation)
            return mesh
        })
    }, {
        deep: true
    })

    const raycasters: readonly TRaycaster[] = [occlusionCullingRaycaster]
</script>

<style scoped>
</style>