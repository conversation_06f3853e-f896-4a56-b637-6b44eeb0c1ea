import {BuildingInput, BuildingPointOfInterestInput, ListingBuildingScan, ListingBuildingScanInput, ListingBuildingScanObject, ListingBuildingScanPointOfInterest, ListingBuildingScanPointOfInterestInput, ListingBuildingScanRoom, ListingBuildingScanRoomInput, ListingBuildingScanSection, ListingBuildingScanSurface} from "@/adapter/graphql/generated/graphql";
import {BoxGeometry, Matrix4, Mesh, ShapeUtils, Vector2, Vector3} from "three";
import {computed, InjectionKey, Ref} from "vue";
import {tCenterOfVectors3, tClusterMeshes, tDecomposeMatrix, tDestroyMesh, tIsMeshNearbyAnyMesh, tOverrideTransformation} from "@/adapter/three/three-utility";
import {TLineSegment3D} from "@/adapter/three/TLineSegment3D";
import {Optional} from "@/model/Optional";
import {TRenderType} from "@/adapter/three/TRenderType";
import {StairClusterElement} from "@/components/listing/building-scan/StairClusterElement";
import {StairCluster} from "@/components/listing/building-scan/StairCluster";
import {StairLocation} from "@/components/listing/building-scan/StairLocation";
import {StairDirection} from "@/components/listing/building-scan/StairDirection";
import {areMeshesIntersecting, surfaceToTestMesh_000_GM} from "@/components/listing/building-scan/renderer/default/3d/d-l-bs-r3d-config";
import {BUILDING_EPSILON, heightOfShape} from "@/components/listing/building/building";
import {createShapeRepresentationFromSurface, transformationMatrixOfArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {isNumberGreaterThanOrEqual, isNumberLessThanOrEqual} from "@/utility/number";

export const DBuildingScanShowSlabsInjection: InjectionKey<Readonly<Ref<boolean>>> = Symbol('dBuildingScanShowSlabsInjection')
export const DBuildingScanShowFurnitureInjection: InjectionKey<Readonly<Ref<boolean>>> = Symbol('dBuildingScanShowFurnitureInjection')
export const DBuildingScanVisibleFloorLevelsInjection: InjectionKey<Readonly<Ref<ReadonlySet<number>>>> = Symbol('dBuildingScanVisibleFloorLevelsInjection')

export const DEBUG_STAIR_CLUSTERS = false

export function widthOfSurface(surface: ListingBuildingScanSurface): number {
    return surface.boundingBox[0]
}

export function heightOfSurface(surface: ListingBuildingScanSurface): number {
    return surface.boundingBox[1]
}

export function depthOfSurface(surface: ListingBuildingScanSurface): number {
    return surface.boundingBox[2]
}

export function widthOfObject(object: ListingBuildingScanObject): number {
    return object.boundingBox[0]
}

export function heightOfObject(object: ListingBuildingScanObject): number {
    return object.boundingBox[1]
}

export function depthOfObject(object: ListingBuildingScanObject): number {
    return object.boundingBox[2]
}

export function squareMetersOfSurface(surface: ListingBuildingScanSurface): number {
    // if (surface.arc != null) {
    //     return -1 //TODO: arc berechnung
    // }
    const polygonVectors = polygonVectorsOfSurface(surface)
    if (polygonVectors.length > 0) {
        return Math.abs(ShapeUtils.area([...polygonVectors]))
    }
    //Bei Böden (FLOOR) ist die Dicke die Höhe, von daher passt das
    return widthOfSurface(surface) * heightOfSurface(surface)
}

function polygonVectorsOfSurface(surface: ListingBuildingScanSurface): readonly Vector2[] {
    return surface.polygon.map(point => new Vector2(point[0], point[1]))
}

export function transformationMatrixOfSurface(surface: ListingBuildingScanSurface): Matrix4 {
    return new Matrix4().fromArray(surface.transformation.flat())
}

export function transformationMatrixOfObject(object: ListingBuildingScanObject): Matrix4 {
    return new Matrix4().fromArray(object.transformation.flat())
}

export function useRoomDataSurface(surface: Readonly<Ref<ListingBuildingScanSurface>>, focusedRoomMesh: Readonly<Ref<Optional<Mesh>>>) {
    // noinspection LocalVariableNamingConventionJS
    const intersectionMesh_000_GM = computed<Mesh>(oldIntersectionMesh => {
        if (oldIntersectionMesh !== undefined) {
            tDestroyMesh(oldIntersectionMesh, true, true)
        }
        return surfaceToTestMesh_000_GM(surface.value)
    })
    const isIntersectingWithFocusedRoomMesh = computed<boolean>(() => {
        const fcsdRoomMesh = focusedRoomMesh.value
        if (fcsdRoomMesh === null) {
            return false
        }
        return areMeshesIntersecting(fcsdRoomMesh, intersectionMesh_000_GM.value)
    })

    return {
        width: computed<number>(() => widthOfSurface(surface.value)),
        height: computed<number>(() => heightOfSurface(surface.value)),
        depth: computed<number>(() => depthOfSurface(surface.value)),
        transformation: computed<Matrix4>(() => transformationMatrixOfSurface(surface.value)),
        polygonVectors: computed<readonly Vector2[]>(() => polygonVectorsOfSurface(surface.value)),
        squareMeters: computed<number>(() => squareMetersOfSurface(surface.value)),
        intersectionMesh: intersectionMesh_000_GM,
        isIntersectingWithFocusedRoomMesh: isIntersectingWithFocusedRoomMesh,
        isNotFocused: computed<boolean>(() => focusedRoomMesh.value !== null && !isIntersectingWithFocusedRoomMesh.value),
        destroyRoomDataSurface: () => {
            tDestroyMesh(intersectionMesh_000_GM.value, true, true)
        },
    }
}

export function useRoomDataObject(object: Readonly<Ref<ListingBuildingScanObject>>) {
    return {
        width: computed<number>(() => widthOfObject(object.value)),
        height: computed<number>(() => heightOfObject(object.value)),
        depth: computed<number>(() => depthOfObject(object.value)),
        transformation: computed<Matrix4>(() => transformationMatrixOfObject(object.value))
    }
}

export function useRoomData(room: Readonly<Ref<ListingBuildingScanRoom | ListingBuildingScanRoomInput>>) {
    const floors = computed<readonly ListingBuildingScanSurface[]>(() => room.value.surfaces.filter(surface => surface.category === 'FLOOR'))

    return {
        floors: floors,
        squareMeters: computed<number>(() => floors.value
            .map(floor => squareMetersOfSurface(floor))
            .reduce((a, b) => a + b, 0)
        ),
    }
}

export function centerVectorOfSection(section: ListingBuildingScanSection): Vector3 {
    return new Vector3(
        section.center[0],
        section.center[1],
        section.center[2]
    )
}

export function calculateDeskew3DLineSegments(walls: readonly ListingBuildingScanSurface[]): TLineSegment3D[] {
    if (walls.length <= 0) {
        return []
    }
    return walls.map(wall => {
        const wallHalfWidth = wall.boundingBox[0] / 2
        const wallHalfHeight = wall.boundingBox[1] / 2

        const linePoint1 = new Vector3(-wallHalfWidth, -wallHalfHeight, 0)
        const linePoint2 = new Vector3(wallHalfWidth, wallHalfHeight, 0)

        const transformationMatrix = transformationMatrixOfSurface(wall)
        linePoint1.applyMatrix4(transformationMatrix)
        linePoint2.applyMatrix4(transformationMatrix)

        return {
            start: linePoint1,
            end: linePoint2
        }
    })
}

// noinspection FunctionNamingConventionJS
export function calculateStairClusterElements_000_G(stairs: readonly ListingBuildingScanObject[], availableFloorLevels: ReadonlySet<number>): StairClusterElement[] {
    if (stairs.length <= 0) {
        return []
    }
    return stairs.map(stair => {
        const width = widthOfObject(stair)
        const height = heightOfObject(stair)
        const depth = depthOfObject(stair)
        const geometry = new BoxGeometry(width, height, depth)

        const mesh = new Mesh(geometry)

        const stairTransformation = transformationMatrixOfObject(stair)
        const transformation = stairTransformation
        tOverrideTransformation(mesh, transformation)
        mesh.position.y = 0

        return {
            element: stair,
            mesh,
            clusterEffectedFloorLevels: clusterEffectedFloorLevelsByStair(stair),
            displayEffectedFloorLevels: displayEffectedFloorLevelsByStair(stair, availableFloorLevels)
        }
    })
}

export function calculateStairClusters(stairClusterElements: readonly StairClusterElement[]): StairCluster[] {
    if (stairClusterElements.length <= 0) {
        return []
    }
    return tClusterMeshes<ListingBuildingScanObject, StairClusterElement, StairCluster>(
        stairClusterElements,
        (cluster, element) => {
            //IST IN DER NÄHE (Höhe/FloorLevel wird ignoriert)
            if (!tIsMeshNearbyAnyMesh(element.mesh, cluster.elements.map(element => element.mesh), 0.5, BUILDING_EPSILON)) {
                return false
            }

            //IST IN DER NÄHE DES GLEICHEN FLOOR LEVELS (+-1 floorLevel)
            return cluster.clusterEffectedFloorLevels.has(element.element.floorLevel)
        },
        (cluster, element) => {
            element.clusterEffectedFloorLevels.forEach(floorLevel => cluster.clusterEffectedFloorLevels.add(floorLevel))
            element.displayEffectedFloorLevels.forEach(floorLevel => cluster.displayEffectedFloorLevels.add(floorLevel))
        },
        (cluster, element) => {
            return {
                ...cluster,
                clusterEffectedFloorLevels: new Set(element.clusterEffectedFloorLevels),
                displayEffectedFloorLevels: new Set(element.displayEffectedFloorLevels)
            }
        }
    )
}

//Welche Stockwerke sind von der Treppe im Cluster betroffen? Nach oben und nach unten hin wird geclustert.
function clusterEffectedFloorLevelsByStair(stair: ListingBuildingScanObject): Set<number> {
    return new Set<number>([
        stair.floorLevel - 1,
        stair.floorLevel,
        stair.floorLevel + 1
    ])
}

//Welche Stockwerke sind in der Darstellung von der Treppe betroffen?
function displayEffectedFloorLevelsByStair(stair: ListingBuildingScanObject, availableFloorLevels: ReadonlySet<number>): Set<number> {
    const floorLevels = new Set<number>([stair.floorLevel])
    if (availableFloorLevels.size <= 0) {
        return floorLevels
    }

    //TODO: Derzeit müssen wir davon ausgehen, dass ein Treppenobjekt immer nach oben geht. Evtl. ändern Apple da etwas dran in der Zukunft.
    // const floorLevelDown = stair.floorLevel - 1;
    // if (availableFloorLevels.has(floorLevelDown)) {
    //     floorLevels.add(floorLevelDown)
    // }

    const floorLevelUp = stair.floorLevel + 1;
    if (availableFloorLevels.has(floorLevelUp)) {
        floorLevels.add(floorLevelUp)
    }

    return floorLevels
}

export function calculateStairLocations(stairClusters: readonly StairCluster[], availableFloorLevels: ReadonlySet<number>): StairLocation[] {
    if (stairClusters.length <= 0 || availableFloorLevels.size <= 0) {
        return []
    }
    return stairClusters
        .flatMap(stairCluster => {
            const floorLevels = stairCluster.displayEffectedFloorLevels

            return [...availableFloorLevels]
                .filter(floorLevel => floorLevels.has(floorLevel))
                .map(floorLevel => {
                    const stairDirection = stairDirectionOf(floorLevel, floorLevels)
                    if (stairDirection === null) {
                        return null
                    }
                    return {
                        mesh: stairCluster.mesh.clone(true),
                        floorLevel,
                        direction: stairDirection
                    }
                })
        })
        .filter(stairLocation => stairLocation !== null)
}

function stairDirectionOf(floorLevel: number, stairFloorLevels: ReadonlySet<number>): Optional<StairDirection> {
    if (stairFloorLevels.size <= 0) {
        return null
    }

    //RoomPlans Treppen gehen immer nach oben, d.h. die Treppe eines einzelnen, aktuellen Stockwerks wird immer nach oben gehen.
    const canGoUp = (stairFloorLevels.size <= 1 && stairFloorLevels.has(floorLevel)) || stairFloorLevels.has(floorLevel + 1)

    //wenn es nur 1 Stockwerk gibt, dann kann eine Treppe nie nach unten gehen, weil RoomPlan das so nicht scannen kann.
    const canGoDown = stairFloorLevels.size > 1 && stairFloorLevels.has(floorLevel - 1)

    if (canGoUp && canGoDown) {
        return "UP_AND_DOWN"
    }
    if (canGoUp) {
        return "UP"
    }
    if (canGoDown) {
        return "DOWN"
    }
    return null
}

export function lineSegmentsOfSurface(surface: ListingBuildingScanSurface, renderType: TRenderType): TLineSegment3D[] {
    const polygon = surface.polygon

    //2D-Nicht-Floor (alles, was hochkant im Raum steht)
    if (renderType === '2D' && surface.category !== "FLOOR") {
        const width = widthOfSurface(surface)
        const halfWidth = width / 2

        return [
            {
                start: new Vector3(-halfWidth, 0, 0),
                end: new Vector3(halfWidth, 0, 0),
            }
        ]
    }

    //Ohne Polygon, 3D oder 2D-Floor
    if (polygon.length <= 0) {
        const width = widthOfSurface(surface)
        const height = heightOfSurface(surface)
        const halfWidth = width / 2
        const halfHeight = height / 2

        return [
            //top left
            {
                start: new Vector3(-halfWidth, halfHeight, 0),
                end: new Vector3(halfWidth, halfHeight, 0),
            },
            //top right
            {
                start: new Vector3(halfWidth, halfHeight, 0),
                end: new Vector3(halfWidth, -halfHeight, 0),
            },
            //bottom right
            {
                start: new Vector3(halfWidth, -halfHeight, 0),
                end: new Vector3(-halfWidth, -halfHeight, 0),
            },
            //bottom left
            {
                start: new Vector3(-halfWidth, -halfHeight, 0),
                end: new Vector3(-halfWidth, halfHeight, 0),
            }
        ]
    }

    //Mit Polygon
    if (polygon.length >= 1) {
        const lineSegments: TLineSegment3D[] = []
        for (let v = 0; v < polygon.length; ++v) {
            const p1 = polygon[v]
            const p2 = polygon[(v + 1) % polygon.length]

            lineSegments.push({
                start: new Vector3(
                    p1[0],
                    p1[1],
                    p1[2]
                ),
                end: new Vector3(
                    p2[0],
                    p2[1],
                    p2[2]
                )
            })
        }
        return lineSegments
    }

    //Alle anderen Fälle
    return []
}

export function lineSegments3DToVectors3(lineSegments: readonly TLineSegment3D[]): Vector3[] {
    return lineSegments.flatMap(lineSegment => {
        return [lineSegment.start, lineSegment.end]
    })
}

export function calculateFloorLevelToPoiMapFromBuildingScan(building: ListingBuildingScan | ListingBuildingScanInput): ReadonlyMap<number, readonly  (ListingBuildingScanPointOfInterest | ListingBuildingScanPointOfInterestInput)[]> {
    if (building.pointsOfInterest === null || building.pointsOfInterest === undefined || building.pointsOfInterest.length <= 0) {
        return new Map()
    }

    const floorLevelToPoiMap = new Map<number, (ListingBuildingScanPointOfInterest | ListingBuildingScanPointOfInterestInput)[]>()
    const poisInMap = new Set<string>()

    const floorLevels = [...new Set<number>(building.rooms.map(room => room.floorLevel))].sort((a, b) => a - b)

    for (let i = floorLevels.length - 1; i >= 0; --i) { //rückwärts die floors durchgehen, falls ein unteres stockwerk ins obere überlappt
        const floorLevel = floorLevels[i]

        const walls = building.surfaces.filter(s => s.category === "WALL" && s.floorLevel === floorLevel)
        const wallShapeRepresentations = walls.map(wall => createShapeRepresentationFromSurface(wall, true))

        const maxWallHeight = wallShapeRepresentations.reduce((maxHeight, wallShapeRepresentation) => Math.max(maxHeight, heightOfShape(wallShapeRepresentation)), 0)
        const halfMaxWallHeight = maxWallHeight / 2

        const wallTranslations = wallShapeRepresentations.map(wsr => {
            const transformationMatrix = transformationMatrixOfShapeRepresentation(wsr)
            const [translation] = tDecomposeMatrix(transformationMatrix)
            return translation
        })
        const centerOfWalls = tCenterOfVectors3(wallTranslations)

        const minY = centerOfWalls.y - halfMaxWallHeight
        const maxY = centerOfWalls.y + halfMaxWallHeight

        for (const poi of building.pointsOfInterest) {
            if (poisInMap.has(poi.id)) {
                continue
            }

            const poiTransformation = transformationMatrixOfArray(poi.transformation)
            const [poiTranslation] = tDecomposeMatrix(poiTransformation)

            if (isNumberGreaterThanOrEqual(poiTranslation.y, minY, BUILDING_EPSILON) && isNumberLessThanOrEqual(poiTranslation.y, maxY, BUILDING_EPSILON)) {
                const poisOfFloorLevel = floorLevelToPoiMap.get(floorLevel)
                if (poisOfFloorLevel === undefined) {
                    floorLevelToPoiMap.set(floorLevel, [poi])
                } else {
                    poisOfFloorLevel.push(poi)
                }

                poisInMap.add(poi.id)
            }
        }
    }

    return floorLevelToPoiMap
}

/**
 * @return floorLevel => center
 */
function calculateFloorYCenters(building: BuildingInput): ReadonlyMap<number, number> {
    const floorLevelToCenter = new Map<number, number>()

    for (const floor of building.floors) {
        const maxWallHeight = floor.walls.reduce((maxHeight, wall) => Math.max(maxHeight, heightOfShape(wall.shapeRepresentation)), 0)
        const floorSlabTransformation = transformationMatrixOfShapeRepresentation(floor.floorSlab.shapeRepresentation);
        const [floorSlabTranslation] = tDecomposeMatrix(floorSlabTransformation)
        const minY = floorSlabTranslation.y
        const maxY = minY + maxWallHeight
        const center = (minY + maxY) / 2
        floorLevelToCenter.set(floor.level, center)
    }

    return floorLevelToCenter
}

// noinspection FunctionTooLongJS
export function calculateFloorLevelToPoiMapFromBuilding(building: BuildingInput): ReadonlyMap<number, readonly  BuildingPointOfInterestInput[]> {
    if (building.pointsOfInterest === null || building.pointsOfInterest === undefined || building.pointsOfInterest.length <= 0 || building.floors.length <= 0) {
        return new Map()
    }

    const floorLevelToPoiMap = new Map<number, BuildingPointOfInterestInput[]>()
    const floorCenters = calculateFloorYCenters(building)

    for (const poi of building.pointsOfInterest) {
        const poiTransformation = transformationMatrixOfArray(poi.transformationMatrix)
        const [poiTranslation] = tDecomposeMatrix(poiTransformation)

        let floorLevel = 0
        let minDistance = Number.MAX_VALUE

        for (const [level, center] of floorCenters) {
            const distance = Math.abs(poiTranslation.y - center)
            if (distance < minDistance) {
                minDistance = distance
                floorLevel = level
            }
        }

        const poisOfFloorLevel = floorLevelToPoiMap.get(floorLevel)
        if (poisOfFloorLevel === undefined) {
            floorLevelToPoiMap.set(floorLevel, [poi])
        } else {
            poisOfFloorLevel.push(poi)
        }
    }

    return floorLevelToPoiMap
}