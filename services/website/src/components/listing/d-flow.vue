<template>
    <router-view v-if="flowConfig"/>
</template>

<script lang="ts"
        setup>
    import {computed, provide, toRef, watch} from "vue";
    import {FCFlowConfigIdInjection, FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {DFlowLoadQueryVariables, FlowConfig, useDFlowLoadLazyQuery} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {createFlowConfigField} from "@/service/flow-config/flow-config-service";
    import {IS_NATIVE_IOS_APP} from "@/service/native-app/native-app";
    import {INTERNET_SPEED, InternetSpeed, IOS_NETWORK_STATUS_IS_VERIFIED} from "@/service/pwa/internet-speed";

    const props = defineProps<{
        flowConfigId: string
    }>()

    const flowConfigId = computed<string>(() => props.flowConfigId)
    const flowConfigQueryVariables = computed<DFlowLoadQueryVariables>(() => ({
        flowConfigId: props.flowConfigId
    }))
    const {
        result: flowConfigResult,
        refetch: refetchFlow,
        load: loadFlow,
        onError: flowConfigError
    } = useDFlowLoadLazyQuery(flowConfigQueryVariables)

    const mayLoadFlow = computed<boolean>(() => {
        if (IS_NATIVE_IOS_APP && !IOS_NETWORK_STATUS_IS_VERIFIED.value) {
            return false
        }
        return INTERNET_SPEED.value > InternetSpeed.OFFLINE;
    })

    watch(mayLoadFlow, mayLoad => {
        if (mayLoad) {
            loadFlow()
        }
    }, {
        immediate: true
    })

    watch(INTERNET_SPEED, (newSpeed, oldSpeed) => {
        if (oldSpeed <= InternetSpeed.OFFLINE && newSpeed > InternetSpeed.OFFLINE) {
            triggerReload()
        }
    })

    async function triggerReload() {
        if (!mayLoadFlow.value) {
            return
        }

        const result = loadFlow()
        if (result === false) {
            await refetchFlow()
        } else {
            await result
        }
    }

    flowConfigError(error => {
        console.warn("Error while loading flow config.", error)
    });

    const rawFlowConfig = computed<Optional<FlowConfig>>(() => flowConfigResult.value?.flowConfig as (FlowConfig | undefined) ?? null)
    const flowConfig = createFlowConfigField(flowConfigId, rawFlowConfig)

    provide(FCFlowConfigInjection, toRef(() => flowConfig.value!))
    provide(FCFlowConfigIdInjection, flowConfigId)
</script>

<style scoped>
</style>