import {BoxShapeRepresentation, deepCopyMatrixArray, deepCopyShapeRepresentation, PolygonShapeRepresentation, RingShapeRepresentation, thicknessOfShape, TypedShapeRepresentation, widthOfShape} from "@/components/listing/building/building";
import {Shape, ShapeRepresentation} from "@/adapter/graphql/generated/graphql";
import {matrix4ToTransformationMatrixArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {tDecomposeMatrix, tSizeOfVectors2} from "@/adapter/three/three-utility";
import {convert} from "@/utility/converter";
import * as THREE from "three";
import {MathUtils, Matrix4, Vector2} from "three";
import {ShapeType} from "@/model/building/ShapeType";
import {Optional} from "@/model/Optional";

//######################################################################
//##### PARENT #####
//######################################################################

// noinspection OverlyComplexFunctionJS
export function changeShapeType<ST extends ShapeType, S extends Shape & { __typename: ST }>(
    oldShapeRepresentation: ShapeRepresentation,
    newShapeType: ST,
    isPositionedAtInterior: boolean
): Optional<TypedShapeRepresentation<S>> {
    const oldShapeType = oldShapeRepresentation.shape.__typename
    if (oldShapeType === undefined) {
        throw new Error("Wall has no shape type")
    }
    if (oldShapeType === newShapeType) {
        return null
    }
    switch (oldShapeType) {
        case "Box":
            // noinspection NestedSwitchStatementJS
            switch (newShapeType) {
                case "Polygon":
                    return convertBoxToPolygon(oldShapeRepresentation as BoxShapeRepresentation) as TypedShapeRepresentation<S>
                case "Ring":
                    return convertBoxToRing(oldShapeRepresentation as BoxShapeRepresentation, isPositionedAtInterior) as TypedShapeRepresentation<S>
                default:
                    throw new Error(`Unsupported shape type: ${newShapeType}`)
            }
        case "Polygon":
            // noinspection NestedSwitchStatementJS
            switch (newShapeType) {
                case "Box":
                    return convertPolygonToBox(oldShapeRepresentation as PolygonShapeRepresentation) as TypedShapeRepresentation<S>
                case "Ring":
                    return convertPolygonToRing(oldShapeRepresentation as PolygonShapeRepresentation, isPositionedAtInterior) as TypedShapeRepresentation<S>
                default:
                    throw new Error(`Unsupported shape type: ${newShapeType}`)
            }
        case "Ring":
            // noinspection NestedSwitchStatementJS
            switch (newShapeType) {
                case "Box":
                    return convertRingToBox(oldShapeRepresentation as RingShapeRepresentation, isPositionedAtInterior) as TypedShapeRepresentation<S>
                case "Polygon":
                    return convertRingToPolygon(oldShapeRepresentation as RingShapeRepresentation, isPositionedAtInterior) as TypedShapeRepresentation<S>
                default:
                    throw new Error(`Unsupported shape type: ${newShapeType}`)
            }
        default:
            throw new Error(`Unsupported shape type: ${newShapeType}`)
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu convertPolygonToBox.
 */
function convertBoxToPolygon(shapeRepresentation: BoxShapeRepresentation): PolygonShapeRepresentation {
    const halfWidth = shapeRepresentation.shape.width / 2
    const halfHeight = shapeRepresentation.shape.height / 2

    return {
        transformationMatrix: deepCopyMatrixArray(shapeRepresentation.transformationMatrix),
        shape: {
            __typename: "Polygon",
            extrusion: shapeRepresentation.shape.depth,
            extrusionDirection: {
                x: 0,
                y: 0,
                z: 1
            },
            vertices: [
                {
                    x: -halfWidth,
                    y: -halfHeight,
                    z: 0
                },
                {
                    x: -halfWidth,
                    y: halfHeight,
                    z: 0
                },
                {
                    x: halfWidth,
                    y: halfHeight,
                    z: 0
                },
                {
                    x: halfWidth,
                    y: -halfHeight,
                    z: 0
                },
                {
                    x: -halfWidth,
                    y: -halfHeight,
                    z: 0
                }
            ],
            holes: [],
        },
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu convertBoxToPolygon.
 */
function convertPolygonToBox(shapeRepresentation: PolygonShapeRepresentation): BoxShapeRepresentation {
    const size = tSizeOfVectors2(shapeRepresentation.shape.vertices.map(v => new Vector2(v.x, v.y)))

    return {
        transformationMatrix: deepCopyMatrixArray(shapeRepresentation.transformationMatrix),
        shape: {
            __typename: "Box",
            width: size.x,
            height: size.y,
            depth: shapeRepresentation.shape.extrusion,
        }
    }
}

/**
 * Erstellt einen 180°-Ring. Das Zentrum des Rings ist die Mitte des Rechtecks.
 *
 * Ist im Grunde die invertierte Funktion zu convertRingToBox.
 */
function convertBoxToRing(shapeRepresentation: BoxShapeRepresentation, isPositionedAtInterior: boolean): RingShapeRepresentation {
    const width = shapeRepresentation.shape.width
    const height = shapeRepresentation.shape.height
    const depth = shapeRepresentation.shape.depth
    const halfDepth = depth / 2
    const baseRadius = width / 2
    const radius = isPositionedAtInterior
        ? baseRadius + depth
        : baseRadius + halfDepth

    let transformation = transformationMatrixOfShapeRepresentation(shapeRepresentation)

    if (isPositionedAtInterior) {
        const depthTranslation = new Matrix4().makeTranslation(0, 0, halfDepth)
        transformation = transformation.multiply(depthTranslation)
    }

    return {
        transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
        shape: {
            __typename: "Ring",
            center: {
                x: 0,
                y: 0,
            },
            startAngle: 0,
            endAngle: 180,
            thetaLength: 180,
            extrusion: height,
            extrusionDirection: {
                x: 0,
                y: 1,
                z: 0,
            },
            size: depth,
            innerRadius: Math.max(0, radius - depth),
            radius: Math.max(0, radius - halfDepth),
            outerRadius: radius
        }
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu convertBoxToRing.
 */
function convertRingToBox(shapeRepresentation: RingShapeRepresentation, isPositionedAtInterior: boolean): BoxShapeRepresentation {
    const outerRadius = isPositionedAtInterior
        ? shapeRepresentation.shape.innerRadius
        : shapeRepresentation.shape.radius
    const depth = shapeRepresentation.shape.size
    const halfDepth = depth / 2
    const width = 2 * outerRadius
    const height = shapeRepresentation.shape.extrusion

    let transformation = transformationMatrixOfShapeRepresentation(shapeRepresentation)

    if (isPositionedAtInterior) {
        const depthTranslation = new Matrix4().makeTranslation(0, 0, -halfDepth)
        transformation = transformation.multiply(depthTranslation)
    }

    return {
        transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
        shape: {
            __typename: "Box",
            width: width,
            height: height,
            depth: depth,
        }
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu convertRingToPolygon.
 */
function convertPolygonToRing(shapeRepresentation: PolygonShapeRepresentation, isPositionedAtInterior: boolean): RingShapeRepresentation {
    const box = convertPolygonToBox(shapeRepresentation)
    return convertBoxToRing(box, isPositionedAtInterior)
}

/**
 * Das ist im Grunde die invertierte Funktion zu convertPolygonToRing.
 */
function convertRingToPolygon(shapeRepresentation: RingShapeRepresentation, isPositionedAtInterior: boolean): PolygonShapeRepresentation {
    const box = convertRingToBox(shapeRepresentation, isPositionedAtInterior)
    return convertBoxToPolygon(box)
}

//######################################################################
//##### CHILD #####
//######################################################################

// noinspection OverlyComplexFunctionJS
export function changeChildShapeIfNeeded(
    oldParent: ShapeRepresentation,
    newParent: ShapeRepresentation,
    child: ShapeRepresentation,
    isPositionedAtInterior: boolean
): ShapeRepresentation {
    if (oldParent.shape.__typename === undefined) {
        throw new Error("Old parent shape has no type")
    }
    if (newParent.shape.__typename === undefined) {
        throw new Error("New parent shape has no type")
    }
    if (oldParent.shape.__typename === newParent.shape.__typename) {
        throw new Error("Old and new parent shape have the same type: " + oldParent.shape.__typename)
    }
    if (child.shape.__typename === undefined) {
        throw new Error("Child shape has no type")
    }

    switch (newParent.shape.__typename) {
        case "Box":
            // noinspection NestedSwitchStatementJS
            switch (child.shape.__typename) {
                case "Box":
                    //do nothing
                    return child
                case "Polygon":
                    //do nothing
                    return child
                case "Ring":
                    return changeChildRingToFitIntoParentBox(
                        child as RingShapeRepresentation,
                        oldParent,
                        newParent as BoxShapeRepresentation
                    )
                default:
                    throw new Error("Unsupported shape type " + child.shape.__typename)
            }
        case "Polygon":
            // noinspection NestedSwitchStatementJS
            switch (child.shape.__typename) {
                case "Box":
                    //do nothing
                    return child
                case "Polygon":
                    //do nothing
                    return child
                case "Ring":
                    return changeChildRingToFitIntoParentPolygon(
                        child as RingShapeRepresentation,
                        oldParent,
                        newParent as PolygonShapeRepresentation,
                        isPositionedAtInterior,
                    )
                default:
                    throw new Error("Unsupported shape type " + child.shape.__typename)
            }
        case "Ring":
            // noinspection NestedSwitchStatementJS
            switch (child.shape.__typename) {
                case "Box":
                    return changeChildBoxToFitIntoParentRing(
                        child as BoxShapeRepresentation,
                        oldParent,
                        newParent as RingShapeRepresentation
                    )
                case "Polygon":
                    return changeChildPolygonToFitIntoParentRing(
                        child as PolygonShapeRepresentation,
                        oldParent,
                        newParent as RingShapeRepresentation,
                        isPositionedAtInterior,
                    )
                case "Ring":
                    //TODO: das sollte eig. gar nicht möglich sein... dazu müsste das alte parent bereits ein ring gewesen sein,
                    // damit es ring children hat. aber wenn altes und neues parent ringe waren, dann failed die bedingung oben
                    return changeChildRingToFitIntoParentRing(
                        child as RingShapeRepresentation,
                        oldParent,
                        newParent as RingShapeRepresentation
                    )
                default:
                    throw new Error("Unsupported shape type " + child.shape.__typename)
            }
        default:
            throw new Error("Unsupported shape type " + newParent.shape.__typename)
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu changeChildPolygonToFitIntoParentRing
 */
function changeChildRingToFitIntoParentPolygon(child: RingShapeRepresentation, oldParent: ShapeRepresentation, newParent: PolygonShapeRepresentation, isPositionedAtInterior: boolean): ShapeRepresentation {
    const newParentBox = changeShapeType(newParent, "Box", isPositionedAtInterior)
    if (newParentBox === null) {
        throw new Error("New parent shape is already a box")
    }
    return changeChildRingToFitIntoParentBox(child, oldParent, newParentBox)
}

/**
 * Das ist im Grunde die invertierte Funktion zu changeChildBoxToFitIntoParentRing
 */
function changeChildRingToFitIntoParentBox(child: RingShapeRepresentation, oldParent: ShapeRepresentation, newParent: BoxShapeRepresentation): ShapeRepresentation {
    if (oldParent.shape.__typename !== "Ring") {
        throw new Error("Old parent is not a ring")
    }

    const oldParentStartAngle = oldParent.shape.startAngle
    const oldParentEndAngle = oldParent.shape.endAngle

    const childStartAngle = child.shape.startAngle
    const childEndAngle = child.shape.endAngle

    const newParentWidth = newParent.shape.width
    const newParentHalfWidth = newParentWidth / 2
    const newParentMinX = -newParentHalfWidth
    const newParentMaxX = newParentHalfWidth

    const childMinX = convert(childStartAngle, oldParentStartAngle, oldParentEndAngle, newParentMinX, newParentMaxX)
    const childMaxX = convert(childEndAngle, oldParentStartAngle, oldParentEndAngle, newParentMinX, newParentMaxX)

    const childTransformationMatrix = transformationMatrixOfShapeRepresentation(child)
    const [childTranslation] = tDecomposeMatrix(childTransformationMatrix)

    const childWidth = childMaxX - childMinX
    const childX = (childMinX + childMaxX) / 2
    const childY = childTranslation.y

    //die "-" vor childX ist invertiert, weil die Hauptachse der Kinder entgegengesetzt zur Hauptachse der Eltern ist
    const transformation = new Matrix4().makeTranslation(-childX, childY, 0)

    return {
        transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
        shape: {
            __typename: "Box",
            width: childWidth,
            height: child.shape.extrusion,
            depth: child.shape.size,
        }
    }
}

/**
 * Das ist im Grunde die invertierte Funktion zu changeChildRingToFitIntoParentBox
 */
function changeChildBoxToFitIntoParentRing(child: BoxShapeRepresentation, oldParent: ShapeRepresentation, newParent: RingShapeRepresentation): ShapeRepresentation {
    const oldParentWidth = widthOfShape(oldParent.shape)
    const oldParentHalfWidth = oldParentWidth / 2
    const oldParentMinX = -oldParentHalfWidth
    const oldParentMaxX = oldParentHalfWidth

    const childTransformationMatrix = transformationMatrixOfShapeRepresentation(child)
    const [childTranslation] = tDecomposeMatrix(childTransformationMatrix)
    const childY = childTranslation.y
    const childWidth = child.shape.width

    //die childTranslation.x ist invertiert, weil die Hauptachse der Kinder entgegengesetzt zur Hauptachse der Eltern ist
    const childMinX = -childTranslation.x - childWidth / 2
    const childMaxX = -childTranslation.x + childWidth / 2

    const parentStartAngle = newParent.shape.startAngle
    const parentEndAngle = newParent.shape.endAngle

    const childStartAngle = convert(childMinX, oldParentMinX, oldParentMaxX, parentStartAngle, parentEndAngle)
    const childEndAngle = convert(childMaxX, oldParentMinX, oldParentMaxX, parentStartAngle, parentEndAngle)

    const transformation = new Matrix4().makeTranslation(0, childY, 0)

    const newChild = deepCopyShapeRepresentation(newParent)
    newChild.transformationMatrix = matrix4ToTransformationMatrixArray(transformation)
    newChild.shape.startAngle = childStartAngle
    newChild.shape.endAngle = childEndAngle
    newChild.shape.thetaLength = childEndAngle - childStartAngle
    newChild.shape.extrusion = child.shape.height

    return newChild
}

/**
 * Das ist im Grunde die invertierte Funktion zu changeChildRingToFitIntoParentPolygon.
 */
function changeChildPolygonToFitIntoParentRing(
    child: PolygonShapeRepresentation,
    oldParent: ShapeRepresentation,
    newParent: RingShapeRepresentation,
    isPositionedAtInterior: boolean,
): ShapeRepresentation {
    const newChildBox = changeShapeType(child, "Box", isPositionedAtInterior)
    if (newChildBox === null) {
        throw new Error("Child shape is already a box")
    }
    return changeChildBoxToFitIntoParentRing(newChildBox, oldParent, newParent)
}

function changeChildRingToFitIntoParentRing(child: RingShapeRepresentation, oldParent: ShapeRepresentation, newParent: RingShapeRepresentation): ShapeRepresentation {
    //TODO <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< siehe kommentar @usage dieser funktion
    throw new Error("Not implemented")
}

export function shapeRepresentationToThreeShape(shapeRepresentation: ShapeRepresentation): THREE.Shape {
    switch (shapeRepresentation.shape.__typename) {
        case "Box":
            return boxShapeRepresentationToThreeShape(shapeRepresentation as BoxShapeRepresentation)
        case "Polygon":
            return polygonShapeRepresentationToThreeShape(shapeRepresentation as PolygonShapeRepresentation)
        case "Ring":
            return ringShapeRepresentationToThreeShape(shapeRepresentation as RingShapeRepresentation)
        default:
            throw new Error("Unsupported shape type: " + shapeRepresentation.shape.__typename)
    }
}

function boxShapeRepresentationToThreeShape(shapeRepresentation: BoxShapeRepresentation): THREE.Shape {
    const shape = new THREE.Shape()

    const halfWidth = shapeRepresentation.shape.width / 2
    const halfDepth = shapeRepresentation.shape.depth / 2

    shape.moveTo(-halfWidth, -halfDepth)
    shape.lineTo(-halfWidth, halfDepth)
    shape.lineTo(halfWidth, halfDepth)
    shape.lineTo(halfWidth, -halfDepth)
    shape.closePath()

    return shape
}

function polygonShapeRepresentationToThreeShape(shapeRepresentation: PolygonShapeRepresentation): THREE.Shape {
    const shape = new THREE.Shape()

    const width = widthOfShape(shapeRepresentation.shape)
    const depth = thicknessOfShape(shapeRepresentation.shape)

    const halfWidth = width / 2
    const halfDepth = depth / 2

    shape.moveTo(-halfWidth, -halfDepth)
    shape.lineTo(-halfWidth, halfDepth)
    shape.lineTo(halfWidth, halfDepth)
    shape.lineTo(halfWidth, -halfDepth)
    shape.closePath()

    return shape
}

function ringShapeRepresentationToThreeShape(shapeRepresentation: RingShapeRepresentation): THREE.Shape {
    const shape = new THREE.Shape()

    const center = shapeRepresentation.shape.center
    const innerRadius = shapeRepresentation.shape.innerRadius
    const outerRadius = shapeRepresentation.shape.outerRadius

    const lengthAngle = shapeRepresentation.shape.endAngle - shapeRepresentation.shape.startAngle

    const thetaStart = MathUtils.degToRad(shapeRepresentation.shape.startAngle) //in radians
    const thetaLength = MathUtils.degToRad(lengthAngle)  //in radians

    shape.absarc(center.x, -center.y, innerRadius, thetaStart, thetaStart + thetaLength, false);
    shape.absarc(center.x, -center.y, outerRadius, thetaStart + thetaLength, thetaStart, true);

    shape.closePath()

    return shape
}
