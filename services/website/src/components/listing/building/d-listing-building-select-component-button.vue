<template>
    <d-btn :icon="mdiOpenInApp"
           size="x-small"
           type="default"
           variant="text"
           @click="onClick"/>
</template>

<script lang="ts"
        setup>
    import {inject} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {mdiOpenInApp} from "@mdi/js";

    const props = defineProps<{
        emitterId: string
    }>()

    const renderer = inject(DBuildingRendererInjection)!

    function onClick() {
        renderer.selectionRaycaster.select(props.emitterId)
    }
</script>

<style scoped>
</style>