<template>
    <d-l-b-r-shape-representation :component="component">
        <d-l-b-r-point-of-interest-camera v-for="(camera, index) in cameras"
                                          :key="index"
                                          :camera="camera"
                                          :index="index"
                                          :point-of-interest-component="component"
                                          :show-i-phone-shape="!isJustAPhoto"/>

        <t-mesh v-if="icon"
                :render-order="renderOrder === null ? undefined : renderOrder + 1">
            <t-circle-geometry :end-angle="360"
                               :radius="size/2 * 0.75"
                               :start-angle="0"/>
            <t-mesh-basic-material :depth-test="false"
                                   transparent>
                <t-texture :texture-url="icon"/>
            </t-mesh-basic-material>
        </t-mesh>
    </d-l-b-r-shape-representation>
</template>

<script lang="ts"
        setup>
    import {BuildingPointOfInterest} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {CustomGeometryShapeRepresentation, PointOfInterestBuildingComponent, RaycastableBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {createSelectionEmitterAndConsumerWithRawAdditionalEmitterIds} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";
    import {BufferGeometry, CircleGeometry, Matrix4} from "three";
    import {transformationMatrixOfArray} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import {createHoverEmitterAndConsumer} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
    import {calculateCamerasOfPointOfInterest, DBuildingRendererInjection, isPointOfInterestJustAPhoto, PointOfInterestCamera, useShapeRepresentation} from "@/components/listing/building/building";
    import DLBRPointOfInterestCamera from "@/components/listing/building/renderer/d-l-b-r-point-of-interest-camera.vue";
    import TTexture from "@/adapter/three/components/t-texture.vue";
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshBasicMaterial from "@/adapter/three/components/material/t-mesh-basic-material.vue";
    import cameraPNG from "@/assets/3d/camera.png?url";
    import fencePNG from "@/assets/3d/fence.png?url";
    import heatingCoilPNG from "@/assets/3d/heating-coil.png?url";
    import lightbulbOnPNG from "@/assets/3d/lightbulb-on.png?url";
    import homeRoofPNG from "@/assets/3d/home-roof.png?url";
    import plusBoxOutlinePNG from "@/assets/3d/plus-box-outline.png?url";
    import windowClosedVariantPNG from "@/assets/3d/window-closed-variant.png?url";
    import radiatorPNG from "@/assets/3d/radiator.png?url";
    import radiatorDisabledPNG from "@/assets/3d/radiator-disabled.png?url";
    import TCircleGeometry from "@/adapter/three/components/geometry/t-circle-geometry.vue";
    import {createEmptyMutableFlowData, FlowData, useFlowDataBoolean, useFlowDataString} from "@/model/listing/FlowData";
    import {Optional} from "@/model/Optional";

    const props = defineProps<{
        pointOfInterest: BuildingPointOfInterest
    }>()

    const poiId = props.pointOfInterest.id
    const propsPoi = toRef(() => props.pointOfInterest)

    const raycasterObject: RaycastableBuildingComponent<BuildingPointOfInterest> = {
        type: "POINT_OF_INTEREST",
        component: propsPoi,
    }

    const size = 0.25
    const cameras = computed<readonly PointOfInterestCamera[]>(() => calculateCamerasOfPointOfInterest(propsPoi.value))
    const isJustAPhoto = computed<boolean>(() => isPointOfInterestJustAPhoto(propsPoi.value, cameras.value))

    const flowData = computed<FlowData>(() => {
        const flowData = createEmptyMutableFlowData()
        flowData.setFromInput(propsPoi.value.customData)
        return flowData
    })

    const isRadiator = useFlowDataBoolean(flowData, toRef({
        field: {
            id: "building_poi_is_radiator"
        },
        arrayIndex: null
    }))
    const isWindowNote = useFlowDataBoolean(flowData, toRef({
        field: {
            id: "building_poi_is_window_note"
        },
        arrayIndex: null
    }))
    const isSpecialThermalComponent = useFlowDataBoolean(flowData, toRef({
        field: {
            id: "building_poi_is_special_thermal_component"
        },
        arrayIndex: null
    }))
    const isDormer = useFlowDataBoolean(flowData, toRef({
        field: {
            id: "building_poi_is_dormer"
        },
        arrayIndex: null
    }))

    const isLamp = useFlowDataBoolean(flowData, toRef({
        field: {
            id: "building_poi_is_lamp"
        },
        arrayIndex: null
    }))
    const radiatorType = useFlowDataString(flowData, toRef({
        field: {
            id: "building_poi_radiator_type"
        },
        arrayIndex: null
    }))

    // noinspection OverlyComplexFunctionJS
    const icon = computed<Optional<string>>(() => {
        if (isJustAPhoto.value) {
            return cameraPNG
        }
        if (isRadiator.value) {
            switch (radiatorType.value) {
                case "RADIATOR":
                    return fencePNG
                case "PANEL_RADIATOR":
                    return radiatorDisabledPNG
                case "TUBE_RADIATOR":
                    return heatingCoilPNG
                case "CONVECTOR_HEATER":
                    return radiatorPNG
                default:
                    return radiatorPNG
            }
        }

        if (isLamp.value) {
            return lightbulbOnPNG
        }

        if (isWindowNote.value) {
            return windowClosedVariantPNG
        }

        if (isSpecialThermalComponent.value) {
            return plusBoxOutlinePNG
        }

        if (isDormer.value) {
            return homeRoofPNG
        }

        return null
    })

    const component: PointOfInterestBuildingComponent = {
        id: poiId,
        type: "POINT_OF_INTEREST",
        component: propsPoi,
        hover: createHoverEmitterAndConsumer(poiId, raycasterObject),
        selection: createSelectionEmitterAndConsumerWithRawAdditionalEmitterIds(poiId, raycasterObject, BuildingRenderer.selectionEmitterIdForPoi(poiId)),
        customShapeRepresentation: {
            customShapeType: "GEOMETRY",
            transformationMatrix: computed<Matrix4>(() => {
                const baseTransformation = transformationMatrixOfArray(propsPoi.value.transformationMatrix)

                if (renderer.renderType === "3D") {
                    return baseTransformation
                }

                const translationY = new Matrix4().makeTranslation(0, 4, 0) //4m höher in 2d
                return translationY.clone().multiply(baseTransformation)
            }),
            geometry: computed<BufferGeometry>(oldGeometry => {
                oldGeometry?.dispose()

                return new CircleGeometry(size / 2, 32)
            }),
            addToScene: false,
        } satisfies CustomGeometryShapeRepresentation,
    }

    const renderer = inject(DBuildingRendererInjection)!
    const {renderOrder} = useShapeRepresentation(renderer, toRef(() => component))

    onUnmounted(() => {
        component.customShapeRepresentation.geometry.value.dispose()
    })
</script>

<style scoped>
</style>