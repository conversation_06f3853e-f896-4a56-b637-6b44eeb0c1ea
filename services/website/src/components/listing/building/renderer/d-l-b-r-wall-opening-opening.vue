<template>
    <!-- OPENING -->
    <d-l-b-r-construction-part :component="component"
                               :construction-part="wallOpeningOpening"/>

    <!-- METRICS -->
    <d-l-b-r-wall-opening-metrics :opening-component="component"
                                  :wall="wall"/>
</template>

<script lang="ts"
        setup>
    import {ConstructionPart, Wall} from "@/adapter/graphql/generated/graphql";
    import {toRef} from "vue";
    import {createSelectionEmitterAndConsumerWithRawAdditionalEmitterIds} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
    import DLBRConstructionPart from "@/components/listing/building/renderer/d-l-b-r-construction-part.vue";
    import {createHoverEmitterAndConsumer} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
    import {RaycastableBuildingComponent, WallOpeningOpeningBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import DLBRWallOpeningMetrics from "@/components/listing/building/renderer/d-l-b-r-wall-opening-metrics.vue";

    const props = defineProps<{
        wall: Wall,
        wallOpeningOpening: ConstructionPart
    }>()

    const wallOpeningOpeningId = props.wallOpeningOpening.id
    const propsWallOpeningOpening = toRef(() => props.wallOpeningOpening)

    const raycasterObject: RaycastableBuildingComponent<ConstructionPart> = {
        type: "WALL_OPENING_OPENING",
        component: propsWallOpeningOpening,
    }

    const component: WallOpeningOpeningBuildingComponent = {
        type: "WALL_OPENING_OPENING",
        component: propsWallOpeningOpening,
        selection: createSelectionEmitterAndConsumerWithRawAdditionalEmitterIds(wallOpeningOpeningId, raycasterObject, BuildingRenderer.selectionEmitterIdForOpening(wallOpeningOpeningId)),
        hover: createHoverEmitterAndConsumer(wallOpeningOpeningId, raycasterObject),
    }
</script>

<style scoped>
</style>