<template>
    <div v-show="false"/>
</template>

<script lang="ts"
        setup>
    import {inject, onMounted, onUnmounted, watch} from "vue";
    import {Object3D} from "three";

    import {Optional} from "@/model/Optional";
    import {TObjectInjection} from "@/adapter/three/TInjectionKeys";
    import {loadThreeBuildingIPhone, THREE_BUILDING_IPHONE} from "@/components/listing/three-building-resources";

    const parentObject = inject(TObjectInjection)!
    let object: Optional<Object3D> = null

    watch(THREE_BUILDING_IPHONE, iphone => {
        if (iphone !== null) {
            object = iphone.clone()
            object.scale.multiplyScalar(0.001)
            parentObject.add(object)
        }
    }, {
        immediate: true
    })

    onMounted(() => {
        loadThreeBuildingIPhone()
    })

    onUnmounted(() => {
        if (object !== null) {
            parentObject.remove(object);
        }
    })
</script>

<style scoped>
</style>