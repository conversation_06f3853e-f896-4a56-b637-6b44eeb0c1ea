<template>
    <d-l-b-r-shape-representation :component="component"/>
    <d-l-b-r-shape-representation :component="componentOverlay"/>
</template>

<script lang="ts"
        setup>
    import {Wall} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, onUnmounted, toRef} from "vue";
    import {adjustedWallHeight, adjustedWallWidth, DBuildingRendererInjection, thicknessOfShape} from "@/components/listing/building/building";
    import {CustomGeometryShapeRepresentation, RaycastableBuildingComponent, WallSizeAdjustmentBuildingComponent, WallSizeAdjustmentOverlayBuildingComponent, WallWithHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {BoxGeometry, BufferGeometry, Matrix4} from "three";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";
    import {createHoverEmitter} from "@/adapter/three/raycasting/hover/THoverRaycasterEmitterOrConsumer";
    import {createSelectionEmitter} from "@/adapter/three/raycasting/selection/TSelectionRaycasterEmitterOrConsumer";
    import {calculateSizeAdjustmentXFor, calculateSizeAdjustmentYFor} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";

    const props = defineProps<{
        id: string
        wall: Wall
        wallWithHolesComponent: WallWithHolesBuildingComponent
    }>()

    const propsWall = toRef(() => props.wall)
    const renderer = inject(DBuildingRendererInjection)!

    const width = computed<number>(() => adjustedWallWidth(renderer, propsWall.value, true))
    const height = computed<number>(() => adjustedWallHeight(renderer, propsWall.value, true))
    const rawThickness = computed<number>(() => thicknessOfShape(propsWall.value.shapeRepresentation.shape))

    const newThickness = computed<number>(() => Math.max(renderer.fallbackSize, rawThickness.value + propsWall.value.sizeAdjustmentZ))

    const raycasterObject: RaycastableBuildingComponent<Wall> = {
        type: "WALL_SIZE_ADJUSTMENT",
        component: propsWall,
    }

    const transformationMatrix = computed<Matrix4>(() => {
        const wall = propsWall.value
        const isPositionedAtInterior = wall.isExterior === true

        const oldThickness = rawThickness.value
        const deltaZ = newThickness.value - oldThickness

        let transformation = isPositionedAtInterior
            ? new Matrix4().makeTranslation(
                0,
                0,
                -deltaZ / 2
            )
            : new Matrix4().identity()

        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        renderer.snappingManager.refreshCounter.value //trigger reactivity

        const sizeAdjustmentXResult = calculateSizeAdjustmentXFor(renderer, wall)
        if (sizeAdjustmentXResult !== null) {
            const lefts = sizeAdjustmentXResult.sides.filter(s => s.type === "LEFT")
            const rights = sizeAdjustmentXResult.sides.filter(s => s.type === "RIGHT")
            const left = lefts.length > 0 ? lefts[0].sizeAdjustmentX : 0
            const right = rights.length > 0 ? rights[0].sizeAdjustmentX : 0
            const sideDelta = (right - left) / 2

            const translationX = new Matrix4().makeTranslation(
                sideDelta,
                0,
                0
            )

            transformation = transformation.multiply(translationX)
        }

        const sizeAdjustmentYResult = calculateSizeAdjustmentYFor(renderer, wall)
        if (sizeAdjustmentYResult !== null) {
            const floors = sizeAdjustmentYResult.sides.filter(s => s.type === "FLOOR")
            const ceilings = sizeAdjustmentYResult.sides.filter(s => s.type === "CEILING")
            const floor = floors.length > 0 ? floors[0].sizeAdjustmentY : 0
            const ceiling = ceilings.length > 0 ? ceilings[0].sizeAdjustmentY : 0
            const sideDelta = (ceiling - floor) / 2

            const translationY = new Matrix4().makeTranslation(
                0,
                sideDelta,
                0
            )

            transformation = transformation.multiply(translationY)
        }

        return transformation
    })

    const geometry = computed<BufferGeometry>(oldGeometry => {
        oldGeometry?.dispose()

        return new BoxGeometry(
            width.value,
            height.value,
            newThickness.value
        )
    })

    onUnmounted(() => {
        geometry.value.dispose()
    })

    const customShapeRepresentation: CustomGeometryShapeRepresentation = {
        customShapeType: "GEOMETRY",
        transformationMatrix,
        geometry,
        addToScene: false,
    }

    const component: WallSizeAdjustmentBuildingComponent = {
        type: "WALL_SIZE_ADJUSTMENT",
        component: propsWall,
        wallComponent: props.wallWithHolesComponent,
        id: props.id,
        customShapeRepresentation,
        hover: createHoverEmitter(props.id, raycasterObject),
        selection: createSelectionEmitter(props.id, raycasterObject),
    }

    const componentOverlay: WallSizeAdjustmentOverlayBuildingComponent = {
        type: "WALL_SIZE_ADJUSTMENT_OVERLAY",
        component: propsWall,
        wallComponent: props.wallWithHolesComponent,
        id: `${props.id}-overlay`,
        customShapeRepresentation,
    }
</script>

<style scoped>
</style>