<template>
    <t-group :transformation="metricsTransformation">
        <d-l-b-r-shape-representation :component="horzontalLineComponent"/>
        <d-l-b-r-shape-representation :component="verticalLeftLineComponent"/>
        <d-l-b-r-shape-representation :component="verticalRightLineComponent"/>
        <d-l-b-r-shape-representation :component="textComponent"/>
        <d-l-b-r-shape-representation :component="textBackgroundComponent"/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted, ref} from "vue";
    import {adjustedWallWidth, BUILDING_EPSILON, DBuildingRendererInjection, thicknessOfShape} from "@/components/listing/building/building";
    import {WallMetricsLineBuildingComponent, WallMetricsTextWidthBackgroundBuildingComponent, WallMetricsTextWidthBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {Box3, BufferGeometry, Matrix4, PlaneGeometry, Vector3} from "three";
    import {transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {useI18n} from "vue-i18n";
    import {Optional} from "@/model/Optional";
    import {isNumberGreaterThan} from "@/utility/number";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";
    import {calculateSizeAdjustmentXFor} from "@/components/listing/building/pipeline/stages/RecalculateAllWallSizeAdjustmentsBPipelineStage";

    const verticalLineSize = 0.1 // 10 cm
    const fontSize = 0.08
    const distance = verticalLineSize / 2 + 0.025

    const props = defineProps<{
        wallWithHolesComponent: WallWithHolesBuildingComponent
        wallWithoutHolesComponent: WallWithoutHolesBuildingComponent
    }>()
    const propsWall = props.wallWithHolesComponent.component
    const wallId = propsWall.value.id

    const renderer = inject(DBuildingRendererInjection)!

    const idHorizontalLine = `${wallId}-metricsOutside-horizontalLine`
    const idVerticalLeftLine = `${wallId}-metricsOutside-verticalLeftLine`
    const idVerticalRightLine = `${wallId}-metricsOutside-verticalRightLine`
    const idText = `${wallId}-metricsOutside-text`
    const idTextBackground = `${wallId}-metricsOutside-textBackground`

    const textBackgroundPaddingX = 0.025
    const textBackgroundPaddingY = 0.025

    const {n} = useI18n()

    const rawThickness = computed<number>(() => thicknessOfShape(propsWall.value.shapeRepresentation.shape))
    const width = computed<number>(() => adjustedWallWidth(renderer, propsWall.value, true))
    const halfWidth = computed<number>(() => width.value / 2)

    const noThicknessOffsetZ = computed<number>(() => {
        const wall = propsWall.value
        const shape = wall.shapeRepresentation.shape

        if (isNumberGreaterThan(rawThickness.value, 0, BUILDING_EPSILON)) {
            return 0
        }

        if (shape.__typename === "Box") {
            return wall.isExterior === true
                ? 0
                : -renderer.fallbackSize / 2
        }

        if (shape.__typename === "Polygon") {
            return wall.isExterior === true
                ? 0
                : -renderer.fallbackSize / 2
        }

        return 0
    })

    const metricsTransformation = computed<Matrix4>(() => {
        const wall = propsWall.value
        const is2D = renderer.renderType === '2D'

        const isExterior = wall.isExterior === true;

        let distanceZ = isExterior
            ? rawThickness.value / 2
            : 0

        distanceZ += isExterior
            ? -(rawThickness.value + wall.sizeAdjustmentZ)
            : -(rawThickness.value + wall.sizeAdjustmentZ) / 2

        distanceZ += noThicknessOffsetZ.value

        if (is2D) {
            distanceZ -= distance
        }

        //Das kann man auch auskommentieren, wenn man richtungsbasierte Messlinien haben möchte
        if (!isExterior) {
            distanceZ = 0
        }

        const wallTransformation = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)
        const translation = is2D
            ? new Matrix4().makeTranslation(0, 0, distanceZ)
            : new Matrix4().makeTranslation(0, distanceZ, 0)
        const rotation = is2D
            ? new Matrix4().identity()
            : new Matrix4().makeRotationX(Math.PI / 2)
        const baseTransformation = wallTransformation.clone().multiply(rotation).multiply(translation)

        //SIDE OFFSET
        const sizeAdjustmentXResult = calculateSizeAdjustmentXFor(renderer, wall)
        if (sizeAdjustmentXResult === null) {
            return baseTransformation
        }
        const lefts = sizeAdjustmentXResult.sides.filter(s => s.type === "LEFT")
        const rights = sizeAdjustmentXResult.sides.filter(s => s.type === "RIGHT")
        const left = lefts.length > 0 ? lefts[0].sizeAdjustmentX : 0
        const right = rights.length > 0 ? rights[0].sizeAdjustmentX : 0
        const sideDelta = (right - left) / 2

        const deltaTransformation = new Matrix4().makeTranslation(
            sideDelta,
            0,
            0
        )

        return baseTransformation.multiply(deltaTransformation)
    })

    const text = computed<Optional<string>>(() => {
        /*const number =*/
        return n(width.value * 100, 'integer')
        // const unit = 'cm'//TODO: t('enums.listingFieldUnit.CENTIMETER') //no font support, fallback to "cm"
        // return `${number} ${unit}`
    })

    const textComponent: WallMetricsTextWidthBuildingComponent = {
        id: idText,
        type: "WALL_METRICS_TEXT_WIDTH",
        component: propsWall,
        wallWithHolesComponent: props.wallWithHolesComponent,
        wallWithoutHolesComponent: props.wallWithoutHolesComponent,
        customShapeRepresentation: {
            customShapeType: "TEXT",
            transformationMatrix: ref(new Matrix4().identity()),
            text,
            fontSize,
            thickness: 0,
            boundingBox: ref(new Box3()),
        },
    }
    const horzontalLineComponentVertices = computed<readonly Vector3[]>(() => [
        new Vector3(-halfWidth.value, 0, 0),
        new Vector3(halfWidth.value, 0, 0),
    ]);
    const horzontalLineComponent: WallMetricsLineBuildingComponent = {
        id: idHorizontalLine,
        type: "WALL_METRICS_LINE",
        component: propsWall,
        textComponent,
        customShapeRepresentation: {
            customShapeType: "LINES",
            transformationMatrix: ref(new Matrix4().identity()),
            linesType: "LINE",
            vertices: horzontalLineComponentVertices,
            addToScene: false,
        },
    }
    const verticalLeftLineComponentVertices = computed<readonly Vector3[]>(() => [
        new Vector3(-halfWidth.value, 0, -verticalLineSize / 2),
        new Vector3(-halfWidth.value, 0, verticalLineSize / 2),
    ]);
    const verticalLeftLineComponent: WallMetricsLineBuildingComponent = {
        id: idVerticalLeftLine,
        type: "WALL_METRICS_LINE",
        component: propsWall,
        textComponent,
        customShapeRepresentation: {
            customShapeType: "LINES",
            transformationMatrix: ref(new Matrix4().identity()),
            linesType: "LINE",
            vertices: verticalLeftLineComponentVertices,
            addToScene: false,
        },
    }
    const verticalRightLineComponentVertices = computed<readonly Vector3[]>(() => [
        new Vector3(halfWidth.value, 0, -verticalLineSize / 2),
        new Vector3(halfWidth.value, 0, verticalLineSize / 2),
    ]);
    const verticalRightLineComponent: WallMetricsLineBuildingComponent = {
        id: idVerticalRightLine,
        type: "WALL_METRICS_LINE",
        component: propsWall,
        textComponent,
        customShapeRepresentation: {
            customShapeType: "LINES",
            transformationMatrix: ref(new Matrix4().identity()),
            linesType: "LINE",
            vertices: verticalRightLineComponentVertices,
            addToScene: false,
        },
    }

    const textBackgroundComponent: WallMetricsTextWidthBackgroundBuildingComponent = {
        id: idTextBackground,
        type: "WALL_METRICS_TEXT_WIDTH_BACKGROUND",
        component: propsWall,
        textComponent,
        customShapeRepresentation: {
            customShapeType: "GEOMETRY",
            transformationMatrix: computed<Matrix4>(() => textComponent.customShapeRepresentation.transformationMatrix.value.clone()),
            geometry: computed<BufferGeometry>(oldGeometry => {
                oldGeometry?.dispose()

                const bbox = textComponent.customShapeRepresentation.boundingBox.value
                const size = bbox.getSize(new Vector3())

                return new PlaneGeometry( //die geometrie muss erneuert werden, weil man die box geometrie nicht verändern kann
                    size.x + 2 * textBackgroundPaddingX,
                    size.y + 2 * textBackgroundPaddingY,
                )
            }),
            addToScene: false,
        }
    }

    onUnmounted(() => {
        textBackgroundComponent.customShapeRepresentation.geometry.value.dispose()
    })
</script>

<style scoped>
</style>