<template>
    <t-group v-if="visibility !== 'ALWAYS_HIDDEN'"
             :visible="isSelfVisible || areChildrenVisible">
        <t-group :debug="debug"
                 :transformation="shapeRepresentation.transformationMatrix.value"
                 :visible="isSelfVisible">

            <t-group v-if="shapeRepresentation.showIPhoneShape"
                     :transformation="rotationMatrix">
                <t-mesh :render-order="renderOrder === null ? undefined : renderOrder"
                        :transformation="iPhoneTranslationMatrix">
                    <t-box-geometry :depth="iPhoneSize.z"
                                    :height="iPhoneSize.y"
                                    :width="iPhoneSize.x"/>
                    <t-mesh-material-raw :material="material"/>
                </t-mesh>

                <!--                <d-l-b-r-shape-representation-custom-perspective-camera-iphone/>-->
            </t-group>

            <t-perspective-camera-raw :debug-cone-color="0x000000"
                                      :debug-frustum-color="0x000000"
                                      :debug-target-color="0xFF0000"
                                      :perspective-camera="shapeRepresentation.perspectiveCamera.value"
                                      :render-order="renderOrder === null ? undefined : renderOrder-1"
                                      :visible="isSelfVisible"
                                      debug
                                      debug-hide-cross
                                      debug-hide-near
                                      debug-hide-up/>
        </t-group>

        <slot/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import TMesh from "@/adapter/three/components/t-mesh.vue";
    import TMeshMaterialRaw from "@/adapter/three/components/material/t-mesh-material-raw.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {BuildingComponentWithCustomPerspectiveCameraShapeRepresentation, CustomPerspectiveCameraShapeRepresentation} from "@/components/listing/building/renderer/BuildingComponent";
    import {computed, inject, toRef} from "vue";
    import {DBuildingRendererInjection, useShapeRepresentation} from "@/components/listing/building/building";
    import {TMaterialDeclaration, TMeshMaterial} from "@/adapter/three/TMaterial";
    import TBoxGeometry from "@/adapter/three/components/geometry/t-box-geometry.vue";
    import TPerspectiveCameraRaw from "@/adapter/three/components/camera/t-perspective-camera-raw.vue";
    import {Matrix4, Vector3} from "three";

    const props = defineProps<{
        component: BuildingComponentWithCustomPerspectiveCameraShapeRepresentation
    }>()

    const iPhoneCameraOffsetPercentageX = 0.35
    const iPhoneCameraOffsetPercentageY = 0.45
    const iPhoneSize = new Vector3(0.0715, 0.1496, 0.00825) //https://www.apple.com/de/iphone-16-pro/specs/
    const rotationMatrix = new Matrix4().makeRotationZ(-Math.PI / 2)
    const iPhoneTranslationMatrix = new Matrix4().makeTranslation(
        iPhoneSize.x * iPhoneCameraOffsetPercentageX,
        iPhoneSize.y * iPhoneCameraOffsetPercentageY,
        iPhoneSize.z / 2
    )

    const componentRef = toRef(() => props.component)

    const renderer = inject(DBuildingRendererInjection)!

    const material = computed<TMaterialDeclaration<TMeshMaterial>>(() => renderer.materialOf(componentRef.value) as TMaterialDeclaration<TMeshMaterial>)
    const shapeRepresentation = computed<CustomPerspectiveCameraShapeRepresentation>(() => componentRef.value.customShapeRepresentation)

    const {
        visibility,
        isSelfVisible,
        areChildrenVisible,
        debug,
        renderOrder,
    } = useShapeRepresentation(renderer, componentRef)
</script>

<style scoped>
</style>