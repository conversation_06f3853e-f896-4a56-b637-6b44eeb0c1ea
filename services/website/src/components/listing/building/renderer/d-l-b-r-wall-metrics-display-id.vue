<template>
    <t-group :transformation="transformation">
        <d-l-b-r-shape-representation :component="textComponent"/>
        <d-l-b-r-shape-representation :component="textBackgroundComponent"/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted, ref} from "vue";
    import {adjustedWallWidth, BUILDING_EPSILON, DBuildingRendererInjection, thicknessOfShape} from "@/components/listing/building/building";
    import {WallMetricsTextDisplayIdBackgroundBuildingComponent, WallMetricsTextDisplayIdBuildingComponent, WallWithHolesBuildingComponent, WallWithoutHolesBuildingComponent} from "@/components/listing/building/renderer/BuildingComponent";
    import {Box3, BufferGeometry, Matrix4, PlaneGeometry, Vector3} from "three";
    import {transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {Optional} from "@/model/Optional";
    import {isNumberGreaterThan} from "@/utility/number";
    import DLBRShapeRepresentation from "@/components/listing/building/renderer/d-l-b-r-shape-representation.vue";

    const fontSize = 0.08

    const props = defineProps<{
        wallWithHolesComponent: WallWithHolesBuildingComponent
        wallWithoutHolesComponent: WallWithoutHolesBuildingComponent
    }>()
    const propsWall = props.wallWithHolesComponent.component
    const wallId = propsWall.value.id

    const renderer = inject(DBuildingRendererInjection)!

    const idText = `${wallId}-metricsDisplayId-text`
    const idTextBackground = `${wallId}-metricsDisplayId-textBackground`

    const textBackgroundPaddingX = 0.025
    const textBackgroundPaddingY = 0.025

    const rawThickness = computed<number>(() => thicknessOfShape(propsWall.value.shapeRepresentation.shape))
    const width = computed<number>(() => adjustedWallWidth(renderer, propsWall.value, true))

    const noThicknessOffsetZ = computed<number>(() => {
        const wall = propsWall.value
        const shape = wall.shapeRepresentation.shape

        if (isNumberGreaterThan(rawThickness.value, 0, BUILDING_EPSILON)) {
            return 0
        }

        if (shape.__typename === "Box") {
            if (!renderer.isEvebiModeEnabled.value) {
                return -renderer.fallbackSize / 2
            }
            return wall.isExterior === true
                ? 0
                : -renderer.fallbackSize / 2
        }

        if (shape.__typename === "Polygon") {
            if (!renderer.isEvebiModeEnabled.value) {
                return -renderer.fallbackSize / 2
            }
            return wall.isExterior === true
                ? 0
                : -renderer.fallbackSize / 2
        }

        return 0
    })

    const transformation = computed<Matrix4>(() => {
        const wall = propsWall.value
        const is2D = renderer.renderType === '2D'

        const isExterior = wall.isExterior === true;

        let distanceZ = isExterior
            ? rawThickness.value / 2
            : 0

        distanceZ += -(rawThickness.value + (renderer.isEvebiModeEnabled.value ? wall.sizeAdjustmentZ : 0)) / 2
        distanceZ += noThicknessOffsetZ.value

        //Das kann man auch auskommentieren, wenn man richtungsbasierte Messlinien haben möchte
        if (!isExterior) {
            distanceZ = 0
        }

        const wallTransformation = transformationMatrixOfShapeRepresentation(wall.shapeRepresentation)
        const translation = is2D
            ? new Matrix4().makeTranslation(width.value / 4, 0, distanceZ) //TODO: width
            : new Matrix4().makeTranslation(width.value / 4, distanceZ, 0) //TODO: width
        const rotation = is2D
            ? new Matrix4().identity()
            : new Matrix4().makeRotationX(Math.PI / 2)
        return wallTransformation.clone().multiply(rotation).multiply(translation)
    })

    const text = computed<Optional<string>>(() => {
        const displayId = propsWall.value.displayId
        if (displayId === null) {
            return null
        }
        return `#${displayId}`
    })

    const textComponent: WallMetricsTextDisplayIdBuildingComponent = {
        id: idText,
        type: "WALL_METRICS_TEXT_DISPLAY_ID",
        component: propsWall,
        wallWithHolesComponent: props.wallWithHolesComponent,
        wallWithoutHolesComponent: props.wallWithoutHolesComponent,
        customShapeRepresentation: {
            customShapeType: "TEXT",
            transformationMatrix: ref(new Matrix4().identity()),
            text,
            fontSize,
            thickness: 0,
            boundingBox: ref(new Box3()),
        },
    }

    const textBackgroundComponent: WallMetricsTextDisplayIdBackgroundBuildingComponent = {
        id: idTextBackground,
        type: "WALL_METRICS_TEXT_DISPLAY_ID_BACKGROUND",
        component: propsWall,
        textComponent,
        customShapeRepresentation: {
            customShapeType: "GEOMETRY",
            transformationMatrix: computed<Matrix4>(() => textComponent.customShapeRepresentation.transformationMatrix.value.clone()),
            geometry: computed<BufferGeometry>(oldGeometry => {
                oldGeometry?.dispose()

                const bbox = textComponent.customShapeRepresentation.boundingBox.value
                const size = bbox.getSize(new Vector3())

                return new PlaneGeometry( //die geometrie muss erneuert werden, weil man die box geometrie nicht verändern kann
                    size.x + 2 * textBackgroundPaddingX,
                    size.y + 2 * textBackgroundPaddingY,
                )
            }),
            addToScene: false,
        }
    }

    onUnmounted(() => {
        textBackgroundComponent.customShapeRepresentation.geometry.value.dispose()
    })
</script>

<style scoped>
</style>