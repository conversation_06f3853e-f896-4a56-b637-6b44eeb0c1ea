<template>
    <t-group v-if="visibility !== 'ALWAYS_HIDDEN'"
             :debug="debug"
             :visible="isSelfVisible || areChildrenVisible">
        <!-- TODO: lines2 gehen nicht mit der gsao render pass, three js bug ticket erstellen -->
        <!-- TODO: eig. muss add to scene auf group-ebene -->
        <t-lines v-if="renderer.renderType === '3D' || renderer.renderer === 'SVG'"
                 :add-to-scene="shapeRepresentation.addToScene"
                 :material="material as TMaterialDeclaration<TLineMaterial>"
                 :render-order="renderOrder === null ? undefined : renderOrder"
                 :transformation="zFightingCorrectedTransformation"
                 :type="shapeRepresentation.linesType"
                 :vertices="shapeRepresentation.vertices.value"
                 :visible="isSelfVisible"/>

        <t-lines2 v-else
                  :add-to-scene="shapeRepresentation.addToScene"
                  :material="material as TMaterialDeclaration<TLine2Material>"
                  :render-order="renderOrder === null ? undefined : renderOrder"
                  :transformation="zFightingCorrectedTransformation"
                  :vertices="shapeRepresentation.vertices.value"
                  :visible="isSelfVisible"/>

        <slot/>
    </t-group>
</template>

<script lang="ts"
        setup>
    import {TLine2Material, TLineMaterial, TMaterialDeclaration} from "@/adapter/three/TMaterial";
    import TLines from "@/adapter/three/components/object/t-lines.vue";
    import {computed, inject, toRef} from "vue";
    import {DBuildingRendererInjection, useShapeRepresentation} from "@/components/listing/building/building";
    import TLines2 from "@/adapter/three/components/object/t-lines2.vue";
    import TGroup from "@/adapter/three/components/object/t-group.vue";
    import {BuildingComponentWithCustomLineShapeRepresentation, CustomLineShapeRepresentation} from "@/components/listing/building/renderer/BuildingComponent";
    import {Matrix4} from "three";

    const props = defineProps<{
        component: BuildingComponentWithCustomLineShapeRepresentation
    }>()

    const renderer = inject(DBuildingRendererInjection)!

    const material = computed<TMaterialDeclaration<TLineMaterial | TLine2Material>>(() => renderer.materialOf(props.component) as TMaterialDeclaration<TLineMaterial | TLine2Material>)
    const shapeRepresentation = computed<CustomLineShapeRepresentation>(() => props.component.customShapeRepresentation)
    const transformation = computed<Matrix4>(() => shapeRepresentation.value.transformationMatrix.value)

    const zFightingCorrectedTransformation = computed<Matrix4>(() => {
        const baseTransformation = transformation.value
        const offsetY = zFightingOffsetY.value
        if (offsetY === null) {
            return baseTransformation
        }
        return baseTransformation.clone().multiply(new Matrix4().makeTranslation(0, offsetY, 0))
    })

    const {
        visibility,
        isSelfVisible,
        areChildrenVisible,
        debug,
        renderOrder,
        zFightingOffsetY,
    } = useShapeRepresentation(renderer, toRef(() => props.component))
</script>

<style scoped>
</style>