import {Box, Building, BuildingPointOfInterest, BuildingPointOfInterestInput, ConstructionPart, CustomizableBuildingComponent, Extrudable, Floor, FloorLevelInfo, FlowConfigField, FlowConfigFieldValue, FlowConfigFieldValueInput, ListingBuildingScanRoom, ListingBuildingScanRoomInput, Polygon, PolygonInput, Ring, RingInput, Room, Shape, ShapeRepresentation, ShapeRepresentationInput, Vector3D, Wall} from "@/adapter/graphql/generated/graphql";
import {BufferGeometry, MathUtils, Matrix4, Mesh, Vector2, Vector3} from "three";
import {computed, InjectionKey, Ref, toRaw, toRef} from "vue";
import {Optional} from "@/model/Optional";
import {createIdentityMatrix, matrix4ToTransformationMatrixArray, shapeToBoxInput, shapeToBufferedGeometry_000, shapeToPolygonInput, shapeToRingInput, transformationMatrixOfArray, transformationMatrixOfShapeRepresentation} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
import {areFlowConfigFieldValuesEqual, createEmptyMutableFlowData, FlowData, useFlowDataDouble, useFlowDataString} from "@/model/listing/FlowData";
import {tAreMatricesEqual, tAreVectors2Equal, tAreVectors3Equal, tCalculateCircleCenterAndRadius, tDecomposeMatrix, tDecomposeMatrixToComponents, tDestroyMesh, tOverrideTransformation, tRotationYInDegreeFromTransformation, tSizeOfVectors2, tSizeOfVectors3} from "@/adapter/three/three-utility";
import {TSnapPoint, TSnapPointPositionChangedCallback} from "@/adapter/three/snapping/TSnapPoint";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {BuildingComponent, RaycastableBuildingComponent, WallRoofPoint} from "@/components/listing/building/renderer/BuildingComponent";
import {RenderVisibility} from "@/components/listing/building/renderer/RenderVisibility";
import {TMaybeRaycasterRelated, TRaycasterRelated} from "@/adapter/three/raycasting/TRaycasterRelated";
import {WallCreator} from "@/components/listing/building/renderer/WallCreator";
import {TSnapLine} from "@/adapter/three/snapping/TSnapLine";
import {NamedValue} from "vue-i18n";
import {areNumbersEqual, areNumbersNotEqual, isNumberGreaterThan, isNumberLessThanOrEqual} from "@/utility/number";
import {ShapeType} from "@/model/building/ShapeType";
import {changeChildShapeIfNeeded, changeShapeType} from "@/components/listing/building/shape-representation-utils";
import {EnsureDefined, mapFlowConfigFieldValueToInput, mapInputToFlowConfigFieldValue} from "@/adapter/graphql/mapper/graphql-mapper";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
import {wallOrOpeningToLineSegment} from "@/components/listing/building/wall-and-opening-lines";
import {TLineSegment2D} from "@/adapter/three/TLineSegment2D";
import {RoomCalculator} from "@/components/listing/building/renderer/RoomCalculator";
import {d3IsVector2InsidePolygon} from "@/adapter/d3/d3-utils";
import {wallTypeOfWall} from "@/model/building/WallType";
import {WallOpeningType} from "@/model/building/WallOpeningType";

export const BUILDING_EPSILON = 0.005 //halber zentimeter abweichung wird ignoriert
export const BUILDING_EPSILON_FOR_ROTATIONS = 0.0025 //https://chatgpt.com/c/67a20512-36a0-8004-873f-060c2bc5b13d
export const BUILDING_WALL_MIN_HEIGHT = 0.05 //in m
export const BUILDING_WALL_MIN_WIDTH = 0.05 //in m
export const BUILDING_WALL_RING_RADIUS_MIN = 0.3 //in m 28.7cm entspricht der Mindestlänge einer Wand von 5cm bei einem Theta von 10°
export const BUILDING_WALL_RING_THETA_MIN = 10 //in degree
export const BUILDING_OPENING_MIN_HEIGHT = 0.05 //in m
export const BUILDING_OPENING_MIN_WIDTH = 0.05 //in m
export const BUILDING_FLOOR_SLAB_THICKNESS_MIN = 0.10 //in m
export const BUILDING_FLOOR_SLAB_THICKNESS_MAX = 10.0 //in m
export const BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT = 0.24// 0.24 //in m
export const BULDING_WALL_THICKNESS_DEFAULT_EVEBI = 0.18 //in m

export const DBuildingRendererInjection: InjectionKey<BuildingRenderer> = Symbol('dBuildingRendererInjection')

export function transformationOfShapeRepresentation(shapeRepresentation: ShapeRepresentationInput): Matrix4 {
    return new Matrix4().fromArray(shapeRepresentation.transformationMatrix.flat())
}

export function vector3DToVector3(vector3D: Vector3D): Vector3 {
    return new Vector3(vector3D.x, vector3D.y, vector3D.z)
}

export function vector2DToVector2(vector2D: Vector3D): Vector2 {
    return new Vector2(vector2D.x, vector2D.y)
}

export function isExtrudable3D(extrudable: Extrudable | PolygonInput | RingInput): boolean {
    return areNumbersEqual(extrudable.extrusionDirection.x, 0, BUILDING_EPSILON) &&
        areNumbersEqual(extrudable.extrusionDirection.y, 0, BUILDING_EPSILON) &&
        areNumbersEqual(extrudable.extrusionDirection.z, 0, BUILDING_EPSILON)
}

export function transformationOfShape(shape: ShapeRepresentationInput | Shape): Matrix4 {
    //BOX
    const box = shapeToBoxInput(shape)
    if (box) {
        return new Matrix4().identity()
    }
    //POLYGON
    const polygon = shapeToPolygonInput(shape)
    if (polygon) {
        if (isExtrudable3D(polygon)) {
            return new Matrix4().identity()
        }
        const extrudableTransformation = transformationOfExtrudable(polygon)
        const extrusionDirection = vector3DToVector3(polygon.extrusionDirection).multiplyScalar(-polygon.extrusion / 2)
        const translation = new Matrix4().makeTranslation(extrusionDirection)
        return translation.multiply(extrudableTransformation)
    }
    //RING
    const ring = shapeToRingInput(shape)
    if (ring) {
        const extrudableTransformation = transformationOfExtrudable(ring)
        const translation = new Matrix4().makeTranslation(ring.center.x, -ring.extrusion / 2, ring.center.y) //TODO: extrusion direction muss berücksichtigt werden
        return translation.multiply(extrudableTransformation)
    }

    console.warn("Unsupported shape type", shape)
    return new Matrix4().identity()
}

//Die Standard-Extrusion-Richtung ist (0,0,1), sie kann aber durch die Direction-Property des Extrudable-Objekts überschrieben werden.
function transformationOfExtrudable(extrudable: Extrudable | PolygonInput | RingInput): Matrix4 {
    if (isExtrudable3D(extrudable)) {
        return new Matrix4().identity()
    }

    const extrusionDirection = vector3DToVector3(extrudable.extrusionDirection)

    const zAxis = new Vector3(0, 0, 1)
    const rotationAxis = zAxis.clone().cross(extrusionDirection).normalize()
    const rotationAngle = Math.acos(zAxis.dot(extrusionDirection))

    return new Matrix4().makeRotationAxis(rotationAxis, rotationAngle)
}

export function extractShapeRepresentationTypes(shapeRepresentation: Readonly<Ref<ShapeRepresentation>>) {
    const box = computed<Optional<Box>>(() => shapeRepresentation.value.shape.__typename === 'Box' ? shapeRepresentation.value.shape : null)
    const polygon = computed<Optional<Polygon>>(() => shapeRepresentation.value.shape.__typename === 'Polygon' ? shapeRepresentation.value.shape : null)
    const ring = computed<Optional<Ring>>(() => shapeRepresentation.value.shape.__typename === 'Ring' ? shapeRepresentation.value.shape : null)

    return {
        box,
        polygon,
        ring
    }
}

export type BoxShapeRepresentation = TypedShapeRepresentation<Box>
export type PolygonShapeRepresentation = TypedShapeRepresentation<Polygon>
export type RingShapeRepresentation = TypedShapeRepresentation<Ring>
export type TypedShapeRepresentation<S extends Shape> = ShapeRepresentation & { shape: S }

export function deepCopyMatrixArray(matrix: readonly (readonly number[])[]): number[][] {
    return matrix.map(a => a.map(b => b))
}

export function deepCopyBoxShapeRepresentation(shapeRepresentation: BoxShapeRepresentation): EnsureDefined<BoxShapeRepresentation> {
    return {
        __typename: "ShapeRepresentation",
        transformationMatrix: deepCopyMatrixArray(shapeRepresentation.transformationMatrix),
        shape: deepCopyBoxShape(shapeRepresentation.shape)
    }
}

export function deepCopyBoxShape(shape: Box): EnsureDefined<Box> {
    return {
        __typename: "Box",
        width: shape.width,
        height: shape.height,
        depth: shape.depth,
    }
}

export function deepCopyPolygonShapeRepresentation(shapeRepresentation: PolygonShapeRepresentation): EnsureDefined<PolygonShapeRepresentation> {
    return {
        __typename: "ShapeRepresentation",
        transformationMatrix: deepCopyMatrixArray(shapeRepresentation.transformationMatrix),
        shape: deepCopyPolygonShape(shapeRepresentation.shape)
    }
}

export function deepCopyPolygonShape(shape: Polygon): EnsureDefined<Polygon> {
    return {
        __typename: "Polygon",
        extrusion: shape.extrusion,
        extrusionDirection: {
            __typename: "Vector3D",
            x: shape.extrusionDirection.x,
            y: shape.extrusionDirection.y,
            z: shape.extrusionDirection.z
        },
        vertices: shape.vertices.map(vertex => ({
            __typename: "Vector3D",
            x: vertex.x,
            y: vertex.y,
            z: vertex.z
        })),
        holes: shape.holes?.map(hole => hole.map(vertex => ({
            x: vertex.x,
            y: vertex.y,
            z: vertex.z
        }))) ?? [] //TODO: später optional fallback entfernen
    }
}

export function deepCopyRingShapeRepresentation(shapeRepresentation: RingShapeRepresentation): EnsureDefined<RingShapeRepresentation> {
    return {
        __typename: "ShapeRepresentation",
        transformationMatrix: deepCopyMatrixArray(shapeRepresentation.transformationMatrix),
        shape: deepCopyRingShape(shapeRepresentation.shape)
    }
}

export function deepCopyRingShape(shape: Ring): EnsureDefined<Ring> {
    return {
        __typename: "Ring",
        size: shape.size,
        radius: shape.radius,
        innerRadius: shape.innerRadius,
        outerRadius: shape.outerRadius,
        extrusion: shape.extrusion,
        extrusionDirection: {
            __typename: "Vector3D",
            x: shape.extrusionDirection.x,
            y: shape.extrusionDirection.y,
            z: shape.extrusionDirection.z
        },
        center: {
            __typename: "Vector2D",
            x: shape.center.x,
            y: shape.center.y,
        },
        startAngle: shape.startAngle,
        endAngle: shape.endAngle,
        thetaLength: shape.thetaLength,
    }
}

export function deepCopyShapeRepresentation<S extends Shape>(shapeRepresentation: TypedShapeRepresentation<S>): EnsureDefined<TypedShapeRepresentation<S>> {
    if (shapeRepresentation.shape.__typename === "Box") {
        return deepCopyBoxShapeRepresentation(shapeRepresentation as BoxShapeRepresentation) as EnsureDefined<TypedShapeRepresentation<S>>
    }
    if (shapeRepresentation.shape.__typename === "Polygon") {
        return deepCopyPolygonShapeRepresentation(shapeRepresentation as PolygonShapeRepresentation) as EnsureDefined<TypedShapeRepresentation<S>>
    }
    if (shapeRepresentation.shape.__typename === "Ring") {
        return deepCopyRingShapeRepresentation(shapeRepresentation as RingShapeRepresentation) as EnsureDefined<TypedShapeRepresentation<S>>
    }
    throw new Error("Unsupported shape type " + shapeRepresentation.shape.__typename)
}

function changeWidthOfBox(shapeRepresentation: BoxShapeRepresentation, newWidth: number): EnsureDefined<BoxShapeRepresentation> {
    const newShapeRepresentation = deepCopyBoxShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.shape.width = newWidth
    return newShapeRepresentation
}

function changeHeightOfBox(shapeRepresentation: BoxShapeRepresentation, newHeight: number, positionedAtBottom: boolean): [BoxShapeRepresentation, Matrix4] {
    const box = shapeRepresentation.shape
    const oldHeight = box.height
    const heightDelta = newHeight - oldHeight

    const heightDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        positionedAtBottom ? heightDelta / 2 : 0,
        0
    )

    const oldTransformationMatrix = transformationOfShapeRepresentation(shapeRepresentation)
    const newTransformationMatrix = oldTransformationMatrix.clone().multiply(heightDeltaTranslationMatrix)
    const newTransformationMatrixArray = matrix4ToTransformationMatrixArray(newTransformationMatrix)

    const newShapeRepresentation = deepCopyBoxShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.transformationMatrix = newTransformationMatrixArray
    newShapeRepresentation.shape.height = newHeight

    return [newShapeRepresentation, heightDeltaTranslationMatrix]
}

function changeWidthOfPolygon(shapeRepresentation: PolygonShapeRepresentation, newWidth: number, epsilon: number = BUILDING_EPSILON): PolygonShapeRepresentation {
    const polygon = shapeRepresentation.shape
    const oldXMin = polygon.vertices.length <= 0 ? 0 : Math.min(...polygon.vertices.map(v => v.x))
    const oldXMax = polygon.vertices.length <= 0 ? 0 : Math.max(...polygon.vertices.map(v => v.x))
    const oldWidth = oldXMax - oldXMin
    const widthDelta = newWidth - oldWidth
    const halfWidthDelta = widthDelta / 2
    const newXMin = oldXMin - halfWidthDelta
    const newXMax = oldXMax + halfWidthDelta

    const newVertices: EnsureDefined<Vector3D>[] = polygon.vertices.map(v => {
        let newX: number
        const oldX = v.x

        if (areNumbersEqual(oldX, oldXMin, epsilon)) {
            newX = newXMin
        } else if (areNumbersEqual(oldX, oldXMax, epsilon)) {
            newX = newXMax
        } else {
            newX = Math.min(newXMax, Math.max(newXMin, oldX))
        }

        return {
            __typename: "Vector3D",
            x: newX,
            y: v.y,
            z: v.z
        }
    })

    //TODO: <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< holes auch updaten

    const newShapeRepresentation = deepCopyPolygonShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.shape.vertices = newVertices

    return newShapeRepresentation
}

export function changeVerticesOfPolygonWall(wall: Wall, newVertices: readonly Vector2[]): void {
    if (wall.shapeRepresentation.shape.__typename !== "Polygon") {
        throw new Error("Unsupported shape type " + wall.shapeRepresentation.shape.__typename)
    }
    if (newVertices.length < 4) {
        throw new Error("Polygon must have at least 3 vertices + first vertex to close the polygon")
    }

    const oldHeight = heightOfShape(wall.shapeRepresentation.shape)
    const newHeight = tSizeOfVectors2(newVertices).y

    if (newHeight <= BUILDING_EPSILON) {
        throw new Error("Polygon must have a height greater than 0")
    }

    const heightDelta = newHeight - oldHeight
    const halfHeightDelta = heightDelta / 2

    const wallHeightDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        halfHeightDelta,
        0
    )

    const oldWallTransformationMatrix = transformationOfShapeRepresentation(wall.shapeRepresentation)
    const newWallTransformationMatrix = oldWallTransformationMatrix.clone().multiply(wallHeightDeltaTranslationMatrix)
    const newWallTransformationMatrixArray = matrix4ToTransformationMatrixArray(newWallTransformationMatrix)

    wall.shapeRepresentation = {
        ...deepCopyShapeRepresentation(wall.shapeRepresentation),
        transformationMatrix: newWallTransformationMatrixArray,
        shape: {
            ...deepCopyPolygonShape(wall.shapeRepresentation.shape),
            vertices: newVertices.map(v => ({
                x: v.x,
                y: v.y - halfHeightDelta,
                z: 0
            }))
        }
    }

    wall.openings.forEach(opening => {
        const newOpeningShapeRepresentation = deepCopyShapeRepresentation(opening.shapeRepresentation)

        const openingTransformation = transformationOfShapeRepresentation(newOpeningShapeRepresentation)
        const newOpeningTransformation = wallHeightDeltaTranslationMatrix.clone().invert().multiply(openingTransformation)
        newOpeningShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)

        opening.shapeRepresentation = newOpeningShapeRepresentation
    })
}

function changeHeightOfPolygon(shapeRepresentation: PolygonShapeRepresentation, newHeight: number, positionedAtBottom: boolean, epsilon: number = BUILDING_EPSILON): [PolygonShapeRepresentation, Matrix4] {
    const polygon = shapeRepresentation.shape
    const oldYMin = polygon.vertices.length <= 0 ? 0 : Math.min(...polygon.vertices.map(v => v.y))
    const oldYMax = polygon.vertices.length <= 0 ? 0 : Math.max(...polygon.vertices.map(v => v.y))
    const oldHeight = oldYMax - oldYMin
    const heightDelta = newHeight - oldHeight
    const halfHeightDelta = heightDelta / 2
    const newYMin = oldYMin - halfHeightDelta
    const newYMax = oldYMax + halfHeightDelta

    const newVertices: EnsureDefined<Vector3D>[] = polygon.vertices.map(v => {
        let newY: number
        const oldY = v.y

        if (areNumbersEqual(oldY, oldYMin, epsilon)) {
            newY = newYMin
        } else if (areNumbersEqual(oldY, oldYMax, epsilon)) {
            newY = newYMax
        } else {
            newY = Math.min(newYMax, Math.max(newYMin, oldY))
        }

        return {
            __typename: "Vector3D",
            x: v.x,
            y: newY,
            z: v.z
        }
    })

    //TODO: holes auch updaten!!! <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    const heightDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        positionedAtBottom ? heightDelta / 2 : 0,
        0
    )

    const oldTransformationMatrix = transformationOfShapeRepresentation(shapeRepresentation)
    const newTransformationMatrix = oldTransformationMatrix.clone().multiply(heightDeltaTranslationMatrix)
    const newTransformationMatrixArray = matrix4ToTransformationMatrixArray(newTransformationMatrix)

    const newShapeRepresentation = deepCopyPolygonShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.transformationMatrix = newTransformationMatrixArray
    newShapeRepresentation.shape.vertices = newVertices

    return [newShapeRepresentation, heightDeltaTranslationMatrix]
}

function changeHeightOfRing(shapeRepresentation: RingShapeRepresentation, newHeight: number, positionedAtBottom: boolean): [RingShapeRepresentation, Matrix4] {
    const ring = shapeRepresentation.shape
    const oldHeight = ring.extrusion
    const heightDelta = newHeight - oldHeight

    const heightDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        positionedAtBottom ? heightDelta / 2 : 0,
        0
    )

    const oldTransformationMatrix = transformationOfShapeRepresentation(shapeRepresentation)
    const newTransformationMatrix = oldTransformationMatrix.clone().multiply(heightDeltaTranslationMatrix)
    const newTransformationMatrixArray = matrix4ToTransformationMatrixArray(newTransformationMatrix)

    const newShapeRepresentation = deepCopyRingShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.transformationMatrix = newTransformationMatrixArray
    newShapeRepresentation.shape.extrusion = newHeight

    return [newShapeRepresentation, heightDeltaTranslationMatrix]
}

function changeThicknessOfBox(
    shapeRepresentation: BoxShapeRepresentation,
    newThickness: number,
    isPositionedAtInterior: boolean,
    invertedDirection: boolean,
    updateTransformationMatrix: boolean,
): [BoxShapeRepresentation, Matrix4] {
    const box = shapeRepresentation.shape
    const oldThickness = box.depth
    const thicknessDelta = newThickness - oldThickness

    const thicknessDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        0,
        (isPositionedAtInterior ? -thicknessDelta / 2 : 0) * (invertedDirection ? -1 : 1),
    )

    const oldTransformationMatrix = transformationOfShapeRepresentation(shapeRepresentation)
    const newTransformationMatrix = oldTransformationMatrix.clone().multiply(thicknessDeltaTranslationMatrix)
    const newTransformationMatrixArray = matrix4ToTransformationMatrixArray(newTransformationMatrix)

    const newShapeRepresentation = deepCopyBoxShapeRepresentation(shapeRepresentation)

    if (updateTransformationMatrix) {
        newShapeRepresentation.transformationMatrix = newTransformationMatrixArray
    }

    newShapeRepresentation.shape.depth = newThickness

    return [newShapeRepresentation, thicknessDeltaTranslationMatrix]
}

function changeThicknessOfPolygon(
    shapeRepresentation: PolygonShapeRepresentation,
    newThickness: number,
    isPositionedAtInterior: boolean,
    invertedDirection: boolean,
    updateTransformationMatrix: boolean,
): [PolygonShapeRepresentation, Matrix4] {
    const polygon = shapeRepresentation.shape
    const oldThickness = polygon.extrusion
    const thicknessDelta = newThickness - oldThickness

    const thicknessDeltaTranslationMatrix = new Matrix4().makeTranslation(
        0,
        0,
        (isPositionedAtInterior ? -thicknessDelta / 2 : 0) * (invertedDirection ? -1 : 1),
    )

    const oldTransformationMatrix = transformationOfShapeRepresentation(shapeRepresentation)
    const newTransformationMatrix = oldTransformationMatrix.clone().multiply(thicknessDeltaTranslationMatrix)
    const newTransformationMatrixArray = matrix4ToTransformationMatrixArray(newTransformationMatrix)

    const newShapeRepresentation = deepCopyPolygonShapeRepresentation(shapeRepresentation)

    if (updateTransformationMatrix) {
        newShapeRepresentation.transformationMatrix = newTransformationMatrixArray
    }

    newShapeRepresentation.shape.extrusion = newThickness

    return [newShapeRepresentation, thicknessDeltaTranslationMatrix]
}

function changeThicknessOfRing(
    shapeRepresentation: RingShapeRepresentation,
    newThickness: number,
    isPositionedAtInterior: boolean,
): [RingShapeRepresentation, Matrix4] {
    const ring = shapeRepresentation.shape
    const oldThickness = ring.size
    const thicknessDelta = newThickness - oldThickness

    const oldRadius = ring.radius
    const radiusDelta = isPositionedAtInterior ? thicknessDelta / 2 : 0
    const newRadius = oldRadius + radiusDelta
    //const radiusDelta = newRadius - oldRadius

    const newInnerRadius = newRadius - newThickness / 2
    const newOuterRadius = newRadius + newThickness / 2

    const newShapeRepresentation = deepCopyRingShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.shape.size = newThickness
    newShapeRepresentation.shape.radius = newRadius
    newShapeRepresentation.shape.innerRadius = newInnerRadius
    newShapeRepresentation.shape.outerRadius = newOuterRadius

    return [newShapeRepresentation, new Matrix4().identity()]
}

function changeAnglesOfRing(shapeRepresentation: RingShapeRepresentation, newStartAngleInDegree: number, newEndAngleInDegree: number): [RingShapeRepresentation, number] {
    const ring = shapeRepresentation.shape
    const startAngleDelta = newStartAngleInDegree - ring.startAngle
    const newThetaLength = newEndAngleInDegree - newStartAngleInDegree

    const newShapeRepresentation = deepCopyRingShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.shape.startAngle = newStartAngleInDegree
    newShapeRepresentation.shape.endAngle = newEndAngleInDegree
    newShapeRepresentation.shape.thetaLength = newThetaLength

    // const startAngle = MathUtils.degToRad(newStartAngleInDegree)
    // const endAngle = MathUtils.degToRad(newEndAngleInDegree)
    // const centerAngle = (startAngle + endAngle) / 2 - (Math.PI / 2)
    //
    // const oldTransformation = transformationOfShapeRepresentation(shapeRepresentation)
    // const [oldTranslation, oldRotation, oldScale] = tDecomposeMatrixToComponents(oldTransformation)
    // const newRotation = new Matrix4().makeRotationY(centerAngle)
    // const newTransformation = oldTranslation.clone().multiply(newRotation).multiply(oldScale)
    // newShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

    return [newShapeRepresentation, startAngleDelta]
}

function changeInnerRadiusOfRing(shapeRepresentation: RingShapeRepresentation, newInnerRadius: number): RingShapeRepresentation {
    const ring = shapeRepresentation.shape
    const oldInnerRadius = ring.innerRadius
    const innerRadiusDelta = newInnerRadius - oldInnerRadius

    const oldRadius = ring.radius
    const newRadius = oldRadius + innerRadiusDelta

    const oldOuterRadius = ring.outerRadius
    const newOuterRadius = oldOuterRadius + innerRadiusDelta

    const newShapeRepresentation = deepCopyRingShapeRepresentation(shapeRepresentation)
    newShapeRepresentation.shape.radius = newRadius
    newShapeRepresentation.shape.innerRadius = newInnerRadius
    newShapeRepresentation.shape.outerRadius = newOuterRadius

    return newShapeRepresentation
}

export function changeThicknessOfShapeRepresentation(
    shapeRepresentation: ShapeRepresentation,
    newThickness: number,
    isPositionedAtInterior: boolean,
    invertedDirection: boolean,
    updateTransformationMatrix: boolean,
): [ShapeRepresentation, Matrix4] {
    //BOX
    if (shapeRepresentation.shape.__typename === "Box") {
        return changeThicknessOfBox(
            shapeRepresentation as BoxShapeRepresentation,
            newThickness,
            isPositionedAtInterior,
            invertedDirection,
            updateTransformationMatrix,
        )
    }
    //POLYGON
    if (shapeRepresentation.shape.__typename === "Polygon") {
        return changeThicknessOfPolygon(
            shapeRepresentation as PolygonShapeRepresentation,
            newThickness,
            isPositionedAtInterior,
            invertedDirection,
            updateTransformationMatrix,
        )
    }
    //RING
    if (shapeRepresentation.shape.__typename === "Ring") {
        return changeThicknessOfRing(
            shapeRepresentation as RingShapeRepresentation,
            newThickness,
            isPositionedAtInterior,
        )
    }

    throw new Error("Unsupported shape type " + shapeRepresentation.shape.__typename)
}

export function hasOpeningInvertedDirection(renderer: BuildingRenderer, wall: Wall, opening: ConstructionPart): boolean {
    const wallLine = createHorizontalWallSnapLine(renderer, toRef(wall))

    const openingWorldTransformation = worldTransformationOfWallOrOpening(renderer, opening)
    const [openingTranslationMatrix, openingRotationMatrix] = tDecomposeMatrixToComponents(openingWorldTransformation)

    const rotation90Matrix = new Matrix4().makeRotationY(Math.PI / 2)
    const rotationMatrix = openingRotationMatrix.clone().multiply(rotation90Matrix)

    const direction = new Vector3(0, 0, 1).applyMatrix4(rotationMatrix)
    const normalizedDirectionXZ = new Vector2(direction.x, direction.z).normalize()

    return !tAreVectors2Equal(wallLine.normalizedDirectionXZ, normalizedDirectionXZ, BUILDING_EPSILON)
}

export function changeWallThickness(renderer: BuildingRenderer, wall: Wall, newThickness: number): void {
    const isPositionedAtInterior = wall.isExterior === true

    const [newWallShapeRepresentation, wallDeltaTransformation] = changeThicknessOfShapeRepresentation(
        wall.shapeRepresentation,
        newThickness,
        isPositionedAtInterior,
        false,
        true,
    )
    wall.shapeRepresentation = newWallShapeRepresentation

    wall.openings.forEach(opening => {
        const hasInvertedDirection = hasOpeningInvertedDirection(renderer, wall, opening)

        const [newOpeningShapeRepresentation] = changeThicknessOfShapeRepresentation(
            opening.shapeRepresentation,
            newThickness,
            isPositionedAtInterior,
            hasInvertedDirection,
            true,
        )

        const openingTransformation = transformationOfShapeRepresentation(newOpeningShapeRepresentation)
        const newOpeningTransformation = wallDeltaTransformation.clone().invert().multiply(openingTransformation)
        newOpeningShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)

        opening.shapeRepresentation = newOpeningShapeRepresentation
    })
}

export function changeWallShape(wall: Wall, newShapeType: ShapeType): void {
    const isPositionedAtInterior = wall.isExterior === true

    const oldParent = wall.shapeRepresentation
    const newParent = changeShapeType(oldParent, newShapeType, isPositionedAtInterior)

    if (newParent === null) {
        return
    }

    wall.shapeRepresentation = newParent

    wall.openings.forEach(opening => {
        const child = opening.shapeRepresentation
        opening.shapeRepresentation = changeChildShapeIfNeeded(oldParent, newParent, child, isPositionedAtInterior)
    })
}

function changeWidthOfShapeRepresentation(shapeRepresentation: ShapeRepresentation, newWidth: number): ShapeRepresentation {
    //BOX
    if (shapeRepresentation.shape.__typename === "Box") {
        return changeWidthOfBox(shapeRepresentation as BoxShapeRepresentation, newWidth)
    }
    //POLYGON
    if (shapeRepresentation.shape.__typename === "Polygon") {
        return changeWidthOfPolygon(shapeRepresentation as PolygonShapeRepresentation, newWidth)
    }

    throw new Error("Unsupported shape type " + shapeRepresentation.shape.__typename)
}

function changeHeightOfShapeRepresentation(shapeRepresentation: ShapeRepresentation, newHeight: number, positionedAtBottom: boolean): [ShapeRepresentation, Matrix4] {
    //BOX
    if (shapeRepresentation.shape.__typename === "Box") {
        return changeHeightOfBox(shapeRepresentation as BoxShapeRepresentation, newHeight, positionedAtBottom)
    }
    //POLYGON
    if (shapeRepresentation.shape.__typename === "Polygon") {
        return changeHeightOfPolygon(shapeRepresentation as PolygonShapeRepresentation, newHeight, positionedAtBottom)
    }
    //RING
    if (shapeRepresentation.shape.__typename === "Ring") {
        return changeHeightOfRing(shapeRepresentation as RingShapeRepresentation, newHeight, positionedAtBottom)
    }

    throw new Error("Unsupported shape type " + shapeRepresentation.shape.__typename)
}

export function changeWallHeight(wall: Wall, newHeight: number): void {
    const correctedNewHeight = Math.max(BUILDING_WALL_MIN_HEIGHT, newHeight)

    const [newWallShapeRepresentation, wallDeltaTransformation] = changeHeightOfShapeRepresentation(wall.shapeRepresentation, correctedNewHeight, true)
    wall.shapeRepresentation = newWallShapeRepresentation

    wall.openings.forEach(opening => {
        const newOpeningShapeRepresentation = deepCopyShapeRepresentation(opening.shapeRepresentation)

        const openingTransformation = transformationOfShapeRepresentation(newOpeningShapeRepresentation)
        const newOpeningTransformation = wallDeltaTransformation.clone().invert().multiply(openingTransformation)
        newOpeningShapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)

        opening.shapeRepresentation = newOpeningShapeRepresentation
    })
}

export function changeWallWidth(renderer: BuildingRenderer, wall: Wall, newWidth: number): void {
    const correctedNewWidth = Math.max(BUILDING_WALL_MIN_WIDTH, newWidth)

    // const wallTransformation = transformationOfShapeRepresentation(wall.shapeRepresentation)
    //
    // const openingIdToDistance = new Map<string, number>()

    // console.log("=================================")
    // for (const opening of wall.openings) {
    //     const distanceLeft = calculateWallOpeningDistanceXFromWallEdge(renderer, "LEFT", wall, opening)
    //     const distanceRight = calculateWallOpeningDistanceXFromWallEdge(renderer, "RIGHT", wall, opening)
    //
    //     const isInverted = hasOpeningInvertedDirection(renderer, wall, opening)
    //     const distance = isInverted ? distanceRight : distanceLeft
    //
    //     // console.log("vorher: ", distanceLeft, distanceRight)
    //     openingIdToDistance.set(opening.id, distance)
    // }

    wall.shapeRepresentation = changeWidthOfShapeRepresentation(wall.shapeRepresentation, correctedNewWidth)

    // const newWallTransformation = transformationOfShapeRepresentation(wall.shapeRepresentation)
    //
    // for (const opening of wall.openings) {
    //     // const distanceLeft = calculateWallOpeningDistanceXFromWallEdge(renderer, "LEFT", wall, opening)
    //     // const distanceRight = calculateWallOpeningDistanceXFromWallEdge(renderer, "RIGHT", wall, opening)
    //     // console.log("nachher: ", distanceLeft, distanceRight)
    //
    //     const distance = openingIdToDistance.get(opening.id) ?? 0
    //
    //     changeWallOpeningDistanceXFromWallLeftEdge(wall, opening, distance) //TODO: irgendwie klappt das noch nicht, erstmal nach hinten schieben
    // }

    // const invertedWallNewTransformation = newWallTransformation.clone().invert()
    //
    // for (const opening of wall.openings) {
    //     const openingTransformation = transformationOfShapeRepresentation(opening.shapeRepresentation)
    //     const newOpeningTransformation = invertedWallNewTransformation.clone().multiply(wallTransformation).multiply(openingTransformation)
    //     opening.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)
    // }
}

export function changeFloorSlabFloorThickness(renderer: BuildingRenderer, floor: Floor, newThickness: number, updateBottomFloorSlabCeiling: boolean = true): void {
    const correctedNewThickness = Math.min(Math.max(BUILDING_FLOOR_SLAB_THICKNESS_MIN, newThickness), BUILDING_FLOOR_SLAB_THICKNESS_MAX)

    floor.floorSlab.shapeRepresentation = changeThicknessOfShapeRepresentation(
        floor.floorSlab.shapeRepresentation,
        correctedNewThickness,
        false,
        false,
        false,
    )[0]

    if (updateBottomFloorSlabCeiling) {
        const floors = renderer.building.value.floors
        const floorIndex = floors.findIndex(f => f.id === floor.id)
        const bottomFloor = floorIndex <= 0 ? null : floors[floorIndex - 1]

        if (bottomFloor !== null) {
            changeFloorSlabCeilingThickness(renderer, bottomFloor, newThickness, false)
        }
    }
}

export function changeFloorSlabCeilingThickness(renderer: BuildingRenderer, floor: Floor, newThickness: number, updateTopFloorSlabFloor: boolean = true): void {
    if (floor.ceilingSlab === null || floor.ceilingSlab === undefined) {
        return
    }
    const correctedNewThickness = Math.min(Math.max(BUILDING_FLOOR_SLAB_THICKNESS_MIN, newThickness), BUILDING_FLOOR_SLAB_THICKNESS_MAX)
    floor.ceilingSlab.shapeRepresentation = changeThicknessOfShapeRepresentation(
        floor.ceilingSlab.shapeRepresentation,
        correctedNewThickness,
        false,
        false,
        false,
    )[0]

    if (updateTopFloorSlabFloor) {
        const floors = renderer.building.value.floors
        const floorIndex = floors.findIndex(f => f.id === floor.id)
        const topFloor = floorIndex >= floors.length - 1 ? null : floors[floorIndex + 1]

        if (topFloor !== null) {
            changeFloorSlabFloorThickness(renderer, topFloor, newThickness, false)
        }
    }
}

export function changeWallOpeningHeight(opening: ConstructionPart, newHeight: number): void {
    const correctedNewHeight = Math.max(BUILDING_OPENING_MIN_HEIGHT, newHeight)

    const [newOpeningShapeRepresentation, openingDeltaTransformation] = changeHeightOfShapeRepresentation(opening.shapeRepresentation, correctedNewHeight, true)
    opening.shapeRepresentation = newOpeningShapeRepresentation
}

export function changeWallOpeningWidth(opening: ConstructionPart, newWidth: number): void {
    const correctedNewWidth = Math.max(BUILDING_OPENING_MIN_WIDTH, newWidth)

    opening.shapeRepresentation = changeWidthOfShapeRepresentation(opening.shapeRepresentation, correctedNewWidth)
}

export function changeWallOpeningSillHeight(wall: Wall, opening: ConstructionPart, newSillHeight: number): void {
    const wallHeight = heightOfShape(wall.shapeRepresentation.shape)
    const openingHeight = heightOfShape(opening.shapeRepresentation.shape)
    opening.shapeRepresentation.transformationMatrix[3][1] = newSillHeight + openingHeight / 2 - wallHeight / 2
}

export function changeWallOpeningDistanceXFromWallLeftEdge(wall: Wall, opening: ConstructionPart, newDistance: number): void {
    const wallWidth = widthOfShape(wall.shapeRepresentation.shape)
    const openingWidth = widthOfShape(opening.shapeRepresentation.shape)
    const distance = newDistance + openingWidth / 2 - wallWidth / 2
    opening.shapeRepresentation.transformationMatrix[3][0] = Math.floor(distance * 100) / 100
}

export function calculateWallOpeningDistanceXFromWallEdge(renderer: BuildingRenderer, edge: "LEFT" | "RIGHT", wall: Wall, opening: ConstructionPart): number {
    const shapeRepresentation = opening.shapeRepresentation;

    const wallWidth = widthOfShape(wall.shapeRepresentation.shape)
    const openingWidth = widthOfShape(shapeRepresentation.shape)
    const x = shapeRepresentation.transformationMatrix[3][0]
    const isDirectionInverted = hasOpeningInvertedDirection(renderer, wall, opening)
    const sideFactor = (edge === "LEFT" ? -1 : 1) * (isDirectionInverted ? -1 : 1)
    const distance = (wallWidth / 2) - (openingWidth / 2) + (x * sideFactor)

    return (edge === "LEFT"
            ? Math.floor(distance * 100)
            : Math.ceil(distance * 100)
    ) / 100
}

/**
 * Berechnet die Brüstungshöhe einer Öffnung ausgehend vom Boden der zugehörigen Wand.
 */
export function calculateWallOpeningSillHeight(wall: Wall, opening: ConstructionPart): number {
    const wallHeight = heightOfShape(wall.shapeRepresentation.shape)
    const openingHeight = heightOfShape(opening.shapeRepresentation.shape)
    const y = opening.shapeRepresentation.transformationMatrix[3][1]
    return y - (openingHeight / 2) + (wallHeight / 2)
}

export function changeRingWallInnerRadius(wall: Wall, newInnerRadius: number): void {
    const correctedNewInnerRadius = Math.max(BUILDING_WALL_RING_RADIUS_MIN, newInnerRadius)

    if (wall.shapeRepresentation.shape.__typename !== "Ring") {
        throw new Error("Unsupported shape type " + wall.shapeRepresentation.shape.__typename)
    }

    wall.shapeRepresentation = changeInnerRadiusOfRing(wall.shapeRepresentation as RingShapeRepresentation, correctedNewInnerRadius)

    wall.openings.forEach(opening => {
        if (opening.shapeRepresentation.shape.__typename !== "Ring") {
            throw new Error("Unsupported shape type " + opening.shapeRepresentation.shape.__typename)
        }
        opening.shapeRepresentation = changeInnerRadiusOfRing(opening.shapeRepresentation as RingShapeRepresentation, correctedNewInnerRadius)
    })
}

export function changeRingWallAngles(wall: Wall, newStartAngleInDegree: number, newEndAngleInDegree: number): void {
    const correctedStartAngle = Math.max(0, newStartAngleInDegree)
    //5 grad unterschied zum start angle
    const correctedEndAngle = Math.max(correctedStartAngle + BUILDING_WALL_RING_THETA_MIN, Math.min(360, newEndAngleInDegree))

    if (wall.shapeRepresentation.shape.__typename !== "Ring") {
        throw new Error("Unsupported shape type " + wall.shapeRepresentation.shape.__typename)
    }

    const [newShapeRepresentation, startAngleDelta] = changeAnglesOfRing(wall.shapeRepresentation as RingShapeRepresentation, correctedStartAngle, correctedEndAngle)
    wall.shapeRepresentation = newShapeRepresentation

    wall.openings.forEach(opening => {
        if (opening.shapeRepresentation.shape.__typename !== "Ring") {
            throw new Error("Unsupported shape type " + opening.shapeRepresentation.shape.__typename)
        }
        const correctedStartAngle = opening.shapeRepresentation.shape.startAngle + startAngleDelta
        const correctedEndAngle = opening.shapeRepresentation.shape.endAngle + startAngleDelta

        const [newShapeRepresentation] = changeAnglesOfRing(opening.shapeRepresentation as RingShapeRepresentation, correctedStartAngle, correctedEndAngle)
        opening.shapeRepresentation = newShapeRepresentation
    })
}

/**
 * TODO: Extrusion direction will be ignored!!!
 */
export function thicknessOfShape(shapeRepresentation: ShapeRepresentationInput | Shape): number {
    //BOX
    const box = shapeToBoxInput(shapeRepresentation)
    if (box) {
        return box.depth
    }
    //POLYGON
    const polygon = shapeToPolygonInput(shapeRepresentation)
    if (polygon) {
        if (isExtrudable3D(polygon)) {
            return tSizeOfVectors3(polygon.vertices.map(v => new Vector3(v.x, v.y, v.z))).z
        }
        return polygon.extrusion
    }
    //RING
    const ring = shapeToRingInput(shapeRepresentation)
    if (ring) {
        return ring.size
    }

    console.warn("Unsupported shape type", shapeRepresentation)
    return 0
}

/**
 * TODO: Extrusion direction will be ignored!!!
 */
export function heightOfShape(shapeRepresentation: ShapeRepresentationInput | Shape): number {
    //BOX
    const box = shapeToBoxInput(shapeRepresentation)
    if (box) {
        return box.height
    }
    //POLYGON
    const polygon = shapeToPolygonInput(shapeRepresentation)
    if (polygon) {
        if (isExtrudable3D(polygon)) {
            return polygon.extrusion
        }

        //TODO: das ist noch nicht sauber alles mit der extrusion

        const extrusionDirection = vector3DToVector3(polygon.extrusionDirection)
        if (!isExtrudable3D(polygon)) {
            if (areNumbersNotEqual(extrusionDirection.y, 0, BUILDING_EPSILON)) {
                return polygon.extrusion //z.B. für Böden
            }
        }
        return tSizeOfVectors3(polygon.vertices.map(v => new Vector3(v.x, v.y, v.z))).y
    }
    //RING
    const ring = shapeToRingInput(shapeRepresentation)
    if (ring) {
        //TODO: das ist noch nicht sauber alles mit der extrusion
        return ring.extrusion
    }

    console.warn("Unsupported shape type", shapeRepresentation)
    return 0
}

/**
 * TODO: Extrusion direction will be ignored!!!
 */
export function widthOfShape(shapeRepresentation: ShapeRepresentationInput | Shape): number {
    //BOX
    const box = shapeToBoxInput(shapeRepresentation)
    if (box) {
        return box.width
    }
    //POLYGON
    const polygon = shapeToPolygonInput(shapeRepresentation)
    if (polygon) {
        return tSizeOfVectors3(polygon.vertices.map(v => new Vector3(v.x, v.y, v.z))).x
    }
    //RING
    const ring = shapeToRingInput(shapeRepresentation)
    if (ring) {
        const thetaInDegree = ring.endAngle - ring.startAngle;
        if (areNumbersEqual(thetaInDegree, 360, 0.01)) {
            return 2 * Math.PI * ring.radius
        }
        const thetaInRadians = MathUtils.degToRad(thetaInDegree)
        return 2 * Math.sin(thetaInRadians / 2) * ring.radius
    }

    console.warn("Unsupported shape type", shapeRepresentation)
    return 0
}

export function findBuildingFloorByLevel(building: Building, floorLevel: number): Optional<Floor> {
    return building.floors.find(floor => floor.level === floorLevel) ?? null
}

export function flowDataOfBuildingComponent(component: CustomizableBuildingComponent | BuildingComponent): FlowData {
    const rawComponent = toRaw(component)
    const customData = "customData" in rawComponent ? rawComponent.customData : rawComponent.component.value.customData

    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(customData)
    return flowData
}

export function floorTypeOfFloor(floor: Floor | (BuildingComponent & { component: Readonly<Ref<Floor>> })): Optional<string> {
    const rawFloor = toRaw(floor)
    const floorComponent = "component" in rawFloor ? rawFloor.component.value : rawFloor
    const flowData = flowDataOfBuildingComponent(floorComponent)

    return useFlowDataString(toRef(flowData), toRef({
        field: {
            id: 'floor_type',
        },
        arrayIndex: null
    })).value
}

export function uValueOf(wallOrConstructionPart: Wall | ConstructionPart | (BuildingComponent & { component: Readonly<Ref<Wall | ConstructionPart>> })): Optional<number> {
    const rawWallOrConstructionPart = toRaw(wallOrConstructionPart)
    const component = "component" in rawWallOrConstructionPart ? rawWallOrConstructionPart.component.value : rawWallOrConstructionPart
    const flowData = flowDataOfBuildingComponent(component)

    const uValue = useFlowDataDouble(toRef(flowData), toRef({
        field: {
            id: determineFieldnameForUValue(component),
        },
        arrayIndex: null
    })).value

    // Prevent exporting 0 since 0 does not exist as u-value
    return uValue === 0 ? null : uValue
}

function determineFieldnameForUValue(component: Wall | ConstructionPart) {
    if (component.__typename === 'Wall') {
        return "wall_u_value"
    } else if (component.__typename === 'ConstructionPart') {
        if (component.type === 'WINDOW') {
            return "window_u_value"
        } else if (component.type === 'DOOR') {
            return "door_u_value"
        }
    }

    return "u_value"
}

export type WallOrOpeningSnapPointType = 'LEFT' | 'RIGHT'
export type WallSnapPointTemplate = {
    wall: Readonly<Ref<Wall>>
    type: WallOrOpeningSnapPointType
}
export type WallSnapPoint = TSnapPoint<WallSnapPointTemplate>

export type WallSnapLineType =
    | 'HORIZONTAL'
    | 'VERTICAL'
    | "X_AXIS"
    | "Z_AXIS"
export type WallSnapLineTemplate = {
    wall: Readonly<Ref<Wall>>
    type: WallSnapLineType
}
export type WallSnapLine = TSnapLine<WallSnapLineTemplate>

export function worldTransformationOfWallOrOpening(renderer: BuildingRenderer, wallOrOpening: Wall | ConstructionPart, excludeWallTransformation: boolean = false): Matrix4 {
    if (wallOrOpening.id === WallCreator.TEMP_WALL_ID) {
        return new Matrix4().identity()
    }

    const buildingTransformation = transformationMatrixOfShapeRepresentation(renderer.building.value.shapeRepresentation)

    const floor = renderer.traversableBuilding.floorOf(wallOrOpening)
    if (floor === null) {
        console.warn("No floor found for", toRaw(wallOrOpening))
        return buildingTransformation.clone() //TODO: beim return eig. nie clonen, alle funktionen sollten beim verwenden drauf achten (gilt für alle usages von vector und matrix von allen funktion des projektes)
    }
    const floorTransformation = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)

    if (excludeWallTransformation) {
        return buildingTransformation.clone().multiply(floorTransformation)
    }

    if (wallOrOpening.__typename === "Wall") {
        const wallTransformation = transformationMatrixOfShapeRepresentation(wallOrOpening.shapeRepresentation)
        return buildingTransformation.clone().multiply(floorTransformation).multiply(wallTransformation)
    }

    const opening = wallOrOpening
    const parentWall = renderer.traversableBuilding.parentOf(opening)
    if (parentWall === null) {
        console.warn("No parent wall found for opening", toRaw(opening))
        return buildingTransformation.clone().multiply(floorTransformation)
    }

    const parentWallTransformation = transformationMatrixOfShapeRepresentation(parentWall.shapeRepresentation)
    const openingTransformation = transformationMatrixOfShapeRepresentation(wallOrOpening.shapeRepresentation)
    return buildingTransformation.clone().multiply(floorTransformation).multiply(parentWallTransformation).multiply(openingTransformation)
}

export function floorsOfBuildingComponents(renderer: BuildingRenderer, buildingComponents: readonly TraversalBuildingComponent[]): Floor[] {
    const idsOfFloors = new Set<string>()

    for (const buildingComponent of buildingComponents) {
        const floor = renderer.traversableBuilding.floorOf(buildingComponent)
        if (floor === null) {
            continue
        }
        idsOfFloors.add(floor.id)
    }

    return renderer.building.value.floors.filter(floor => idsOfFloors.has(floor.id))
}

/**
 * @return [leftSnapPoint, rightSnapPoint]
 */
export function wallIdToSnapPointIds(wallId: string): [string, string] {
    return [
        wallIdToSnapPointId(wallId, 'LEFT'),
        wallIdToSnapPointId(wallId, 'RIGHT'),
    ]
}

export function wallToSnapLineIds(wall: Readonly<Ref<Wall>>, ignoreType: Optional<WallOrOpeningSnapPointType>): Set<string> {
    const snapLineIds = new Set<string>()

    snapLineIds.add(wallToSnapLineId("HORIZONTAL", wall))

    if (ignoreType === null || ignoreType !== "LEFT") {
        const leftSnapPointTemplate: WallSnapPointTemplate = {wall, type: "LEFT"}
        snapLineIds.add(wallToSnapLineId("VERTICAL", leftSnapPointTemplate))
        snapLineIds.add(wallToSnapLineId("X_AXIS", leftSnapPointTemplate))
        snapLineIds.add(wallToSnapLineId("Z_AXIS", leftSnapPointTemplate))
    }

    if (ignoreType === null || ignoreType !== "RIGHT") {
        const rightSnapPointTemplate: WallSnapPointTemplate = {wall, type: "RIGHT"}
        snapLineIds.add(wallToSnapLineId("VERTICAL", rightSnapPointTemplate))
        snapLineIds.add(wallToSnapLineId("X_AXIS", rightSnapPointTemplate))
        snapLineIds.add(wallToSnapLineId("Z_AXIS", rightSnapPointTemplate))
    }

    return snapLineIds
}

export function wallIdToSnapPointId(wallId: string, type: WallOrOpeningSnapPointType): string {
    return `wallSnapPoint-${wallId}-${type}`
}

export function wallToSnapLineId(type: "HORIZONTAL", wall: Readonly<Ref<Wall>>): string;
export function wallToSnapLineId(type: "VERTICAL" | "X_AXIS" | "Z_AXIS", snapPointTemplate: WallSnapPointTemplate): string;
export function wallToSnapLineId(type: WallSnapLineType, reference: Readonly<Ref<Wall>> | WallSnapPointTemplate): string {
    switch (type) {
        case 'HORIZONTAL':
            return `wallSnapLine-${(reference as Readonly<Ref<Wall>>).value.id}-${type}`
        case 'VERTICAL':
        case 'X_AXIS':
        case 'Z_AXIS':
            return `wallSnapLine-${(reference as WallSnapPointTemplate).wall.value.id}-${(reference as WallSnapPointTemplate).type}-${type}`
    }
}

function createWallSnapLine(
    id: string,
    wall: Readonly<Ref<Wall>>,
    type: WallSnapLineType,
    positionXZ: Vector2,
    normalizedDirectionXZ: Vector2,
): WallSnapLine {
    return {
        id,
        positionXZ: positionXZ.clone(),
        normalizedDirectionXZ: normalizedDirectionXZ.clone(),
        groupId: wall.value.id,
        object: {
            type,
            wall,
        },
    }
}

/**
 * @return [positionXZ, normalizedDirectionXZ]
 */
export function calculateHorizontalWallSnapLinePositionXZAndDirectionXZ(
    renderer: BuildingRenderer,
    wall: Wall,
): [Vector2, Vector2] {
    const worldTransformation = worldTransformationOfWallOrOpening(renderer, wall)
    const [translation] = tDecomposeMatrix(worldTransformation)
    const [translationMatrix, wallRotationMatrix] = tDecomposeMatrixToComponents(worldTransformation)

    const rotation90Matrix = new Matrix4().makeRotationY(Math.PI / 2)
    const rotationMatrix = wallRotationMatrix.clone().multiply(rotation90Matrix)

    const isPositionedAtInterior = wall.isExterior === true
    const isRing = wall.shapeRepresentation.shape.__typename === "Ring"
    const offsetZ = isPositionedAtInterior || isRing ? -thicknessOfShape(wall.shapeRepresentation.shape) / 2 : 0;
    const offsetZVector = new Vector3(offsetZ, 0, 0).applyMatrix4(rotationMatrix)

    const positionXZ = new Vector2(
        translation.x,
        translation.z
    ).add(new Vector2(offsetZVector.x, offsetZVector.z))

    const direction = new Vector3(0, 0, 1).applyMatrix4(rotationMatrix)
    const normalizedDirectionXZ = new Vector2(direction.x, direction.z).normalize()

    return [positionXZ, normalizedDirectionXZ]
}

export function createHorizontalWallSnapLine(
    renderer: BuildingRenderer,
    wall: Readonly<Ref<Wall>>,
): WallSnapLine {
    const id = wallToSnapLineId('HORIZONTAL', wall)
    const [positionXZ, normalizedDirectionXZ] = calculateHorizontalWallSnapLinePositionXZAndDirectionXZ(renderer, wall.value)

    return createWallSnapLine(
        id,
        wall,
        'HORIZONTAL',
        positionXZ,
        normalizedDirectionXZ
    )
}

/**
 * @return [positionXZ, normalizedDirectionXZ]
 */
export function calculateWallSnapLinePositionXZAndDirectionXZFromSnapPoint(
    renderer: BuildingRenderer,
    snapPoint: WallSnapPoint,
    type: "VERTICAL" | "X_AXIS" | "Z_AXIS"
): [Vector2, Vector2] {
    let direction: Vector3
    switch (type) {
        case "X_AXIS":
            direction = new Vector3(1, 0, 0)
            break
        case 'VERTICAL':
        case 'Z_AXIS':
            direction = new Vector3(0, 0, 1)
            break
        // case 'DIAGONAL_LEFT':
        //     direction = new Vector3(-1, 0, 1)
        //     break
        // case 'DIAGONAL_RIGHT':
        //     direction = new Vector3(1, 0, 1)
        //     break
        default:
            throw new Error("Unsupported type " + type)
    }

    const worldTransformation = worldTransformationOfWallOrOpening(renderer, snapPoint.object.wall.value)
    const [translationMatrix, wallRotationMatrix] = tDecomposeMatrixToComponents(worldTransformation)
    const rotate = type === "VERTICAL" //|| type === "DIAGONAL_LEFT" || type === "DIAGONAL_RIGHT"

    const rotatedDirection = direction.clone().applyMatrix4(wallRotationMatrix)
    const normalizedDirectionXZ = new Vector2(
        rotate ? rotatedDirection.x : direction.x,
        rotate ? rotatedDirection.z : direction.z
    ).normalize()

    return [snapPoint.positionXZ, normalizedDirectionXZ]
}

export function createWallSnapLineFromSnapPoint(
    renderer: BuildingRenderer,
    snapPoint: WallSnapPoint,
    type: "VERTICAL" | "X_AXIS" | "Z_AXIS"
): WallSnapLine {
    const id = wallToSnapLineId(type, snapPoint.object)
    const [positionXZ, normalizedDirectionXZ] = calculateWallSnapLinePositionXZAndDirectionXZFromSnapPoint(renderer, snapPoint, type)

    return createWallSnapLine(
        id,
        snapPoint.object.wall,
        type,
        positionXZ,
        normalizedDirectionXZ
    )
}

export function calculateBoxOrPolygonWallOrOpeningSnapPointPositionXZ(
    renderer: BuildingRenderer,
    wallOrOpening: Wall | ConstructionPart,
    type: WallOrOpeningSnapPointType,
    wall: Wall,
    ignoreExteriorOffset: boolean,
    forcedOffsetZ?: number
): Vector2 {
    const shape = wallOrOpening.shapeRepresentation.shape
    if (shape.__typename !== "Box" && shape.__typename !== "Polygon") {
        throw new Error("Unsupported shape type " + shape.__typename)
    }

    const isPositionedAtInterior = wall.isExterior === true && !ignoreExteriorOffset
    const offsetZ = isPositionedAtInterior
        ? thicknessOfShape(shape) / 2 + (forcedOffsetZ ?? 0)
        : 0;

    const wallOrOpeningWorldTransformation = worldTransformationOfWallOrOpening(renderer, wallOrOpening)
    const halfWidth = widthOfShape(shape) / 2

    const translation = new Matrix4().makeTranslation(
        halfWidth * (type === 'LEFT' ? -1 : 1),
        0,
        offsetZ
    )

    const transformation = wallOrOpeningWorldTransformation.clone().multiply(translation)
    const [position] = tDecomposeMatrix(transformation)
    return new Vector2(position.x, position.z)
}

function calculateRingWallOrOpeningSnapPointPositionXZ(
    renderer: BuildingRenderer,
    wallOrOpening: Wall | ConstructionPart,
    type: WallOrOpeningSnapPointType,
    wall: Wall,
    ignoreExteriorOffset: boolean,
    forcedOffsetZ?: number,
): Vector2 {
    const shape = wallOrOpening.shapeRepresentation.shape

    if (shape.__typename !== "Ring") {
        throw new Error("Unsupported shape type " + shape.__typename)
    }

    const isPositionedAtInterior = wall.isExterior === true && !ignoreExteriorOffset
    const wallOrOpeningWorldTransformation = worldTransformationOfWallOrOpening(renderer, wallOrOpening)

    const ring = shape
    let radius = isPositionedAtInterior ? ring.innerRadius : ring.radius
    if (forcedOffsetZ !== undefined) {
        radius += forcedOffsetZ
    }

    const centerTranslation = new Matrix4().makeTranslation(ring.center.x, 0, ring.center.y)
    const angle = MathUtils.degToRad(360 - (type === "LEFT" ? ring.startAngle : ring.endAngle))

    const translation = new Matrix4().makeTranslation(
        radius * Math.cos(angle),
        0,
        radius * Math.sin(angle)
    )

    const transformation = wallOrOpeningWorldTransformation.clone().multiply(centerTranslation).multiply(translation)
    const [position] = tDecomposeMatrix(transformation)
    return new Vector2(position.x, position.z)
}

export function createMutableBuildingFrom(building: Building): Building {
    /*const newBuilding =*/
    return JSON.parse(JSON.stringify(toRaw(building))) //deep copy

    //=== WÄNDE FÜR JEDE HIMMELSRICHTUNG ===
    // const createEmptyWall: (transformation: Matrix4) => Wall = transformation => ({
    //     __typename: "Wall",
    //     id: uuidv4(),
    //     customData: createNewWallCustomData("EXTERIOR"),
    //     openings: [],
    //     shapeRepresentation: {
    //         __typename: "ShapeRepresentation",
    //         transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
    //         shape: {
    //             __typename: "Box",
    //             width: 1,
    //             height: 2,
    //             depth: 0.5,
    //         }
    //     },
    //     roomIds: [],
    //     isExterior: true,
    //     isPartition: false,
    // })
    //
    // const lineSize = 8
    //
    // for (const floor of newBuilding.floors) {
    //     const translation = new Matrix4().makeTranslation(0, 0, -lineSize)
    //
    //     const wallNorth = createEmptyWall(new Matrix4().makeRotationY(0).multiply(translation))
    //     const wallEast = createEmptyWall(new Matrix4().makeRotationY(-Math.PI / 2).multiply(translation))
    //     const wallSouth = createEmptyWall(new Matrix4().makeRotationY(Math.PI).multiply(translation))
    //     const wallWest = createEmptyWall(new Matrix4().makeRotationY(Math.PI / 2).multiply(translation))
    //
    //     floor.walls.push(wallNorth)
    //     floor.walls.push(wallEast)
    //     floor.walls.push(wallSouth)
    //     floor.walls.push(wallWest)
    // }

    // return newBuilding
}

export function calculateWallOrOpeningSnapPointPositionXZ(
    renderer: BuildingRenderer,
    wallOrOpening: Wall | ConstructionPart,
    type: WallOrOpeningSnapPointType,
    wall: Wall,
    ignoreExteriorOffset: boolean,
    forcedOffsetZ?: number,
): Vector2 {
    const shape = wallOrOpening.shapeRepresentation.shape

    if (shape.__typename === "Box" || shape.__typename === "Polygon") {
        return calculateBoxOrPolygonWallOrOpeningSnapPointPositionXZ(
            renderer,
            wallOrOpening,
            type,
            wall,
            ignoreExteriorOffset,
            forcedOffsetZ,
        )
    }

    if (shape.__typename === "Ring") {
        return calculateRingWallOrOpeningSnapPointPositionXZ(
            renderer,
            wallOrOpening,
            type,
            wall,
            ignoreExteriorOffset,
            forcedOffsetZ,
        )
    }

    throw new Error("Unsupported shape type " + shape.__typename)
}

export function createWallSnapPoint(renderer: BuildingRenderer, wall: Readonly<Ref<Wall>>, type: WallOrOpeningSnapPointType, onPositionChanged: TSnapPointPositionChangedCallback): WallSnapPoint {
    const wallValue = wall.value
    const shape = wallValue.shapeRepresentation.shape
    const id = wallIdToSnapPointId(wall.value.id, type)

    if (shape.__typename === "Box" || shape.__typename === "Polygon") {
        return {
            id,
            positionXZ: calculateBoxOrPolygonWallOrOpeningSnapPointPositionXZ(renderer, wallValue, type, wallValue, false),
            isFixed: false,
            object: {
                type,
                wall,
            },
            groupId: wall.value.id,
            onPositionChanged,
        }
    }

    if (shape.__typename === "Ring") {
        return {
            id,
            positionXZ: calculateRingWallOrOpeningSnapPointPositionXZ(renderer, wallValue, type, wallValue, false),
            isFixed: false,
            object: {
                type,
                wall,
            },
            groupId: wall.value.id,
            onPositionChanged,
        }
    }

    throw new Error("Unsupported shape type " + shape.__typename)
}

export function rotationMatrixY2(point1XZ: Vector2, point2XZ: Vector2): Matrix4 {
    const directionXZ = point2XZ.clone().sub(point1XZ)
    const angle = Math.atan2(directionXZ.y, directionXZ.x)
    return new Matrix4().makeRotationY(-angle)
}

export function rotationMatrixY3(point1: Vector3, point2: Vector3): Matrix4 {
    return rotationMatrixY2(
        new Vector2(point1.x, point1.z),
        new Vector2(point2.x, point2.z),
    )
}

export function wallToIgnoredSnapPointIdsForSnappingManager(renderer: BuildingRenderer, wall: Wall, change: WallOrOpeningSnapPointType): Set<string> {
    const snapPointsOfSameGroup = renderer.snappingManager.snapPointIdToSnapPointsOfSameGroup(wall, wallIdToSnapPointId(wall.id, change), false)
    return new Set(snapPointsOfSameGroup.map(sp => sp.id))
}

export function wallToIgnoredSnapLineIdsForSnappingManager(renderer: BuildingRenderer, wall: Wall, change: WallOrOpeningSnapPointType): Set<string> {
    const snapPointIdOfWall = wallIdToSnapPointId(wall.id, change)
    const snapPointsOfSameGroup = renderer.snappingManager.snapPointIdToSnapPointsOfSameGroup(wall, snapPointIdOfWall, false)

    const snapLineIds = new Set<string>()

    if (renderer.wallCreator !== null) {
        wallToSnapLineIds(renderer.wallCreator.tempWall, null).forEach(id => snapLineIds.add(id))
    }

    for (const snapPoint of snapPointsOfSameGroup) {
        switch (snapPoint.object.type) {
            case "LEFT":
                wallToSnapLineIds(snapPoint.object.wall, "RIGHT").forEach(id => snapLineIds.add(id))
                break
            case "RIGHT":
                wallToSnapLineIds(snapPoint.object.wall, "LEFT").forEach(id => snapLineIds.add(id))
                break
        }
    }

    return snapLineIds
}

// noinspection FunctionTooLongJS,ParameterNamingConventionJS
export function updateWallShapeRepresentation(
    renderer: BuildingRenderer,
    wall: Wall,
    leftWorldPositionXZ: Vector2,
    rightWorldPositionXZ: Vector2,
) {
    const oldShape = wall.shapeRepresentation.shape

    switch (oldShape.__typename) {
        case "Box":
        case "Polygon":
            updateBoxOrPolygonWallShapeRepresentation(renderer, wall, leftWorldPositionXZ, rightWorldPositionXZ)
            break

        case "Ring":
            updateRingWallShapeRepresentation(renderer, wall, leftWorldPositionXZ, rightWorldPositionXZ)
            break

        default:
            throw new Error("Unsupported shape type " + oldShape.__typename)
    }
}

export function updateBoxOrPolygonWallShapeRepresentation(
    renderer: BuildingRenderer,
    wall: Wall,
    leftWorldPositionXZ: Vector2,
    rightWorldPositionXZ: Vector2,
) {
    const boxOrPolygon = wall.shapeRepresentation.shape
    if (boxOrPolygon.__typename !== "Box" && boxOrPolygon.__typename !== "Polygon") {
        throw new Error("Unsupported shape type " + boxOrPolygon.__typename)
    }

    const worldTransformation = worldTransformationOfWallOrOpening(renderer, wall, true)
    const invertedWorldTransformation = worldTransformation.clone().invert()

    const worldTransformationForLeftAndRight = worldTransformationOfWallOrOpening(renderer, wall)
    const [worldTranslationForLeftAndRight] = tDecomposeMatrix(worldTransformationForLeftAndRight)

    const isPositionedAtInterior = wall.isExterior === true
    const offsetZ = isPositionedAtInterior ? thicknessOfShape(boxOrPolygon) / 2 : 0

    const leftTranslationWorld = new Vector3(
        leftWorldPositionXZ.x,
        worldTranslationForLeftAndRight.y,
        leftWorldPositionXZ.y
    )
    const rightTranslationWorld = new Vector3(
        rightWorldPositionXZ.x,
        worldTranslationForLeftAndRight.y,
        rightWorldPositionXZ.y
    )

    const leftTranslationLocal = leftTranslationWorld.clone().applyMatrix4(invertedWorldTransformation)
    const rightTranslationLocal = rightTranslationWorld.clone().applyMatrix4(invertedWorldTransformation)

    const offsetVector = new Vector3()
        .subVectors(rightTranslationLocal, leftTranslationLocal)
        .applyAxisAngle(new Vector3(0, 1, 0), Math.PI / 2)
        .normalize()
        .multiplyScalar(offsetZ)

    const center = leftTranslationLocal.clone().add(rightTranslationLocal).multiplyScalar(0.5).add(offsetVector)
    const width = leftTranslationLocal.distanceTo(rightTranslationLocal)

    const translationMatrix = new Matrix4().makeTranslation(center)
    const rotationYMatrix = rotationMatrixY3(leftTranslationLocal, rightTranslationLocal)
    const transformation = translationMatrix.multiply(rotationYMatrix)

    switch (boxOrPolygon.__typename) {
        case "Box":
        case "Polygon":
            wall.shapeRepresentation = {
                ...deepCopyShapeRepresentation(wall.shapeRepresentation),
                transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
            }
            changeWallWidth(renderer, wall, width)
            break
        default:
            throw new Error("Unsupported shape type " + boxOrPolygon.__typename)
    }
}

export function updateRingWallShapeRepresentation(
    renderer: BuildingRenderer,
    wall: Wall,
    leftWorldPositionXZ: Vector2,
    rightWorldPositionXZ: Vector2,
) {
    const ring = wall.shapeRepresentation.shape
    if (ring.__typename !== "Ring") {
        throw new Error("Unsupported shape type " + ring.__typename)
    }

    const worldTransformation = worldTransformationOfWallOrOpening(renderer, wall, true)
    const invertedWorldTransformation = worldTransformation.clone().invert()

    const worldTransformationForLeftAndRight = worldTransformationOfWallOrOpening(renderer, wall)
    const [worldTranslationForLeftAndRight] = tDecomposeMatrix(worldTransformationForLeftAndRight)

    const localTranslation = worldTranslationForLeftAndRight.applyMatrix4(invertedWorldTransformation)

    const isPositionedAtInterior = wall.isExterior === true

    const leftTranslationWorld = new Vector3(
        leftWorldPositionXZ.x,
        worldTranslationForLeftAndRight.y,
        leftWorldPositionXZ.y
    )
    const rightTranslationWorld = new Vector3(
        rightWorldPositionXZ.x,
        worldTranslationForLeftAndRight.y,
        rightWorldPositionXZ.y
    )

    const leftTranslationLocal = leftTranslationWorld.clone().applyMatrix4(invertedWorldTransformation)
    const rightTranslationLocal = rightTranslationWorld.clone().applyMatrix4(invertedWorldTransformation)

    const leftTranslationLocal2D = new Vector2(leftTranslationLocal.x, leftTranslationLocal.z)
    const rightTranslationLocal2D = new Vector2(rightTranslationLocal.x, rightTranslationLocal.z)

    const startAngleInDegree = ring.startAngle
    const endAngleInDegree = ring.endAngle

    const centerAndRadius = tCalculateCircleCenterAndRadius(leftTranslationLocal2D, startAngleInDegree, rightTranslationLocal2D, endAngleInDegree)
    const center2D = centerAndRadius[0]
    let radius = centerAndRadius[1]

    if (!isPositionedAtInterior) {
        radius -= thicknessOfShape(ring) / 2
    }

    const center = new Vector3(
        center2D.x,
        localTranslation.y,
        center2D.y,
    )

    const centerToStart = leftTranslationLocal.clone().sub(center).normalize() //TODO: center statt rightTranslationLocal?
    const centerToStartAngle = Math.atan2(centerToStart.z, centerToStart.x)
    const angle = centerToStartAngle + MathUtils.degToRad(startAngleInDegree)

    const rotationYMatrix = new Matrix4().makeRotationY(-angle)

    const transformation = new Matrix4().makeTranslation(center).multiply(rotationYMatrix)

    wall.shapeRepresentation = {
        ...deepCopyShapeRepresentation(wall.shapeRepresentation),
        transformationMatrix: matrix4ToTransformationMatrixArray(transformation),
    }

    changeRingWallInnerRadius(wall, radius)
}

export const BUILDING_HOLE_SCALAR = 2

// noinspection ParameterNamingConventionJS
export function useParentChildMeshes(
    parentId: Readonly<Ref<string>>,
    parent: Readonly<Ref<ShapeRepresentation>>,
    children: Readonly<Ref<readonly ShapeRepresentation[]>>,
    useSegmentation: boolean,
    childPolygonToBox: boolean | "FORCE_FALSE",
    applyParentTransformationToChildren: boolean,
    renderer: BuildingRenderer,
    childrenAreHoles: boolean,
    useTrueSize: boolean,
) {
    const fallbackSize = useTrueSize || parentId.value === WallCreator.TEMP_WALL_ID
        ? 0.001 //0.001 in Meter (1mm) muss sein, sonst können keine Geometrien berechnet werden
        : renderer.fallbackSize

    const parentTransformation = computed<Matrix4>(() => transformationOfShapeRepresentation(parent.value))
    const parentShapeTransformation = computed<Matrix4>(() => transformationOfShape(parent.value.shape))
    // noinspection LocalVariableNamingConventionJS
    const parentGeometry_000 = computed<BufferGeometry>(oldParentGeometry => {
        oldParentGeometry?.dispose()

        return shapeToBufferedGeometry_000(
            parent.value.shape,
            useSegmentation,
            false,
            fallbackSize
        )
    })
    const parentHeight = computed<number>(() => heightOfShape(parent.value.shape))

    const parentMesh = computed<Mesh>(() => {
        const geometry = parentGeometry_000.value
        const mesh = new Mesh(geometry)

        const transformation = parentTransformation.value
        const shapeTransformation = parentShapeTransformation.value
        const meshTransformation = transformation.clone().multiply(shapeTransformation)

        tOverrideTransformation(mesh, meshTransformation)

        mesh.updateMatrix()
        mesh.updateMatrixWorld(true)

        return mesh
    })

    // noinspection LocalVariableNamingConventionJS
    const childMeshes_000_G = computed<readonly Mesh[]>(oldChildren => {
        if (oldChildren !== undefined) {
            for (const oldChild of oldChildren) {
                tDestroyMesh(oldChild, true, false)
            }
        }

        return children.value.map(child => {
            const childShape = child.shape;

            const forcedThickness = childrenAreHoles ? Math.max(fallbackSize, thicknessOfShape(childShape)) * 10 : undefined;
            const forcedHeight = childrenAreHoles && renderer.renderType === '2D' ? Math.max(fallbackSize, parentHeight.value * BUILDING_HOLE_SCALAR) : undefined;

            const childGeometry = shapeToBufferedGeometry_000(
                childShape,
                useSegmentation,
                childPolygonToBox === 'FORCE_FALSE'
                    ? false
                    : (childPolygonToBox || (childrenAreHoles && renderer.renderType === '2D')),
                fallbackSize,
                forcedThickness,
                forcedHeight,
            )

            const childMesh = new Mesh(childGeometry)

            const parentTransformationMatrix = applyParentTransformationToChildren ? parentTransformation.value : new Matrix4().identity()

            const childTransformation = transformationOfShapeRepresentation(child)
            // noinspection NestedConditionalExpressionJS
            const childShapeTransformation = childPolygonToBox === "FORCE_FALSE"
                ? transformationOfShape(childShape)
                : childPolygonToBox && childShape.__typename === 'Polygon' ? new Matrix4().identity() : transformationOfShape(childShape)

            //parentShapeTransformation hier nicht anwenden (s.o. inverse)
            const childMeshTransformation = parentTransformationMatrix.clone()
                .multiply(childTransformation)
                .multiply(childShapeTransformation)

            tOverrideTransformation(childMesh, childMeshTransformation)

            childMesh.updateMatrix()
            childMesh.updateMatrixWorld(true)

            return childMesh
        })
    })

    return {
        parentTransformation,
        parentShapeTransformation,
        parentGeometry: parentGeometry_000,
        parentMesh,
        childMeshes: childMeshes_000_G,
        destroyParentChildMeshes: () => {
            parentGeometry_000.value.dispose()

            for (const childMesh of childMeshes_000_G.value) {
                tDestroyMesh(childMesh, true, false)
            }
        }
    }
}

export function useShapeRepresentation(renderer: BuildingRenderer, component: Readonly<Ref<BuildingComponent>>) {
    const visibility = computed<RenderVisibility>(() => renderer.visibility(component.value))
    const isSelfVisible = computed<boolean>(() => visibility.value === "VISIBLE")
    const areChildrenVisible = computed<boolean>(() => visibility.value === "VISIBLE" || visibility.value === "SELF_HIDDEN_CHILDREN_VISIBLE")
    const debug = computed<boolean>(() => renderer.debug(component.value))
    const renderOrder = computed<Optional<number>>(() => renderer.renderOrderOf(component.value))
    const hasBillboardEffect = computed<boolean>(() => renderer.hasBillboardEffect(component.value))
    const zFightingOffsetY = computed<Optional<number>>(() => renderer.zFightingOffsetYOf(component.value))
    const zFightingOffsetZ = computed<Optional<number>>(() => renderer.zFightingOffsetZOf(component.value))
    const zFightingScale = computed<Optional<Vector3>>(() => renderer.zFightingScaleOf(component.value))
    const isCameraFitIgnored = computed<boolean>(() => renderer.isCameraFitIgnored(component.value))

    return {
        visibility,
        isSelfVisible,
        areChildrenVisible,
        debug,
        renderOrder,
        hasBillboardEffect,
        zFightingOffsetY,
        zFightingOffsetZ,
        zFightingScale,
        isCameraFitIgnored,
    }
}

export function useRaycasterRelatedShapeRepresentation(renderer: BuildingRenderer, component: Readonly<Ref<TMaybeRaycasterRelated<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint, BuildingComponent>>>) {
    const raycasterObject = computed<Optional<BuildingComponent & TRaycasterRelated<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>>(() => {
        const c = toRaw(component.value)
        if ("hover" in c) {
            return c
        }
        if ("occlusionCulling" in c) {
            return c
        }
        if ("selection" in c) {
            return c
        }
        if ("draggable" in c) {
            return c
        }
        return null
    })
    const raycasterIds = computed<ReadonlySet<string>>(() => renderer.raycasterIdsOf(component.value))

    return {
        raycasterObject,
        raycasterIds,
    }
}

/**
 * @param room
 * @param roomIndex if roomIndex is lower than 0, the number will be ignored
 * @param t
 * @param n
 */
export function roomName(
    room: Room | ListingBuildingScanRoomInput | ListingBuildingScanRoom,
    roomIndex: number,
    t: (key: "listing.building.cad.defaultRoomName", named: NamedValue) => string,
    n: (value: number, keyOrOptions: "integer") => string
): string {
    const customData: readonly FlowConfigFieldValue[] = room.customData.map(cd => "isComputed" in cd ? cd : mapInputToFlowConfigFieldValue(cd, false))

    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(customData)

    const roomName = useFlowDataString(toRef(flowData), toRef({
        field: {
            id: 'room_name',
        },
        arrayIndex: null
    })).value

    if (roomName === null) {
        return t('listing.building.cad.defaultRoomName', {
            roomNumber: roomIndex < 0 ? undefined : n(roomIndex + 1, 'integer')
        })
    }

    return roomName
}

/**
 * @param room
 * @param roomIndex if roomIndex is lower than 0, the number will be ignored
 * @param t
 * @param n
 */
export function useRoomName(
    room: Readonly<Ref<Room | ListingBuildingScanRoomInput | ListingBuildingScanRoom>>,
    roomIndex: Readonly<Ref<number>>,
    t: (key: "listing.building.cad.defaultRoomName", named: NamedValue) => string,
    n: (value: number, keyOrOptions: "integer") => string
) {
    return computed<string>(() => roomName(room.value, roomIndex.value, t, n))
}

/**
 * Untergeschosse werden standardmäßig als "UG" bezeichnet, außer es gibt mehr als 1 UG, dann wird die Nummer hinzugenommen.
 * Das gleiche gilt für Obergeschosse.
 * Erdgeschosse und Dachgeschosse werden immer als "EG" und "DG" bezeichnet. Der Einfachheit halber wird die Nummer niemals hinzugefügt,
 * da es keine Gebäude mit mehreren Erd- oder Dachgeschossen geben kann.
 */
export function floorLevelInfoToShortDisplayName(
    floorLevelInfo: FloorLevelInfo,
    undergroundFloorCount: number,
    fullFloorCount: number,
    t: (key: string, number: number) => string
): string {
    const levelType = floorLevelInfo.levelType
    let floorNumber = floorLevelInfo.number
    if (
        (levelType === "UG" && undergroundFloorCount <= 1) ||
        (levelType === "OG" && fullFloorCount <= 1) ||
        (levelType === "EG" || levelType === "DG")
    ) {
        floorNumber = 0
    }

    return t(`listing.building.cad.floorLevelInfo.type.short.${levelType}`, floorNumber);
}

export function createNewWallInputCustomData(): EnsureDefined<FlowConfigFieldValueInput>[] {
    return [
        {
            field: {
                id: "construction_type",
            },
            arrayIndex: null,
            valueBoolean: null,
            valueInteger: null,
            valueDouble: null,
            valueString: "BY_U_VALUE",
        }
    ]
}

export function createNewWallOpeningWindowCustomData(): EnsureDefined<FlowConfigFieldValue>[] {
    return [
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_roller_shutters",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_heizkoerpernische",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        }
    ]
}

export function createNewWallOpeningCustomData(wallOpeningType: WallOpeningType): EnsureDefined<FlowConfigFieldValue>[] {
    switch (wallOpeningType) {
        case "WINDOW":
            return createNewWallOpeningWindowCustomData()
        case "DOOR":
            return []
        case "OPENING":
            return []
        default:
            console.warn(`Unsupported wall opening type ${wallOpeningType}`)
            return []
    }
}

export function createNewWallCustomData(): EnsureDefined<FlowConfigFieldValue>[] {
    return [
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "construction_type",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: null,
            valueInteger: null,
            valueDouble: null,
            valueString: "BY_U_VALUE",
        }
    ]
}

export function createNewPointOfInterestCustomData(): EnsureDefined<FlowConfigFieldValue>[] {
    return [
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_defect",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_radiator",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_light_switch",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_wall_socket",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_lamp",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "building_poi_is_todo",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        }
    ]
}

export function createNewRoomCustomData(): EnsureDefined<FlowConfigFieldValue>[] {
    return [
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "is_room_heated",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: true,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_radiators",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_underfloor_heating",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_fire_place",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_convector",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_wall_heating",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_ceiling_heating",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        },
        {
            __typename: "FlowConfigFieldValue",
            field: {
                __typename: "FlowConfigField",
                id: "has_indirect_heating",
            },
            arrayIndex: null,
            isComputed: false,
            valueBoolean: false,
            valueInteger: null,
            valueDouble: null,
            valueString: null,
        }
    ]
}

export function createEmptyShapeRepresentation(): EnsureDefined<ShapeRepresentation> {
    return {
        __typename: "ShapeRepresentation",
        transformationMatrix: createIdentityMatrix(),
        shape: {
            __typename: "Box",
            width: 0,
            height: 0,
            depth: 0,
        },
    }
}

const DEFAULT_VALID_ROOM_DATA = createNewRoomCustomData()

export function isRoomValidForRecognition(room: Room | ListingBuildingScanRoom): boolean {
    // return room.customData.some(cd => !cd.isComputed)
    if (!room.customData.some(cd => !cd.isComputed)) {
        return false
    }
    return !areFlowConfigFieldValuesEqual(room.customData, DEFAULT_VALID_ROOM_DATA)
}

/**
 * @return Azimuth 0 to 360 in degree
 */
export function azimuthInDegreeOfWallOrOpening(renderer: BuildingRenderer, wallOrOpening: Wall | ConstructionPart): number {
    const worldTransformation = worldTransformationOfWallOrOpening(renderer, wallOrOpening)

    // Extract the forward vector (3rd column of the matrix)
    const forward = new Vector3(worldTransformation.elements[8], worldTransformation.elements[9], worldTransformation.elements[10]);

    const azimuthRadians = Math.atan2(-forward.x, forward.z);
    let azimuthDegrees = MathUtils.radToDeg(azimuthRadians)

    // Ensure the azimuth is in the range [0, 360]
    if (azimuthDegrees < 0) {
        azimuthDegrees += 360;
    }

    return azimuthDegrees;
}

const cameraTransformationMatrixField: FlowConfigField = {
    id: "photo_camera_transformation_matrix"
}
const cameraProjectionMatrixField: FlowConfigField = {
    id: "photo_camera_projection_matrix"
}
export type PointOfInterestCamera = {
    arrayIndex: number,
    transformationMatrix: Matrix4,
    projectionMatrix: Matrix4,
}

export function calculateCamerasOfPointOfInterest(pointOfInterest: BuildingPointOfInterest | BuildingPointOfInterestInput): readonly PointOfInterestCamera[] {
    const rawPointOfInterest = toRaw(pointOfInterest)
    const customData = "__typename" in rawPointOfInterest ? rawPointOfInterest.customData : rawPointOfInterest.customData.map(cd => mapInputToFlowConfigFieldValue(cd, false))

    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(customData)

    const arrayIndices = flowData.arrayIndicesOf([cameraTransformationMatrixField])

    return arrayIndices
        .map(arrayIndex => {
            const transformationMatrixString = flowData.getString({
                field: cameraTransformationMatrixField,
                arrayIndex,
            })
            const projectionMatrixString = flowData.getString({
                field: cameraProjectionMatrixField,
                arrayIndex,
            })

            if (transformationMatrixString === null || projectionMatrixString === null) {
                return null
            }

            const transformationMatrixNumberArray = JSON.parse(transformationMatrixString) as number[][]
            const projectionMatrixNumberArray = JSON.parse(projectionMatrixString) as number[][]

            const transformationMatrix = transformationMatrixOfArray(transformationMatrixNumberArray)
            const projectionMatrix = transformationMatrixOfArray(projectionMatrixNumberArray)

            return {
                arrayIndex,
                transformationMatrix: transformationMatrix ?? new Matrix4().identity(),
                projectionMatrix: projectionMatrix ?? new Matrix4().identity(),
            } satisfies PointOfInterestCamera
        })
        .filter(values => values !== null)
}

export function isPointOfInterestJustAPhoto(pointOfInterest: BuildingPointOfInterest, cameras?: readonly PointOfInterestCamera[]): boolean {
    const cams = cameras ?? calculateCamerasOfPointOfInterest(pointOfInterest)
    if (cams.length !== 1) {
        return false
    }
    const camMatrix = cams[0].transformationMatrix
    const poiMatrix = transformationMatrixOfArray(pointOfInterest.transformationMatrix)
    return tAreMatricesEqual(poiMatrix, camMatrix, BUILDING_EPSILON)
}

export function overwriteCameraTransformationMatrixOfPointOfInterest(
    pointOfInterest: BuildingPointOfInterest,
    arrayIndex: number,
    transformationMatrix: Matrix4,
) {
    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(pointOfInterest.customData)

    flowData.setString(
        {
            arrayIndex,
            field: cameraTransformationMatrixField,
        },
        JSON.stringify(matrix4ToTransformationMatrixArray(transformationMatrix)),
        true
    )

    pointOfInterest.customData = flowData.generateInput(false)
}

export function overwriteCameraTransformationMatrixOfPointOfInterestInput(
    pointOfInterest: BuildingPointOfInterestInput,
    arrayIndex: number,
    transformationMatrix: Matrix4,
) {
    const customData = pointOfInterest.customData.map(cd => mapInputToFlowConfigFieldValue(cd, false))

    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(customData)

    flowData.setString(
        {
            arrayIndex,
            field: cameraTransformationMatrixField,
        },
        JSON.stringify(matrix4ToTransformationMatrixArray(transformationMatrix)),
        true
    )

    pointOfInterest.customData = flowData.generateInput(false).map(cd => mapFlowConfigFieldValueToInput(cd))
}

export function buildingRotationYDegreesTowardsCardinalDirection(renderer: BuildingRenderer): number {
    const building = renderer.building.value
    const correctionAngle = building.rotationYCorrectionAngle
    const baseAngle = tRotationYInDegreeFromTransformation(transformationMatrixOfShapeRepresentation(building.shapeRepresentation))

    return correctionAngle + baseAngle
}

export function createCmToMField(cmField: Ref<Optional<number>>): Ref<Optional<number>> {
    return computed<Optional<number>>({
        get: () => cmField.value === null ? null : cmField.value / 100,
        set: valueM => {
            cmField.value = valueM === null ? null : Math.round(valueM * 100)
        }
    })
}

export function noThicknessWallOffsetZ(renderer: BuildingRenderer, wall: Wall): Optional<number> {
    const shape = wall.shapeRepresentation.shape
    const thickness = thicknessOfShape(shape)

    if (isNumberGreaterThan(thickness, 0, BUILDING_EPSILON)) {
        return null
    }

    if (shape.__typename === "Box" && wall.isExterior === true) {
        return -renderer.fallbackSize / 2
    }

    if (shape.__typename === "Polygon") {
        return wall.isExterior === true
            ? -renderer.fallbackSize
            : -renderer.fallbackSize / 2
    }

    return null
}

export function noThicknessOpeningOffsetZ(renderer: BuildingRenderer, opening: ConstructionPart): Optional<number> {
    const wall = renderer.traversableBuilding.parentOf(opening)
    if (wall === null) {
        return null
    }
    if (wall.__typename !== "Wall") {
        return null
    }

    const shape = wall.shapeRepresentation.shape

    if (shape.__typename === "Polygon") {
        const thickness = thicknessOfShape(shape)
        if (isNumberLessThanOrEqual(thickness, 0, BUILDING_EPSILON)) {
            return renderer.fallbackSize / 2
        }
    }

    return null
}

export function rotateWallBy180Degrees(renderer: BuildingRenderer, wall: Wall) {
    const oldThickness = thicknessOfShape(wall.shapeRepresentation.shape)
    changeWallThickness(renderer, wall, 0)

    const wallTransformation = transformationOfShapeRepresentation(wall.shapeRepresentation)
    const rotationMatrix = new Matrix4().makeRotationY(MathUtils.degToRad(-180))
    const newWallTransformation = wallTransformation.clone().multiply(rotationMatrix)
    wall.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newWallTransformation)

    const invertedWallNewTransformation = newWallTransformation.clone().invert()

    for (const opening of wall.openings) {
        const openingTransformation = transformationOfShapeRepresentation(opening.shapeRepresentation)
        const newOpeningTransformation = invertedWallNewTransformation.clone().multiply(wallTransformation).multiply(openingTransformation)
        opening.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newOpeningTransformation)
    }

    changeWallThickness(renderer, wall, oldThickness)
}

// noinspection FunctionTooLongJS
function angleInDegreesBetweenLineSegmentPoints(
    s1: Vector3,
    e1: Vector3,
    s2: Vector3,
    e2: Vector3,
    epsilon: number,
    doTranslation: boolean = true
): number {
    let a: Vector3
    let b: Vector3
    let c: Vector3

    // noinspection IfStatementWithTooManyBranchesJS
    if (tAreVectors3Equal(s1, s2, epsilon)) {
        a = e1
        b = s1
        c = e2
    } else if (tAreVectors3Equal(s1, e2, epsilon)) {
        a = e1
        b = s1
        c = s2
    } else if (tAreVectors3Equal(e1, s2, epsilon)) {
        a = s1
        b = e1
        c = e2
    } else if (tAreVectors3Equal(e1, e2, epsilon)) {
        a = s1
        b = e1
        c = s2
    } else {
        //Move the second line segment to the first (second start, first end)
        if (doTranslation) {
            const translationVector = e1.clone().sub(s2)

            const newS2 = s2.clone().add(translationVector)
            const newE2 = e2.clone().add(translationVector)

            // noinspection TailRecursionJS
            return angleInDegreesBetweenLineSegmentPoints(s1, e1, newS2, newE2, epsilon, false) //recursion
        }
        console.warn("The walls are not connected", s1, e1, s2, e2)
        return -1
    }

    const ba = a.clone().sub(b).normalize()
    const bc = c.clone().sub(b).normalize()

    // Calculate the dot product to get the angle between the two vectors
    const dotProduct = ba.dot(bc)
    let angle = Math.acos(dotProduct)

    // Calculate the cross product to determine the direction (clockwise/counterclockwise)
    const crossProduct = new Vector3().crossVectors(ba, bc)

    // Use the sign of the cross product's y-component (or another axis based on your coordinate system)
    // to determine if the angle is clockwise or counterclockwise
    if (crossProduct.y < 0) {
        // If the cross product's y is negative, it means the angle is greater than 180 degrees
        angle = 2 * Math.PI - angle
    }

    let angleInDegrees = MathUtils.radToDeg(angle)
    angleInDegrees = isNaN(angleInDegrees) ? 180 : angleInDegrees
    angleInDegrees = Math.round(angleInDegrees)

    return angleInDegrees
}

// noinspection FunctionTooLongJS
export function angleInDegreesBetweenWalls(renderer: BuildingRenderer, wall1: Wall, wall2: Wall, epsilon: number): number {
    const lineSegment1 = wallOrOpeningToLineSegment(renderer, wall1, wall1, false)
    const lineSegment2 = wallOrOpeningToLineSegment(renderer, wall2, wall2, false)

    const s1 = new Vector3(lineSegment1.start.x, 0, lineSegment1.start.y)
    const e1 = new Vector3(lineSegment1.end.x, 0, lineSegment1.end.y)
    const s2 = new Vector3(lineSegment2.start.x, 0, lineSegment2.start.y)
    const e2 = new Vector3(lineSegment2.end.x, 0, lineSegment2.end.y)

    return angleInDegreesBetweenLineSegmentPoints(s1, e1, s2, e2, epsilon)
}

export function typeNameOfWall(wall: Wall, t: (key: string) => string): string {
    const type = wallTypeOfWall(wall)
    if (type === "INTERMEDIATE") {
        return t('listing.building.wallType.INTERMEDIATE_WALL')
    }
    const subtype = wall.isPartition ? "PARTITION_WALL" : "WALL"

    return t(`listing.building.wallType.${type}_${subtype}`)
}

export function adjustedWallWidth(renderer: BuildingRenderer, wall: Wall, useFallbackSize: boolean): number {
    const rawWidth = widthOfShape(wall.shapeRepresentation.shape)
    if (wall.id === WallCreator.TEMP_WALL_ID) {
        return rawWidth
    }

    let width = rawWidth + wall.sizeAdjustmentX + wall.sizeAdjustmentUserX

    //Hier dürfen wir nicht auf calculatePossibleSizeAdjustmentXRelatedWalls prüfen, da das zu teuer ist hinsichtlich performance
    //Andernfalls wird drag&drop unbenutzbar
    for (const wallId of wall.sizeAdjustmentUserXRelatedWallIds) {
        const wall = renderer.traversableBuilding.wallOf(wallId)
        if (wall !== null) {
            width += adjustedWallThickness(renderer, wall, useFallbackSize)
        }
    }

    return useFallbackSize
        ? Math.max(renderer.fallbackSize, width)
        : width
}

export function adjustedWallHeight(renderer: BuildingRenderer, wall: Wall, useFallbackSize: boolean): number {
    const rawHeight = heightOfShape(wall.shapeRepresentation.shape)
    if (wall.id === WallCreator.TEMP_WALL_ID) {
        return rawHeight
    }

    const height = rawHeight + wall.sizeAdjustmentY + wall.sizeAdjustmentUserY

    return useFallbackSize
        ? Math.max(renderer.fallbackSize, height)
        : height
}

export function adjustedWallThickness(renderer: BuildingRenderer, wall: Wall, useFallbackSize: boolean): number {
    const rawThickness = thicknessOfShape(wall.shapeRepresentation.shape)
    if (wall.id === WallCreator.TEMP_WALL_ID) {
        return rawThickness
    }

    const thickness = rawThickness + wall.sizeAdjustmentZ

    return useFallbackSize
        ? Math.max(renderer.fallbackSize, thickness)
        : thickness
}

export type PolygonEdgeType = "LEFT" | "RIGHT" | "BOTTOM" | "TOP" | "CENTER"
export type PolygonEdge = {
    type: PolygonEdgeType,
    lineSegment: TLineSegment2D,
}

export function wallToPolygonEdges(wall: Wall): readonly PolygonEdge[] {
    if (wall.shapeRepresentation.shape.__typename === "Polygon") {
        return calculateEdgesOfPolygon(wall.shapeRepresentation.shape)
    }
    const polygonShape = changeShapeType(wall.shapeRepresentation, "Polygon", wall.isExterior === true)
    if (polygonShape === null) {
        console.warn("Wall is not a polygon", wall)
        return []
    }
    return calculateEdgesOfPolygon(polygonShape.shape)
}

// noinspection OverlyComplexFunctionJS
export function calculateEdgesOfPolygon(polygon: Polygon): PolygonEdge[] {
    if (polygon.vertices.length <= 0) {
        return []
    }

    const vertices = polygon.vertices.map(v => new Vector2(v.x, v.y))

    const xs = polygon.vertices.map(v => v.x)
    const xMin = xs.length <= 0 ? 0 : Math.min(...xs)
    const xMax = xs.length <= 0 ? 0 : Math.max(...xs)

    const ys = polygon.vertices.map(v => v.y)
    const yMin = ys.length <= 0 ? 0 : Math.min(...ys)
    const yMax = ys.length <= 0 ? 0 : Math.max(...ys)

    const edges: PolygonEdge[] = []

    for (let i = 0; i < vertices.length; ++i) {
        const start = vertices[i]
        const end = vertices[(i + 1) % vertices.length]

        //Leerer Punkt
        if (tAreVectors2Equal(start, end, BUILDING_EPSILON)) {
            continue
        }
        //Linke Kante
        if (areNumbersEqual(start.x, xMin, BUILDING_EPSILON) && areNumbersEqual(end.x, xMin, BUILDING_EPSILON)) {
            edges.push({
                type: "LEFT",
                lineSegment: {
                    start: new Vector2(start.x, start.y),
                    end: new Vector2(end.x, end.y),
                }
            })
            continue
        }
        //Rechte Kante
        if (areNumbersEqual(start.x, xMax, BUILDING_EPSILON) && areNumbersEqual(end.x, xMax, BUILDING_EPSILON)) {
            edges.push({
                type: "RIGHT",
                lineSegment: {
                    start: new Vector2(start.x, start.y),
                    end: new Vector2(end.x, end.y),
                }
            })
            continue
        }
        //Untere Kante
        if (areNumbersEqual(start.y, yMin, BUILDING_EPSILON) && areNumbersEqual(end.y, yMin, BUILDING_EPSILON)) {
            edges.push({
                type: "BOTTOM",
                lineSegment: {
                    start: new Vector2(start.x, start.y),
                    end: new Vector2(end.x, end.y),
                }
            })
            continue
        }
        //Obere Kante
        if (areNumbersEqual(start.y, yMax, BUILDING_EPSILON) && areNumbersEqual(end.y, yMax, BUILDING_EPSILON)) {
            edges.push({
                type: "TOP",
                lineSegment: {
                    start: new Vector2(start.x, start.y),
                    end: new Vector2(end.x, end.y),
                }
            })
            continue
        }
        edges.push({
            type: "CENTER",
            lineSegment: {
                start: new Vector2(start.x, start.y),
                end: new Vector2(end.x, end.y),
            }
        })
    }

    return edges
}

export function calculatePermimeterOfPolygon(polygon: Polygon, edges?: readonly PolygonEdge[]): number {
    const polygonEdges = edges ?? calculateEdgesOfPolygon(polygon)
    return polygonEdges.reduce((acc, edge) => acc + edge.lineSegment.start.distanceTo(edge.lineSegment.end), 0)
}

export function calculateTopAndCenterPerimeterOfPolygon(polygon: Polygon, edges?: readonly PolygonEdge[]): number {
    const polygonEdges = edges ?? calculateEdgesOfPolygon(polygon)
    return polygonEdges.reduce((acc, edge) => {
        if (edge.type === "TOP" || edge.type === "CENTER") {
            return acc + edge.lineSegment.start.distanceTo(edge.lineSegment.end)
        }
        return acc
    }, 0)
}

export function calculateTopPerimeterOfPolygon(polygon: Polygon, edges?: readonly PolygonEdge[]): number {
    const polygonEdges = edges ?? calculateEdgesOfPolygon(polygon)
    return polygonEdges.reduce((acc, edge) => {
        if (edge.type === "TOP") {
            return acc + edge.lineSegment.start.distanceTo(edge.lineSegment.end)
        }
        return acc
    }, 0)
}

export function worldTransformationOfRoom(renderer: BuildingRenderer, room: Room): Matrix4 {
    //wir dürfen hier nicht das traversableBuilding verwenden,
    //weil in der Pipeline neue Räume erstellt werden, die aber noch nicht gerendert wurden und damit beim TraversableBuilding registriert wurden.
    //d.h. man findet noch nicht immer einen floor für den raum

    const buildingTransformation = transformationMatrixOfShapeRepresentation(renderer.building.value.shapeRepresentation)
    const roomTransformation = transformationMatrixOfShapeRepresentation(room.shapeRepresentation)

    for (const floor of renderer.building.value.floors) {
        for (const r of floor.rooms) {
            if (r.id === room.id) {
                const floorTransformation = transformationMatrixOfShapeRepresentation(floor.shapeRepresentation)
                return buildingTransformation.clone().multiply(floorTransformation).multiply(roomTransformation)
            }
        }
    }
    return buildingTransformation
}

export function isWorldPointInsideRoom(renderer: BuildingRenderer, room: Room, worldPoint: Vector2): boolean {
    if (room.shapeRepresentation.shape.__typename !== "Polygon") {
        console.warn("Room is not a polygon", room)
        return false
    }

    const worldTransformation = worldTransformationOfRoom(renderer, room)

    const vertices3D = room.shapeRepresentation.shape.vertices.map(v => new Vector3(v.x, 0, -v.y).applyMatrix4(worldTransformation).applyMatrix4(RoomCalculator.vertexTransformation))
    const vertices2D = vertices3D.map(v => new Vector2(v.x, -v.z))

    return d3IsVector2InsidePolygon(worldPoint, vertices2D)
}

export function flipExteriorWallIfNeeded(renderer: BuildingRenderer, wall: Wall, ignoreThickness: boolean) {
    if (wall.isExterior !== true) {
        return
    }
    if (wall.isPartition === true) {
        return //Teilwände ignorieren, Richtung spielt keine Rolle
    }
    if (wall.shapeRepresentation.shape.__typename === "Ring") {
        return //TODO: ring supporten?! Zentrum der Wand liegt nie im Raum, auch nicht mit forciertem Offset
    }
    if (wall.roomIds === undefined || wall.roomIds === null || wall.roomIds.length <= 0) {
        return //nicht ermittelbar, ob Wand innen oder außen liegt
    }
    if (!ignoreThickness) {
        const thickness = thicknessOfShape(wall.shapeRepresentation.shape)
        if (isNumberLessThanOrEqual(thickness, 0, BUILDING_EPSILON)) {
            return //wenn wir Wände invertieren, die eine Stärke haben, dann kann es zu ungewünschten Verschneidungen kommen, das sollten wir lassen
        }
    }

    const forcedOffsetZ = 0.1
    const start = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "LEFT", wall, false, forcedOffsetZ)
    const end = calculateWallOrOpeningSnapPointPositionXZ(renderer, wall, "RIGHT", wall, false, forcedOffsetZ)
    const wallCenterWorld = start.clone().add(end).multiplyScalar(0.5)
    const roomIds = new Set(wall.roomIds)
    const rooms = renderer.building.value.floors.flatMap(f => f.rooms.filter(r => roomIds.has(r.id)))

    //wall.roomIds.some(roomId => {
    // const room = renderer.traversableBuilding.roomOf(roomId) //wir dürfen hier nicht das traversableBuilding verwenden,
    //weil in der Pipeline neue Räume erstellt werden, die aber noch nicht gerendert wurden und damit beim TraversableBuilding registriert wurden.
    const isInsideAnyRoom = rooms.some(room => isWorldPointInsideRoom(renderer, room, wallCenterWorld))

    if (!isInsideAnyRoom) {
        rotateWallBy180Degrees(renderer, wall)
    }
}