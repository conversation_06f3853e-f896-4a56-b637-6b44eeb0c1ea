<template>
    <v-dialog v-model="isDialogVisible"
              :max-width="400"
              :offset="27"
              location="bottom center"
              location-strategy="connected"
              origin="auto">
        <template #activator="{props: dialogProps}">
            <d-btn v-if="variant === 'BUTTON'"
                   :icon="xs ? mdiHomeExportOutline : undefined"
                   :loading="isLoading"
                   :prepend-icon="xs ? undefined : mdiHomeExportOutline"
                   :text="xs ? undefined : t('components.listing.buildingModel.exportDialog.button')"
                   type="default"
                   v-bind="dialogProps"/>

            <d-list-item v-else-if="variant === 'LIST_ITEM'"
                         :prepend-icon="mdiHomeExportOutline"
                         :title="t('components.listing.buildingModel.exportDialog.button')"
                         lines="one"
                         v-bind="dialogProps"/>
        </template>

        <d-card is-in-dialog>
            <template #title>
                <v-layout class="justify-space-between align-center">
                    {{ t('components.listing.buildingModel.exportDialog.title') }}
                    <d-btn :icon="mdiClose"
                           density="compact"
                           type="default"
                           @click.stop="onDialogClose"/>
                </v-layout>
            </template>

            <d-divider/>

            <d-sheet>
                <v-container fluid>
                    <v-row>
                        <v-col>
                            <d-select v-model="exportType"
                                      :clearable="false"
                                      :display-name-supplier="v => t(`enums.buildingExportType.${v}`)"
                                      :id-supplier="v => v"
                                      :items="visibleBuildingExportTypeValues"/>
                        </v-col>
                    </v-row>
                    <v-slide-y-transition>
                        <v-row v-if="exportType === 'IFC'">
                            <v-col>
                                <d-select v-model="ifcTargetCAD"
                                          :clearable="false"
                                          :display-name-supplier="v => t(`enums.ifcExportContextTargetCAD.${v}`)"
                                          :id-supplier="v => v"
                                          :items="IFCExportContextTargetCADValues"
                                          :label="t('components.listing.buildingModel.exportDialog.optimizeExportFor')"/>
                            </v-col>
                        </v-row>
                    </v-slide-y-transition>
                    <v-slide-y-transition>
                        <v-row v-if="exportType === 'PDF'">
                            <v-col>
                                <d-select v-model="pdfExportLevel"
                                          :clearable="false"
                                          :display-name-supplier="v => t(`enums.pdfExport.levels.${v}`)"
                                          :id-supplier="v => v"
                                          :items="PDFExportTypeValues"
                                          :label="t('enums.pdfExport.label')"/>
                            </v-col>
                        </v-row>
                    </v-slide-y-transition>
                    <v-slide-y-transition>
                        <v-row v-if="exportType === 'PDF'">
                            <v-col>
                                <d-select v-model="pdfPageOrientation"
                                          :clearable="false"
                                          :display-name-supplier="v => t(`enums.pdfExport.pageOrientation.${v}`)"
                                          :id-supplier="v => v"
                                          :items="['portrait', 'landscape']"
                                          :label="t('components.listing.buildingModel.exportDialog.pageOrientation')"/>
                            </v-col>
                        </v-row>
                    </v-slide-y-transition>

                    <v-slide-y-transition>
                        <v-row v-if="exportType === 'PDF' && svgRenderer !== null">
                            <v-col :style="{'aspect-ratio': pdfPageOrientation === 'portrait' ? 0.7864815230746872 : 1.649858900995532}"> <!-- DIN A4 - Paddings -->
                                <d-listing-building-renderer-wrapper :renderer="svgRenderer">
                                    <d-l-b-r ref="tRendererSvg"
                                             :needs-image-download="false"
                                             :show-signature="false"
                                             renderer-id="svgDownload"/>
                                </d-listing-building-renderer-wrapper>
                            </v-col>
                        </v-row>
                    </v-slide-y-transition>
                    <!--                    <v-row v-else-if="exportType === 'GBXML'">-->
                    <!--                        <v-col>-->
                    <!--                            <d-select v-model="gbXMLTargetCAD"-->
                    <!--                                      :clearable="false"-->
                    <!--                                      :display-name-supplier="v => t(`enums.gbXMLExportType.${v}`)"-->
                    <!--                                      :id-supplier="v => v"-->
                    <!--                                      :items="GBXMLExportTypeValues"-->
                    <!--                                      :label="t('components.listing.buildingModel.exportDialog.optimizeExportFor')"/>-->
                    <!--                        </v-col>-->
                    <!--                    </v-row>-->
                    <!--                    <v-row v-else-if="exportType === 'EVEX'">-->
                    <!--                        <v-col>-->
                    <!--                            <d-select v-model="evexTargetCAD"-->
                    <!--                                      :clearable="false"-->
                    <!--                                      :display-name-supplier="v => t(`enums.evexExportType.${v}`)"-->
                    <!--                                      :id-supplier="v => v"-->
                    <!--                                      :items="EvexExportTypeValues"-->
                    <!--                                      :label="t('components.listing.buildingModel.exportDialog.optimizeExportFor')"/>-->
                    <!--                        </v-col>-->
                    <!--                    </v-row>-->
                    <!--                    <v-row>-->
                    <!--                        <v-col>-->
                    <!--                            <d-switch-input v-model="onlyEnergeticRelevantData"-->
                    <!--                                            :label="t(`components.listing.buildingModel.exportDialog.onlyEnergeticRelevantParts`)"/>-->
                    <!--                        </v-col>-->
                    <!--                    </v-row>-->
                </v-container>
            </d-sheet>

            <d-divider/>

            <template #actions>
                <v-layout class="justify-end px-2 py-2">
                    <d-btn :loading="isLoading || svgRenderer !== null"
                           :prepend-icon="mdiDownload"
                           :text="t('components.listing.buildingModel.exportDialog.downloadButton')"
                           type="primary"
                           @click.stop="triggerExport"/>
                </v-layout>
            </template>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {mdiClose, mdiDownload, mdiHomeExportOutline} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useI18n} from "vue-i18n";
    import {computed, inject, onMounted, shallowRef, toRef} from "vue";
    import {useDisplay} from "vuetify";
    import {Building, DListingBuildingExportDialogDownloadEvexQueryVariables, Floor, useDListingBuildingExportDialogDownloadEvexLazyQuery, useDListingImagesDownloadMutation} from "@/adapter/graphql/generated/graphql";
    import {IFCExportContext, IFCExportContextTargetCAD, IFCExportContextTargetCADValues} from "@/adapter/ifc/IFCExportContext";
    import {ifcDownload} from "@/adapter/ifc/ifc-helper";
    import {Address, useAddressService} from "@/utility/address";
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DSheet from "@/adapter/vuetify/theme/components/card/d-sheet.vue";
    import {createMutableBuildingFrom, DBuildingRendererInjection} from "@/components/listing/building/building";
    import {BuildingExportType, BuildingExportTypeValues, EvexExportType, PDFExportType, PDFExportTypeValues} from "@/model/building/BuildingExportType";
    import {downloadBlob} from "@/utility/download";
    import {useListingListFields} from "@/components/listing/list/useListingListFields";
    import {LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import {Optional} from "@/model/Optional";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {jsPDF} from 'jspdf';
    import DListingBuildingRendererWrapper from "@/components/listing/building/d-listing-building-renderer-wrapper.vue";
    import DLBR from "@/components/listing/building/renderer/d-l-b-r.vue";
    import TRendererSvg from "@/adapter/three/components/t-renderer-svg.vue";
    import {BuildingRenderer2DSvg} from "@/components/listing/building/renderer/BuildingRenderer2DSvg";
    import {emptySet} from "@/utility/set";
    import {svg2pdf} from 'svg2pdf.js'
    import {useScreenOrientation} from "@vueuse/core";

    const props = defineProps<{
        listingId: string
        building: Building
        address: Address
        variant: "BUTTON" | "LIST_ITEM"
    }>()

    const {t} = useI18n();
    const {xs} = useDisplay()

    const tRendererSvg = shallowRef<Optional<typeof TRendererSvg>>(null)

    const isLoading = shallowRef<boolean>(false)
    const isDialogVisible = shallowRef<boolean>(false)

    const exportType = shallowRef<BuildingExportType>("EVEX")
    const ifcTargetCAD = shallowRef<IFCExportContextTargetCAD>("ECAD")
    const pdfExportLevel = shallowRef<PDFExportType>("ALL")
    const {
        orientation
    } = useScreenOrientation()
    const pdfPageOrientation = shallowRef<"portrait" | "landscape">("landscape")

    // const gbXMLTargetCAD = shallowRef<GBXMLExportType>("EVEBI")
    const evexTargetCAD = shallowRef<EvexExportType>("EVEBI")
    const onlyEnergeticRelevantData = shallowRef<boolean>(false)

    const renderer = inject(DBuildingRendererInjection)!
    const propBuilding = toRef(() => props.building)

    const svgRenderer = shallowRef<Optional<BuildingRenderer2DSvg>>(null)

    onMounted(() => {
        pdfPageOrientation.value = orientation.value === 'landscape-primary' ? 'landscape' : 'portrait'
    })

    const {
        addressDisplayNameOf
    } = useAddressService()

    function onDialogClose() {
        isDialogVisible.value = false
    }

    const visibleBuildingExportTypeValues = computed<readonly BuildingExportType[]>(() => {
        if (renderer.renderType === '3D') {
            return BuildingExportTypeValues.filter(v => v !== "PDF")
        }
        return BuildingExportTypeValues
    })

    async function triggerExport() {
        isLoading.value = true

        try {
            switch (exportType.value) {
                case "IFC":
                    return await downloadIFC()
                // case "GBXML":
                //     return await triggerGBXMLDownload()
                case "EVEX":
                    return await triggerEvexDownload()
                case "IMAGES":
                    return await triggerImagesDownload()
                case "PDF":
                    return await triggerPDFDownload()
            }
        } finally {
            isLoading.value = false
            isDialogVisible.value = false
        }
    }

    async function triggerPDFDownload() {
        console.log("Exporting PDF for", pdfExportLevel.value)

        const newRenderer = new BuildingRenderer2DSvg(
            props.listingId,
            propBuilding,
        )
        newRenderer.visibleFloorLevels.value = new Set([0])
        svgRenderer.value = newRenderer

        await new Promise(resolve => setTimeout(resolve, 1000))

        const oldRenderer = renderer

        if (renderer === undefined) {
            return
        }

        const oldVisibleFloorLevels = oldRenderer?.visibleFloorLevels.value ?? emptySet()
        if (oldVisibleFloorLevels.size <= 0) {
            newRenderer.visibleFloorLevels.value = newRenderer.building.value.floors.length > 0 ? new Set([newRenderer.building.value.floors[0].level]) : emptySet()
        } else if (oldVisibleFloorLevels.size <= 1) {
            newRenderer.visibleFloorLevels.value = oldVisibleFloorLevels
        } else {
            switch (newRenderer.renderType) {
                case "2D":
                    newRenderer.visibleFloorLevels.value = new Set([oldVisibleFloorLevels.values().next().value!])
                    break
                case "3D":
                    newRenderer.visibleFloorLevels.value = oldVisibleFloorLevels
                    break
            }
        }

        newRenderer.semiVisibleFloorLevels.value = oldRenderer.semiVisibleFloorLevels.value
        newRenderer.showFurniture.value = oldRenderer.showFurniture.value
        newRenderer.showCompass.value = oldRenderer.showCompass.value
        newRenderer.showFloorGrid.value = oldRenderer.showFloorGrid.value
        newRenderer.showRoomTexts.value = oldRenderer.showRoomTexts.value
        newRenderer.visibleWallTypes.value = oldRenderer.visibleWallTypes.value
        newRenderer.showPointsOfInterest.value = oldRenderer.showPointsOfInterest.value
        newRenderer.showWallWidths.value = oldRenderer.showWallWidths.value
        newRenderer.showWallThicknesses.value = oldRenderer.showWallThicknesses.value
        newRenderer.showDisplayIds.value = oldRenderer.showDisplayIds.value
        newRenderer.showRoofAreas.value = oldRenderer.showRoofAreas.value
        newRenderer.isEvebiModeEnabled.value = oldRenderer.isEvebiModeEnabled.value

        if (newRenderer.wallOpeningCreator !== null && oldRenderer.wallOpeningCreator) {
            newRenderer.wallOpeningCreator.openingType.value = oldRenderer.wallOpeningCreator.openingType.value
        }

        const doc = new jsPDF({orientation: pdfPageOrientation.value === 'portrait' ? 'portrait' : 'landscape', unit: 'pt', format: 'a4'});
        const width = doc.internal.pageSize.getWidth();
        const height = doc.internal.pageSize.getHeight();

        console.log("PDF page size", width, height, width / height)

        const compassSvg = (rotationYCorrectionAngle: number) => {
            const svgText = `

            <svg fill="#000000" height="800px" width="800px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 490 490" xml:space="preserve" style="transform: rotate(-90deg); transform-origin: center;">
            <g>
                <g>
                    <g>
                        <polygon points="50.3,248.3 143.5,191.9 236.8,248.3 143.5,-0" transform="rotate(${rotationYCorrectionAngle}, 143.5, 143.5)"/>
                    </g>
                </g>
            </g>
            </svg>
        `
            return new DOMParser().parseFromString(svgText, "image/svg+xml").documentElement
        }

        const writePage = async (floor: Floor, switchFloor: boolean) => {
            doc.text(
                t(`listing.building.cad.floorLevelInfo.type.long.${floor.levelInfo.levelType}`, {
                    n: floor.levelInfo.number
                }),
                width / 2,
                50,
                {align: "center"}
            );

            if (switchFloor) {
                newRenderer.visibleFloorLevels.value = new Set([floor.level])
                await new Promise(resolve => setTimeout(resolve, 1_000))
            }

            await svg2pdf(tRendererSvg.value!.generateSVG(), doc, {
                x: 0,
                y: 50,
                width: width,
                height: height - 50 - 35
            });
        }

        switch (pdfExportLevel.value) {
            case 'ALL':
                for (let i = 0; i < props.building.floors.length; ++i) {
                    const floor = props.building.floors[i]
                    if (i > 0) {
                        doc.addPage()
                    }
                    await writePage(floor, true)
                }
                break

            case 'CURRENT_FLOOR':
                await writePage(newRenderer.currentFloor.value ?? newRenderer.building.value.floors[0], true)
                break
        }

        const pageCount = doc.getNumberOfPages();
        const compass = compassSvg(props.building.rotationYCorrectionAngle)
        for (let i = 1; i <= pageCount; i++) {
            await svg2pdf(compass, doc, {x: width - 35, y: height - 35, width: 50, height: 50});

            doc.setPage(i);
            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100); // Gray color
            doc.text("generated by doorbit", width / 2, height - 17.5, {align: "center"});
        }

        doc.save(filename.value + ".pdf");

        newRenderer.destroy()
        svgRenderer.value = null
    }

    async function downloadIFC() {
        const context: IFCExportContext = {
            ifcVersion: "IFC4",
            targetCAD: ifcTargetCAD.value,
            onlyEnergeticRelevantParts: onlyEnergeticRelevantData.value,
        }

        console.log("Exporting IFC with context", context)

        try {
            await ifcDownload(
                createMutableBuildingFrom(props.building),
                context,
                ifcProjectName.value,
                filename.value,
            )
        } catch (e) {
            console.warn("IFC download failed", e)
        }
    }

    // const downloadGbXmlParameter = computed<DListingBuildingExportDialogDownloadGbXmlQueryVariables>(() => ({
    //     listingId: props.listingId,
    //     onlyEnergeticRelevantParts: onlyEnergeticRelevantData.value
    // }))
    //
    // const {
    //     load: downloadGbXml,
    //     refetch: redownloadGbXml,
    // } = useDListingBuildingExportDialogDownloadGbXmlLazyQuery(downloadGbXmlParameter, {
    //     fetchPolicy: "no-cache"
    // })

    const downloadEvexParameter = computed<DListingBuildingExportDialogDownloadEvexQueryVariables>(() => ({
        listingId: props.listingId,
        onlyEnergeticRelevantParts: onlyEnergeticRelevantData.value
    }))

    const {
        load: downloadEvex,
        refetch: redownloadEvex,
    } = useDListingBuildingExportDialogDownloadEvexLazyQuery(downloadEvexParameter, {
        fetchPolicy: "no-cache"
    })

    // async function triggerGBXMLDownload() {
    //     const cad = gbXMLTargetCAD.value
    //     const onlyEnergeticRelevantParts = onlyEnergeticRelevantData.value
    //
    //     console.log("Exporting GBXML for", cad, onlyEnergeticRelevantParts)
    //
    //     let rawString: Optional<string>
    //     try {
    //         const resultDL = downloadGbXml()
    //         if (resultDL === false) {
    //             const resultReDL = await redownloadGbXml()
    //             rawString = resultReDL?.data.exportListingBuildingAsGbXml as (undefined | null | string) ?? null
    //         } else {
    //             rawString = (await resultDL).exportListingBuildingAsGbXml
    //         }
    //     } catch (e) {
    //         console.warn("GBXML download failed", e)
    //         return
    //     }
    //
    //     if (rawString === null) {
    //         console.warn("GBXML download not available")
    //         return
    //     }
    //
    //     const blob = new Blob([rawString]);
    //     downloadBlob(filename.value, "xml", blob)
    // }

    async function triggerEvexDownload() {
        const cad = evexTargetCAD.value
        const onlyEnergeticRelevantParts = onlyEnergeticRelevantData.value

        console.log("Exporting Evex for", cad, onlyEnergeticRelevantParts)

        let rawString: Optional<string>
        try {
            const resultDL = downloadEvex()
            if (resultDL === false) {
                const resultReDL = await redownloadEvex()
                rawString = resultReDL?.data.exportListingAsEvex as (undefined | null | string) ?? null
            } else {
                rawString = (await resultDL).exportListingAsEvex
            }
        } catch (e) {
            console.warn("Evex download failed", e)
            return
        }

        if (rawString === null) {
            console.warn("Evex download not available")
            return
        }

        const base64String = rawString
        const binaryString = atob(base64String)

        const length = binaryString.length;
        const bytes = new Uint8Array(length);
        for (let i = 0; i < length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        const blob = new Blob([bytes], {type: "application/zip"});
        downloadBlob(filename.value, "evex", blob)
    }

    async function triggerImagesDownload() {
        const {mutate} = useDListingImagesDownloadMutation()
        const result = await mutate({
            listingId: props.listingId,
        })
        const url = result?.data?.generateListingImageZipArchiveUrl
        if (url) {
            const filename = url.substring(url.lastIndexOf('/') + 1)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', filename)
            document.body.appendChild(link)
            link.click()
            setTimeout(() => {
                document.body.removeChild(link)
                window.URL.revokeObjectURL(url)
            }, 100)
        } else {
            console.warn('Image download not available')
        }
    }

    const listing = inject(LListingInjection)!
    const {
        listingTitle: listingTitleRaw,
        extId,
    } = useListingListFields(listing)

    const listingTitle = computed<Optional<string>>(() => listingTitleRaw.value ?? extId.value)

    const providerName = computed<Optional<string>>(() => {
        switch (exportType.value) {
            case "IFC":
                return t(`enums.ifcExportContextTargetCAD.${ifcTargetCAD.value}`)
            case "PDF":
                return null
            // case "GBXML":
            //     return t(`enums.gbXMLExportType.${gbXMLTargetCAD.value}`)
            case "EVEX":
                return t(`enums.evexExportType.${evexTargetCAD.value}`)
            case "IMAGES":
                return null
            default:
                return null
        }
    })

    const ifcProjectName = computed<string>(() => {
        const title = listingTitle.value

        const parts = [
            title,
            addressDisplayNameOf(props.address),
            props.listingId,
        ].filter(p => p !== null)
        return parts.join(" - ")
    })

    const filename = computed<string>(() => {
        const title = listingTitle.value
        const provider = providerName.value
        const energeticState = onlyEnergeticRelevantData.value ? t('components.listing.buildingModel.exportDialog.onlyEnergeticRelevantPartsFilenameToken') : null

        const parts = [
            title,
            addressDisplayNameOf(props.address),
            props.listingId,
            provider,
            energeticState,
        ].filter(p => p !== null)
        return parts.join(" - ")
    })
</script>

<style scoped>
</style>