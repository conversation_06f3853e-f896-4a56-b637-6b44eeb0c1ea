<template>
    <v-dialog :offset="27"
              location="bottom center"
              location-strategy="connected"
              max-width="350"
              origin="auto">
        <template #activator="{ props: menuProps }">
            <d-btn :icon="hideButtonText ? mdiEye : undefined"
                   :prepend-icon="hideButtonText ? undefined : mdiEye"
                   :text="hideButtonText ? undefined : t('components.listing.buildingModel.visibilityMenu.button')"
                   type="default"
                   v-bind="menuProps"
                   variant="text"/>
        </template>

        <d-card is-in-dialog>
            <d-list keep-y-padding>
                <template v-for="(item, index) in listItems"
                          :key="item.id">
                    <template v-if="item.type === 'SECTION_TITLE'">
                        <d-list-item :class="{'mt-6': index > 0}"
                                     :subtitle="item.title"
                                     class="mb-1"
                                     lines="one"
                                     style="min-height: 0;"/>
                        <d-divider/>
                    </template>

                    <d-list-item v-else-if="item.type === 'BUTTON'"
                                 :disabled="!isButtonEnabled(item)"
                                 :prepend-icon="item.icon"
                                 :title="item.title"
                                 lines="one"
                                 multiline
                                 no-icon-min-width
                                 @click.stop.prevent="item.onToggle">
                        <template #append>
                            <d-icon :icon="item.isVisible.value ? mdiEye : mdiEyeOff"
                                    :type="item.isVisible.value ? 'success' : 'error'"/>
                        </template>
                    </d-list-item>
                </template>
            </d-list>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import {mdiArrowExpand, mdiArrowExpandHorizontal, mdiCompass, mdiEye, mdiEyeOff, mdiPageLayoutBody, mdiPoundBox} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {VuetifyIconValue} from "@/adapter/vuetify/VuetifyIconValue";
    import {computed, Ref} from "vue";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {WallTypeIcon, WallTypeValues} from "@/model/building/WallType";
    import {BuildingComponentCADTypeToIcon} from "@/components/listing/building/renderer/BuildingComponent";

    const props = defineProps<{
        renderer: BuildingRenderer
        hideButtonText: boolean
    }>()

    const {t} = useI18n()

    type ListItem = ListItemSectionTitle | ListItemButton
    type ListItemSectionTitle = {
        readonly id: string
        readonly type: "SECTION_TITLE"
        readonly title: string
    }
    type ListItemButton = {
        readonly id: string
        readonly type: "BUTTON"
        readonly title: string
        readonly icon: VuetifyIconValue
        readonly isVisible: Readonly<Ref<boolean>>
        readonly isEnabled?: Readonly<Ref<boolean>>
        readonly onToggle: () => void
    }

    function isButtonEnabled(item: ListItemButton): boolean {
        return item.isEnabled ? item.isEnabled.value : true
    }

    function wallTypeListItems(): ListItem[] {
        const listItems: ListItem[] = []

        for (const wallType of WallTypeValues) {
            listItems.push({
                id: `wallType.${wallType}`,
                type: "BUTTON",
                title: t(`enums.wallType.${wallType}`, {n: 2}),
                icon: WallTypeIcon[wallType],
                isVisible: computed<boolean>(() => props.renderer.visibleWallTypes.value.has(wallType)),
                onToggle: () => {
                    const rend = props.renderer
                    const wallTypes = rend.visibleWallTypes.value

                    if (wallTypes.has(wallType)) {
                        rend.visibleWallTypes.value = new Set([...wallTypes].filter(wt => wt !== wallType))
                    } else {
                        rend.visibleWallTypes.value = new Set([...wallTypes, wallType])
                    }
                }
            })
        }

        return listItems
    }

    const listItems = computed<readonly ListItem[]>(() => {
        const items: ListItem[] = [
            {
                id: "generic",
                type: "SECTION_TITLE",
                title: t("components.listing.buildingModel.visibilityMenu.items.generic")
            },
            {
                id: "roomTexts",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.roomTexts"),
                icon: BuildingComponentCADTypeToIcon["ROOM"],
                isVisible: computed<boolean>(() => props.renderer.showRoomTexts.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showRoomTexts.value = !rend.showRoomTexts.value
                }
            },
            {
                id: "compass",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.compass"),
                icon: mdiCompass,
                isVisible: computed<boolean>(() => props.renderer.showCompass.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showCompass.value = !rend.showCompass.value
                }
            },
            {
                id: "pointsOfInterest",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.pointsOfInterest"),
                icon: BuildingComponentCADTypeToIcon["POINT_OF_INTEREST"],
                isVisible: computed<boolean>(() => props.renderer.showPointsOfInterest.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showPointsOfInterest.value = !rend.showPointsOfInterest.value
                }
            },
            {
                id: "furniture",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.furniture"),
                icon: BuildingComponentCADTypeToIcon["FURNITURE"],
                isVisible: computed<boolean>(() => props.renderer.showFurniture.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showFurniture.value = !rend.showFurniture.value
                }
            },
            {
                id: "walls",
                type: "SECTION_TITLE",
                title: t("components.listing.buildingModel.visibilityMenu.items.walls")
            },
            ...wallTypeListItems(),
            {
                id: "wallWidths",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.wallWidths"),
                icon: mdiArrowExpandHorizontal,
                isVisible: computed<boolean>(() => props.renderer.showWallWidths.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showWallWidths.value = !rend.showWallWidths.value
                }
            },
            {
                id: "wallThicknesses",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.wallThicknesses"),
                icon: mdiArrowExpand,
                isVisible: computed<boolean>(() => props.renderer.showWallThicknesses.value),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showWallThicknesses.value = !rend.showWallThicknesses.value
                }
            },
            {
                id: "3d",
                type: "SECTION_TITLE",
                title: "3D"
            },
            {
                id: "roofAreas",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.roofAreas"),
                icon: BuildingComponentCADTypeToIcon["ROOF_AREA"],
                isVisible: computed<boolean>(() => props.renderer.showRoofAreas.value),
                isEnabled: computed<boolean>(() => props.renderer.renderType === '3D'),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showRoofAreas.value = !rend.showRoofAreas.value
                }
            },
            {
                id: "slabs",
                type: "BUTTON",
                title: t("components.listing.buildingModel.visibilityMenu.items.slabs"),
                icon: mdiPageLayoutBody,
                isVisible: computed<boolean>(() => props.renderer.showSlabs.value),
                isEnabled: computed<boolean>(() => props.renderer.renderType === '3D'),
                onToggle: () => {
                    const rend = props.renderer
                    rend.showSlabs.value = !rend.showSlabs.value
                }
            },
        ]

        const floors = props.renderer.building.value.floors
        if (floors.length > 1) {
            items.push({
                id: "outlines",
                type: "SECTION_TITLE",
                title: t("components.listing.buildingModel.visibilityMenu.items.outlines")
            })

            for (const floor of floors.toReversed()) {
                items.push({
                    id: `outline.${floor.id}`,
                    type: "BUTTON",
                    title: t(`listing.building.cad.floorLevelInfo.type.long.${floor.levelInfo.levelType}`, {
                        n: floor.levelInfo.number
                    }),
                    icon: BuildingComponentCADTypeToIcon["FLOOR"],
                    isVisible: computed<boolean>(() => props.renderer.semiVisibleFloorLevels.value.has(floor.level)),
                    isEnabled: computed(() => props.renderer.renderType === '2D'),
                    onToggle: () => {
                        const rend = props.renderer
                        if (rend.semiVisibleFloorLevels.value.has(floor.level)) {
                            rend.semiVisibleFloorLevels.value = new Set([...rend.semiVisibleFloorLevels.value].filter(level => level !== floor.level))
                        } else {
                            rend.semiVisibleFloorLevels.value = new Set([...rend.semiVisibleFloorLevels.value, floor.level])
                        }
                    }
                })
            }
        }

        items.push({
            id: "evebi",
            type: "SECTION_TITLE",
            title: "Evebi"
        })

        items.push({
            id: "displayIds",
            type: "BUTTON",
            title: t("components.listing.buildingModel.visibilityMenu.items.displayIds"),
            icon: mdiPoundBox,
            isVisible: computed<boolean>(() => props.renderer.showDisplayIds.value),
            isEnabled: computed<boolean>(() => props.renderer.isEvebiModeEnabled.value),
            onToggle: () => {
                const rend = props.renderer
                rend.showDisplayIds.value = !rend.showDisplayIds.value
            }
        })

        return items
    })
</script>

<style scoped>
</style>