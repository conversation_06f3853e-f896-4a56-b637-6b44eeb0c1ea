<template>
    <div class="buttonGroup rounded-pill">
        <d-tooltip location="bottom"
                   offset="16"
                   type="default">
            <template #activator="{props: tooltipProps, isActive: isTooltipActive}">
                <d-badge :model-value="isTooltipActive"
                         location="bottom"
                         no-pointer
                         type="warning">
                    <template #badge>
                        <kbd class="text-uppercase">{{ useKeyName('Meta', t) }}</kbd><span class="text-caption mx-1">+</span><kbd class="text-uppercase">{{ HistoryManager.HOTKEY_UNDO }}</kbd>
                    </template>

                    <d-btn :disabled="!renderer.historyManager.canUndo.value"
                           :icon="mdiUndo"
                           :loading="renderer.isLoading.value"
                           type="default"
                           v-bind="tooltipProps"
                           variant="text"
                           @click="renderer.historyManager.undo()"/>
                </d-badge>
            </template>

            {{ t('listing.building.cad.history.undo') }}
        </d-tooltip>

        <d-tooltip location="bottom"
                   offset="16"
                   type="default">
            <template #activator="{props: tooltipProps, isActive: isTooltipActive}">
                <d-badge :model-value="isTooltipActive"
                         location="bottom"
                         no-pointer
                         type="warning">
                    <template #badge>
                        <kbd class="text-uppercase">{{ useKeyName('Meta', t) }}</kbd><span class="text-caption mx-1">+</span><kbd class="text-uppercase">{{ HistoryManager.HOTKEY_REDO }}</kbd>
                    </template>

                    <d-btn :disabled="!renderer.historyManager.canRedo.value"
                           :icon="mdiRedo"
                           :loading="renderer.isLoading.value"
                           type="default"
                           v-bind="tooltipProps"
                           variant="text"
                           @click="renderer.historyManager.redo()"/>
                </d-badge>
            </template>

            {{ t('listing.building.cad.history.redo') }}
        </d-tooltip>
    </div>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import {HistoryManager} from "@/utility/history-manager";
    import DTooltip from "@/adapter/vuetify/theme/components/d-tooltip.vue";
    import DBadge from "@/adapter/vuetify/theme/components/d-badge.vue";
    import {useKeyName} from "@/utility/key-name";
    import {mdiRedo, mdiUndo} from "@mdi/js";

    defineProps<{
        renderer: BuildingRenderer
    }>()

    const {t} = useI18n()
</script>

<style scoped>
    .buttonGroup {
        background-color: rgb(var(--v-theme-background));
        text-wrap: nowrap;
    }
</style>