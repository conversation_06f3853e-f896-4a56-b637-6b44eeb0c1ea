import {computed, Ref} from "vue";
import {ShapeRepresentation, Wall} from "@/adapter/graphql/generated/graphql";
import {useParentChildMeshes} from "@/components/listing/building/building";
import {BufferGeometry, MathUtils, Matrix4, Quaternion, Shape, ShapeGeometry, Vector2, Vector3} from "three";
import {tObjectToVertices2D, tObjectToVertices3D, tReduceCollinearPoints2D} from "@/adapter/three/three-utility";
import * as concaveman from "concaveman";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {clipperPathsToVectors2, clipperPolygonToPaths} from "@/adapter/clipper/clipper-utils";

// noinspection ParameterNamingConventionJS
export function useFloorCalculation(
    renderer: BuildingRenderer,
    parentId: Readonly<Ref<string>>,
    parentShapeRepresentation: Readonly<Ref<ShapeRepresentation>>,
    walls: Readonly<Ref<readonly Wall[]>>,
    applyParentTransformationToChildren: boolean,
) {
    const wallShapeRepresentations = computed<readonly ShapeRepresentation[]>(() => walls.value.map(wall => wall.shapeRepresentation))
    const {
        parentTransformation: floorTransformation,
        childMeshes,
        destroyParentChildMeshes,
    } = useParentChildMeshes(
        parentId,
        parentShapeRepresentation,
        wallShapeRepresentations,
        true,
        true,
        applyParentTransformationToChildren,
        renderer,
        false,
        true,
    )

    const wallVertices2D = computed<readonly Vector2[]>(() => childMeshes.value.flatMap(mesh => tObjectToVertices2D(mesh)))
    const wallVertices3D = computed<readonly Vector3[]>(() => childMeshes.value.flatMap(mesh => tObjectToVertices3D(mesh)))

    const floorPositionY = computed<number>(() => {
        const floorPosition = new Vector3()
        floorTransformation.value.decompose(floorPosition, new Quaternion(), new Vector3())
        return floorPosition.y
    })

    const convaveHullPoints2D = computed<readonly Vector2[]>(() => {
        const vertices = wallVertices2D.value
        if (vertices.length <= 0) {
            return []
        }
        const newVertices = concaveman.default(vertices.map(point => [point.x, point.y]), 2, 0.1).map(point => new Vector2(point[0], point[1]))

        //this will optimize the path via clipper
        const path = clipperPolygonToPaths(newVertices, [])
        const optimizedVertices = clipperPathsToVectors2(path)

        if (optimizedVertices.length !== 1) {
            console.warn("Floor shape has more than one path")
            return tReduceCollinearPoints2D(newVertices, MathUtils.degToRad(0.1)) //0.1° Toleranz, damit Rundungen ausreichend rund sind, aber genug Punkte verschwinden
        }
        return optimizedVertices[0]
    })
    const convaveHullPoints3D = computed<readonly Vector3[]>(() => {
        const y = floorPositionY.value
        return convaveHullPoints2D.value.map(point => new Vector3(point.x, y, point.y))
    })

    // noinspection LocalVariableNamingConventionJS
    const floorShapeGeometry_000 = computed<BufferGeometry>(oldFloorShapeGeometry => {
        oldFloorShapeGeometry?.dispose()

        const points = convaveHullPoints2D.value
        if (points.length <= 0) {
            return new BufferGeometry()
        }

        const shape = new Shape([...points]);
        return new ShapeGeometry(shape);
    })
    const floorShapeTransformation = computed<Matrix4>(() => {
        const rotationMatrix = new Matrix4().makeRotationX(Math.PI / 2)
        const translationMatrix = new Matrix4().makeTranslation(0, floorPositionY.value, 0)
        return translationMatrix.multiply(rotationMatrix)
    })

    return {
        wallVertices3D,
        convaveHullPoints3D,
        floorShapeGeometry: floorShapeGeometry_000,
        floorShapeTransformation,
        convaveHullPoints2D,
        destroyFloorCalculation: () => {
            floorShapeGeometry_000.value.dispose()
            destroyParentChildMeshes()
        }
    }
}