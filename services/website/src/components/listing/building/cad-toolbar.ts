import {VuetifyIconValue} from "@/adapter/vuetify/VuetifyIconValue";
import {Ref} from "vue";
import {Optional} from "@/model/Optional";

export type CadToolbarItem =
    | CadToolbarButton
    | CadToolbarDivider

export type CadToolbarItemTemplate = {
    readonly id: string
    readonly isVisible: Readonly<Ref<boolean>>
}

export type CadToolbarDivider = CadToolbarItemTemplate & {
    readonly type: "DIVIDER"
}

export type CadToolbarButton = CadToolbarItemTemplate & {
    readonly type: "BUTTON"
    readonly hotkey: string
    readonly isActive: Readonly<Ref<boolean>>
    readonly isEnabled: Readonly<Ref<boolean>>
    readonly iconColor?: Readonly<Ref<Optional<string>>>
    readonly icon: VuetifyIconValue
    readonly subIcon?: VuetifyIconValue
    readonly tooltip: string
    readonly nestedBadgeContent?: string
    readonly canBeTriggeredWhileActive: boolean,
    readonly onClick: () => void
    readonly menu?: {
        readonly isEnabled: Readonly<Ref<boolean>>
        readonly isActive: Readonly<Ref<boolean>>
        readonly onClick: () => void
    }
    readonly subMenu?: {
        readonly items: CadToolbarItem[]
    }
}