<template>
    <div class="selectionWrapper">
        <d-card :loading="renderer.isLoading.value"
                class="selectionCard ma-4"
                elevation="2"
                rounded="xl"
                variant="elevated">
            <template #title>
                <v-layout class="justify-space-between align-center">
                    <div>
                        <v-slide-x-transition>
                            <d-icon v-if="selectedBuildingComponentType"
                                    :icon="BuildingComponentCADTypeToIcon[selectedBuildingComponentType]"
                                    class="me-1 pb-1"
                                    size="28px"
                                    type="warning"/>
                        </v-slide-x-transition>

                        <v-slide-x-transition>
                            <div v-if="selectedBuildingComponentTypesString"
                                 class="d-inline-block">{{ selectedBuildingComponentTypesString }}
                            </div>
                        </v-slide-x-transition>
                    </div>

                    <d-btn :disabled="renderer.isLoading.value"
                           :icon="mdiClose"
                           type="default"
                           variant="text"
                           @click="onClose"/>
                </v-layout>
            </template>

            <template v-if="IS_DEVELOPMENT || (singleSelectedFloor && floorSubtitleString) || singleSelectedWall || (renderer.isEvebiModeEnabled.value && (displayIdsString || roomNumbersString))"
                      #subtitle>
                <ul v-if="IS_DEVELOPMENT"
                    class="text-caption">
                    <li v-for="selectedItem in multiSelection"
                        :key="selectedItem.type === 'WALL_ROOF_POINT' ? (selectedItem as WallRoofPoint).id : selectedItem.component.value.id">{{ selectedItem.type === 'WALL_ROOF_POINT' ? (selectedItem as WallRoofPoint).id : selectedItem.component.value.id }}
                    </li>
                </ul>

                <v-slide-y-transition>
                    <p v-if="singleSelectedFloor && floorSubtitleString">{{ floorSubtitleString }}</p>
                </v-slide-y-transition>

                <v-slide-y-transition>
                    <v-layout v-if="singleSelectedWall"
                              class="align-center">
                        <d-icon :icon="WallTypeIcon[wallTypeOfWall(singleSelectedWall)]"
                                class="me-1"
                                size="14"
                                type="default"/>
                        {{ typeNameOfWall(singleSelectedWall, t) }}
                    </v-layout>
                </v-slide-y-transition>

                <!--                <p v-if="singleSelectedWall || singleSelection?.type === 'WALL_OPENING_DOOR' || singleSelection?.type === 'WALL_OPENING_WINDOW' || singleSelection?.type === 'WALL_OPENING_OPENING'">-->
                <!--                    Azimuth: {{ Math.round(azimuthInDegreeOfWallOrOpening(renderer, (singleSelectedWall ?? singleSelection) as Wall | ConstructionPart)) }}°-->
                <!--                </p>-->
                <!--                <p v-if="singleSelectedWall">-->
                <!--                    Räume-->
                <!--                    <ul>-->
                <!--                        <li v-for="(roomId, index) in singleSelectedWall.roomIds"-->
                <!--                            :key="index">{{ roomId }}-->
                <!--                        </li>-->
                <!--                    </ul>-->
                <!--                </p>-->
                <!--                <p v-if="singleSelection && singleSelection.type === 'ROOM'">-->
                <!--                    Wände-->
                <!--                    <ul>-->
                <!--                        <li v-for="(wallId, index) in (singleSelection.component.value as Room).wallIds"-->
                <!--                            :key="index">{{ wallId }}-->
                <!--                        </li>-->
                <!--                    </ul>-->
                <!--                </p>-->

                <v-slide-y-transition>
                    <p v-if="renderer.isEvebiModeEnabled.value && displayIdsString">{{ t('listing.building.cad.selection.evebiDisplayIds', {n: displayIds.length}) }}: {{ displayIdsString }}</p>
                </v-slide-y-transition>

                <v-slide-y-transition>
                    <p v-if="renderer.isEvebiModeEnabled.value && roomNumbersString">{{ t('listing.building.cad.selection.evebiRoomNumbers', {n: roomNumbers.length}) }}: {{ roomNumbersString }}</p>
                </v-slide-y-transition>
            </template>

            <d-divider/>

            <v-slide-y-transition>
                <d-listing-building-selection-building v-if="renderer.canEdit && singleSelectedBuilding"
                                                       :key="renderer.isEvebiModeEnabled.value + singleSelectedBuilding.id"
                                                       :building="singleSelectedBuilding"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <d-listing-building-selection-floor v-if="renderer.canEdit && singleSelectedFloor"
                                                    :key="renderer.isEvebiModeEnabled.value + singleSelectedFloor.id"
                                                    :floor="singleSelectedFloor"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <d-listing-building-selection-opening v-if="renderer.canEdit && singleSelectedOpening"
                                                      :key="renderer.isEvebiModeEnabled.value + singleSelectedOpening.id"
                                                      :opening="singleSelectedOpening"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <d-listing-building-selection-wall v-if="renderer.canEdit && singleSelectedWall"
                                                   :key="renderer.isEvebiModeEnabled.value + singleSelectedWall.id"
                                                   :wall="singleSelectedWall"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <div v-show="!renderer.isDeleting.value && customUIElement !== null && multiSelection.length > 0 && selectedComponentIds.length > 0 && buildingComponentToSubflowType(multiSelection[0]) !== null && (!renderer.isEvebiModeEnabled.value || singleSelectedWall === null)"
                     class="subflow">
                    <d-listing-subflow-building-component v-model:loading="renderer.isCustomDataSaving.value"
                                                          :component-ids="selectedComponentIds"
                                                          :custom-ui-element="customUIElement"
                                                          :forced-context="isSingleSelectedPoiPhoto ? 'VIEW' : undefined"
                                                          :subflow-type="multiSelection.length > 0 ? buildingComponentToSubflowType(multiSelection[0]) : null"
                                                          embedded
                                                          target="BUILDING"
                                                          @change="onComponentDataChanged"/>
                </div>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <div v-if="!renderer.isDeleting.value && customUIElement !== null && multiSelection.length > 0 && selectedComponentIds.length > 0 && buildingComponentToSubflowType(multiSelection[0]) !== null && (renderer.isEvebiModeEnabled.value && singleSelectedWall !== null)"
                     class="subflow">
                    <d-listing-subflow-building-component v-model:loading="renderer.isCustomDataSaving.value"
                                                          :component-ids="selectedComponentIds"
                                                          :custom-ui-element="customUIElement"
                                                          :subflow-type="multiSelection.length > 0 ? 'WALL_EVEBI' : null"
                                                          embedded
                                                          target="BUILDING"
                                                          @change="onComponentDataChanged"/>
                </div>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <d-listing-building-selection-wall-overwrite-u-values v-if="renderer.canEdit && singleSelectedWall && !renderer.isEvebiModeEnabled.value"
                                                                      :key="renderer.isEvebiModeEnabled.value + singleSelectedWall.id"
                                                                      :wall="singleSelectedWall"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <d-listing-building-selection-opening-overwrite-u-values v-if="renderer.canEdit && singleSelectedOpening && !renderer.isEvebiModeEnabled.value && (singleSelectedOpening.type === 'WINDOW' || singleSelectedOpening.type === 'DOOR')"
                                                                         :key="renderer.isEvebiModeEnabled.value + singleSelectedOpening.id"
                                                                         :opening="singleSelectedOpening"/>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <div v-if="canDeleteConstructionPart">
                    <d-divider/>

                    <v-layout class="justify-center py-2 px-4">
                        <d-confirmation-wrapper show-question
                                                @onconfirm="onDeletion">
                            <template #default="{props: confirmationWrapperProps}">
                                <d-btn :disabled="renderer.isLoading.value"
                                       :loading="renderer.isDeleting.value"
                                       :text="t('listing.building.cad.subflow.deleteButton', { constructionPart: selectedBuildingComponentTypesString })"
                                       type="error"
                                       v-bind="confirmationWrapperProps"/>
                            </template>
                        </d-confirmation-wrapper>
                    </v-layout>
                </div>
            </v-slide-y-transition>

            <d-listing-building-selection-wall-roof-points v-if="renderer.renderType === '3D'"/>
        </d-card>
    </div>
</template>

<script lang="ts"
        setup>
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {mdiClose} from "@mdi/js";
    import {computed, inject, toRaw} from "vue";
    import DListingSubflowBuildingComponent from "@/components/listing/subflow/building-component/d-listing-subflow-building-component.vue";
    import {Optional} from "@/model/Optional";
    import {FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {Building, ConstructionPart, CustomUiElement, Floor, FlowConfigFieldValue, Wall} from "@/adapter/graphql/generated/graphql";
    import {findCustomUIElementById} from "@/components/flow-config/use-flow-config";
    import {useI18n} from "vue-i18n";
    import {DBuildingRendererInjection, isPointOfInterestJustAPhoto, typeNameOfWall} from "@/components/listing/building/building";
    import {IS_DEVELOPMENT} from "@/utility/environment";
    import {BuildingComponentCADType, BuildingComponentCADTypeToIcon, RaycastableBuildingComponent, WallRoofPoint} from "@/components/listing/building/renderer/BuildingComponent";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import DListingBuildingSelectionOpening from "@/components/listing/building/d-listing-building-selection-opening.vue";
    import {AUTH_SERVICE} from "@/service/auth/AuthService";
    import DListingBuildingSelectionWall from "@/components/listing/building/d-listing-building-selection-wall.vue";
    import DListingBuildingSelectionFloor from "@/components/listing/building/d-listing-building-selection-floor.vue";
    import DListingBuildingSelectionBuilding from "@/components/listing/building/d-listing-building-selection-building.vue";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
    import {stringToInt} from "@/utility/converter";
    import DListingBuildingSelectionWallOverwriteUValues from "@/components/listing/building/d-listing-building-selection-wall-overwrite-u-values.vue";
    import DListingBuildingSelectionOpeningOverwriteUValues from "@/components/listing/building/d-listing-building-selection-opening-overwrite-u-values.vue";
    import {WallTypeIcon, wallTypeOfWall} from "@/model/building/WallType";
    import DListingBuildingSelectionWallRoofPoints from "@/components/listing/building/d-listing-building-selection-wall-roof-points.vue";

    const props = defineProps<{
        customUiElementId: string
    }>()

    const {t} = useI18n()

    const renderer = inject(DBuildingRendererInjection)!
    const flowConfig = inject(FCFlowConfigInjection)!

    const customUIElement = computed<Optional<CustomUiElement>>(() => findCustomUIElementById(flowConfig.value, props.customUiElementId))
    const selectedComponentIds = computed<readonly string[]>(() => multiSelection.value
        .map(s => s.type === "WALL_ROOF_POINT" ? null : s.component.value.id)
        .filter(id => id !== null)
    )

    const singleSelection = computed<Optional<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>(() => multiSelection.value.length === 1 ? multiSelection.value[0] : null)
    const multiSelection = renderer.selectionRaycaster.selection

    const displayIds = computed<readonly number[]>(() => multiSelection.value
        .map(s => {
            if (s.type === "WALL_ROOF_POINT") {
                return null
            }

            const component = s.component.value

            return component.displayId === null || component.displayId === undefined
                ? null
                : stringToInt(component.displayId)
        })
        .filter(displayId => displayId !== null)
        .toSorted((a, b) => a - b)
    )
    const displayIdsString = computed<string>(() => displayIds.value.join(", "))

    const roomNumbers = computed<readonly number[]>(() => multiSelection.value
        .map(s => {
            if (s.type === "WALL_ROOF_POINT") {
                return null
            }

            const component = s.component.value

            return component.__typename === "Room" && component.roomNumber !== null && component.roomNumber !== undefined
                ? stringToInt(component.roomNumber)
                : null
        })
        .filter(roomNumber => roomNumber !== null)
        .toSorted((a, b) => a - b)
    )
    const roomNumbersString = computed<string>(() => roomNumbers.value.join(", "))

    const singleSelectedBuilding = computed<Optional<Building>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "BUILDING") {
            return null
        }
        const building = selectedComponent.component.value
        if (building.__typename === "Building") {
            return building
        }
        return null
    })

    const singleSelectedFloor = computed<Optional<Floor>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "FLOOR") {
            return null
        }
        const floor = selectedComponent.component.value
        if (floor.__typename === "Floor") {
            return floor
        }
        return null
    })

    const singleSelectedOpening = computed<Optional<ConstructionPart>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "WALL_OPENING_DOOR" && selectedComponent.type !== "WALL_OPENING_WINDOW" && selectedComponent.type !== "WALL_OPENING_OPENING") {
            return null
        }
        const opening = selectedComponent.component.value
        if (opening.__typename === "ConstructionPart") {
            return opening
        }
        return null
    })

    const singleSelectedWall = computed<Optional<Wall>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "WALL_WITH_HOLES") {
            return null
        }
        const wall = selectedComponent.component.value
        if (wall.__typename === "Wall") {
            return wall
        }
        return null
    })

    const canDeleteConstructionPart = computed<boolean>(() => renderer.canEdit && AUTH_SERVICE.isLoggedIn.value && renderer.canSelectionBeDeleted.value)

    const selectedBuildingComponentType = computed<Optional<BuildingComponentCADType>>(() => {
        const selection = multiSelection.value
        if (selection.length <= 0) {
            return null
        }
        const firstElement = selection[0]
        return buildingComponentToType(firstElement)
    })

    const selectedBuildingComponentTypesString = computed<Optional<string>>(() => {
        const type = selectedBuildingComponentType.value
        if (type === null) {
            return null
        }
        const floor = singleSelectedFloor.value
        if (type === "FLOOR" && floor !== null) {
            return t(`listing.building.cad.floorLevelInfo.type.long.${floor.levelInfo.levelType}`, {
                n: floor.levelInfo.number
            })
        }
        return t(`listing.building.cad.componentTypes.${type}`, multiSelection.value.length)
    })

    const floorSubtitleString = computed<Optional<string>>(() => {
        const type = selectedBuildingComponentType.value
        if (type === null) {
            return null
        }
        if (type === "FLOOR") {
            return t(`listing.building.cad.componentTypes.${type}`, multiSelection.value.length)
        }
        return null
    })

    const isSingleSelectedPoiPhoto = computed<boolean>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return false
        }
        if (selectedComponent.type !== "POINT_OF_INTEREST") {
            return false
        }
        const poi = selectedComponent.component.value
        if (poi.__typename === "BuildingPointOfInterest") {
            return isPointOfInterestJustAPhoto(poi)
        }
        return false
    })

    // noinspection OverlyComplexFunctionJS
    function buildingComponentToSubflowType(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): Optional<string> {
        const isSingleSelectionEnabled = singleSelection.value !== null

        switch (component.type) {
            case "BUILDING":
                return isSingleSelectionEnabled ? "BUILDING" : null
            case "WALL_WITH_HOLES":
                return isSingleSelectionEnabled ? "WALL" : "WALL_GROUP"
            case "WALL_OPENING_WINDOW":
                return isSingleSelectionEnabled ? "WINDOW" : "WINDOW_GROUP"
            case "WALL_OPENING_DOOR":
                return isSingleSelectionEnabled ? "DOOR" : "DOOR_GROUP"
            case "ROOM":
            case "UNRECOGNIZED_ROOM":
                return isSingleSelectionEnabled ? "ROOM" : "ROOM_GROUP"
            case "FLOOR":
                return isSingleSelectionEnabled ? "FLOOR" : null
            case "ROOM_SLAB_FLOOR":
                return isSingleSelectionEnabled ? "ROOM_SLAB" : null
            case "FLOOR_SLAB_FLOOR":
                return isSingleSelectionEnabled ? "FLOOR_SLAB" : null
            case "POINT_OF_INTEREST":
                if (isSingleSelectedPoiPhoto.value) {
                    return "POI_PHOTO"
                }
                return isSingleSelectionEnabled ? "POI" : null
            case "WALL_ROOF_POINT":
                return null
            case "ROOF_AREA":
                return isSingleSelectionEnabled ? "ROOF_AREA" : null
            default:
                return null
        }
    }

    // noinspection OverlyComplexFunctionJS
    function buildingComponentToType(component: RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint): BuildingComponentCADType {
        switch (component.type) {
            case "BUILDING":
                return "BUILDING"
            case "FLOOR":
                return "FLOOR"
            // case "FLOOR_SLAB_FLOOR":
            //     return "FLOOR_SLAB_FLOOR"
            case "FURNITURE":
                return "FURNITURE"
            case "ROOM":
            case "UNRECOGNIZED_ROOM":
                return "ROOM"
            // case "ROOM_SLAB_FLOOR":
            //     return "ROOM_SLAB_FLOOR"
            case "WALL_OPENING_DOOR":
                return "WALL_OPENING_DOOR"
            case "WALL_OPENING_OPENING":
                return "WALL_OPENING_OPENING"
            case "WALL_OPENING_WINDOW":
                return "WALL_OPENING_WINDOW"
            case "WALL_WITH_HOLES":
                return "WALL"
            case "POINT_OF_INTEREST":
                return isSingleSelectedPoiPhoto.value ? "POINT_OF_INTEREST_PHOTO" : "POINT_OF_INTEREST"
            case "WALL_ROOF_POINT":
                return "WALL_ROOF_POINT"
            case "ROOF_AREA":
                return "ROOF_AREA"
            default:
                return "UNKNOWN"
        }
    }

    function onClose() {
        renderer.selectionRaycaster.reset()
    }

    async function onComponentDataChanged(oldData: readonly FlowConfigFieldValue[], newData: readonly FlowConfigFieldValue[]) {
        renderer.saveBuildingStateToHistory() //zu diesem zeitpunkt wurde das gebäude bereits indirekt gespeichert in d-listing-subflow-building-component, was wiederum durch den offline cache das gebäude überschreibt

        for (const component of multiSelection.value) {
            if (component.type === "WALL_ROOF_POINT") {
                continue
            }
            component.component.value.customData = JSON.parse(JSON.stringify(toRaw(newData)))
        }
    }

    async function onDeletion() {
        await renderer.deleteSelection()
    }
</script>

<style scoped>
    .selectionWrapper {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 450px;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
        pointer-events: none;
        z-index: 2;
    }

    @media (max-width: 450px) {
        .selectionWrapper {
            width: 100%;
        }
    }

    .selectionCard {
        margin: 16px;
        max-height: calc(100% - 16px * 2);
        overflow-y: auto;
        pointer-events: auto;
    }

    :deep(.text-h4) {
        line-height: 1.5rem;
    }

    :deep(.v-card-subtitle) {
        padding: 0;
        line-height: 1rem;
    }

    :deep(.v-card-item) {
        padding: 8px 4px 8px 16px;
    }

    :deep(.v-card-subtitle) {
        padding-right: 12px;
    }

    .subflow :deep(.text-h3) {
        font-size: 1.2rem !important;
        margin-top: 4px !important;
        margin-bottom: 4px !important;
    }

    .subflow :deep(.v-table tbody td),
    .subflow :deep(.v-table tbody th) {
        height: auto !important;
        padding-top: 6px;
        padding-bottom: 6px;
    }
</style>