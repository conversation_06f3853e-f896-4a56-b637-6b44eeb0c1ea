import {TLineSegment2D} from "@/adapter/three/TLineSegment2D";
import {ConstructionPart, Wall} from "@/adapter/graphql/generated/graphql";
import {Vector2, Vector3} from "three";
import {BUILDING_EPSILON, calculateWallOrOpeningSnapPointPositionXZ} from "@/components/listing/building/building";
import {Optional} from "@/model/Optional";
import {tAreVectors2Equal, tLineSegment2DIntersectionPoint} from "@/adapter/three/three-utility";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";

export type LineSegment<T extends Wall | ConstructionPart> = TLineSegment2D & {
    readonly wallOrOpening: T
    readonly vertices: readonly Vector3[]
}

export type LineSegmentIntersectionPoint<T extends Wall | ConstructionPart> = {
    readonly intersectionPoint: Vector3
    readonly intersectionPointXZ: Vector2
    readonly lineSegment1: LineSegment<T>
    readonly lineSegment2: LineSegment<T>
}

export function wallOrOpeningToLineSegment<T extends Wall | ConstructionPart>(
    renderer: BuildingRenderer,
    wallOrOpening: T,
    wall: Wall,
    ignoreExteriorOffset: boolean,
): LineSegment<T> {
    const start = calculateWallOrOpeningSnapPointPositionXZ(renderer, wallOrOpening, "LEFT", wall, ignoreExteriorOffset)
    const end = calculateWallOrOpeningSnapPointPositionXZ(renderer, wallOrOpening, "RIGHT", wall, ignoreExteriorOffset)

    const startPoint = new Vector3(start.x, 0, start.y);
    const endPoint = new Vector3(end.x, 0, end.y);

    return {
        wallOrOpening,
        start,
        end,
        vertices: [
            startPoint,
            endPoint,
        ],
    }
}

export function wallOrOpeningToLineSegments<T extends Wall | ConstructionPart>(
    renderer: BuildingRenderer,
    wallOrOpening: T,
    wall: Wall,
    ignoreExteriorOffset: boolean,
): LineSegment<T>[] {
    const lineSegment = wallOrOpeningToLineSegment(renderer, wallOrOpening, wall, ignoreExteriorOffset)

    switch (wallOrOpening.shapeRepresentation.shape.__typename) {
        case "Box":
        case "Polygon":
            return [lineSegment]

        case "Ring":
            //TODO: streng genommen müssen hier diverse linien erzeugen, da alle kanten auch andere wände schneiden könnten (derzeit aber durchs snapping auch nicht möglich)
            //Beim Ring erzeugen wir keine "Luftlinie" zwischen dem Start- und Endpunkt, da sonst Wände, die im Inneren liegen, auch geschnitten und damit geteilt werden würden.
            //Stattdessen erzeugen wir zwei Linien, wo Start- und Endpunkte jeweils die gleiche Koordinate beschreiben. Das führt dazu, dass der Start- und Endpunkt der runden Wand trotzdem
            //andere Wände schneiden kann, das Innenleben aber ignoriert wird.
            return [
                {
                    ...lineSegment,
                    vertices: [
                        lineSegment.vertices[0],
                        lineSegment.vertices[0].clone(),
                    ],
                },
                {
                    ...lineSegment,
                    vertices: [
                        lineSegment.vertices[1],
                        lineSegment.vertices[1].clone(),
                    ],
                }
            ]

        default:
            throw new Error(`Unsupported shape type: ${wallOrOpening.shapeRepresentation.shape.__typename}`)
    }
}

export function intersectionPointXZOfLineSegments<T extends Wall | ConstructionPart>(lineSegment1: LineSegment<T>, lineSegment2: LineSegment<T>, ignoreStartAndEndConnections: boolean): Optional<LineSegmentIntersectionPoint<T>> {
    if (lineSegment1.wallOrOpening.id === lineSegment2.wallOrOpening.id) {
        return null
    }
    if (ignoreStartAndEndConnections) {
        if (tAreVectors2Equal(lineSegment1.start, lineSegment2.start, BUILDING_EPSILON)) {
            return null
        }
        if (tAreVectors2Equal(lineSegment1.end, lineSegment2.end, BUILDING_EPSILON)) {
            return null
        }
        if (tAreVectors2Equal(lineSegment1.start, lineSegment2.end, BUILDING_EPSILON)) {
            return null
        }
        if (tAreVectors2Equal(lineSegment1.end, lineSegment2.start, BUILDING_EPSILON)) {
            return null
        }
    }
    const intersectionPointXZ = tLineSegment2DIntersectionPoint(lineSegment1, lineSegment2, BUILDING_EPSILON)
    if (intersectionPointXZ === null) {
        return null
    }
    return {
        intersectionPoint: new Vector3(intersectionPointXZ.x, 0, intersectionPointXZ.y),
        intersectionPointXZ,
        lineSegment1,
        lineSegment2,
    }
}

export function calculateWallLineIntersectionPoints(wallLineSegments: readonly LineSegment<Wall>[]): LineSegmentIntersectionPoint<Wall>[] {
    const intersectionPoints: LineSegmentIntersectionPoint<Wall>[] = []

    for (const lineSegment1 of wallLineSegments) {
        for (const lineSegment2 of wallLineSegments) {
            if (lineSegment1.wallOrOpening.id === lineSegment2.wallOrOpening.id) {
                continue
            }

            if (lineSegment1.wallOrOpening.shapeRepresentation.shape.__typename !== "Box" && lineSegment2.wallOrOpening.shapeRepresentation.shape.__typename !== "Box") { //TODO: <<<<<<<<<< Mehr als nur boxen erlauben
                //keine box, die geschnitten wird, also skip
                continue
            }
            const intersectionPoint = intersectionPointXZOfLineSegments(lineSegment1, lineSegment2, true)
            if (intersectionPoint === null) {
                continue
            }

            //wenn nicht in der mitte geschnitten, dann skip
            const newLineSegment1 =
                tAreVectors2Equal(lineSegment1.start, intersectionPoint.intersectionPointXZ, BUILDING_EPSILON) ||
                tAreVectors2Equal(lineSegment1.end, intersectionPoint.intersectionPointXZ, BUILDING_EPSILON)
                    ? undefined
                    : lineSegment1

            //wenn nicht in der mitte geschnitten, dann skip
            const newLineSegment2 =
                tAreVectors2Equal(lineSegment2.start, intersectionPoint.intersectionPointXZ, BUILDING_EPSILON) ||
                tAreVectors2Equal(lineSegment2.end, intersectionPoint.intersectionPointXZ, BUILDING_EPSILON)
                    ? undefined
                    : lineSegment2

            if (newLineSegment1 === undefined && newLineSegment2 === undefined) {
                continue
            }

            intersectionPoints.push(intersectionPoint)
        }
    }
    return intersectionPoints
}