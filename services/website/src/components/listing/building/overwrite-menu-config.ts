import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";
import {computed, Ref} from "vue";
import {VuetifyIconValue} from "@/adapter/vuetify/VuetifyIconValue";
import {ConstructionPart, Wall} from "@/adapter/graphql/generated/graphql";
import {mdiBorderAll, mdiFloorPlan, mdiWall} from "@mdi/js";
import {WallType, WallTypeIcon, wallTypeOfWall, WallTypeValues} from "@/model/building/WallType";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {WallOpeningType, WallOpeningTypeToIcon} from "@/model/building/WallOpeningType";
import {Optional} from "@/model/Optional";
import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";

export type OverwriteMenuConfigItem<T extends TraversalBuildingComponent> = {
    readonly name: string
    readonly icon: VuetifyIconValue
    readonly targets: readonly T[]
}

export type OverwriteMenuConfig<T extends TraversalBuildingComponent> = {
    readonly buttonText: string
    readonly description: string
    readonly processor: (targets: readonly T[]) => Promise<void>
    readonly displayValue: Readonly<Ref<string>>
    readonly unit: ListingFieldUnit
    readonly source: Readonly<Ref<T>>
    readonly items: Readonly<Ref<readonly (readonly OverwriteMenuConfigItem<T>[])[]>>
}

export function generateWallsOfTypeItems(walls: readonly Wall[], nameSupplier: (wallType: WallType) => string, wallTypes: readonly WallType[]): OverwriteMenuConfigItem<Wall>[] {
    return wallTypes.map(wallType => {
        const wallsOfType = walls.filter(w => wallTypeOfWall(w) === wallType)
        return {
            name: nameSupplier(wallType),
            icon: WallTypeIcon[wallType],
            targets: wallsOfType
        }
    })
}

export function useItemsForWallOverwriteMenuConfig(
    renderer: BuildingRenderer,
    wall: Readonly<Ref<Wall>>,
    t: (key: string) => string
): Readonly<Ref<readonly (readonly OverwriteMenuConfigItem<Wall>[])[]>> {
    return computed(() => {
        const floor = renderer.traversableBuilding.floorOf(wall.value)
        const walls = renderer.building.value.floors.flatMap(f => f.walls)
        const wallsOnFloor = floor?.walls ?? []

        return [
            [
                ...generateWallsOfTypeItems(wallsOnFloor, wallType => t(`listing.building.cad.selection.wall.overwriteMenu.menuItems.allOfTypeOnFloor.${wallType}`), WallTypeValues),
                {
                    name: t('listing.building.cad.selection.wall.overwriteMenu.menuItems.allOnFloor'),
                    icon: mdiBorderAll,
                    targets: wallsOnFloor
                } satisfies OverwriteMenuConfigItem<Wall>,
            ],
            [
                ...generateWallsOfTypeItems(walls, wallType => t(`listing.building.cad.selection.wall.overwriteMenu.menuItems.allOfType.${wallType}`), WallTypeValues),
                {
                    name: t('listing.building.cad.selection.wall.overwriteMenu.menuItems.all'),
                    icon: mdiBorderAll,
                    targets: walls
                } satisfies OverwriteMenuConfigItem<Wall>
            ]
        ]
    })
}

export function useItemsForOpeningOverwriteMenuConfig(
    renderer: BuildingRenderer,
    opening: Readonly<Ref<ConstructionPart>>,
    t: (key: string) => string
): Readonly<Ref<readonly (readonly OverwriteMenuConfigItem<ConstructionPart>[])[]>> {
    return computed(() => {
        const floor = renderer.traversableBuilding.floorOf(opening.value)
        const wall = renderer.traversableBuilding.parentOf(opening.value) as Optional<Wall>

        const openingType = opening.value.type as WallOpeningType

        const wallOpeningsOfType = wall?.openings.filter(o => o.type === openingType) ?? []
        const floorOpeningsOfType = floor?.walls.flatMap(w => w.openings.filter(o => o.type === openingType)) ?? []
        const openingsOfType = renderer.building.value.floors.flatMap(f => f.walls.flatMap(w => w.openings.filter(o => o.type === openingType)))

        return [
            [
                {
                    name: t(`listing.building.cad.selection.opening.overwriteMenu.menuItems.allOfTypeOnWall.${openingType}`),
                    icon: mdiWall,
                    targets: wallOpeningsOfType
                } satisfies OverwriteMenuConfigItem<ConstructionPart>,
                {
                    name: t(`listing.building.cad.selection.opening.overwriteMenu.menuItems.allOfTypeOnFloor.${openingType}`),
                    icon: mdiFloorPlan,
                    targets: floorOpeningsOfType
                } satisfies OverwriteMenuConfigItem<ConstructionPart>,
                {
                    name: t(`listing.building.cad.selection.opening.overwriteMenu.menuItems.allOfType.${openingType}`),
                    icon: WallOpeningTypeToIcon[openingType],
                    targets: openingsOfType
                } satisfies OverwriteMenuConfigItem<ConstructionPart>
            ]
        ]
    })
}