<template>
    <d-loading v-if="!listing?.hasBuilding && !listing?.hasBuildingScan"/>

    <div v-else-if="isBuildingCreationLoading || createBuildingLoading"
         class="h-100 w-100">
        <d-loading :text="t(listing.building ? 'listing.building.creationProcess.optimizeBuilding' : 'listing.building.creationProcess.creatingBuilding')"/>
    </div>

    <d-listing-building-cad v-else-if="listing.building"
                            :building="listing.building"
                            :custom-ui-element-id="customUiElementId"
                            :hide-controls="hideControls"
                            :initialize-with3d="initializeWith3d"
                            :is-embedded="isEmbedded"
                            :show-back-button="showBackButton"
                            :show-full-screen-button="showFullScreenButton"
                            :show-signature="showSignature"/>

    <div v-else-if="buildingScanLoading"
         class="h-100 w-100">
        <d-loading :text="t('listing.building.creationProcess.loadingBuildingScan')"/>
    </div>

    <v-container v-else-if="showBuildingScanNotCompletedWarning"
                 class="h-100 w-100">
        <v-row align-content="center"
               class="h-100"
               justify="center">
            <v-col cols="auto">
                <d-alert :text="t('listing.building.creationProcess.buildingScanNotCompleted')"
                         type="error"/>
            </v-col>
        </v-row>
    </v-container>

    <v-container v-else-if="buildingScanQueryError || showBuildingScanNotLoadableWarning"
                 class="h-100 w-100">
        <v-row align-content="center"
               class="h-100"
               justify="center">
            <v-col cols="auto">
                <d-alert :text="t('listing.building.creationProcess.buildingScanNotFound')"
                         type="error"/>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {Building, BuildingInput, DListingBuildingLoadBuildingScanQueryVariables, DListingBuildingQueryVariables, Listing, ListingBuildingScan, useDListingBuildingCreateMutation, useDListingBuildingLoadBuildingScanLazyQuery, useDListingBuildingQuery} from "@/adapter/graphql/generated/graphql";
    import {computed, inject, provide, shallowRef, toRef, watch} from "vue";
    import {LArrayIndexInjection, LListingContextInjection, LListingEnvironmentInjection, LListingIdInjection, LSuggestionsInjection} from "@/components/listing/ListingInjectionKeys";
    import {Optional} from "@/model/Optional";
    import DListingBuildingCad from "@/components/listing/building/d-listing-building-cad.vue";
    import DLoading from "@/components/fragment/d-loading.vue";
    import {useI18n} from "vue-i18n";
    import {ListingContext} from "@/model/listing/ListingContext";
    import {createEmptyFlowData} from "@/model/listing/FlowData";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
    import {BuildingCreationPipeline} from "@/components/listing/building-creation/pipeline/BuildingCreationPipeline";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {buildingInputToBuilding} from "@/adapter/graphql/mapper/buildinginput-to-building-mapper";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import {BuildingRendererEmpty} from "@/components/listing/building/renderer/BuildingRendererEmpty";
    import {createMutableBuildingFrom} from "@/components/listing/building/building";
    import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
    import {reportMessageToBff} from "@/adapter/graphql/apollo-links/MonitoringReporter";

    const props = withDefaults(defineProps<{
        context: ListingContext
        customUiElementId: string
        isEmbedded?: boolean
        hideControls?: boolean
        showFullScreenButton?: boolean
        showBackButton?: boolean
        showSignature?: boolean
        initializeWith3d?: boolean
    }>(), {
        isEmbedded: false,
        hideControls: false,
        showFullScreenButton: false,
        showBackButton: false,
        showSignature: true,
        initializeWith3d: false,
    })

    provide(LListingContextInjection, toRef(() => props.context))

    provide(LArrayIndexInjection, toRef(null))

    const suggestions = createEmptyFlowData()
    provide(LSuggestionsInjection, toRef(suggestions))

    const env = computed<ListingEnvironment>(() => ({
        context: props.context,
        layout: {
            fill: false,
            position: "DEFAULT"
        }
    }))
    provide(LListingEnvironmentInjection, env)

    const listingId = inject(LListingIdInjection)!

    const showBuildingScanNotLoadableWarning = shallowRef<boolean>(false)
    const showBuildingScanNotCompletedWarning = shallowRef<boolean>(false)

    const {t} = useI18n()

    const checkQueryVariables = computed<DListingBuildingQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        result: listingBuildingQueryResult,
        onError: onListingBuildingQueryError,
    } = useDListingBuildingQuery(checkQueryVariables)

    const listing = computed<Optional<Listing>>(() => listingBuildingQueryResult?.value?.listing as Listing ?? null)

    onListingBuildingQueryError(error => {
        console.warn("Error while loading building.", error)
        reportMessageToBff("Building", `Error while loading building for listing ${listingId.value}.`, JSON.stringify(error))
    })

    const buildingScanQueryVariables = computed<DListingBuildingLoadBuildingScanQueryVariables>(() => ({
        listingId: listingId.value
    }))

    const {
        error: buildingScanQueryError,
        loading: buildingScanLoading,
        load: loadBuildingScan,
        refetch: refetchBuildingScan
    } = useDListingBuildingLoadBuildingScanLazyQuery(buildingScanQueryVariables)

    const {
        mutate: createBuildingMutation,
        loading: createBuildingLoading
    } = useDListingBuildingCreateMutation()

    const watchStopHandle = watch(listing, async listing => {
        if (listing === null) {
            return
        }

        console.log(`Checking if building needs to be generated for listing ${listingId.value} ...`)
        reportMessageToBff("Building", `Checking if building needs to be generated for listing ${listingId.value} ...`)

        const hasNoBuildingButBuildingScan = !listing.hasBuilding && listing.hasBuildingScan;

        if (hasNoBuildingButBuildingScan) {
            console.log(`Listing ${listingId.value} has building scan but no building, generating building ...`)
            reportMessageToBff("Building", `Listing ${listingId.value} has building scan but no building, generating building ...`)

            try {
                await generateBuilding()
            } catch (e) {
                console.warn(`Could not generate Building for listing ${listingId.value}`, e)
                reportMessageToBff("Building", `Could not generate Building for listing ${listingId.value}`, JSON.stringify(e))
            }
        } else {
            console.log(`No need to generate building for listing ${listingId.value}`)
            reportMessageToBff("Building", `No need to generate building for listing ${listingId.value}`)
        }

        const timeoutId = setTimeout(() => {
            watchStopHandle()
            clearTimeout(timeoutId)
        }, 1_000)
    }, {
        immediate: true
    })

    const isBuildingCreationLoading = shallowRef<boolean>(false)

    async function generateBuilding() {
        console.log(`Generating building for listing ${listingId.value} ...`)
        reportMessageToBff("Building", `Generating building for listing ${listingId.value} ...`)

        let listingBuildingScan: Optional<ListingBuildingScan>

        const loadResult = loadBuildingScan()
        if (loadResult === false) {
            console.log(`Refetching building scan for listing ${listingId.value} ...`)
            reportMessageToBff("Building", `Refetching building scan for listing ${listingId.value} ...`)

            const refetchResult = await refetchBuildingScan()
            listingBuildingScan = refetchResult?.data.listingBuildingScan as (ListingBuildingScan | null | undefined) ?? null
        } else {
            console.log(`Loading building scan for listing ${listingId.value} ...`)
            reportMessageToBff("Building", `Loading building scan for listing ${listingId.value} ...`)

            listingBuildingScan = (await loadResult).listingBuildingScan as (ListingBuildingScan | null | undefined) ?? null
        }

        if (listingBuildingScan === null) {
            console.warn(`Building scan for listing ${listingId.value} not loadable`)
            reportMessageToBff("Building", `Building scan for listing ${listingId.value} not loadable`)

            showBuildingScanNotLoadableWarning.value = true //TODO: sauberes error handling
            return
        }

        if (!listingBuildingScan.isComplete) {
            console.warn(`Building scan for listing ${listingId.value} not completed`)
            reportMessageToBff("Building", `Building scan for listing ${listingId.value} not completed`)

            showBuildingScanNotCompletedWarning.value = true //TODO: sauberes error handling
            return
        }

        isBuildingCreationLoading.value = true
        try {
            console.log(`Creating building from scan for listing ${listingId.value} ...`)
            reportMessageToBff("Building", `Creating building from scan for listing ${listingId.value} ...`)

            await createBuilding(listingBuildingScan)
        } finally {
            isBuildingCreationLoading.value = false
        }
    }

    // noinspection FunctionTooLongJS
    async function createBuilding(listingBuildingScan: ListingBuildingScan) {
        console.log(`Creating building for listing ${listingId.value} ...`)
        reportMessageToBff("Building", `Creating building for listing ${listingId.value} ...`)

        try {
            console.log(`Processing building scan for listing ${listingId.value} ...`)
            reportMessageToBff("Building", `Processing building scan for listing ${listingId.value} ...`)

            const newBuilding: Optional<BuildingInput> = await BuildingCreationPipeline.process(listingBuildingScan)

            if (newBuilding === null) {
                console.error(`Could not create Building for listing ${listingId.value}`)
                reportMessageToBff("Building", `Could not create Building for listing ${listingId.value}`)

                return
            }

            const lId = listingId.value

            try {
                console.log(`Saving building for listing ${lId} ...`)
                reportMessageToBff("Building", `Saving building for listing ${lId} ...`)

                const result = await createBuildingMutation({
                    listingBuildingData: {
                        listingId: lId,
                        building: newBuilding
                    }
                }, {
                    update(cache, response) {
                        console.log(`Updating cache for building of listing ${lId} ...`)
                        reportMessageToBff("Building", `Updating cache for building of listing ${lId} ...`)

                        const building = response.data?.saveListingBuilding as (undefined | EnsureDefined<Building>) ?? buildingInputToBuilding(newBuilding, false)
                        cache.modify({
                            id: cache.identify({
                                __typename: "Listing",
                                id: lId
                            }),
                            fields: {
                                building(): Optional<EnsureDefined<Building>> {
                                    return building
                                },
                                hasBuilding(): boolean {
                                    return true
                                },
                            }
                        });
                    }
                })

                const saveListingBuilding = result?.data?.saveListingBuilding as undefined | EnsureDefined<Building>
                if (saveListingBuilding === undefined) {
                    console.warn(`Building save failed for listing ${lId}`)
                    reportMessageToBff("Building", `Building save failed for listing ${lId}`)

                } else {
                    console.log(`Building successfully saved for listing ${lId}`)
                    reportMessageToBff("Building", `Building successfully saved for listing ${lId}`)
                }

                const savedBuilding = saveListingBuilding ?? buildingInputToBuilding(newBuilding, false)

                const mutableBuilding = createMutableBuildingFrom(savedBuilding)
                const emptyRenderer = new BuildingRendererEmpty(lId, toRef(() => mutableBuilding))

                console.log(`Running post-building creation pipeline for listing ${lId} ...`)
                reportMessageToBff("Building", `Running post-building creation pipeline for listing ${lId} ...`)

                await BuildingPipelineBuilder
                    .create("PostBuildingCreationPipeline", emptyRenderer)
                    .doAll(false) //currently false, deskewing floors makes more broken
                    .build()
                    .execute()

                console.log(`Post-building creation pipeline completed for listing ${lId}`)
                reportMessageToBff("Building", `Post-building creation pipeline completed for listing ${lId}`)

                emptyRenderer.destroy()
            } catch (e) {
                console.warn(`Error when saving building for listing ${lId}`, e)
                reportMessageToBff("Building", `Error when saving building for listing ${lId}`, JSON.stringify(e))

            }
        } catch (e) {
            console.warn(`Could not create Building for listing ${listingId.value}`, e)
            reportMessageToBff("Building", `Could not create Building for listing ${listingId.value}`, JSON.stringify(e))

        }
    }
</script>

<style scoped>
</style>