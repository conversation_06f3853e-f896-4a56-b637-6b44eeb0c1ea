<template>
    <slot/>
</template>

<script lang="ts"
        setup>
    import {provide} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";

    const props = defineProps<{
        renderer: BuildingRenderer
    }>()

    provide(DBuildingRendererInjection, props.renderer)
</script>

<style scoped>
</style>