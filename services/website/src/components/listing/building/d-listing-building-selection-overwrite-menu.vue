<template>
    <v-menu v-model="isMenuVisible">
        <template #activator="{ props: menuProps }">
            <d-btn :disabled="renderer.isLoading.value || validItems.length <= 0"
                   :prepend-icon="mdiContentDuplicate"
                   :text="config.buttonText"
                   type="default"
                   v-bind="menuProps"
                   variant="text"/>
        </template>

        <d-card style="width: 300px;">
            <p class="ma-4 text-caption">{{ config.description }}</p>

            <v-layout class="align-center justify-center mb-4">
                <d-alert class="d-inline-block ps-2 pe-0 py-0"
                         hide-icon
                         rounded="pill"
                         style="flex: none;"
                         type="info">
                    <v-layout class="align-center justify-center text-subtitle-1">
                        {{ config.displayValue.value }}
                        <d-input-unit :unit="config.unit"
                                      class="ms-1"/>
                    </v-layout>
                </d-alert>
            </v-layout>

            <d-divider/>
            <d-divider/>
            <d-divider/>

            <d-list>
                <template v-for="(section, sIndex) in validItems"
                          :key="sIndex">
                    <template v-if="sIndex > 0">
                        <d-divider/>
                        <d-divider/>
                        <d-divider/>
                    </template>

                    <template v-for="(item, iIndex) in section"
                              :key="iIndex">
                        <d-divider v-if="iIndex > 0"/>

                        <d-list-item :disabled="item.targets.length <= 0"
                                     :prepend-icon="item.icon"
                                     :title="item.name"
                                     multiline
                                     @click="config.processor(item.targets)">
                            <template #append>
                                <d-badge inline
                                         type="default">
                                    <template #badge>{{ item.targets.length }}</template>
                                </d-badge>
                            </template>
                        </d-list-item>
                    </template>
                </template>
            </d-list>
        </d-card>
    </v-menu>
</template>

<script generic="T extends TraversalBuildingComponent"
        lang="ts"
        setup>
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {mdiContentDuplicate} from "@mdi/js";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {computed, inject} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import DBadge from "@/adapter/vuetify/theme/components/d-badge.vue";
    import {OverwriteMenuConfig, OverwriteMenuConfigItem} from "@/components/listing/building/overwrite-menu-config";
    import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";

    const props = defineProps<{
        config: OverwriteMenuConfig<T>
    }>()

    const isMenuVisible = defineModel<boolean>("modelValue", {
        required: false
    })
    const renderer = inject(DBuildingRendererInjection)!

    const validItems = computed<readonly (readonly OverwriteMenuConfigItem<T>[])[]>(() => {
        const sections: OverwriteMenuConfigItem<T>[][] = []
        const sourceId = props.config.source.value.id

        for (const section of props.config.items.value) {
            const validItems = section.filter(item => item.targets.filter(target => target.id !== sourceId).length > 0)
            if (validItems.length > 0) {
                sections.push(validItems)
            }
        }

        return sections
    })
</script>

<style scoped>
</style>