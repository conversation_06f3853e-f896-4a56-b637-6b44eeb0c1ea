<template>
    <v-slide-y-transition>
        <v-container v-if="isMultiSelectionWallRoofPointEnabled"
                     fluid>
            <v-row>
                <v-col class="text-caption">
                    {{ t('listing.building.cad.selection.wallRoofPoints.info') }}
                </v-col>
            </v-row>

            <v-slide-y-transition>
                <v-row v-if="!renderer.roofAreaCreator!.isWallRoofPointSelectionValid.value">
                    <v-col>
                        <d-alert :text="t('listing.building.cad.selection.wallRoofPoints.warning')"
                                 type="warning"/>
                    </v-col>
                </v-row>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <v-row v-if="renderer.roofAreaCreator!.isWallRoofPointSelectionValid.value"
                       justify="center">
                    <v-col cols="auto">
                        <d-chip type="info">
                            <v-layout class="align-center">
                                {{ n(renderer.roofAreaCreator!.tempRoofArea.value.area, "decimal") }}
                                <d-input-unit class="ms-1"
                                              no-margin
                                              unit="SQUARE_METER"/>
                            </v-layout>
                        </d-chip>
                    </v-col>

                    <v-slide-x-transition>
                        <v-col v-if="renderer.roofAreaCreator!.tempRoofArea.value.isRectangular"
                               cols="auto">
                            <d-chip type="info">
                                <v-layout class="align-center">
                                    {{ n(renderer.roofAreaCreator!.tempRoofArea.value.width * 100, "integer") }} × {{ n(renderer.roofAreaCreator!.tempRoofArea.value.height * 100, "integer") }}
                                    <d-input-unit class="ms-1"
                                                  no-margin
                                                  unit="CENTIMETER"/>
                                </v-layout>
                            </d-chip>
                        </v-col>
                    </v-slide-x-transition>
                </v-row>
            </v-slide-y-transition>

            <v-row class="mt-12"
                   justify="center">
                <v-col cols="auto">
                    <d-btn :text="t('listing.building.cad.selection.wallRoofPoints.deselectButton')"
                           type="tertiary"
                           @click="renderer.selectionRaycaster.reset()"/>
                </v-col>
            </v-row>

            <v-row justify="center">
                <v-col cols="auto">
                    <d-btn :disabled="!renderer.roofAreaCreator!.isWallRoofPointSelectionValid.value"
                           :prepend-icon="mdiCheckAll"
                           :text="t('listing.building.cad.selection.wallRoofPoints.createRoofAreaButton')"
                           size="x-large"
                           type="primary"
                           @click="renderer.roofAreaCreator!.addRoofArea()"/>
                </v-col>
            </v-row>
        </v-container>
    </v-slide-y-transition>
</template>

<script lang="ts"
        setup>
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {computed, inject} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import {mdiCheckAll} from "@mdi/js";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import DChip from "@/adapter/vuetify/theme/components/chip/d-chip.vue";
    import {useI18n} from "vue-i18n";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";

    const renderer = inject(DBuildingRendererInjection)!

    const {t, n} = useI18n()

    const multiSelection = renderer.selectionRaycaster.selection
    const isMultiSelectionWallRoofPointEnabled = computed<boolean>(() => multiSelection.value.length > 0 && multiSelection.value.some(s => s.type === "WALL_ROOF_POINT"))
</script>

<style scoped>
</style>