<template>
    <v-container class="mt-0 pt-0"
                 fluid>
        <v-row dense>
            <v-col class="pe-0 pt-0 mt-0 text-end">
                <d-listing-building-selection-overwrite-menu :config="uValueOverwriteMenuConfig"/>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import {computed, inject, toRef} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import {ConstructionPart} from "@/adapter/graphql/generated/graphql";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import {OverwriteMenuConfig, useItemsForOpeningOverwriteMenuConfig} from "@/components/listing/building/overwrite-menu-config";
    import {createEmptyMutableFlowData, FlowConfigFieldWithArrayIndex, MutableFlowData, useFlowDataDouble, useMutableFlowDataDouble} from "@/model/listing/FlowData";
    import DListingBuildingSelectionOverwriteMenu from "@/components/listing/building/d-listing-building-selection-overwrite-menu.vue";
    import {WallOpeningType} from "@/model/building/WallOpeningType";

    const props = defineProps<{
        opening: ConstructionPart
    }>()

    const renderer = inject(DBuildingRendererInjection)!

    const {t, n} = useI18n()
    const itemsForOpeningOverwriteMenuConfig = useItemsForOpeningOverwriteMenuConfig(renderer, toRef(() => props.opening), t)

    const flowData = computed<MutableFlowData>(() => {
        const flowData = createEmptyMutableFlowData()
        flowData.setFromInput(props.opening.customData)
        return flowData
    })

    const fieldId = computed<string>(() => {
        const openingType = props.opening.type as WallOpeningType
        switch (openingType) {
            case "DOOR":
                return "door_u_value"
            case "WINDOW":
                return "window_u_value"
            default:
                return ""
        }
    })
    const uValueField = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: {
            id: fieldId.value,
        },
        arrayIndex: null
    }))
    const uValue = useFlowDataDouble(flowData, uValueField)

    const uValueOverwriteMenuConfig: OverwriteMenuConfig<ConstructionPart> = {
        buttonText: t(`listing.building.cad.selection.opening.overwriteMenu.variant.uValue.button.${props.opening.type}`),
        description: t(`listing.building.cad.selection.opening.overwriteMenu.variant.uValue.description.${props.opening.type}`),
        processor: async (openings) => {
            if (openings.length <= 0) {
                return
            }
            const newUValue = uValue.value

            for (const opening of openings) {
                const flowData = createEmptyMutableFlowData()
                flowData.setFromInput(opening.customData)
                const uValue = useMutableFlowDataDouble(toRef(flowData), uValueField)
                uValue.value = newUValue
                opening.customData = flowData.generateInput(false)
            }

            await BuildingPipelineBuilder
                .create("ChangeUValuesOfMultipleOpenings", renderer)
                .save()
                .build()
                .execute()
        },
        displayValue: computed<string>(() => n(uValue.value ?? 0, 'decimal')),
        unit: "WATT_PER_SQUARE_METER_PER_KELVIN",
        source: toRef(() => props.opening),
        items: itemsForOpeningOverwriteMenuConfig
    }
</script>

<style scoped>
</style>