query dListingBuilding (
    $listingId: String!
) {
    listing(
        id: $listingId
    ) {
        id
        hasBuilding
        hasBuildingScan
        building {
            ...allOfBuilding
        }
    }
}

query dListingBuildingLoadBuildingScan(
    $listingId: String!
) {
    listingBuildingScan(
        listingId: $listingId
    ) {
        ...allOfListingBuildingScan
    }
}

mutation dListingBuildingCreate(
    $listingBuildingData: ListingBuildingDataInput!
) {
    saveListingBuilding(
        listingBuildingData: $listingBuildingData
    ) {
        ...allOfBuilding
    }
}