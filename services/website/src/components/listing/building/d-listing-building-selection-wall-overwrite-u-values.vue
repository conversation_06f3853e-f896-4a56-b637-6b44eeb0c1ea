<template>
    <v-container class="mt-0 pt-0"
                 fluid>
        <v-row dense>
            <v-col class="pe-0 pt-0 mt-0 text-end">
                <d-listing-building-selection-overwrite-menu :config="uValueOverwriteMenuConfig"/>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import {computed, inject, toRef} from "vue";
    import {DBuildingRendererInjection} from "@/components/listing/building/building";
    import {Wall} from "@/adapter/graphql/generated/graphql";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import {OverwriteMenuConfig, useItemsForWallOverwriteMenuConfig} from "@/components/listing/building/overwrite-menu-config";
    import {createEmptyMutableFlowData, MutableFlowData, useFlowDataDouble, useMutableFlowDataDouble} from "@/model/listing/FlowData";
    import DListingBuildingSelectionOverwriteMenu from "@/components/listing/building/d-listing-building-selection-overwrite-menu.vue";

    const props = defineProps<{
        wall: Wall
    }>()

    const renderer = inject(DBuildingRendererInjection)!

    const {t, n} = useI18n()
    const itemsForWallOverwriteMenuConfig = useItemsForWallOverwriteMenuConfig(renderer, toRef(() => props.wall), t)

    const flowData = computed<MutableFlowData>(() => {
        const flowData = createEmptyMutableFlowData()
        flowData.setFromInput(props.wall.customData)
        return flowData
    })

    const uValueField = toRef({
        field: {
            id: "wall_u_value"
        },
        arrayIndex: null
    })
    const uValue = useFlowDataDouble(flowData, uValueField)

    const uValueOverwriteMenuConfig: OverwriteMenuConfig<Wall> = {
        buttonText: t('listing.building.cad.selection.wall.overwriteMenu.variant.uValue.button'),
        description: t('listing.building.cad.selection.wall.overwriteMenu.variant.uValue.description'),
        processor: async (walls) => {
            if (walls.length <= 0) {
                return
            }
            const newUValue = uValue.value

            for (const wall of walls) {
                const flowData = createEmptyMutableFlowData()
                flowData.setFromInput(wall.customData)
                const uValue = useMutableFlowDataDouble(toRef(flowData), uValueField)
                uValue.value = newUValue
                wall.customData = flowData.generateInput(false)
            }

            await BuildingPipelineBuilder
                .create("ChangeUValuesOfMultipleWalls", renderer)
                .save()
                .build()
                .execute()
        },
        displayValue: computed<string>(() => n(uValue.value ?? 0, 'decimal')),
        unit: "WATT_PER_SQUARE_METER_PER_KELVIN",
        source: toRef(() => props.wall),
        items: itemsForWallOverwriteMenuConfig
    }
</script>

<style scoped>
</style>