import {angleInDegreesBetweenWalls, BUILDING_EPSILON, calculateEdgesOfPolygon, calculateTopAndCenterPerimeterOfPolygon, calculateTopPerimeterOfPolygon, heightOfShape, thicknessOfShape, WallOrOpeningSnapPointType} from "@/components/listing/building/building";
import {BuildingPipelineStage} from "@/components/listing/building/pipeline/BuildingPipelineStage";
import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
import {areNumbersEqual, areNumbersNotEqual, isNumberGreaterThanOrEqual, isNumberLessThanOrEqual} from "@/utility/number";
import {Floor, Wall} from "@/adapter/graphql/generated/graphql";
import {Optional} from "@/model/Optional";
import {SnappingManager} from "@/components/listing/building/renderer/SnappingManager";

// noinspection JSClassNamingConvention
export type WallSizeAdjustmentXSideResultTerm = {
    readonly wall: Wall
    readonly value: number
}

export type WallSizeAdjustmentXSideResult = {
    readonly type: WallOrOpeningSnapPointType
    readonly sizeAdjustmentX: number
    readonly terms: readonly WallSizeAdjustmentXSideResultTerm[]
}

export type WallSizeAdjustmentXResult = {
    readonly sides: WallSizeAdjustmentXSideResult[]
    readonly sizeAdjustmentX: number
}

// noinspection JSClassNamingConvention
export type WallSizeAdjustmentYSideResultType = "FLOOR" | "CEILING"

export type WallSizeAdjustmentYSideResult = {
    readonly type: WallSizeAdjustmentYSideResultType
    readonly sizeAdjustmentY: number
    readonly floor: Floor
    readonly info?: {
        readonly percent: number
        readonly orginalSizeAdjustmentY: number
    }
}

export type WallSizeAdjustmentYResult = {
    readonly sides: WallSizeAdjustmentYSideResult[]
    readonly sizeAdjustmentY: number
}

const ANGLE_RANGE_IN_DEGREE = 45 / 2
const ANGLE_RANGE_EXTERIOR_MINUS_WALLS_CENTER = 90
const ANGLE_RANGE_EXTERIOR_MINUS_WALLS_MIN = ANGLE_RANGE_EXTERIOR_MINUS_WALLS_CENTER - ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_EXTERIOR_MINUS_WALLS_MAX = ANGLE_RANGE_EXTERIOR_MINUS_WALLS_CENTER + ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_EXTERIOR_PLUS_WALLS_CENTER = 270
const ANGLE_RANGE_EXTERIOR_PLUS_WALLS_MIN = ANGLE_RANGE_EXTERIOR_PLUS_WALLS_CENTER - ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_EXTERIOR_PLUS_WALLS_MAX = ANGLE_RANGE_EXTERIOR_PLUS_WALLS_CENTER + ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_INTERIOR_WALLS_CENTER = 270
const ANGLE_RANGE_INTERIOR_WALLS_MIN = ANGLE_RANGE_INTERIOR_WALLS_CENTER - ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_INTERIOR_WALLS_MAX = ANGLE_RANGE_INTERIOR_WALLS_CENTER + ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_CENTER_ = 90
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_MIN = ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_CENTER_ - ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_MAX = ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_CENTER_ + ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_CENTER_ = 270
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_MIN = ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_CENTER_ - ANGLE_RANGE_IN_DEGREE
const ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_MAX = ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_CENTER_ + ANGLE_RANGE_IN_DEGREE

// noinspection JSClassNamingConvention
export class RecalculateAllWallSizeAdjustmentsBPipelineStage extends BuildingPipelineStage {
    constructor(private readonly forceSnappingManagerUpdates: boolean) {
        super(`RecalculateAllWallSizeAdjustments(forceSnappingManagerUpdates=${forceSnappingManagerUpdates})`)
    }

    async execute(renderer: BuildingRenderer): Promise<void> {
        const building = renderer.building.value

        for (const floor of building.floors) {
            if (this.forceSnappingManagerUpdates) {
                renderer.snappingManager.forceRefreshAll()
            } else if (!renderer.snappingManager.isAutoRefreshEnabled(floor.level)) {
                console.error(`Auto refresh must be enabled for floor level ${floor.level} to recalculate wall size adjustments`)
            }

            for (const wall of floor.walls) {
                wall.sizeAdjustmentX = calculateSizeAdjustmentXFor(renderer, wall)?.sizeAdjustmentX ?? 0
                wall.sizeAdjustmentY = calculateSizeAdjustmentYFor(renderer, wall)?.sizeAdjustmentY ?? 0

                if (wall.isExterior !== true) {
                    wall.sizeAdjustmentZ = -thicknessOfShape(wall.shapeRepresentation.shape)
                }
            }
        }
    }
}

export function calculateSizeAdjustmentXNeighborExteriorMinusWallsOf(renderer: BuildingRenderer, wall: Wall, type: WallOrOpeningSnapPointType): Wall[] {
    return renderer.neighborWallsOf(wall, type, true).filter(neighborWall => {
        if (neighborWall.isExterior !== true || neighborWall.isPartition === true) {
            return false
        }
        const angleInDegrees = calculateNeighborAngle(renderer, wall, neighborWall, type)

        return isNumberGreaterThanOrEqual(angleInDegrees, ANGLE_RANGE_EXTERIOR_MINUS_WALLS_MIN, BUILDING_EPSILON) &&
            isNumberLessThanOrEqual(angleInDegrees, ANGLE_RANGE_EXTERIOR_MINUS_WALLS_MAX, BUILDING_EPSILON)
    })
}

export function calculateSizeAdjustmentXNeighborExteriorPlusWallsOf(renderer: BuildingRenderer, wall: Wall, type: WallOrOpeningSnapPointType): Wall[] {
    return renderer.neighborWallsOf(wall, type, true).filter(neighborWall => {
        if (neighborWall.isExterior !== true || neighborWall.isPartition === true) {
            return false
        }
        const angleInDegrees = calculateNeighborAngle(renderer, wall, neighborWall, type)

        return isNumberGreaterThanOrEqual(angleInDegrees, ANGLE_RANGE_EXTERIOR_PLUS_WALLS_MIN, BUILDING_EPSILON) &&
            isNumberLessThanOrEqual(angleInDegrees, ANGLE_RANGE_EXTERIOR_PLUS_WALLS_MAX, BUILDING_EPSILON)
    })
}

export function calculateNeighborAngle(renderer: BuildingRenderer, wall: Wall, neighborWall: Wall, type: WallOrOpeningSnapPointType): number {
    switch (type) {
        case "LEFT":
            return angleInDegreesBetweenWalls(renderer, wall, neighborWall, SnappingManager.WALL_SNAP_POINT_SIZE_BIG) //parameter order matters
        case "RIGHT":
            return angleInDegreesBetweenWalls(renderer, neighborWall, wall, SnappingManager.WALL_SNAP_POINT_SIZE_BIG) //parameter order matters
        default:
            throw new Error(`Unknown type: ${type}`)
    }
}

export function calculateOrthogonalInteriorWallsOf(renderer: BuildingRenderer, wall: Wall): Wall[] {
    if (wall.isExterior !== true) {
        return []
    }
    if (wall.isPartition === true) {
        return []
    }
    return renderer
            .traversableBuilding
            .floorOf(wall)
            ?.walls
            .filter(otherWall => {
                if (otherWall.id === wall.id) {
                    return false
                }
                if (otherWall.isExterior === true) {
                    return false
                }
                const angleInDegrees = angleInDegreesBetweenWalls(renderer, wall, otherWall, SnappingManager.WALL_SNAP_POINT_SIZE_BIG)

                return (
                    isNumberGreaterThanOrEqual(angleInDegrees, ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_MIN, BUILDING_EPSILON) &&
                    isNumberLessThanOrEqual(angleInDegrees, ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_1_MAX, BUILDING_EPSILON)
                ) || (
                    isNumberGreaterThanOrEqual(angleInDegrees, ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_MIN, BUILDING_EPSILON) &&
                    isNumberLessThanOrEqual(angleInDegrees, ANGLE_RANGE_ORTHOGONAL_INTERIOR_WALLS_2_MAX, BUILDING_EPSILON)
                )
            })
        ?? []
}

export function calculatePossibleSizeAdjustmentXRelatedWalls(renderer: BuildingRenderer, wall: Wall): Wall[] {
    const sizeAdjustmentXWallIds = new Set(calculateSizeAdjustmentXFor(renderer, wall)
            ?.sides
            ?.flatMap(side => side.terms.flatMap(term => term.wall.id))
        ?? [])
    return calculateOrthogonalInteriorWallsOf(renderer, wall).filter(w => !sizeAdjustmentXWallIds.has(w.id))
}

export function calculateRemainingPossibleSizeAdjustmentXRelatedWalls(renderer: BuildingRenderer, wall: Wall): Wall[] {
    const relatedWallIds = new Set(wall.sizeAdjustmentUserXRelatedWallIds)
    return calculatePossibleSizeAdjustmentXRelatedWalls(renderer, wall).filter(w => !relatedWallIds.has(w.id))
}

export function calculateSizeAdjustmentXRelatedWalls(renderer: BuildingRenderer, wall: Wall): Wall[] {
    const relatedWallIds = new Set(wall.sizeAdjustmentUserXRelatedWallIds)
    return calculatePossibleSizeAdjustmentXRelatedWalls(renderer, wall).filter(w => relatedWallIds.has(w.id))
}

export function calculateSizeAdjustmentXNeighborInteriorWallsOf(renderer: BuildingRenderer, wall: Wall, type: WallOrOpeningSnapPointType): Wall[] {
    return renderer.neighborWallsOf(wall, type, true).filter(neighborWall => {
        if (neighborWall.isExterior === true) {
            return false
        }
        const angleInDegrees = calculateNeighborAngle(renderer, wall, neighborWall, type)

        return isNumberGreaterThanOrEqual(angleInDegrees, ANGLE_RANGE_INTERIOR_WALLS_MIN, BUILDING_EPSILON) &&
            isNumberLessThanOrEqual(angleInDegrees, ANGLE_RANGE_INTERIOR_WALLS_MAX, BUILDING_EPSILON)
    })
}

// noinspection OverlyComplexFunctionJS
export function calculateSizeAdjustmentXForNeighborsOf(renderer: BuildingRenderer, wall: Wall, type: WallOrOpeningSnapPointType): Optional<WallSizeAdjustmentXSideResult> {
    //const interiorNeighbors = calculateSizeAdjustmentXNeighborInteriorWallsOf(renderer, wall, type)
    const exteriorMinusNeighbors = calculateSizeAdjustmentXNeighborExteriorMinusWallsOf(renderer, wall, type)
    const exteriorPlusNeighbors = calculateSizeAdjustmentXNeighborExteriorPlusWallsOf(renderer, wall, type)
    if (/*interiorNeighbors.length <= 0 && */exteriorMinusNeighbors.length <= 0 && exteriorPlusNeighbors.length <= 0) {
        return null
    }

    //TODO: schlauere logik bauen, um die besten nachbarn zu wählen und nicht nur die ersten
    //const interior = interiorNeighbors.length > 0 ? interiorNeighbors[0] : null
    const exteriorMinus = exteriorMinusNeighbors.length > 0 ? exteriorMinusNeighbors[0] : null
    const exteriorPlus = exteriorPlusNeighbors.length > 0 ? exteriorPlusNeighbors[0] : null

    //let interiorThickness = interior === null ? 0 : thicknessOfShape(interior.shapeRepresentation.shape) + interior.sizeAdjustmentZ
    let exteriorMinusThickness = exteriorMinus === null ? 0 : thicknessOfShape(exteriorMinus.shapeRepresentation.shape) + exteriorMinus.sizeAdjustmentZ
    let exteriorPlusThickness = exteriorPlus === null ? 0 : thicknessOfShape(exteriorPlus.shapeRepresentation.shape) + exteriorPlus.sizeAdjustmentZ

    //Innenwände zählen halb
    //interiorThickness /= 2

    //Zwischenwände zählen wie Innenwände halb
    if (exteriorMinus !== null && exteriorMinus.isIntermediate === true) {
        exteriorMinusThickness /= 2
    }

    //Zwischenwände zählen wie Innenwände halb
    if (exteriorPlus !== null && exteriorPlus.isIntermediate === true) {
        exteriorPlusThickness /= 2
    }

    const sizeAdjustmentX = /*interiorThickness*/ -exteriorMinusThickness + exteriorPlusThickness

    const result: WallSizeAdjustmentXSideResult = {
        type,
        sizeAdjustmentX,
        terms: [
            /*{
                wall: interior,
                value: interiorThickness,
            },*/
            {
                wall: exteriorMinus,
                value: -exteriorMinusThickness,
            },
            {
                wall: exteriorPlus,
                value: exteriorPlusThickness,
            }
        ].filter(t => t.wall !== null && areNumbersNotEqual(t.value, 0, BUILDING_EPSILON)) as WallSizeAdjustmentXSideResultTerm[]
    }

    return result.terms.length > 0 ? result : null
}

export function calculateSizeAdjustmentXFor(renderer: BuildingRenderer, wall: Wall): Optional<WallSizeAdjustmentXResult> {
    if (wall.isExterior !== true) {
        return null
    }
    const left = calculateSizeAdjustmentXForNeighborsOf(renderer, wall, "LEFT")
    const right = calculateSizeAdjustmentXForNeighborsOf(renderer, wall, "RIGHT")

    if (left === null && right === null) {
        return null
    }

    const sides: WallSizeAdjustmentXSideResult[] = []
    if (left !== null) {
        sides.push(left)
    }
    if (right !== null) {
        sides.push(right)
    }

    return {
        sides,
        sizeAdjustmentX: (left?.sizeAdjustmentX ?? 0) + (right?.sizeAdjustmentX ?? 0),
    }
}

export function calculateSizeAdjustmentYFor(renderer: BuildingRenderer, wall: Wall): Optional<WallSizeAdjustmentYResult> {
    const ceiling = calculateSizeAdjustmentYSide(renderer, wall, "CEILING")
    // const floor = calculateSizeAdjustmentYSide(renderer, wall, "FLOOR")

    if (ceiling === null/* && floor === null*/) {
        return null
    }

    const sides: WallSizeAdjustmentYSideResult[] = []
    //if (ceiling !== null) {
    sides.push(ceiling)
    //}
    // if (floor !== null) {
    //     sides.push(floor)
    // }

    return {
        sides,
        sizeAdjustmentY: (ceiling?.sizeAdjustmentY ?? 0) /*+ (floor?.sizeAdjustmentY ?? 0)*/,
    }
}

// noinspection OverlyComplexFunctionJS,FunctionTooLongJS
export function calculateSizeAdjustmentYSide(renderer: BuildingRenderer, wall: Wall, type: WallSizeAdjustmentYSideResultType): Optional<WallSizeAdjustmentYSideResult> {
    const floor = renderer.traversableBuilding.floorOf(wall)
    if (floor === null) {
        return null
    }

    const floors = renderer.building.value.floors
    const floorIndex = floors.findIndex(f => f.id === floor.id)

    if (type === "FLOOR") {
        const floorSlabHeight = heightOfShape(floor.floorSlab.shapeRepresentation.shape)
        if (isNumberLessThanOrEqual(floorSlabHeight, 0, BUILDING_EPSILON)) {
            return null
        }

        const isFirst = floorIndex === 0
        const floorSlabOffset = floorSlabHeight * (isFirst ? 1 : 0.5)

        return {
            type,
            sizeAdjustmentY: floorSlabOffset,
            floor,
        }
    }

    if (type === "CEILING") {
        if (floor.ceilingSlab === null || floor.ceilingSlab === undefined) {
            return null
        }
        const ceilingSlabHeight = heightOfShape(floor.ceilingSlab.shapeRepresentation.shape)
        if (isNumberLessThanOrEqual(ceilingSlabHeight, 0, BUILDING_EPSILON)) {
            return null
        }

        //Halbhohe Wände ignorieren
        const floorHeight = heightOfShape(floor.shapeRepresentation.shape)
        const wallHeight = heightOfShape(wall.shapeRepresentation.shape)
        if (areNumbersNotEqual(floorHeight, wallHeight, BUILDING_EPSILON)) {
            return null
        }

        //const isLast = floorIndex === floors.length - 1
        const ceilingSlabOffset = /*(*/ceilingSlabHeight// * (isLast ? 1 : 0.5))

        if (wall.shapeRepresentation.shape.__typename === "Polygon") {
            const polygon = wall.shapeRepresentation.shape
            const edges = calculateEdgesOfPolygon(polygon)
            const topPerimeter = calculateTopPerimeterOfPolygon(polygon, edges)
            const topAndCenterPerimeter = calculateTopAndCenterPerimeterOfPolygon(polygon, edges)
            const factor = topPerimeter / topAndCenterPerimeter
            const newCeilingSlabOffset = ceilingSlabOffset * factor;

            if (areNumbersEqual(factor, 1, BUILDING_EPSILON)) {
                return {
                    type,
                    sizeAdjustmentY: ceilingSlabOffset,
                    floor,
                }
            }
            if (areNumbersEqual(newCeilingSlabOffset, 0, BUILDING_EPSILON)) {
                return null
            }
            return {
                type,
                sizeAdjustmentY: newCeilingSlabOffset,
                floor,
                info: {
                    percent: factor,
                    orginalSizeAdjustmentY: ceilingSlabOffset,
                },
            }
        }

        return {
            type,
            sizeAdjustmentY: ceilingSlabOffset,
            floor,
        }
    }

    return null
}