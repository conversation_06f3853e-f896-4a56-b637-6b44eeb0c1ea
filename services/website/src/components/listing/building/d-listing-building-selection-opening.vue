<template>
    <v-container fluid>
        <v-row>
            <v-col>
                <d-select v-model="selectedOpeningType"
                          :clearable="false"
                          :display-name-supplier="ot => t(`enums.wallOpeningType.${ot}`)"
                          :icon-supplier="ot => WallOpeningTypeToIcon[ot]"
                          :id-supplier="ot => ot"
                          :items="WallOpeningTypeValues"
                          :label="t('listing.building.cad.selection.opening.type')"
                          :loading="renderer.isLoading.value"/>
            </v-col>
        </v-row>

        <v-slide-y-transition>
            <v-row dense
                   justify="space-between">
                <v-col class="ps-0">
                    <d-btn :disabled="wall.shapeRepresentation.shape.__typename === 'Ring' || renderer.isLoading.value"
                           :prepend-icon="mdiCursorMove"
                           :text="t('listing.building.cad.selection.opening.relocateButton')"
                           density="compact"
                           type="tertiary"
                           variant="text"
                           @click="onRelocateButtonClicked"/>
                </v-col>
                <v-col class="pe-0"
                       cols="auto">
                    <d-btn :disabled="renderer.isLoading.value"
                           :prepend-icon="mdiRotateRight"
                           class="pe-0"
                           density="compact"
                           type="default"
                           variant="text"
                           @click="onRotate(180)">
                        180
                        <d-input-unit unit="DEGREE_OF_ARC"/>
                    </d-btn>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="opening.shapeRepresentation.shape.__typename === 'Box'">
                <v-col>
                    <d-text-field v-model="openingWidthCm"
                                  :label="t('listing.building.cad.selection.opening.width')"
                                  :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandHorizontal"
                                  :value-max="100*100"
                                  :value-min="Math.round(BUILDING_OPENING_MIN_WIDTH * 100)"
                                  center-input-text
                                  no-append-padding
                                  small-prepend-inner-icon
                                  type="number">
                        <template #appendInner>
                            <d-input-unit unit="CENTIMETER"/>
                        </template>
                    </d-text-field>
                </v-col>
                <v-col>
                    <d-text-field v-model="openingHeightCm"
                                  :label="t('listing.building.cad.selection.opening.height')"
                                  :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandVertical"
                                  :value-max="10*100"
                                  :value-min="Math.round(BUILDING_OPENING_MIN_HEIGHT * 100)"
                                  center-input-text
                                  no-append-padding
                                  small-prepend-inner-icon
                                  type="number">
                        <template #appendInner>
                            <d-input-unit unit="CENTIMETER"/>
                        </template>
                    </d-text-field>
                </v-col>
            </v-row>
        </v-slide-y-transition>

        <v-slide-y-transition>
            <v-row v-if="opening.shapeRepresentation.shape.__typename === 'Box' && (selectedOpeningType === 'WINDOW' || selectedOpeningType === 'OPENING')"
                   justify="end">
                <v-col cols="6">
                    <d-text-field v-model="openingSillHeightCm"
                                  :label="t('listing.building.cad.selection.opening.sillHeight')"
                                  :prepend-inner-icon="smAndDown ? undefined : mdiArrowExpandUp"
                                  :value-max="10*100"
                                  :value-min="0"
                                  center-input-text
                                  no-append-padding
                                  small-prepend-inner-icon
                                  type="number">
                        <template #appendInner>
                            <d-input-unit unit="CENTIMETER"/>
                        </template>
                    </d-text-field>
                </v-col>
            </v-row>
        </v-slide-y-transition>
    </v-container>
</template>

<script lang="ts"
        setup>
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import {useI18n} from "vue-i18n";
    import {WallOpeningType, WallOpeningTypeToIcon, WallOpeningTypeValues} from "@/model/building/WallOpeningType";
    import {computed, inject, shallowRef, watch} from "vue";
    import {BUILDING_EPSILON, BUILDING_OPENING_MIN_HEIGHT, BUILDING_OPENING_MIN_WIDTH, calculateWallOpeningSillHeight, changeWallOpeningHeight, changeWallOpeningSillHeight, changeWallOpeningWidth, createCmToMField, DBuildingRendererInjection, transformationOfShapeRepresentation} from "@/components/listing/building/building";
    import {ConstructionPart, Wall} from "@/adapter/graphql/generated/graphql";
    import {mdiArrowExpandHorizontal, mdiArrowExpandUp, mdiArrowExpandVertical, mdiCursorMove, mdiRotateRight} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import {MathUtils, Matrix4} from "three";
    import {matrix4ToTransformationMatrixArray} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import debounce from "debounce";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {areNumbersEqual} from "@/utility/number";
    import {useDisplay} from "vuetify";
    import {WallOpeningCreator} from "@/components/listing/building/renderer/WallOpeningCreator";
    import {Optional} from "@/model/Optional";

    const props = defineProps<{
        opening: ConstructionPart
    }>()

    const renderer = inject(DBuildingRendererInjection)!
    const {t} = useI18n()
    const selectedOpeningType = shallowRef<Optional<WallOpeningType>>(null)

    const {smAndDown} = useDisplay()
    const debounceDelay = 250
    const debouncedSaveBuilding = debounce(saveBuilding, debounceDelay)

    const wall = computed<Wall>(() => {
        const parent = renderer.traversableBuilding.parentOf(props.opening)
        if (parent === null) {
            throw new Error("Opening has no parent")
        }
        if (parent.__typename !== "Wall") {
            throw new Error("Opening is not a child of a wall")
        }
        return parent
    })

    async function saveBuilding() {
        await BuildingPipelineBuilder
            .create("SaveSelectedOpening", renderer)
            .save()
            .build()
            .execute()
    }

    function onRotate(degrees: 90 | 180) {
        const opening = props.opening

        if (opening.shapeRepresentation.shape.__typename === "Ring") {
            opening.shapeRepresentation.shape = {
                ...opening.shapeRepresentation.shape,
                startAngle: (opening.shapeRepresentation.shape.startAngle + 180) % 360,
                endAngle: (opening.shapeRepresentation.shape.endAngle + 180) % 360
            }
        }

        const transformation = transformationOfShapeRepresentation(opening.shapeRepresentation)
        const rotationMatrix = new Matrix4().makeRotationY(MathUtils.degToRad(-degrees))
        const newTransformation = transformation.clone().multiply(rotationMatrix)
        opening.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)

        debouncedSaveBuilding()
    }


    function onRelocateButtonClicked() {
        renderer.mode.value = {
            type: "RELOCATE_OPENING",
            wall: wall.value,
            opening: props.opening
        }
    }

    const openingWidthCm = shallowRef<Optional<number>>(0)
    const openingWidthM = createCmToMField(openingWidthCm)

    const openingHeightCm = shallowRef<Optional<number>>(0)
    const openingHeightM = createCmToMField(openingHeightCm)

    const openingSillHeightCm = shallowRef<Optional<number>>(0)
    const openingSillHeightM = createCmToMField(openingSillHeightCm)

    function updateInputs() {
        const opening = props.opening;
        const shapeRepresentation = opening.shapeRepresentation

        selectedOpeningType.value = opening.type as WallOpeningType

        if (shapeRepresentation.shape.__typename === "Box") {
            if (!areNumbersEqual(openingWidthM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.width, BUILDING_EPSILON)) {
                openingWidthM.value = shapeRepresentation.shape.width
            }
            if (!areNumbersEqual(openingHeightM.value ?? Number.MIN_VALUE, shapeRepresentation.shape.height, BUILDING_EPSILON)) {
                openingHeightM.value = shapeRepresentation.shape.height
            }
        }

        openingSillHeightM.value = calculateWallOpeningSillHeight(wall.value, opening)
    }

    watch(() => props.opening, () => {
        updateInputs()
    }, {
        immediate: true
    })

    watch(selectedOpeningType, (newOpeningType, oldOpeningType) => {
        if (oldOpeningType === null || newOpeningType === null) {
            return
        }

        const opening = props.opening

        if (newOpeningType === "DOOR") {
            changeWallOpeningSillHeight(wall.value, opening, WallOpeningCreator.openingTypeToSillHeight(newOpeningType))
        }

        opening.type = newOpeningType

        debouncedSaveBuilding()
    })


    //#############
    //### WIDTH ###
    //#############
    function updateWidth(width: Optional<number>) {
        changeWallOpeningWidth(props.opening, width ?? WallOpeningCreator.openingTypeToSize(props.opening.type as WallOpeningType).width)
        debouncedSaveBuilding()
    }

    const debouncedUpdateWidth = debounce(updateWidth, debounceDelay)
    watch(openingWidthM, debouncedUpdateWidth)

    //##############
    //### HEIGHT ###
    //##############
    function updateHeight(height: Optional<number>) {
        changeWallOpeningHeight(props.opening, height ?? WallOpeningCreator.openingTypeToSize(props.opening.type as WallOpeningType).height)
        debouncedSaveBuilding()
    }

    const debouncedUpdateHeight = debounce(updateHeight, debounceDelay)
    watch(openingHeightM, debouncedUpdateHeight)

    //###################
    //### SILL HEIGHT ###
    //###################
    function updateSillHeight(sillHeight: Optional<number>) {
        changeWallOpeningSillHeight(wall.value, props.opening, sillHeight ?? WallOpeningCreator.openingTypeToSillHeight(props.opening.type as WallOpeningType))
        debouncedSaveBuilding()
    }

    const debouncedUpdateSillHeight = debounce(updateSillHeight, debounceDelay)
    watch(openingSillHeightM, debouncedUpdateSillHeight)
</script>

<style scoped>
</style>