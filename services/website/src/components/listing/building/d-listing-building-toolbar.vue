<template>
    <div class="toolbarWrapper">
        <div class="toolbarArea ma-2 pa-2">
            <d-listing-building-toolbar-items :is-embedded="isEmbedded"
                                              :items="toolbarItems"
                                              type="MAIN"/>

        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onMounted, onUnmounted, toRef, watch} from "vue";
    import {DBuildingRendererInjection, findBuildingFloorByLevel, floorLevelInfoToShortDisplayName, floorTypeOfFloor} from "@/components/listing/building/building";
    import {mdiCursorDefault, mdiCursorMove, mdiPlusThick, mdiSetAll, mdiSetNone} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import {BuildingRenderer} from "@/components/listing/building/renderer/BuildingRenderer";
    import {Building, Floor, Listing, useDListingBuildingToolbarQuery} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {BuildingComponentCADTypeToIcon, RaycastableBuildingComponent, WallRoofPoint} from "@/components/listing/building/renderer/BuildingComponent";
    import {CadToolbarButton, CadToolbarDivider, CadToolbarItem} from "@/components/listing/building/cad-toolbar";
    import DListingBuildingToolbarItems from "@/components/listing/building/d-listing-building-toolbar-items.vue";
    import {TraversalBuildingComponent} from "@/components/listing/building/renderer/TraversableBuilding";
    import {WallOpeningTypeToIcon} from "@/model/building/WallOpeningType";
    import {SEMI_VISIBLE_FLOOR_COLORS} from "@/components/listing/building/renderer/BuildingRenderer2D";

    const props = defineProps<{
        isEmbedded: boolean
    }>()

    const renderer = inject(DBuildingRendererInjection)!
    const {t} = useI18n()

    //TODO: redundant mit d-listing-building-selection >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    const singleSelection = computed<Optional<RaycastableBuildingComponent<TraversalBuildingComponent> | WallRoofPoint>>(() => multiSelection.value.length === 1 ? multiSelection.value[0] : null)
    const multiSelection = renderer.selectionRaycaster.selection

    const singleSelectedBuilding = computed<Optional<Building>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "BUILDING") {
            return null
        }
        const building = selectedComponent.component.value
        if (building.__typename === "Building") {
            return building
        }
        return null
    })

    const singleSelectedFloor = computed<Optional<Floor>>(() => {
        const selectedComponent = singleSelection.value
        if (selectedComponent === null) {
            return null
        }
        if (selectedComponent.type !== "FLOOR") {
            return null
        }
        const floor = selectedComponent.component.value
        if (floor.__typename === "Floor") {
            return floor
        }
        return null
    })
    //TODO: redundant mit d-listing-building-selection <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

    const selectBuildingItem: CadToolbarButton = {
        id: "selectBuilding",
        type: "BUTTON",
        hotkey: "a",
        isVisible: toRef(true),
        isEnabled: computed<boolean>(() => renderer.mode.value === "DEFAULT" || renderer.mode.value === "POI_ADDING"),
        isActive: computed<boolean>(() => {
            switch (renderer.renderType) {
                case "2D":
                    return singleSelectedBuilding.value !== null
                case "3D":
                    return renderer.visibleFloorLevels.value.size >= renderer.building.value.floorLevels.length
                default:
                    console.error(`Unknown render type: ${renderer.renderType}`)
                    return false
            }
        }),
        icon: BuildingComponentCADTypeToIcon["BUILDING"],
        tooltip: t('listing.building.cad.toolbar.selection.building.tooltip'),
        canBeTriggeredWhileActive: true,
        onClick: () => {
            if (renderer.renderType === "2D") {
                onBuildingSelected()
                return
            }
            onToggleAllFloors()
        },
        menu: {
            isEnabled: computed<boolean>(() => renderer.mode.value === "DEFAULT"),
            isActive: computed<boolean>(() => singleSelectedBuilding.value !== null),
            onClick: onBuildingSelected,
        }
    }

    function createFloorItem(
        floor: Floor,
        floorMenuItem: Floor,
        undergroundFloorCount: number,
        fullFloorCount: number,
        isFirst: boolean,
        isLast: boolean,
    ): CadToolbarButton {
        return {
            id: `selectFloor${floor.level}`,
            type: "BUTTON",
            hotkey: floor.level.toString(),
            isVisible: toRef(true),
            isEnabled: computed<boolean>(() => renderer.mode.value === "DEFAULT" || renderer.mode.value === "POI_ADDING"),
            isActive: computed<boolean>(() => renderer.visibleFloorLevels.value.has(floor.level)),
            icon: BuildingComponentCADTypeToIcon["FLOOR"],
            iconColor: computed<Optional<string>>(() =>
                renderer.renderType === "2D" &&
                renderer.semiVisibleFloorLevels.value.has(floor.level) &&
                !renderer.visibleFloorLevels.value.has(floor.level)
                    ? SEMI_VISIBLE_FLOOR_COLORS[floor.level % SEMI_VISIBLE_FLOOR_COLORS.length]
                    : null
            ),
            tooltip: t(`listing.building.cad.floorLevelInfo.type.long.${floorMenuItem.levelInfo.levelType}`, {
                n: floorMenuItem.levelInfo.number
            }),
            nestedBadgeContent: floor.levelInfo
                ? floorLevelInfoToShortDisplayName(
                    floorMenuItem.levelInfo,
                    undergroundFloorCount,
                    fullFloorCount,
                    t
                )
                : floorMenuItem.level.toString(),
            canBeTriggeredWhileActive: true,
            onClick: () => onToggleFloor(floor),
            menu: {
                isEnabled: computed<boolean>(() => renderer.mode.value === "DEFAULT"),
                isActive: computed<boolean>(() => singleSelectedFloor.value !== null && singleSelectedFloor.value.id === floor.id),
                onClick: () => onFloorSelected(floor),
            }
        }
    }

    const modeDivider: CadToolbarDivider = {
        id: "modeDivider",
        type: "DIVIDER",
        isVisible: computed<boolean>(() => renderer.canEdit),
    }

    const modeSelectionItem: CadToolbarButton = {
        id: "modeSelection",
        type: "BUTTON",
        hotkey: "q",
        isVisible: computed<boolean>(() => renderer.canEdit),
        isActive: computed<boolean>(() => renderer.mode.value === "DEFAULT"),
        isEnabled: toRef(true),
        icon: mdiCursorDefault,
        tooltip: t('listing.building.cad.toolbar.mode.selection.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onDefaultModeSelected,
    }

    const modeDragAndDropSubItemSnapped: CadToolbarButton = {
        id: "modeDragAndDropSubItemGrouped",
        type: "BUTTON",
        hotkey: "g",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.mode.value === "DRAG_AND_DROP"),
        isEnabled: toRef(true),
        isActive: computed<boolean>(() => renderer.snappingManager.isGroupingEnabled.value),
        icon: mdiSetAll,//mdiVectorUnion,
        tooltip: t('listing.building.cad.toolbar.mode.dragAndDrop.subItem.grouped.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: () => {
            renderer.snappingManager.setGroupingEnabled(true)
        }
    }

    const modeDragAndDropSubItemUnsnapped: CadToolbarButton = {
        id: "modeDragAndDropSubItemUngrouped",
        type: "BUTTON",
        hotkey: "f",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.mode.value === "DRAG_AND_DROP"),
        isEnabled: toRef(true),
        isActive: computed<boolean>(() => !renderer.snappingManager.isGroupingEnabled.value),
        icon: mdiSetNone,//,mdiVectorDifference,
        tooltip: t('listing.building.cad.toolbar.mode.dragAndDrop.subItem.ungrouped.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: () => {
            renderer.snappingManager.setGroupingEnabled(false)
        }
    }

    const modeDragAndDropItem: CadToolbarButton = {
        id: "modeDragAndDrop",
        type: "BUTTON",
        hotkey: "w",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.renderType === '2D'),
        isActive: computed<boolean>(() => renderer.mode.value === "DRAG_AND_DROP"),
        isEnabled: toRef(true),
        icon: BuildingComponentCADTypeToIcon["WALL"],
        subIcon: mdiCursorMove,
        tooltip: t('listing.building.cad.toolbar.mode.dragAndDrop.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onDragAndDropModeSelected,
        subMenu: {
            items: [
                modeDragAndDropSubItemSnapped,
                modeDragAndDropSubItemUnsnapped,
            ]
        }
    }

    const modeWallCreationItem: CadToolbarButton = {
        id: "modeWallCreation",
        type: "BUTTON",
        hotkey: "e",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.renderType === '2D'),
        isActive: computed<boolean>(() => renderer.mode.value === "WALL_CREATION"),
        isEnabled: toRef(true),
        icon: BuildingComponentCADTypeToIcon["WALL"],
        subIcon: mdiPlusThick,
        tooltip: t('listing.building.cad.toolbar.mode.wallCreation.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onWallCreationModeSelected,
    }

    const modePoiAddingItem: CadToolbarButton = {
        id: "modePoiAdding",
        type: "BUTTON",
        hotkey: "r",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.renderType === '3D'),
        isActive: computed<boolean>(() => renderer.mode.value === "POI_ADDING"),
        isEnabled: toRef(true),
        icon: BuildingComponentCADTypeToIcon["POINT_OF_INTEREST"],
        subIcon: mdiPlusThick,
        tooltip: t('listing.building.cad.toolbar.mode.poiAdding.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onPoiAddingModeSelected,
    }

    const modeRoofAreaCreationItem: CadToolbarButton = {
        id: "modeRoofAreaCreation",
        type: "BUTTON",
        hotkey: "j",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.renderType === '3D'),
        isActive: computed<boolean>(() => renderer.mode.value === "ROOF_AREA_CREATION"),
        isEnabled: toRef(true),
        icon: BuildingComponentCADTypeToIcon["WALL_ROOF_POINT"],
        subIcon: mdiPlusThick,
        tooltip: t('listing.building.cad.toolbar.mode.roofAreaCreation.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onRoofAreaCreationModeSelected,
    }

    const modeOpeningCreationSubItemWindow: CadToolbarButton = {
        id: "modeOpeningCreationSubItemWindow",
        type: "BUTTON",
        hotkey: "a",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.mode.value === "OPENING_CREATION"),
        isEnabled: toRef(true),
        isActive: computed<boolean>(() => renderer.wallOpeningCreator?.openingType.value === "WINDOW"),
        icon: BuildingComponentCADTypeToIcon["WALL_OPENING_WINDOW"],
        tooltip: t('listing.building.cad.toolbar.mode.openingCreation.subItem.window.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: () => {
            const wallOpeningCreator = renderer.wallOpeningCreator
            if (wallOpeningCreator !== null) {
                wallOpeningCreator.openingType.value = "WINDOW"
            }
        }
    }

    const modeOpeningCreationSubItemDoor: CadToolbarButton = {
        id: "modeOpeningCreationSubItemDoor",
        type: "BUTTON",
        hotkey: "s",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.mode.value === "OPENING_CREATION"),
        isEnabled: toRef(true),
        isActive: computed<boolean>(() => renderer.wallOpeningCreator?.openingType.value === "DOOR"),
        icon: WallOpeningTypeToIcon["DOOR"],
        tooltip: t('listing.building.cad.toolbar.mode.openingCreation.subItem.door.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: () => {
            const wallOpeningCreator = renderer.wallOpeningCreator
            if (wallOpeningCreator !== null) {
                wallOpeningCreator.openingType.value = "DOOR"
            }
        }
    }

    const modeOpeningCreationSubItemOpening: CadToolbarButton = {
        id: "modeOpeningCreationSubItemOpening",
        type: "BUTTON",
        hotkey: "d",
        isVisible: computed<boolean>(() => renderer.canEdit && renderer.mode.value === "OPENING_CREATION"),
        isEnabled: toRef(true),
        isActive: computed<boolean>(() => renderer.wallOpeningCreator?.openingType.value === "OPENING"),
        icon: WallOpeningTypeToIcon["OPENING"],
        tooltip: t('listing.building.cad.toolbar.mode.openingCreation.subItem.opening.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: () => {
            const wallOpeningCreator = renderer.wallOpeningCreator
            if (wallOpeningCreator !== null) {
                wallOpeningCreator.openingType.value = "OPENING"
            }
        }
    }

    const modeOpeningCreationItem: CadToolbarButton = {
        id: "modeOpeningCreation",
        type: "BUTTON",
        hotkey: "t",
        isVisible: computed<boolean>(() => renderer.canEdit),
        isActive: computed<boolean>(() => renderer.mode.value === "OPENING_CREATION"),
        isEnabled: toRef(true),
        icon: BuildingComponentCADTypeToIcon["WALL_OPENING_OPENING"],
        subIcon: mdiPlusThick,
        tooltip: t('listing.building.cad.toolbar.mode.openingCreation.tooltip'),
        canBeTriggeredWhileActive: false,
        onClick: onOpeningCreationModeSelected,
        subMenu: {
            items: [
                modeOpeningCreationSubItemWindow,
                modeOpeningCreationSubItemDoor,
                modeOpeningCreationSubItemOpening,
            ]
        }
    }

    const {
        result: toolbarResult,
        refetch: refetch
    } = useDListingBuildingToolbarQuery({
        listingId: renderer.listingId,
    }, {
        fetchPolicy: "no-cache"
    })

    watch(() => renderer.building.value.floors.map(floor => floorTypeOfFloor(floor)?.toString() || ''), () => {
        refetch();
    });

    const toolbarItems = computed<readonly CadToolbarItem[]>(() => {
        const listing = toolbarResult.value?.listing as (Listing | undefined | null) ?? null
        if (listing === null || listing.building === null || listing.building === undefined) {
            return []
        }

        const items: CadToolbarItem[] = []

        if (!props.isEmbedded) {
            items.push(selectBuildingItem)
        }

        const sortedFloors = listing.building.floors.toSorted((a, b) => b.level - a.level)
        const undergroundFloors = listing.building.floors.filter(floor => floor.levelInfo.levelType === "UG").length
        const fullFloors = listing.building.floors.filter(floor => floor.levelInfo.levelType === "OG").length

        const building = renderer.building.value
        for (let i = 0; i < sortedFloors.length; ++i) {
            const floorMenuItem = sortedFloors[i]
            const floor = findBuildingFloorByLevel(building, floorMenuItem.level) //TODO: <<<<<<<<<<<<<<<<<<<<<WAS SOLL DIE UNTERSCHEIDUNG BRINGEN?!?!?!?!
            if (floor === null) {
                continue
            }
            items.push(createFloorItem(
                floor,
                floorMenuItem,
                undergroundFloors,
                fullFloors,
                i === 0,
                i === sortedFloors.length - 1
            ));
        }

        if (!props.isEmbedded) {
            items.push(modeDivider)
            items.push(modeSelectionItem)
            items.push(modeDragAndDropItem)
            items.push(modeWallCreationItem)
            items.push(modePoiAddingItem)
            items.push(modeRoofAreaCreationItem)
            items.push(modeOpeningCreationItem)
        }

        return items
    })
    const toolbarButtons = computed<readonly CadToolbarButton[]>(() => toolbarItems.value.filter(item => item.type === "BUTTON"))

    const onKeyDown = (event: KeyboardEvent) => { //implicit "this" binding
        if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
            return
        }
        if (renderer.showSelectionDetails.value) {
            return
        }
        for (const item of toolbarButtons.value) {
            if (item.hotkey === event.key && item.isEnabled.value && item.isVisible.value && (item.canBeTriggeredWhileActive || !item.isActive.value)) {
                item.onClick()
                break
            }
            for (const subItem of item.subMenu?.items || []) {
                if (subItem.type === "BUTTON" && subItem.hotkey === event.key && subItem.isEnabled.value && subItem.isVisible.value && (subItem.canBeTriggeredWhileActive || !subItem.isActive.value)) {
                    subItem.onClick()
                    break
                }
            }
        }
    }
    onMounted(() => {
        window.addEventListener("keydown", onKeyDown)
    })
    onUnmounted(() => {
        window.removeEventListener("keydown", onKeyDown)
    })

    function onBuildingSelected() {
        renderer.selectionRaycaster.select(BuildingRenderer.selectionEmitterIdForBuilding())
    }

    function onFloorSelected(floor: Floor) {
        renderer.selectionRaycaster.select(BuildingRenderer.selectionEmitterIdForFloor(floor.id))
    }

    function onToggleAllFloors() {
        renderer.clearSelection()

        if (renderer.visibleFloorLevels.value.size >= renderer.building.value.floorLevels.length) {
            renderer.visibleFloorLevels.value = new Set()
        } else {
            renderer.visibleFloorLevels.value = new Set(renderer.building.value.floorLevels)
        }
    }

    function onToggleFloor(floor: Floor) {
        renderer.clearSelection()

        switch (renderer.renderType) {
            case "2D":
                renderer.visibleFloorLevels.value = new Set([floor.level])
                break
            case "3D":
                if (renderer.visibleFloorLevels.value.has(floor.level)) {
                    renderer.visibleFloorLevels.value = new Set([...renderer.visibleFloorLevels.value].filter(level => level !== floor.level))
                } else {
                    renderer.visibleFloorLevels.value = new Set([...renderer.visibleFloorLevels.value, floor.level])
                }
                break
        }
    }

    function onDefaultModeSelected() {
        renderer.mode.value = "DEFAULT"
    }

    function onDragAndDropModeSelected() {
        renderer.mode.value = "DRAG_AND_DROP"
    }

    function onWallCreationModeSelected() {
        renderer.isEvebiModeEnabled.value = false
        renderer.mode.value = "WALL_CREATION"
    }

    function onPoiAddingModeSelected() {
        renderer.isEvebiModeEnabled.value = false
        renderer.mode.value = "POI_ADDING"
    }

    function onRoofAreaCreationModeSelected() {
        renderer.isEvebiModeEnabled.value = false
        renderer.mode.value = "ROOF_AREA_CREATION"
    }

    function onOpeningCreationModeSelected() {
        renderer.isEvebiModeEnabled.value = false
        renderer.mode.value = "OPENING_CREATION"
    }
</script>

<style scoped>
    .toolbarWrapper {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
        pointer-events: none;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-items: center;
    }

    .toolbarArea {
        max-height: calc(100% - 16px * 2);
        overflow-y: auto;
        display: flex;
        position: relative;
    }
</style>