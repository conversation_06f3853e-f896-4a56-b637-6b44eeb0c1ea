<template>
    <d-btn :icon="smAndDown ? icon : undefined"
           :on-primary="onPrimary"
           :prepend-icon="smAndDown ? undefined : icon"
           :text="smAndDown ? undefined : text"
           :to="route"
           :variant="smAndDown ? 'text' : 'tonal'"
           size="large"
           type="default"/>
</template>

<script lang="ts"
        setup>
    import {useDisplay} from "vuetify";
    import {RouteLocationRaw} from "vue-router";
    import {mdiChevronLeft} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";

    defineProps<{
        text: string,
        route: RouteLocationRaw,
        onPrimary?: boolean
    }>()

    const icon = mdiChevronLeft
    const {smAndDown} = useDisplay();
</script>

<style scoped>
</style>