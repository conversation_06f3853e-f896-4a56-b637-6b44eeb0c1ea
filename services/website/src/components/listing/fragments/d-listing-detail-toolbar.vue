<template>
    <v-toolbar :color="themeColorBackgroundSurface"
               :elevation="1"
               class="toolbar">
        <slot/>
    </v-toolbar>
</template>

<script lang="ts"
        setup>
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";

    const {
        themeColorBackgroundSurface
    } = useDoorbitTheme()
</script>

<style lang="scss"
       scoped>
    .toolbar {
        position: fixed;
        z-index: 1004;
        height: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top)) !important;
    }

    :deep(.v-toolbar__content) {
        margin-top: env(safe-area-inset-top);
    }
</style>