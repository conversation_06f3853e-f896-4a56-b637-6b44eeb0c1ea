<template>
    <d-share-button :hide-text="smAndDown || hideText"
                    :share-data="shareData"
                    :size="size"
                    tooltip-location="left"
                    variant="text"/>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import {urlToFile} from "@/utility/file";
    import DShareButton from "@/components/fragment/share/d-share-button.vue";
    import {Optional} from "@/model/Optional";
    import {computed, inject, shallowRef, watch} from "vue";
    import {useDisplay} from "vuetify";
    import {Size} from "@/model/Size";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {WHITE_LABEL_CONTEXT} from "@/service/white-label/white-label-context";

    const props = defineProps<{
        hideText?: boolean
        imageUrl?: string
        size: Size
    }>()

    const listingId = inject(LListingIdInjection)!

    const {t} = useI18n()
    const {smAndDown} = useDisplay();

    const shareData = computed<ShareData>(() => ({
        title: document.title,
        text: t(`components.listing.actions.shareButtonMessage.${WHITE_LABEL_CONTEXT.id}`),
        url: `${window.location.origin}/l/${listingId.value}`,
        files: file.value === null ? [] : [file.value]
    }))

    const file = shallowRef<Optional<File>>(null)
    watch(() => props.imageUrl, imageURL => {
        if (imageURL === undefined) {
            file.value = null
            return
        }
        urlToFile(imageURL).then(f => {
            file.value = f
        })
    }, {
        immediate: true
    })
</script>

<style scoped>
</style>