<template>
    <div class="w-100 h-100 position-relative">
        <router-view v-if="listing !== null && flowConfig !== null"/>

        <d-loading v-else-if="isListingLoading"/>

        <div v-else-if="hasListingLoadingError"
             class="wrapper pa-4">
            <div class="content">
                <d-alert class="text-center text ma-4"
                         type="error">
                    {{ t('listing.loadingError.message') }}

                    <div class="text-center w-100">
                        <v-btn :text="t('listing.loadingError.backButton')"
                               :to="{name: 'root'}"
                               class="mt-4"
                               color="on-error"
                               variant="text"/>
                    </div>
                </d-alert>
            </div>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, onMounted, onUnmounted, provide, Ref, ref, shallowRef, toRef, watch} from "vue";
    import {DListingLoadFlowConfigQueryVariables, DListingLoadListingQueryVariables, FlowConfig, Listing, useDListingLoadFlowConfigLazyQuery, useDListingLoadListingQuery} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {areFlowDataEqual, createEmptyMutableFlowData, MutableFlowData} from "@/model/listing/FlowData";
    import {LFlowDataInjection, LListingIdInjection, LListingInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import {FCFlowConfigIdInjection, FCFlowConfigInjection} from "@/components/flow-config/FlowConfigInjectionKey";
    import {CONFIG} from "@/config";
    import {needsRefresh} from "@/components/flow-config/use-flow-config";
    import {useRoute} from "vue-router";
    import {IS_DEVELOPMENT} from "@/utility/environment";
    import {useListingListFields} from "@/components/listing/list/useListingListFields";
    import {updatePageTitle} from "@/utility/page-title";
    import {useI18n} from "vue-i18n";
    import DLoading from "@/components/fragment/d-loading.vue";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {createListingField} from "@/service/native-app/native-app-current-listing";
    import {createFlowConfigField} from "@/service/flow-config/flow-config-service";

    //####################################################################################################################################
    //ALMOST ALL NEW CHANGES IN THIS FILE SHOULD BE MADE IN d-native-app-room-data.vue, d-native-app-poi-data.vue AS WELL >>>>>>>>>>>>>>>>
    //####################################################################################################################################

    const props = defineProps<{
        listingId: string,
        refetch?: Ref<boolean>
    }>()

    const loadListingVariables = computed<DListingLoadListingQueryVariables>(() => ({
        id: props.listingId,
    }))

    const {t} = useI18n()
    const hasListingLoadingError = shallowRef<boolean>(false)

    const {
        result: loadListingResult,
        loading: isListingLoading,
        onError: onLoadListingError,
        refetch: refetchListing,
    } = useDListingLoadListingQuery(loadListingVariables)

    const route = useRoute()
    const rawListing = computed<Optional<Listing>>(() => loadListingResult.value?.listing as (Listing | undefined) ?? null)
    const listing = createListingField(toRef(() => props.listingId), rawListing)
    const flowConfigId = computed<Optional<string>>(() => listing.value?.flowConfigId ?? null)

    const {
        listingTitle,
        extId,
    } = useListingListFields(listing)

    watch([listingTitle, extId], ([listingTitle, extId]) => {
        const title = listingTitle ?? extId ?? undefined
        updatePageTitle(t, title)
    }, {
        immediate: true
    })

    const loadFlowConfigVariables = computed<DListingLoadFlowConfigQueryVariables>(() => ({
        id: flowConfigId.value ?? "",
    }))

    onLoadListingError((error) => {
        console.warn("Error while loading listing.", error)
        hasListingLoadingError.value = true
    });

    const {
        result: loadFlowConfigResult,
        load: loadFlowConfig,
        refetch: refetchFlowConfig,
    } = useDListingLoadFlowConfigLazyQuery(loadFlowConfigVariables);

    const rawFlowConfig = computed<Optional<FlowConfig>>(() => loadFlowConfigResult.value?.flowConfig as (FlowConfig | undefined) ?? null)
    const flowConfig = createFlowConfigField(flowConfigId, rawFlowConfig)
    const flowData = ref<MutableFlowData>(createEmptyMutableFlowData()) //ref okay

    let lastRouterPath = route.path
    watch(route, async route => { //TODO: <<<<<<<<<<<<<<<<<<<<<< gefährlich, dass hier hardcoded "/model-edit/edit/" drin ist ...
        if (
            (route.path.includes('/view/') && lastRouterPath.includes('/edit/')) ||
            (route.path.includes('/edit/') && lastRouterPath.includes('/model-edit/edit/'))
        ) {
            if (IS_DEVELOPMENT) {
                console.log("Navigated from edit to view or model-edit. Refetching listing.")
            }

            await refetchListing()
        }
        lastRouterPath = route.path
    });

    watch(flowConfigId, async flowConfigId => {
        if (flowConfigId === null) {
            return
        }
        try {
            const loadResult = await loadFlowConfig()
            if (loadResult === false) {
                await refetchFlowConfig()
            }
        } catch (e) {
            console.warn("Error while loading flow config.", e)
        }
    }, {
        immediate: true
    })

    function resetTimeout() {
        if (timeoutId !== null) {
            clearTimeout(timeoutId)
            timeoutId = null
        }
    }

    let timeoutId: Optional<number> = null

    onMounted(() => {
        resetTimeout()
        timeoutId = setTimeout(() => {
            if (flowConfig.value !== null) {
                if (needsRefresh(flowConfig.value)) {
                    refetchFlowConfig()
                }
            }
        }, CONFIG.APOLLO_REFETCH_FLOW_CONFIG_AFTER_LOAD_MS);
    })

    onUnmounted(() => {
        resetTimeout()
    })

    provide(LListingInjection, toRef(() => listing.value!))
    provide(LListingIdInjection, toRef(() => props.listingId))
    provide(LFlowDataInjection, flowData)
    provide(LMutableFlowDataInjection, flowData)

    provide(FCFlowConfigInjection, toRef(() => flowConfig.value!))
    provide(FCFlowConfigIdInjection, toRef(() => flowConfig.value!.id))

    watch(listing, listing => {
        if (listing === null) {
            flowData.value = createEmptyMutableFlowData()
        } else {
            const newFlowData = createEmptyMutableFlowData()
            newFlowData.setFromInput(listing.fieldValues)

            if (areFlowDataEqual(newFlowData, flowData.value, false)) {
                return
            }
            flowData.value = newFlowData
        }
    }, {
        deep: true,
        immediate: true
    })

    //####################################################################################################################################
    //ALMOST ALL NEW CHANGES IN THIS FILE SHOULD BE MADE IN d-native-app-room-data.vue, d-native-app-poi-data.vue AS WELL <<<<<<<<<<<<<<<<
    //####################################################################################################################################
</script>

<style scoped>
    .wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        justify-items: center;
        align-items: center;
        flex-direction: row;
    }

    .content {
        text-align: center;
        margin: auto;
    }

    .text {
        max-width: 550px;
    }
</style>