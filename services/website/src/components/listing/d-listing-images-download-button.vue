<template>
    <d-btn :disabled="INTERNET_SPEED < InternetSpeed.MEDIUM"
           :icon="smAndDown ? mdiDownload : undefined"
           :prepend-icon="smAndDown ? undefined : mdiDownload"
           :size="smAndDown ? 'large' : undefined"
           :text="smAndDown ? undefined : t('components.listing.detail.cta.downloadImages')"
           :to="{
                name: 'listingImagesDownload',
                params: {
                    id: listingId
                }
           }"
           type="tertiary"
           variant="elevated"/>
</template>

<script lang="ts"
        setup>
    import {mdiDownload} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import {useDisplay} from "vuetify";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {inject} from "vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";

    const listingId = inject(LListingIdInjection)!

    const {t} = useI18n()
    const {smAndDown} = useDisplay()
</script>

<style scoped>
</style>