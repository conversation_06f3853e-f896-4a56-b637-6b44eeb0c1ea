<template>
    <v-menu v-model="isMenuVisible"
            location="bottom center"
            min-width="0"
            offset="5"
            transition="slide-y-transition">
        <template #activator="{props: menuProps}">
            <slot :props="menuProps"/>
        </template>

        <d-card :title="showQuestion ? t('delete.confirmationHeading') : undefined">
            <slot name="content"/>

            <template #actions>
                <v-layout :class="{
                    'justify-space-between': $slots.content === undefined,
                    'justify-center': $slots.content !== undefined
                }">
                    <d-btn :class="{'me-2': $slots.content !== undefined}"
                           :icon="mdiCheck"
                           size="default"
                           type="success"
                           variant="text"
                           @click="onConfirmClicked"/>
                    <d-btn :class="{'ms-2': $slots.content !== undefined}"
                           :icon="mdiClose"
                           size="default"
                           type="error"
                           variant="text"/>
                </v-layout>
            </template>
        </d-card>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {mdiCheck, mdiClose} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {shallowRef, watchEffect} from "vue";

    const props = defineProps<{
        showQuestion?: boolean
        disabled?: boolean
    }>()

    const {t} = useI18n();
    const isMenuVisible = shallowRef<boolean>(false)

    const emit = defineEmits(['onconfirm']);

    function onConfirmClicked() {
        emit('onconfirm');
    }

    watchEffect(() => {
        if (isMenuVisible.value && props.disabled) {
            isMenuVisible.value = false
            onConfirmClicked()
        }
    });
</script>

<style scoped>
    :deep(.v-card-title h4) {
        margin-top: 0 !important;
    }

    :deep(.v-card-item) {
        padding-bottom: 0 !important;
    }
</style>