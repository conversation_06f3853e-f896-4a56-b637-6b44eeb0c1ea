<template>
    <v-menu v-model="isMenuVisible"
            location="bottom center">
        <template #activator="{ props: menuProps }">
            <template v-if="hideText">
                <d-tooltip :location="tooltipLocation"
                           type="default">
                    <template #activator="{ props: tooltipProps }">
                        <d-btn :icon="icon"
                               :size="buttonSize"
                               :variant="variant"
                               type="default"
                               v-bind="{...tooltipProps, ...menuProps}"
                               @click="share"/>
                    </template>

                    {{ t('components.shareButton.title') }}
                </d-tooltip>
            </template>
            <d-btn v-else
                   :prepend-icon="icon"
                   :size="buttonSize"
                   :text="t('components.shareButton.title')"
                   :variant="variant"
                   type="default"
                   v-bind="menuProps"
                   @click="share"/>
        </template>
        <d-card v-if="showMenu && message">
            <d-list>
                <d-list-item v-if="canUseClipboard"
                             :prepend-icon="mdiContentCopy"
                             :title="t('components.shareButton.copy')"
                             @click="copyToClipboard"/>
                <d-list-item :prepend-icon="mdiWhatsapp"
                             title="WhatsApp"
                             @click="shareToWhatsApp"/>
            </d-list>
        </d-card>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {computed, shallowRef} from "vue";
    import {useI18n} from "vue-i18n";
    import {mdiContentCopy, mdiShareVariant, mdiWhatsapp} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {Size} from "@/model/Size";
    import {DBtnSize, DBtnVariant} from "@/adapter/vuetify/theme/components/button/d-btn";
    import {Optional} from "@/model/Optional";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DTooltip from "@/adapter/vuetify/theme/components/d-tooltip.vue";

    const props = defineProps<{
        shareData: ShareData
        size: Size //preparation for new sizes
        hideText?: boolean
        variant?: DBtnVariant
        tooltipLocation?: 'left'
    }>()

    //TODO: remove it when button was converted to new size system
    const buttonSize = computed<DBtnSize>(() => {
        switch (props.size) {
            case "S":
                return "small"
            case "M":
                return "default"
            case "L":
                return "large"
            default:
                return "default"
        }
    })

    const {t} = useI18n()
    const icon = mdiShareVariant
    const isMenuVisible = shallowRef<boolean>(false)
    const canUseShareAPI = computed<boolean>(() => Boolean(navigator.canShare) && navigator.canShare(props.shareData))
    const canUseClipboard = computed<boolean>(() => Boolean(navigator.clipboard?.writeText))
    const message = computed<Optional<string>>(() => {
        if (props.shareData.text && props.shareData.url) {
            return `${props.shareData.text}\n${props.shareData.url}`
        }
        return props.shareData.url ?? props.shareData.text ?? null
    })

    //show it not for shareAPI or WhatsApp only
    const showMenu = computed<boolean>(() => !canUseShareAPI.value && canUseClipboard.value)

    function share() {
        if (showMenu.value) {
            return
        }
        if (canUseShareAPI.value) {
            shareViaAPI()
        } else {
            shareToWhatsApp()
        }
        isMenuVisible.value = false;
    }

    function shareViaAPI() {
        if (!canUseShareAPI.value) {
            return;
        }
        navigator.share(props.shareData)
    }

    function copyToClipboard() {
        const text = message.value
        if (!canUseClipboard.value || text === null) {
            return;
        }
        navigator.clipboard.writeText(text)
    }

    //https://faq.whatsapp.com/5913398998672934/?locale=en_US
    function shareToWhatsApp() {
        const text = message.value
        if (text === null) {
            return;
        }
        const urlEncodedText = encodeURIComponent(text)
        const url = `https://api.whatsapp.com/send?text=${urlEncodedText}` //TODO: wa.me?text für IOS einbauen
        window.open(url, "_blank")
    }
</script>

<style scoped>
</style>