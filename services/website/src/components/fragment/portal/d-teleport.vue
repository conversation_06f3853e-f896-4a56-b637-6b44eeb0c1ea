<template>
    <template v-if="isPortalReady">
        <teleport :disabled="disabled"
                  :to="`#${toPortal}`">
            <slot/>
        </teleport>
    </template>
</template>

<script lang="ts"
        setup>
    import {Portal} from "@/components/fragment/portal/Portal";
    import {nextTick, onMounted, shallowRef} from "vue";

    defineProps<{
        toPortal: Portal
        disabled?: boolean
    }>();

    const isPortalReady = shallowRef<boolean>(false)

    onMounted(() => {
        nextTick(() => { //this will be executed after the next DOM update cycle. It's needed to wait for the teleport portal to be rendered
            isPortalReady.value = true
        })
    })
</script>

<style scoped>
</style>