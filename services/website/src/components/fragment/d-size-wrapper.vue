<template>
    <div ref="wrapper"
         class="w-100 h-100">
        <slot :height="height"
              :width="width"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {useElementSize} from "@vueuse/core";
    import {shallowRef} from "vue";
    import {Optional} from "@/model/Optional";

    const wrapper = shallowRef<Optional<HTMLDivElement>>(null)

    const {
        width,
        height
    } = useElementSize(wrapper)
</script>

<style scoped>
</style>