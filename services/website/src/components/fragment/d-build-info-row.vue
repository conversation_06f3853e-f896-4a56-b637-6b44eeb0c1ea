<template>
    <v-row justify="center">
        <v-col class="text-caption mt-6 mb-4 text-center"
               cols="auto"
               style="font-size: 0.65rem !important;">
            <a class="releaseNotesLink"
               href="https://doorbit.com/de/docs/blog/"
               target="_blank">Release Notes</a>

            <div style="opacity: 0.25;">
                {{ BUILD_VERSION }} — {{ BUILD_DATE.format('DD.MM.YYYY, HH:mm:ss') }}<br>
                SW: {{ SERVICE_WORKER_MANAGER.state.value }}, {{ SERVICE_WORKER_MANAGER.hasUpdate.value }}<br>
                BFFMQ: {{ LS__BFF_OFFLINE_MESSAGES_QUEUE.state.value.length }}
            </div>
        </v-col>
    </v-row>
</template>

<script lang="ts"
        setup>
    import {BUILD_DATE, BUILD_VERSION} from "@/utility/environment";
    import {SERVICE_WORKER_MANAGER} from "@/service/pwa/service-worker";
    import {LS__BFF_OFFLINE_MESSAGES_QUEUE} from "@/service/local-storage/local-storage";
</script>

<style scoped>
    .releaseNotesLink {
        color: rgb(var(--v-theme-d-text-tertiary)) !important;
        opacity: 0.75;
    }
</style>