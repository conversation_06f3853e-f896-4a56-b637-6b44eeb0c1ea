<template>
    <v-bottom-navigation :active="route.meta.hideNativeBottomNavigation == null || route.meta.hideNativeBottomNavigation !== true"
                         :bg-color="themeColorBackgroundSurface"
                         class="bottomNavigation overflow-x-auto"
                         grow>
        <d-progress-linear v-if="loading"
                           class="position-absolute"
                           height="1"
                           indeterminate
                           type="default"/>

        <slot name="prepend"/>

        <v-btn v-for="item in items"
               :key="item.id"
               :to="item.route"
               class="pa-0">
            <v-icon :icon="item.icon"/>

            <span class="ma-1 pa-0 text-center text-caption">{{ item.label }}</span>
        </v-btn>
    </v-bottom-navigation>
</template>

<script lang="ts"
        setup>
    import {useRoute} from 'vue-router';
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {DBottomNavigationItem} from "@/components/fragment/d-bottom-navigation";
    import DProgressLinear from "@/adapter/vuetify/theme/components/progress/d-progress-linear.vue";

    // Defines the props
    defineProps<{
        items: readonly DBottomNavigationItem[]
        loading?: boolean
    }>()

    const route = useRoute();

    const {
        themeColorBackgroundSurface,
    } = useDoorbitTheme()
</script>

<style lang="scss"
       scoped>
    .bottomNavigation {
        height: calc(var(--v-d-native-app-bottom-bar-height) + env(safe-area-inset-bottom)) !important;
    }

    :deep(.v-bottom-navigation__content) {
        margin-bottom: env(safe-area-inset-bottom);
        min-width: 100%;
        width: auto;
    }
</style>