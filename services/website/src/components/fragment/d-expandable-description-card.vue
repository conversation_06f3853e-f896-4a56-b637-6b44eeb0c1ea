<template>
    <v-card
        class="bg-transparent"
        variant="flat">
        <div ref="displayedDescription"
             :class="{ 'expanded': descriptionExpanded }"
             class="description text-body-1">
            {{ escapedDescription }}
        </div>
        <v-card-actions v-if="expandable"
                        class="mt-2 pa-0">
            <d-btn :append-icon="descriptionExpanded ? mdiChevronUp : mdiChevronDown"
                   :text="descriptionExpanded ? t('components.listing.objectDescription.showLess') : t('components.listing.objectDescription.showMore')"
                   type="tertiary"
                   variant="text"
                   @click="descriptionExpanded = !descriptionExpanded"/>
        </v-card-actions>
    </v-card>
</template>

<script lang="ts"
        setup>
    import {computed, shallowRef} from "vue";
    import {useI18n} from "vue-i18n";
    import {mdiChevronDown, mdiChevronUp} from "@mdi/js";
    import {Optional} from "@/model/Optional";
    import {useElementSize} from "@vueuse/core";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";

    const {t} = useI18n();
    const props = defineProps<{
        description: string,
    }>()

    const descriptionExpanded = shallowRef<boolean>(false);
    const displayedDescription = shallowRef<Optional<HTMLElement>>(null)
    const {height: descriptionHeight} = useElementSize(displayedDescription)

    const domParser = new DOMParser();  // TODO: wird benötigt, da descriptions aktuell nicht korrekt geparsed werden
    const escapedDescription = computed<string>(() => domParser.parseFromString(props.description ?? '', "text/html").documentElement.textContent ?? ''); // TODO deprecated

    const expandable = computed<boolean>(() => {
        return props.description != null && descriptionHeight.value >= 160;
    })
</script>

<style scoped>
    .description {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 8;
        line-clamp: 8;
        -webkit-box-orient: vertical;
        word-break: normal;
        white-space: pre-line;
        line-height: 1.25em;
    }

    .description.expanded {
        -webkit-line-clamp: initial;
        line-clamp: initial;
    }
</style>