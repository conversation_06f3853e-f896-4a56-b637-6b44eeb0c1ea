<template>
    <v-hover>
        <template #default="{isHovering, props: hoverProps}">
            <div v-click-outside="onClickOutside"
                 v-touch="{
              left: xs ? undefined : onSwipeLeft,
              right: xs ? undefined : onSwipeRight
          }"
                 class="swipeMenuWrapper"
                 v-bind="hoverProps">
                <div class="content">
                    <slot/>
                </div>
                <div :class="{active: isMenuVisible || isHovering}"
                     :style="{'background': themeColorBackgroundTertiary}"
                     class="contentOverlay"
                     @click="onToggleMenu"/>
                <div class="swipeMenuBox"
                     @click="onToggleMenu">
                    <v-slide-x-reverse-transition>
                        <div v-if="isMenuVisible || isHovering"
                             class="swipeMenu">
                            <div :class="{isOdd: items.length % 2 !== 0}"
                                 class="swipeMenuButtons">
                                <div v-for="(item, index) in items"
                                     :key="index"
                                     class="swipeMenuButton">
                                    <d-btn :to="item.to"
                                           type="default"
                                           variant="elevated"
                                           @click.stop.prevent="() => item.action?.()">
                                        <v-layout class="flex-column align-center justify-center">
                                            <v-icon :icon="item.icon"
                                                    class="mb-2 mt-5"
                                                    size="32"/>
                                            <span class="text-caption">{{ item.label }}</span>
                                        </v-layout>
                                    </d-btn>
                                </div>
                            </div>
                        </div>
                    </v-slide-x-reverse-transition>
                </div>
            </div>
        </template>
    </v-hover>
</template>

<script lang="ts"
        setup>
    import {shallowRef} from "vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {useDisplay} from "vuetify";
    import {SwipeMenuItem} from "@/components/fragment/swipe-menu/SwipeMenuItem";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";

    defineProps<{
        items: SwipeMenuItem[]
    }>()

    const {
        themeColorBackgroundTertiary
    } = useDoorbitTheme()

    const isMenuVisible = shallowRef<boolean>(false);
    const {xs} = useDisplay()

    function onToggleMenu() {
        if (xs.value) {
            isMenuVisible.value = !isMenuVisible.value;
        }
    }

    function onClickOutside() {
        isMenuVisible.value = false;
    }

    function onSwipeLeft() {
        isMenuVisible.value = true;
    }

    function onSwipeRight() {
        isMenuVisible.value = false;
    }
</script>

<style scoped>
    .swipeMenuWrapper {
        position: relative;
    }

    .swipeMenuWrapper:has(.contentOverlay.active) .content {
        filter: blur(2px);
    }

    .content {
        position: relative;
        z-index: 1;
    }

    .contentOverlay {
        position: absolute;
        height: 100%;
        width: 100%;
        opacity: 0;
        top: 0;
        left: 0;
        z-index: 2;
        cursor: pointer;
        border-radius: 8px;
    }

    .contentOverlay.active {
        opacity: 0.4;
    }

    .swipeMenuBox {
        pointer-events: none;
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        z-index: 3;
        overflow: hidden;
        border-radius: 8px;
    }

    .swipeMenu {
        pointer-events: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
    }

    .swipeMenuButtons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .swipeMenuButton > :deep(*) {
        height: 100px;
        width: 100px;
    }
</style>