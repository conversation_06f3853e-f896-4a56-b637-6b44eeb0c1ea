<template>
    <div class="wrapper pa-4">
        <div class="content">
            <d-card class="card pa-4"
                    rounded="circle"
                    variant="elevated">
                <d-progress-circular :size="mdAndUp ? 100 : 75"
                                     indeterminate
                                     width="6"/>
            </d-card>

            <d-h3 v-if="text"
                  class="text-center text ma-4">
                {{ text }}
            </d-h3>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import DProgressCircular from "@/adapter/vuetify/theme/components/progress/d-progress-circular.vue";
    import {useDisplay} from "vuetify";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DH3 from "@/adapter/vuetify/theme/components/text/headline/d-h3.vue";

    defineProps<{
        text?: string
    }>()

    const {mdAndUp} = useDisplay()
</script>

<style scoped>
    .wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        justify-items: center;
        align-items: center;
        flex-direction: row;
    }

    .content {
        text-align: center;
        margin: auto;
    }

    .card {
        display: inline-block;
    }

    .text {
        max-width: 550px;
    }
</style>
