<template>
    <div id="consent-management"
         class="d-none"/>
</template>

<script lang="ts"
        setup>
    import {klaroJsConfig} from "@/components/consent-management/klaro-js.config";
    import * as Klaro from 'klaro';
    import {useRoute} from "vue-router";
    import {IS_NATIVE_APP} from "@/service/native-app/native-app";
    import {watchEffect} from "vue";
    import {IS_EMBEDDED} from "@/service/embedded-view/embedded-view";
    import {LS__LANGUAGE} from "@/service/local-storage/local-storage";

    const route = useRoute();

    klaroJsConfig.lang = LS__LANGUAGE.state.value === 'de' ? 'de' : 'en';

    if (IS_NATIVE_APP) {
        const cookieValue = {"Klaro.js": true, "Keycloak": true, "google-tag-manager": true, "google-analytics": true}
        document.cookie = `klaro=${encodeURIComponent(JSON.stringify(cookieValue))}`
    }

    watchEffect(() => {
        if (!IS_EMBEDDED) {
            klaroJsConfig.mustConsent = route.name !== 'privacyPolicy'
            Klaro.setup(klaroJsConfig)
        }
    })
</script>

<style lang="scss">
    @import 'vuetify/settings';

    #klaro {
        color: $application-color;
        font-family: $body-font-family;
        font-size: $font-size-root;

        .cookie-modal, .cookie-notice {
            z-index: 9999;
        }

        .cookie-modal a {
            color: rgb(var(--v-theme-secondary));
        }

        .cm-modal {
            background-color: rgb(var(--v-theme-cookie-modal-background));
        }

        .cookie-notice p, .cm-modal h1, .cm-modal p {
            color: rgb(var(--v-theme-on-cookie-modal-background));
        }

        .cm-required, .cm-list-title {
            color: rgb(var(--v-theme-on-cookie-modal-background));
        }

        .cm-btn {
            height: $button-height;
            background: rgb(var(--v-theme-cookie-modal-secondary-button-background));
            color: rgb(var(--v-theme-on-cookie-modal-secondary-button-background));
            font-size: $button-font-size;
            font-weight: $button-font-weight;
            text-transform: $button-text-transform;
            letter-spacing: $button-text-transform;
            border-radius: 24px;
            padding-left: 16px;
            padding-right: 16px;
        }

        .cm-btn-accept-all {
            background: rgb(var(--v-theme-secondary));
            color: rgb(var(--v-theme-on-secondary));
        }

        .slider {
            box-shadow: none;
        }

        .cm-list-input:not(:checked) + .cm-list-label .slider:before {
            background-color: rgb(var(--v-theme-cookie-modal-slider-button))
        }

        .cm-list-input:checked + .cm-list-label .slider {
            background-color: rgb(var(--v-theme-secondary));
        }

        .cm-list-description, .purposes {
            color: rgb(var(--v-theme-cookie-modal-description))
        }

        @media(max-width: 600px) {
            .cm-footer-buttons {
                flex-flow: column-reverse;
                gap: 16px;
            }
        }

    }
</style>