<template>
    <d-card>
        <v-expansion-panels>
            <v-expansion-panel :value="false"
                               title="Raw">
                <template #text>
                    <pre>
                        {{ rawData }}
                    </pre>
                </template>
            </v-expansion-panel>
        </v-expansion-panels>
    </d-card>
</template>

<script lang="ts"
        setup>
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";

    defineProps<{
        rawData: any
    }>()
</script>

<style scoped>
</style>