<template>
    <div class="w-100 h-100">
        <div :class="{'pa-0': !mdAndUp, 'h-100': fullHeight, 'py-0': noYPadding, wider}"
             :style="{'max-width': ignoreMaxWidth ? 'none' : undefined}"
             class="container">
            <slot/>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {useDisplay} from "vuetify";

    defineProps<{
        fullHeight?: boolean
        noYPadding?: boolean
        ignoreMaxWidth?: boolean
        wider?: boolean
    }>()

    const {mdAndUp} = useDisplay()
</script>

<style scoped>
    .container {
        --padding: 32px;
        max-width: calc(800px + 2 * var(--padding));
        margin: 0 auto;
        padding: var(--padding);
        min-height: 100%;
        box-sizing: border-box;
    }

    .container.wider {
        max-width: calc(1000px + 2 * var(--padding));
    }
</style>