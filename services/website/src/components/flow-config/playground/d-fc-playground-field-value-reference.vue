<template>
    <d-card>
        <v-table class="bg-transparent"
                 density="compact">
            <tbody>
                <tr>
                    <th>Field</th>
                    <td>
                        <d-fc-playground-field :field="fieldValueReference.field"/>
                    </td>
                </tr>
                <tr>
                    <th>Unit</th>
                    <td>{{ fieldValueReference.unit }}</td>
                </tr>
                <tr>
                    <th>Possible Enum Values</th>
                    <td>
                        <ul v-if="fieldValueReference.possibleEnumValues.length > 0">
                            <li v-for="possibleEnumValue in fieldValueReference.possibleEnumValues"
                                :key="possibleEnumValue.value">
                                {{ possibleEnumValue.value }}

                                <d-fc-playground-translations :translations="possibleEnumValue.label"/>
                            </li>
                        </ul>
                    </td>
                </tr>
            </tbody>
        </v-table>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {FlowConfigFieldValueReference} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";

    defineProps<{
        fieldValueReference: FlowConfigFieldValueReference
    }>()
</script>

<style scoped>
    li + li {
        margin-top: 32px;
    }
</style>