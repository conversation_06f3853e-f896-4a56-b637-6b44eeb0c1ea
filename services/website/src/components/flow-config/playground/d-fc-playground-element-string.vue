<template>
    <d-fc-playground-element :element="stringElement"/>

    <tr>
        <th>Field</th>
        <td>
            <d-fc-playground-field :field="stringElement.field"/>
        </td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ stringElement.type }}</td>
    </tr>
    <tr>
        <th>Length Minimum</th>
        <td>{{ stringElement.lengthMinimum }}</td>
    </tr>
    <tr>
        <th>Length Maximum</th>
        <td>{{ stringElement.lengthMaximum }}</td>
    </tr>
    <tr>
        <th>Rows</th>
        <td>{{ stringElement.rows }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {StringUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";

    defineProps<{
        stringElement: StringUiElement
    }>()
</script>

<style scoped>
</style>