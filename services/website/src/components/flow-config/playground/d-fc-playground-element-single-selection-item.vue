<template>
    <d-card>
        <v-table class="bg-transparent"
                 density="compact">
            <tbody>
                <!-- label + hint handled inside -->
                <d-fc-playground-element :element="singleSelectionElementItem"/>

                <tr>
                    <th>Image URL</th>
                    <td>{{ singleSelectionElementItem.imageURL }}</td>
                </tr>
                <tr>
                    <th>Icon</th>
                    <td>
                        <d-fc-playground-icon :icon-name="singleSelectionElementItem.icon"/>
                    </td>
                </tr>
                <tr>
                    <th>Value</th>
                    <td>{{ singleSelectionElementItem.value }}</td>
                </tr>
            </tbody>
        </v-table>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {SingleSelectionUiElementItem} from "@/adapter/graphql/generated/graphql";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundIcon from "@/components/flow-config/playground/d-fc-playground-icon.vue";

    defineProps<{
        singleSelectionElementItem: SingleSelectionUiElementItem
    }>()
</script>

<style scoped>
</style>