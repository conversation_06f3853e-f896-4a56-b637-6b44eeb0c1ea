<template>
    <d-card>
        <v-table class="bg-transparent"
                 density="compact">
            <tbody>
                <tr>
                    <th>Value</th>
                    <td>{{ singleSelectionElementItemOther.value }}</td>
                </tr>
                <tr>
                    <th>Element</th>
                    <td class="py-2">
                        <d-card>
                            <v-table class="bg-transparent"
                                     density="compact">
                                <tbody>
                                    <d-fc-playground-element-string :string-element="singleSelectionElementItemOther.element"/>
                                </tbody>
                            </v-table>
                        </d-card>
                    </td>
                </tr>
            </tbody>
        </v-table>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {SingleSelectionUiElementItemOther} from "@/adapter/graphql/generated/graphql";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcPlaygroundElementString from "@/components/flow-config/playground/d-fc-playground-element-string.vue";

    defineProps<{
        singleSelectionElementItemOther: SingleSelectionUiElementItemOther
    }>()
</script>

<style scoped>
</style>