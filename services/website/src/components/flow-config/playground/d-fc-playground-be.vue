<template>
    <d-fc-playground-be-and v-if="expression.__typename === 'AndUIElementBooleanExpression'"
                            :expression="expression as AndUiElementBooleanExpression"/>

    <d-fc-playground-be-comparison v-else-if="expression.__typename === 'ComparisonUIElementBooleanExpression'"
                                   :expression="expression as ComparisonUiElementBooleanExpression"/>

    <d-fc-playground-be-context v-else-if="expression.__typename === 'ContextUIElementBooleanExpression'"
                                :expression="expression as ContextUiElementBooleanExpression"/>

    <d-fc-playground-be-equality v-else-if="expression.__typename === 'EqualityUIElementBooleanExpression'"
                                 :expression="expression as EqualityUiElementBooleanExpression"/>

    <d-fc-playground-be-in v-else-if="expression.__typename === 'InUIElementBooleanExpression'"
                           :expression="expression as InUiElementBooleanExpression"/>

    <d-fc-playground-be-not v-else-if="expression.__typename === 'NotUIElementBooleanExpression'"
                            :expression="expression as NotUiElementBooleanExpression"/>

    <d-fc-playground-be-or v-else-if="expression.__typename === 'OrUIElementBooleanExpression'"
                           :expression="expression as OrUiElementBooleanExpression"/>

    <d-fc-unknown-type v-else
                       :data="expression"
                       :supported-types="[
                           'AndUIElementBooleanExpression',
                           'ComparisonUIElementBooleanExpression',
                           'ContextUIElementBooleanExpression',
                           'EqualityUIElementBooleanExpression',
                           'InUIElementBooleanExpression',
                           'NotUIElementBooleanExpression',
                           'OrUIElementBooleanExpression'
                       ]"
                       :type="expression.__typename ?? 'undefined'"
                       clazz="UIElementBooleanExpression"/>
</template>

<script lang="ts"
        setup>
    import {AndUiElementBooleanExpression, ComparisonUiElementBooleanExpression, ContextUiElementBooleanExpression, EqualityUiElementBooleanExpression, InUiElementBooleanExpression, NotUiElementBooleanExpression, OrUiElementBooleanExpression, UiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundBeAnd from "@/components/flow-config/playground/d-fc-playground-be-and.vue";
    import DFcPlaygroundBeEquality from "@/components/flow-config/playground/d-fc-playground-be-equality.vue";
    import DFcPlaygroundBeIn from "@/components/flow-config/playground/d-fc-playground-be-in.vue";
    import DFcPlaygroundBeNot from "@/components/flow-config/playground/d-fc-playground-be-not.vue";
    import DFcPlaygroundBeOr from "@/components/flow-config/playground/d-fc-playground-be-or.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcPlaygroundBeComparison from "@/components/flow-config/playground/d-fc-playground-be-comparison.vue";
    import DFcPlaygroundBeContext from "@/components/flow-config/playground/d-fc-playground-be-context.vue";

    defineProps<{
        expression: UiElementBooleanExpression
    }>()
</script>

<style scoped>
</style>