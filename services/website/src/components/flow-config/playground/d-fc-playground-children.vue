<template>
    <d-card>
        <v-container fluid>
            <v-row>
                <v-col>
                    <d-h6>Fields ({{ collectFieldIdsOfUIElement(parentElement).length }})</d-h6>
                    <d-list density="compact">
                        <d-list-item v-for="(element, index) in [...collectFieldIdsOfUIElement(parentElement)].toSorted()"
                                     :key="index">
                            {{ element.id }}
                        </d-list-item>
                    </d-list>
                </v-col>
            </v-row>
            <v-row>
                <v-col cols="auto">
                    <v-tabs v-model="selectedTab"
                            center-active
                            direction="vertical">
                        <!-- (parentElement.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
                        <v-tab v-for="(child, index) in (parentElement as UiParent).children ?? (parentElement as ChipGroupUiElement).chips ?? []"
                               :key="index">
                            <span class="me-1">{{ index + 1 }})</span>
                            <template v-if="(child as any).label !== undefined && flowConfigTranslator((child as any).label) !== null">
                                {{ flowConfigTranslator((child as any).label) }}
                            </template>
                            <template v-else-if="(child as any).title !== undefined && flowConfigTranslator((child as any).title) !== null">
                                {{ flowConfigTranslator((child as any).title) }}
                            </template>
                            <template v-else>
                                <span style="color: darkgoldenrod;">{{ child.__typename }}</span>
                                <span v-if="(child as any).field !== undefined"
                                      class="ms-1"
                                      style="font-size: 75%;">({{ ((child as any).field as FlowConfigField).id }})</span>
                                <span v-else-if="(child as any).type !== undefined"
                                      class="ms-1"
                                      style="font-size: 75%;">({{ (child as any).type }})</span>
                            </template>
                        </v-tab>
                    </v-tabs>
                </v-col>

                <v-col>
                    <v-window v-model="selectedTab">
                        <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
                        <v-window-item v-for="(child, childIndex) in (parentElement as UiParent).children ?? (parentElement as ChipGroupUiElement).chips ?? []"
                                       :key="childIndex">
                            <v-container fluid>
                                <v-row>
                                    <v-col>
                                        <v-table class="bg-transparent rounded-lg"
                                                 density="compact">
                                            <tbody>
                                                <d-fc-playground-element-boolean v-if="child.__typename === 'BooleanUIElement'"
                                                                                 :boolean-element="child"/>

                                                <d-fc-playground-element-number v-else-if="child.__typename === 'NumberUIElement'"
                                                                                :number-element="child"/>

                                                <d-fc-playground-element-string v-else-if="child.__typename === 'StringUIElement'"
                                                                                :string-element="child"/>

                                                <d-fc-playground-element-date v-else-if="child.__typename === 'DateUIElement'"
                                                                              :date-element="child"/>

                                                <d-fc-playground-element-single-selection v-else-if="child.__typename === 'SingleSelectionUIElement'"
                                                                                          :single-selection-element="child"/>

                                                <d-fc-playground-element-file v-else-if="child.__typename === 'FileUIElement'"
                                                                              :file-element="child"/>

                                                <d-fc-playground-element-group v-else-if="child.__typename === 'GroupUIElement'"
                                                                               :group-element="child"/>

                                                <d-fc-playground-element-array v-else-if="child.__typename === 'ArrayUIElement'"
                                                                               :array-element="child"/>

                                                <d-fc-playground-element-custom v-else-if="child.__typename === 'CustomUIElement'"
                                                                                :custom-element="child"/>

                                                <d-fc-playground-element-key-value-list v-else-if="child.__typename === 'KeyValueListUIElement'"
                                                                                        :key-value-list-element="child"/>

                                                <d-fc-playground-element-text v-else-if="child.__typename === 'TextUIElement'"
                                                                              :text-element="child"/>

                                                <d-fc-playground-element-field-text v-else-if="child.__typename === 'FieldTextUIElement'"
                                                                                    :field-text-element="child"/>

                                                <d-fc-playground-element-table v-else-if="child.__typename === 'TableUIElement'"
                                                                               :table-element="child"/>

                                                <d-fc-playground-element-image-gallery v-else-if="child.__typename === 'ImageGalleryUIElement'"
                                                                                       :image-gallery-element="child"/>

                                                <d-fc-playground-element-chip-group v-else-if="child.__typename === 'ChipGroupUIElement'"
                                                                                    :chip-group-element="child"/>

                                                <d-fc-unknown-type v-else
                                                                   :data="child"
                                                                   :supported-types="[
                                                                       'BooleanUIElement',
                                                                       'NumberUIElement',
                                                                       'StringUIElement',
                                                                       'DateUIElement',
                                                                       'SingleSelectionUIElement',
                                                                       'FileUIElement',
                                                                       'GroupUIElement',
                                                                       'ArrayUIElement',
                                                                       'CustomUIElement',
                                                                       'ChipGroupUIElement',
                                                                       'KeyValueListUIElement',
                                                                       'TextUIElement',
                                                                       'FieldTextUIElement',
                                                                       'TableUIElement',
                                                                       'ImageGalleryUIElement'
                                                                   ]"
                                                                   :type="child.__typename ?? '?'"
                                                                   clazz="UIElement"/>
                                            </tbody>
                                        </v-table>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-window-item>
                    </v-window>
                </v-col>
            </v-row>
        </v-container>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {ChipGroupUiElement, FlowConfigField, FlowConfigPage, Subflow, UiElement, UiParent} from "@/adapter/graphql/generated/graphql";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {collectFieldIdsOfUIElement, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DFcPlaygroundElementBoolean from "@/components/flow-config/playground/d-fc-playground-element-boolean.vue";
    import DFcPlaygroundElementNumber from "@/components/flow-config/playground/d-fc-playground-element-number.vue";
    import DFcPlaygroundElementString from "@/components/flow-config/playground/d-fc-playground-element-string.vue";
    import DFcPlaygroundElementSingleSelection from "@/components/flow-config/playground/d-fc-playground-element-single-selection.vue";
    import DFcPlaygroundElementDate from "@/components/flow-config/playground/d-fc-playground-element-date.vue";
    import DFcPlaygroundElementFile from "@/components/flow-config/playground/d-fc-playground-element-file.vue";
    import DFcPlaygroundElementGroup from "@/components/flow-config/playground/d-fc-playground-element-group.vue";
    import DFcPlaygroundElementArray from "@/components/flow-config/playground/d-fc-playground-element-array.vue";
    import DFcPlaygroundElementCustom from "@/components/flow-config/playground/d-fc-playground-element-custom.vue";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import DFcPlaygroundElementKeyValueList from "@/components/flow-config/playground/d-fc-playground-element-key-value-list.vue";
    import DFcPlaygroundElementText from "@/components/flow-config/playground/d-fc-playground-element-text.vue";
    import DFcPlaygroundElementFieldText from "@/components/flow-config/playground/d-fc-playground-element-field-text.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcPlaygroundElementTable from "@/components/flow-config/playground/d-fc-playground-element-table.vue";
    import DFcPlaygroundElementImageGallery from "@/components/flow-config/playground/d-fc-playground-element-image-gallery.vue";
    import DFcPlaygroundElementChipGroup from "@/components/flow-config/playground/d-fc-playground-element-chip-group.vue";
    import {shallowRef} from "vue";

    defineProps<{
        parentElement: (UiParent & (UiElement | FlowConfigPage | Subflow)) | ChipGroupUiElement
    }>()

    const selectedTab = shallowRef<number>(0)

    const {
        flowConfigTranslator
    } = useFlowConfig()
</script>

<style scoped>
    button {
        text-transform: none;
    }
</style>