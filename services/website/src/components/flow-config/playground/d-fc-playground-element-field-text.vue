<template>
    <d-fc-playground-element :element="fieldTextElement"/>

    <tr>
        <th>Field Value</th>
        <td class="py-1">
            <d-fc-playground-field-value-reference :field-value-reference="fieldTextElement.fieldValue"/>
        </td>
        <th>Horizontal Alignment</th>
        <td class="py-1">
            {{ fieldTextElement.horizontalAlignment }}
        </td>
        <th>Type</th>
        <td class="py-1">
            {{ fieldTextElement.type }}
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {FieldTextUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundFieldValueReference from "@/components/flow-config/playground/d-fc-playground-field-value-reference.vue";

    defineProps<{
        fieldTextElement: FieldTextUiElement
    }>()
</script>

<style scoped>
</style>