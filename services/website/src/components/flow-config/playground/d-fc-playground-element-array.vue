<template>
    <d-fc-playground-element :element="arrayElement"/>

    <tr>
        <th>Size Minimum</th>
        <td>{{ arrayElement.sizeMinimum }}</td>
    </tr>
    <tr>
        <th>Size Maximum</th>
        <td>{{ arrayElement.sizeMaximum }}</td>
    </tr>
    <tr>
        <th>Children</th>
        <td class="py-2">
            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
            <d-fc-playground-children v-if="(arrayElement.children ?? []).length > 0"
                                      :parent-element="arrayElement"/>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {ArrayUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundChildren from "@/components/flow-config/playground/d-fc-playground-children.vue";

    defineProps<{
        arrayElement: ArrayUiElement
    }>()
</script>

<style scoped>
</style>