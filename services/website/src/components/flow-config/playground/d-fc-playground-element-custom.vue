<template>
    <d-fc-playground-element :element="customElement"/>

    <tr>
        <th>ID</th>
        <td>{{ customElement.id }}</td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ customElement.type }}</td>
    </tr>
    <tr>
        <th>Related Custom UI Element ID</th>
        <td>{{ customElement.relatedCustomUIElementId }}</td>
    </tr>
    <tr>
        <th>Children</th>
        <td class="py-2">
            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
            <d-fc-playground-children v-if="(customElement.children ?? []).length > 0"
                                      :parent-element="customElement"/>
        </td>
    </tr>
    <tr>
        <th>Subflows</th>
        <td class="py-2">
            <d-card>
                <v-container fluid>
                    <v-row>
                        <v-col>
                            <v-tabs v-model="selectedTab"
                                    center-active
                                    mandatory>
                                <v-tab v-for="(subflow, subflowIndex) in customElement.subflows"
                                       :key="subflowIndex">
                                    {{ subflow.type }} ({{ subflowIndex + 1 }})
                                </v-tab>
                            </v-tabs>
                        </v-col>
                    </v-row>

                    <v-row>
                        <v-col>
                            <v-window v-model="selectedTab">
                                <v-window-item v-for="(subflow, subflowIndex) in customElement.subflows"
                                               :key="subflowIndex">
                                    <v-row>
                                        <v-col>
                                            <v-table class="bg-transparent"
                                                     density="compact">
                                                <tbody>
                                                    <tr>
                                                        <th>Type</th>
                                                        <td>{{ subflow.type }}</td>
                                                    </tr>
                                                    <tr>
                                                        <th>Fields</th>
                                                        <td class="py-1">
                                                            {{ collectFieldIdsOfUIElement(subflow).length }}
                                                            <d-list density="compact">
                                                                <d-list-item v-for="(element, index) in [...collectFieldIdsOfUIElement(subflow)].toSorted()"
                                                                             :key="index">
                                                                    {{ element.id }}
                                                                </d-list-item>
                                                            </d-list>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th>Children</th>
                                                        <td class="py-2">
                                                            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
                                                            <d-fc-playground-children v-if="(subflow.children ?? []).length > 0"
                                                                                      :parent-element="subflow"/>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </v-table>
                                        </v-col>
                                    </v-row>

                                    <v-row>
                                        <v-col>
                                            <d-fc-raw-data :raw-data="subflow"/>
                                        </v-col>
                                    </v-row>
                                </v-window-item>
                            </v-window>
                        </v-col>
                    </v-row>
                </v-container>
            </d-card>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {CustomUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundChildren from "@/components/flow-config/playground/d-fc-playground-children.vue";
    import {collectFieldIdsOfUIElement} from "@/components/flow-config/use-flow-config";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcRawData from "@/components/flow-config/d-fc-raw-data.vue";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {shallowRef} from "vue";

    defineProps<{
        customElement: CustomUiElement
    }>()

    const selectedTab = shallowRef<number>(0)
</script>

<style scoped>
</style>