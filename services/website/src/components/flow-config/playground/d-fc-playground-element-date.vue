<template>
    <d-fc-playground-element :element="dateElement"/>

    <tr>
        <th>Field</th>
        <td>
            <d-fc-playground-field :field="dateElement.field"/>
        </td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ dateElement.type }}</td>
    </tr>
    <tr>
        <th>Minimum</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ (dateElement as any).dateMinimum }}</td>
    </tr>
    <tr>
        <th>Maximum</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ (dateElement as any).dateMaximum }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {DateUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";

    defineProps<{
        dateElement: DateUiElement
    }>()
</script>

<style scoped>
</style>