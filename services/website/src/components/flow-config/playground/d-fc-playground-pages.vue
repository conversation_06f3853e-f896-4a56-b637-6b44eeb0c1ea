<template>
    <d-card>
        <v-container fluid>
            <v-row>
                <v-col>
                    <v-tabs v-model="selectedTab"
                            center-active>
                        <v-tab v-for="page in pages"
                               :key="page.id">
                            {{ flowConfigTranslator(page.titleShort) ?? page.id }}
                        </v-tab>
                    </v-tabs>
                </v-col>
            </v-row>

            <v-row>
                <v-col>
                    <v-window v-model="selectedTab">
                        <v-window-item v-for="page in pages"
                                       :key="page.id">
                            <v-container fluid>
                                <v-row>
                                    <v-col>
                                        <v-table class="bg-transparent"
                                                 density="compact">
                                            <tbody>
                                                <d-fc-playground-element :element="page"/>

                                                <tr>
                                                    <th>ID</th>
                                                    <td>{{ page.id }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Icon</th>
                                                    <td>
                                                        <d-fc-playground-icon :icon-name="page.icon"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Title Short</th>
                                                    <td>
                                                        <d-fc-playground-translations :translations="page.titleShort"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Title Long</th>
                                                    <td>
                                                        <d-fc-playground-translations :translations="page.titleLong"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Type</th>
                                                    <td>{{ page.type }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Layout</th>
                                                    <td>{{ page.layout }}</td>
                                                </tr>
                                                <tr>
                                                    <th>Related Pages</th>
                                                    <td>
                                                        <ul v-if="page.relatedPages.length > 0">
                                                            <li v-for="(relatedPage, index) in page.relatedPages"
                                                                :key="index">
                                                                {{ relatedPage.context }}: {{ relatedPage.pageId }}
                                                            </li>
                                                        </ul>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Children</th>
                                                    <td class="py-2">
                                                        <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
                                                        <d-fc-playground-children v-if="(page.children ?? []).length > 0"
                                                                                  :parent-element="page"/>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </v-table>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-window-item>
                    </v-window>
                </v-col>
            </v-row>
        </v-container>
    </d-card>
</template>

<script lang="ts"
        setup>
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundChildren from "@/components/flow-config/playground/d-fc-playground-children.vue";
    import DFcPlaygroundIcon from "@/components/flow-config/playground/d-fc-playground-icon.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";
    import {shallowRef} from "vue";

    defineProps<{
        pages: readonly FlowConfigPage[]
    }>()

    const selectedTab = shallowRef<number>(0)

    const {
        flowConfigTranslator
    } = useFlowConfig()
</script>

<style scoped>
</style>