<template>
    <d-fc-playground-element :element="groupElement"/>

    <tr>
        <th>Title</th>
        <td>
            <d-fc-playground-translations :translations="groupElement.title"/>
        </td>
    </tr>
    <tr>
        <th>Description</th>
        <td>
            <d-fc-playground-translations :translations="groupElement.description"/>
        </td>
    </tr>
    <tr>
        <th>Is Collapsible</th>
        <td>{{ groupElement.isCollapsible }}</td>
    </tr>
    <tr>
        <th>Nesting Depth</th>
        <td>{{ groupElement.nestingDepth }}</td>
    </tr>
    <tr>
        <th>Children</th>
        <td class="py-2">
            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
            <d-fc-playground-children v-if="(groupElement.children ?? []).length > 0"
                                      :parent-element="groupElement"/>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {GroupUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundChildren from "@/components/flow-config/playground/d-fc-playground-children.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";

    defineProps<{
        groupElement: GroupUiElement
    }>()
</script>

<style scoped>
</style>