<template>
    <d-fc-playground-element :element="singleSelectionElement"/>

    <tr>
        <th>Field</th>
        <td>
            <d-fc-playground-field :field="singleSelectionElement.field"/>
        </td>
    </tr>
    <tr>
        <th>Sorting</th>
        <td>{{ singleSelectionElement.sorting }}</td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ singleSelectionElement.type }}</td>
    </tr>
    <tr>
        <th>Default Value</th>
        <td>
            <!-- Ummapping des Feldes in der GraphQL-Query -->
            {{ (singleSelectionElement as any).singleSelectionDefaultValue }}
        </td>
    </tr>
    <tr>
        <th>Items</th>
        <td>
            <v-container fluid>
                <v-row v-for="(item, index) in singleSelectionElement.items"
                       :key="index">
                    <v-col>
                        <d-fc-playground-element-single-selection-item :single-selection-element-item="item"/>
                    </v-col>
                </v-row>
            </v-container>
        </td>
    </tr>
    <tr>
        <th>Other User Value</th>
        <td class="py-2">
            <d-fc-playground-element-single-selection-item-other v-if="singleSelectionElement.otherUserValue"
                                                                 :single-selection-element-item-other="singleSelectionElement.otherUserValue"/>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {SingleSelectionUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElementSingleSelectionItemOther from "@/components/flow-config/playground/d-fc-playground-element-single-selection-item-other.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundElementSingleSelectionItem from "@/components/flow-config/playground/d-fc-playground-element-single-selection-item.vue";

    defineProps<{
        singleSelectionElement: SingleSelectionUiElement
    }>()
</script>

<style scoped>
</style>