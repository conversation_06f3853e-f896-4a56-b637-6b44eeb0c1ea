<template>
    <d-fc-playground-element :element="tableElement"/>

    <tr>
        <th>Hide Index Column</th>
        <td>{{ tableElement.hideIndexColumn }}</td>
    </tr>
    <tr>
        <th>Columns</th>
        <td class="py-1">
            <ol>
                <li v-for="(column, index) in tableElement.columns"
                    :key="index">
                    <d-fc-playground-translations :translations="column.header"/>
                    <br>
                    <d-fc-playground-field-value-reference :field-value-reference="column.fieldValue"/>
                </li>
            </ol>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {TableUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";
    import DFcPlaygroundFieldValueReference from "@/components/flow-config/playground/d-fc-playground-field-value-reference.vue";

    defineProps<{
        tableElement: TableUiElement
    }>()
</script>

<style scoped>
    li + li {
        margin-top: 32px;
    }
</style>