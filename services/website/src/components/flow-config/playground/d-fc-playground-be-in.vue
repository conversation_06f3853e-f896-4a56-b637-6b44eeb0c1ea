<template>
    <span>(</span><span>{{ expression.field.id }} {{ operator }} [{{ expression.values.join(', ') }}]</span><span>)</span>
</template>

<script lang="ts"
        setup>
    import {InUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";

    const props = defineProps<{
        expression: InUiElementBooleanExpression
    }>()

    const operator = computed<string>(() => {
        switch (props.expression.operator) {
            case "in":
                return "in";
            case "nin":
                return "!in";
            default:
                return "???";
        }
    })
</script>

<style scoped>
</style>