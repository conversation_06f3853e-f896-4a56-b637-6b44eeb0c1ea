<template>
    <d-fc-playground-element :element="keyValueListElement"/>

    <tr>
        <th>Type</th>
        <td>{{ keyValueListElement.type }}</td>
    </tr>
    <tr>
        <th>Items</th>
        <td>
            <v-container fluid>
                <v-row v-for="(item, index) in keyValueListElement.items"
                       :key="index">
                    <v-col>
                        <d-fc-playground-element-key-value-list-item :key-value-list-element-item="item"/>
                    </v-col>
                </v-row>
            </v-container>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {KeyValueListUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundElementKeyValueListItem from "@/components/flow-config/playground/d-fc-playground-element-key-value-list-item.vue";

    defineProps<{
        keyValueListElement: KeyValueListUiElement
    }>()
</script>

<style scoped>
</style>