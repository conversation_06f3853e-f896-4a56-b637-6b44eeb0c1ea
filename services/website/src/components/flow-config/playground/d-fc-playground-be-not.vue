<template>
    <span>!(</span><span><d-fc-playground-be :expression="expression.expression"/></span><span>)</span>
</template>

<script lang="ts"
        setup>
    import {NotUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundBe from "@/components/flow-config/playground/d-fc-playground-be.vue";

    defineProps<{
        expression: NotUiElementBooleanExpression
    }>()
</script>

<style scoped>
</style>