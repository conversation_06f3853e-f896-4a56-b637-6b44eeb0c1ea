<template>
    <v-layout class="align-center">
        <d-icon v-if="iconName"
                :icon="flowConfigIconSupplier(iconName) ?? mdiHelp"
                :size="24"
                class="me-1"
                type="default"/>
        {{ iconName }}
    </v-layout>
</template>

<script lang="ts"
        setup>
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {Optional} from "@/model/Optional";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import {mdiHelp} from "@mdi/js";

    defineProps<{
        iconName?: Optional<string>
    }>()

    const {flowConfigIconSupplier} = useFlowConfig()
</script>

<style scoped>
</style>