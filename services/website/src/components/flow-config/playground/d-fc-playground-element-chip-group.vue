<template>
    <d-fc-playground-element :element="chipGroupElement"/>

    <tr>
        <th>Title</th>
        <td>
            <d-fc-playground-translations :translations="chipGroupElement.title"/>
        </td>
    </tr>
    <tr>
        <th>Description</th>
        <td>
            <d-fc-playground-translations :translations="chipGroupElement.description"/>
        </td>
    </tr>
    <tr>
        <th>Chips</th>
        <td class="py-2">
            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
            <d-fc-playground-children v-if="chipGroupElement.chips.length > 0"
                                      :parent-element="chipGroupElement"/>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {ChipGroupUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundChildren from "@/components/flow-config/playground/d-fc-playground-children.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";

    defineProps<{
        chipGroupElement: ChipGroupUiElement
    }>()
</script>

<style scoped>
</style>