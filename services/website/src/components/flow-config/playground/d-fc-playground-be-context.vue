<template>
    <span>(</span><span>CONTEXT {{ operator }} {{ expression.context }}</span><span>)</span>
</template>

<script lang="ts"
        setup>
    import {ContextUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";

    const props = defineProps<{
        expression: ContextUiElementBooleanExpression
    }>()

    const operator = computed<string>(() => {
        switch (props.expression.operator) {
            case "eq":
                return "=";
            case "ne":
                return "!=";
            default:
                return "???";
        }
    })
</script>

<style scoped>
</style>