<template>
    <d-fc-playground-element :element="fileElement"/>

    <tr>
        <th>ID Field</th>
        <td>
            <d-fc-playground-field :field="fileElement.idField"/>
        </td>
    </tr>
    <tr>
        <th>File Type</th>
        <td>{{ fileElement.fileType }}</td>
    </tr>
    <tr>
        <th>Allowed Mime Types</th>
        <td>
            <ul>
                <li v-for="(allowedMimeType, index) in fileElement.allowedMimeTypes"
                    :key="index">
                    {{ allowedMimeType }}
                </li>
            </ul>
        </td>
    </tr>
    <tr>
        <th>File Count Minimum</th>
        <td>{{ fileElement.fileCountMinimum }}</td>
    </tr>
    <tr>
        <th>File Count Maximum</th>
        <td>{{ fileElement.fileCountMaximum }}</td>
    </tr>
    <tr>
        <th>Name Element</th>
        <td class="py-2">
            <d-card v-if="fileElement.nameElement">
                <v-table class="bg-transparent"
                         density="compact">
                    <tbody>
                        <d-fc-playground-element-string :string-element="fileElement.nameElement"/>
                    </tbody>
                </v-table>
            </d-card>
        </td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {FileUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElementString from "@/components/flow-config/playground/d-fc-playground-element-string.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";

    defineProps<{
        fileElement: FileUiElement
    }>()
</script>

<style scoped>
</style>