<template>
    <d-list class="bg-transparent">
        <d-list-item v-for="(translation, index) in translations"
                     :key="index"
                     :subtitle="translation.string"
                     :title="translation.languageCode"
                     class="pa-0"/>
    </d-list>
</template>

<script lang="ts"
        setup>
    import {UiElementTranslation} from "@/adapter/graphql/generated/graphql";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";

    defineProps<{
        translations: readonly UiElementTranslation[]
    }>()
</script>

<style scoped>
</style>