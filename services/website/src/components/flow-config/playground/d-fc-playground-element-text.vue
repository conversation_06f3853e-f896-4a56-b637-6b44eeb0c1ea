<template>
    <d-fc-playground-element :element="textElement"/>

    <tr>
        <th>Text</th>
        <td>
            <d-fc-playground-translations :translations="textElement.text"/>
        </td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ textElement.type }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {TextUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";

    defineProps<{
        textElement: TextUiElement
    }>()
</script>

<style scoped>
</style>