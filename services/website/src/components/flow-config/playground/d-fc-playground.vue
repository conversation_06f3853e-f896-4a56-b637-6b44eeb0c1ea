<template>
    <v-container fluid>
        <v-row>
            <v-col>
                <d-card>
                    <v-container fluid>
                        <v-row>
                            <v-col>
                                <v-tabs v-model="selectedTab"
                                        center-active
                                        mandatory>
                                    <v-tab v-for="flowConfig in flowConfigs"
                                           :key="flowConfig.id">
                                        {{ flowConfig.id }}
                                    </v-tab>
                                </v-tabs>
                            </v-col>
                        </v-row>

                        <v-row>
                            <v-col>
                                <v-window v-model="selectedTab">
                                    <v-window-item v-for="flowConfig in flowConfigs"
                                                   :key="flowConfig.id">
                                        <v-row>
                                            <v-col>
                                                <v-table class="bg-transparent"
                                                         density="compact">
                                                    <tbody>
                                                        <tr>
                                                            <th>ID</th>
                                                            <td>{{ flowConfig.id }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th>Icon</th>
                                                            <td>
                                                                <d-fc-playground-icon :icon-name="flowConfig.icon"/>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Title Short</th>
                                                            <td>
                                                                <d-fc-playground-translations :translations="flowConfig.titleShort"/>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Title Long</th>
                                                            <td>
                                                                <d-fc-playground-translations :translations="flowConfig.titleLong"/>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th>Fields</th>
                                                            <td class="py-1">
                                                                {{ collectFieldIdsOfUIElement(flowConfig).length }}
                                                                <d-list density="compact">
                                                                    <d-list-item v-for="(element, index) in [...collectFieldIdsOfUIElement(flowConfig)].toSorted()"
                                                                                 :key="index">
                                                                        {{ element.id }}
                                                                    </d-list-item>
                                                                </d-list>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </v-table>
                                            </v-col>
                                        </v-row>

                                        <v-row>
                                            <v-col>
                                                <d-h6>Pages View</d-h6>
                                            </v-col>
                                        </v-row>

                                        <v-row dense>
                                            <v-col>
                                                <d-fc-playground-pages :pages="flowConfig.pagesView"/>
                                            </v-col>
                                        </v-row>

                                        <v-row>
                                            <v-col>
                                                <d-h6>Pages Edit</d-h6>
                                            </v-col>
                                        </v-row>

                                        <v-row dense>
                                            <v-col>
                                                <d-fc-playground-pages :pages="flowConfig.pagesEdit"/>
                                            </v-col>
                                        </v-row>

                                        <v-row>
                                            <v-col>
                                                <d-fc-raw-data :raw-data="flowConfig"/>
                                            </v-col>
                                        </v-row>
                                    </v-window-item>
                                </v-window>
                            </v-col>
                        </v-row>
                    </v-container>
                </d-card>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {computed, shallowRef} from "vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";
    import DFcPlaygroundPages from "@/components/flow-config/playground/d-fc-playground-pages.vue";
    import DFcRawData from "@/components/flow-config/d-fc-raw-data.vue";
    import {collectFieldIdsOfUIElement} from "@/components/flow-config/use-flow-config";
    import DFcPlaygroundIcon from "@/components/flow-config/playground/d-fc-playground-icon.vue";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {FlowConfig, useDFcPlaygroundLoadFlowConfigsQuery} from "@/adapter/graphql/generated/graphql";
    import {createFlowConfigsField} from "@/service/flow-config/flow-config-service";

    const selectedTab = shallowRef<number>(0)

    const {
        result: flowConfigsResult,
        //loading: areFlowConfigsLoading, //TODO: loading
        //TODO: error
    } = useDFcPlaygroundLoadFlowConfigsQuery()

    const rawFlowConfigs = computed<readonly FlowConfig[]>(() => flowConfigsResult.value?.flowConfigs as (FlowConfig[] | undefined) ?? [])
    const flowConfigs = createFlowConfigsField(rawFlowConfigs)
</script>

<style scoped>
</style>