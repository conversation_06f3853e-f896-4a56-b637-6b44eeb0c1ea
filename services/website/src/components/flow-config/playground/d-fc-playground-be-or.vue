<template>
    (<span v-for="(e, index) in expression.expressions"
           :key="index"><span v-if="index > 0"> || </span><d-fc-playground-be :expression="e"/></span>)
</template>

<script lang="ts"
        setup>
    import {OrUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundBe from "@/components/flow-config/playground/d-fc-playground-be.vue";

    defineProps<{
        expression: OrUiElementBooleanExpression
    }>()
</script>

<style scoped>
</style>