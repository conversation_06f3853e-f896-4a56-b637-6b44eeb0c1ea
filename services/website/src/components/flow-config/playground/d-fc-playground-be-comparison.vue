<template>
    <!-- .value wird umgemappt in graphql zu comparisonValue -->
    <span>(</span><span>{{ expression.field.id }} {{ operator }} {{ (expression as any).comparisonValue }}</span><span>)</span>
</template>

<script lang="ts"
        setup>
    import {ComparisonUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";

    const props = defineProps<{
        expression: ComparisonUiElementBooleanExpression
    }>()

    const operator = computed<string>(() => {
        switch (props.expression.operator) {
            case "gt":
                return ">";
            case "lt":
                return "<";
            case 'gte':
                return ">=";
            case 'lte':
                return "<=";
            default:
                return "???";
        }
    })
</script>

<style scoped>
</style>