<template>
    <!-- ### INPUT ### -->
    <tr v-if="(element as UiInput).label !== undefined">
        <th>Label</th>
        <td>
            <d-fc-playground-translations :translations="(element as UiInput).label"/>
        </td>
    </tr>
    <tr v-if="(element as UiInput).hint !== undefined">
        <th>Hint</th>
        <td>
            <d-fc-playground-translations :translations="(element as UiInput).hint"/>
        </td>
    </tr>
    <tr v-if="(element as UiInput).hintImageURL !== undefined">
        <th>Hint Image URL</th>
        <td>{{ (element as UiInput).hintImageURL }}</td>
        <!-- TODO: vorschaubild -->
    </tr>
    <tr v-if="(element as UiInput).isRequired !== undefined">
        <th>Required</th>
        <td>{{ (element as UiInput).isRequired }}</td>
    </tr>

    <!-- ### POSITIONABLE ### -->
    <tr v-if="(element as UiLayoutPositionable).preferredLayoutPosition !== undefined">
        <th>Preferred Position</th>
        <td>{{ (element as UiLayoutPositionable).preferredLayoutPosition }}</td>
    </tr>

    <!-- ### HIDEABLE ### -->
    <tr v-if="(element as UiHideable).visibilityCondition !== undefined">
        <th>Visibility Condition</th>
        <td>
            <d-fc-playground-be v-if="(element as UiHideable).visibilityCondition !== null"
                                :expression="(element as UiHideable).visibilityCondition!"/>
        </td>
    </tr>

    <!-- ### TYPENAME ### -->
    <tr v-if="(element as UiElement).__typename !== undefined">
        <th>GraphQL Type</th>
        <td>{{ (element as UiElement).__typename }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";
    import {UiElement, UiHideable, UiInput, UiLayoutPositionable} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundBe from "@/components/flow-config/playground/d-fc-playground-be.vue";

    defineProps<{
        element: UiInput | UiHideable | UiLayoutPositionable | UiElement
    }>()
</script>

<style scoped>
</style>