<template>
    <d-fc-playground-element :element="imageGalleryElement"/>

    <tr>
        <th>ID Field Value</th>
        <td class="py-1">
            <d-fc-playground-field-value-reference v-if="imageGalleryElement.idFieldValue"
                                                   :field-value-reference="imageGalleryElement.idFieldValue"/>
        </td>
    </tr>
    <tr>
        <th>Preferred Size</th>
        <td>{{ imageGalleryElement.preferredSize }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {ImageGalleryUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundFieldValueReference from "@/components/flow-config/playground/d-fc-playground-field-value-reference.vue";

    defineProps<{
        imageGalleryElement: ImageGalleryUiElement
    }>()
</script>

<style scoped>
</style>