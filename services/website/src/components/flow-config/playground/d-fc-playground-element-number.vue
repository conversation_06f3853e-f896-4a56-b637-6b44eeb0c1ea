<template>
    <d-fc-playground-element :element="numberElement"/>

    <tr>
        <th>Field</th>
        <td>
            <d-fc-playground-field :field="numberElement.field"/>
        </td>
    </tr>
    <tr>
        <th>Type</th>
        <td>{{ numberElement.type }}</td>
    </tr>
    <tr>
        <th>Unit</th>
        <td>{{ numberElement.unit }}</td>
    </tr>
    <tr>
        <th>Minimum</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ (numberElement as any).numberMinimum }}</td>
    </tr>
    <tr>
        <th>Maximum</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ (numberElement as any).numberMaximum }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {NumberUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";

    defineProps<{
        numberElement: NumberUiElement
    }>()
</script>

<style scoped>
</style>