<template>
    <d-fc-playground-element :element="booleanElement"/>

    <tr>
        <th>Field</th>
        <td>
            <d-fc-playground-field :field="booleanElement.field"/>
        </td>
    </tr>
    <tr>
        <th>Default Value</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ (booleanElement as any).booleanDefaultValue }}</td>
    </tr>
    <tr>
        <th>Icon</th>
        <!-- Ummapping des Feldes in der GraphQL-Query -->
        <td>{{ booleanElement.icon }}</td>
    </tr>
</template>

<script lang="ts"
        setup>
    import {BooleanUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcPlaygroundField from "@/components/flow-config/playground/d-fc-playground-field.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";

    defineProps<{
        booleanElement: BooleanUiElement
    }>()
</script>

<style scoped>
</style>