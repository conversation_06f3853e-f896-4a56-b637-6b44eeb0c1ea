<template>
    <d-card>
        <v-table class="bg-transparent"
                 density="compact">
            <tbody>
                <!-- label + hint handled inside -->
                <d-fc-playground-element :element="keyValueListElementItem"/>

                <tr>
                    <th>Icon</th>
                    <td>
                        <d-fc-playground-icon :icon-name="keyValueListElementItem.icon"/>
                    </td>
                </tr>
                <tr>
                    <th>Key</th>
                    <td>
                        <d-fc-playground-translations :translations="keyValueListElementItem.key"/>
                    </td>
                </tr>
                <tr>
                    <th>Field Value</th>
                    <td class="py-1">
                        <d-fc-playground-field-value-reference :field-value-reference="keyValueListElementItem.fieldValue"/>
                    </td>
                </tr>
            </tbody>
        </v-table>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {KeyValueListUiElementItem} from "@/adapter/graphql/generated/graphql";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcPlaygroundElement from "@/components/flow-config/playground/d-fc-playground-element.vue";
    import DFcPlaygroundIcon from "@/components/flow-config/playground/d-fc-playground-icon.vue";
    import DFcPlaygroundTranslations from "@/components/flow-config/playground/d-fc-playground-translations.vue";
    import DFcPlaygroundFieldValueReference from "@/components/flow-config/playground/d-fc-playground-field-value-reference.vue";

    defineProps<{
        keyValueListElementItem: KeyValueListUiElementItem
    }>()
</script>

<style scoped>
</style>