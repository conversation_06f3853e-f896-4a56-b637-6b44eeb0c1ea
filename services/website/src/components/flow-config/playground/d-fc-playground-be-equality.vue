<template>
    <!-- .value wird umgemappt in graphql zu equalityValue -->
    <span>(</span><span>{{ expression.field.id }} {{ operator }} {{ (expression as any).equalityValue == null ? "null" : (expression as any).equalityValue }}</span><span>)</span>
</template>

<script lang="ts"
        setup>
    import {EqualityUiElementBooleanExpression} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";

    const props = defineProps<{
        expression: EqualityUiElementBooleanExpression
    }>()

    const operator = computed<string>(() => {
        switch (props.expression.operator) {
            case "eq":
                return "=";
            case "ne":
                return "!=";
            default:
                return "???";
        }
    })
</script>

<style scoped>
</style>