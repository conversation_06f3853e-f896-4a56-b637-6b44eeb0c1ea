import {AndUiElementBooleanExpression, ComparisonUiElementBooleanExpression, ContextUiElementBooleanExpression, CustomUiElement, EqualityUiElementBooleanExpression, FlowConfig, FlowConfigField, FlowConfigPage, ImageGalleryUiElement, InUiElementBooleanExpression, Listing, NotUiElementBooleanExpression, OrUiElementBooleanExpression, RelatedFlowConfigPage, Subflow, UiElement, UiElementBooleanExpression, UiElementTranslation, UiHideableElement} from "@/adapter/graphql/generated/graphql";
import {toRaw} from "vue";
import {Optional} from "@/model/Optional";
import {ListingContext} from "@/model/listing/ListingContext";
import {FlowData} from "@/model/listing/FlowData";
import {stringToDouble} from "@/utility/converter";
import {areNumbersEqual, areNumbersNotEqual} from "@/utility/number";
import * as allIcons from "@mdi/js";
import {RouteLocationNamedRaw, RouteLocationNormalizedLoaded} from "vue-router";
import {LS__LANGUAGE} from "@/service/local-storage/local-storage";
import {CONFIG} from "@/config";
import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
import {utcDate} from "@/utility/date-converter";
import {isImageValidForMainGallery} from "@/components/flow-config/ui-element/image-gallery/image-gallery-helper";
import {APOLLO_FETCH_FLOWCONFIGS_CACHE_TTL_MINUTES} from "@/utility/environment";

export type FlowConfigTranslator = (translations: readonly UiElementTranslation[]) => Optional<string>;
export type FlowConfigIconSupplier = (iconName?: Optional<string>) => Optional<string>;

export function useFlowConfig() {
    const flowConfigTranslator: FlowConfigTranslator = translations => {
        const languageCodeValue = LS__LANGUAGE.state.value;
        const translation = translations.find(translation => translation.languageCode === languageCodeValue);
        return translation === undefined ? null : translation.string;
    }

    const flowConfigIconSupplier: FlowConfigIconSupplier = iconName => {
        if (iconName === null || iconName === undefined) {
            return null
        }
        return (allIcons as any)[iconName] ?? null
    }

    return {
        flowConfigTranslator,
        flowConfigIconSupplier
    }
}

export function needsRefresh(flowConfig: FlowConfig): boolean {
    return utcDate(flowConfig.lastFetchDate)
            ?.isBefore(utcDate(new Date().toISOString())!.subtract(APOLLO_FETCH_FLOWCONFIGS_CACHE_TTL_MINUTES, 'minutes'))
        ?? false
}

function isBooleanExpressionTrue(
    booleanExpression: UiElementBooleanExpression,
    context: ListingContext,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
) {
    switch (booleanExpression.__typename) {
        case 'OrUIElementBooleanExpression':
            return isOrExpressionTrue(booleanExpression, context, flowData, arrayIndexOfSourceElement)
        case 'AndUIElementBooleanExpression':
            return isAndExpressionTrue(booleanExpression, context, flowData, arrayIndexOfSourceElement)
        case 'NotUIElementBooleanExpression':
            return isNotExpressionTrue(booleanExpression, context, flowData, arrayIndexOfSourceElement)
        case 'ComparisonUIElementBooleanExpression':
            return isComparisonExpressionTrue(booleanExpression, flowData, arrayIndexOfSourceElement)
        case 'EqualityUIElementBooleanExpression':
            return isEqualityExpressionTrue(booleanExpression, flowData, arrayIndexOfSourceElement)
        case 'ContextUIElementBooleanExpression':
            return isContextExpressionTrue(booleanExpression, context)
        case 'InUIElementBooleanExpression':
            return isInExpressionTrue(booleanExpression, flowData, arrayIndexOfSourceElement)
        default:
            console.warn("Unknown boolean expression type", booleanExpression.__typename, 'in', booleanExpression)
            return false
    }
}

function isOrExpressionTrue(
    expression: OrUiElementBooleanExpression,
    context: ListingContext,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    return expression.expressions.some(e => isBooleanExpressionTrue(
        e,
        context,
        flowData,
        arrayIndexOfSourceElement
    ))
}

function isAndExpressionTrue(
    expression: AndUiElementBooleanExpression,
    context: ListingContext,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    return expression.expressions.every(e => isBooleanExpressionTrue(
        e,
        context,
        flowData,
        arrayIndexOfSourceElement
    ))
}

function isNotExpressionTrue(
    expression: NotUiElementBooleanExpression,
    context: ListingContext,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    return !isBooleanExpressionTrue(
        expression.expression,
        context,
        flowData,
        arrayIndexOfSourceElement
    )
}

function isComparisonExpressionTrue(
    expression: ComparisonUiElementBooleanExpression,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    const rawValue = flowData.getValue({
        field: expression.field,
        arrayIndex: arrayIndexOfSourceElement
    })

    if (rawValue === null) {
        return false
    }

    if (typeof rawValue === "boolean" || typeof rawValue === "string") {
        console.warn("Comparison expression used on non-numeric field", expression)
        return false
    }

    const number = rawValue.value
    const otherNumber = (expression as any).comparisonValue as number //.value wird umgemappt in graphql

    switch (expression.operator) {
        case 'gt':
            return number > otherNumber
        case 'gte':
            return number >= otherNumber
        case 'lt':
            return number < otherNumber
        case 'lte':
            return number <= otherNumber
        default:
            console.warn("Unknown comparison operator", expression.operator, 'in', expression)
            return false
    }
}

const numberComparisonEpsilon = Number.EPSILON

function isEqualityExpressionTrue(
    expression: EqualityUiElementBooleanExpression,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    const operator = expression.operator
    const left = flowData.getValue({
        field: expression.field,
        arrayIndex: arrayIndexOfSourceElement
    })
    const value = (expression as any).equalityValue as (string | null | undefined) //value wird umgemappt in graphql
    const right = value === undefined ? null : value

    //NULLABILITY
    if (left === null || right === null) {
        return isEqualityTrue<Optional<any>>(expression, left, operator, right)
    }

    //BOOLEAN
    if (typeof left === "boolean") {
        const rightBoolean = right === 'true'

        if (!rightBoolean && right !== 'false') {
            console.warn("Could not parse boolean", right, 'in', expression)
            return false
        }

        return isEqualityTrue<boolean>(expression, left, operator, rightBoolean)
    }

    //STRING
    if (typeof left === "string") {
        return isEqualityTrue<string>(expression, left, operator, right)
    }

    //NUMBER
    const rightNumber = stringToDouble(right)
    if (rightNumber === null) {
        console.warn("Could not parse number", right, 'in', expression)
        return false
    }
    const leftNumber = left.value

    switch (operator) {
        case 'eq':
            return areNumbersEqual(leftNumber, rightNumber, numberComparisonEpsilon)
        case 'ne':
            return areNumbersNotEqual(leftNumber, rightNumber, numberComparisonEpsilon)
        default:
            console.warn("Unknown equality operator", operator, 'in', expression)
            return false
    }
}

function isEqualityTrue<T>(
    expression: EqualityUiElementBooleanExpression,
    left: T,
    operator: string,
    right: T
): boolean {
    switch (operator) {
        case 'eq':
            return left === right
        case 'ne':
            return left !== right
        default:
            console.warn("Unknown equality operator", operator, 'in', expression)
            return false
    }
}

function isContextExpressionTrue(
    expression: ContextUiElementBooleanExpression,
    context: ListingContext,
): boolean {
    switch (expression.operator) {
        case 'eq':
            return context === expression.context
        case 'ne':
            return context !== expression.context
        default:
            console.warn("Unknown context operator", expression.operator, 'in', expression)
            return false
    }
}

function isInExpressionTrue(
    expression: InUiElementBooleanExpression,
    flowData: FlowData,
    arrayIndexOfSourceElement: Optional<number>,
): boolean {
    const values = new Set(expression.values)

    if (values.size <= 0) {
        console.warn("In expression has no values", expression)
        return false
    }

    const rawValue = flowData.getValue({
        field: expression.field,
        arrayIndex: arrayIndexOfSourceElement
    })

    if (rawValue === null) {
        switch (expression.operator) {
            case 'in':
                return false
            case 'nin':
                return true
            default:
                console.warn("Unknown in operator", expression.operator, 'in', expression)
                return false
        }
    }

    if (typeof rawValue !== "string") {
        console.warn("In expression used on non-string field", expression)
        return false
    }

    switch (expression.operator) {
        case 'in':
            return values.has(rawValue)
        case 'nin':
            return !values.has(rawValue)
        default:
            console.warn("Unknown in operator", expression.operator, 'in', expression)
            return false
    }
}

//TODO: der ArrayIndex wird derzeit so verwendet, dass eine Boolsche Expression immer nur ein anderes Feld referenzieren kann, dass sich im selben Array Element befindet (== ArrayIndex).
//TODO: Das kann zu diversen Problemen führen und wird später behoben.
// noinspection JSClassNamingConvention,OverlyComplexFunctionJS
export function isUIElementVisible(
    element: UiHideableElement,
    arrayIndex: Optional<number>,
    context: ListingContext,
    listing: Listing,
    flowData: FlowData
): boolean {
    const visibilityCondition = element.visibilityCondition
    const isVisible = (visibilityCondition === undefined || visibilityCondition === null)
        ? true
        : isBooleanExpressionTrue(visibilityCondition, context, flowData, arrayIndex)

    if (!isVisible) {
        return false
    }

    switch (element.__typename) {
        case 'BooleanUIElement':
        case 'NumberUIElement':
        case 'StringUIElement':
        case 'DateUIElement':
            return true
        case 'SingleSelectionUIElement':
            return isAnyUIElementVisible(
                element.items,
                arrayIndex,
                context,
                listing,
                flowData
            )
        case 'SingleSelectionUIElementItem':
            return true
        case 'FileUIElement':
            return true
        case 'GroupUIElement':
            //(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            return isAnyUIElementVisible(
                element.children ?? [],
                arrayIndex,
                context,
                listing,
                flowData
            )
        case 'ArrayUIElement':
            //(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            return isAnyUIElementVisible(
                element.children ?? [],
                arrayIndex,
                context,
                listing,
                flowData
            )
        case 'CustomUIElement':
            return true //children werden hier ignoriert, weil es auch custom elemente geben kann, die keine children haben
        case 'ChipGroupUIElement':
            return isAnyUIElementVisible(
                element.chips,
                arrayIndex,
                context,
                listing,
                flowData
            )
        case 'KeyValueListUIElement':
            return isAnyUIElementVisible(
                element.items,
                arrayIndex,
                context,
                listing,
                flowData
            )
        case 'KeyValueListUIElementItem':
            return flowData.existsValue({
                field: element.fieldValue.field,
                arrayIndex: arrayIndex
            })
        case 'TextUIElement':
            return true
        case 'FieldTextUIElement':
            return flowData.existsValue({
                field: element.fieldValue.field,
                arrayIndex: arrayIndex
            })
        case 'TableUIElement':
            return isAnyUIElementVisible(
                element.columns,
                arrayIndex,
                context,
                listing,
                flowData
            ) && flowData.existsAnyArrayValue(element)
        case 'TableUIElementColumn':
            return true //hier wird nicht die fieldValue ausgewertet, weil die Parent-Table die Values bereits auswertet
        case 'ImageGalleryUIElement':
            //wenn null, dann werden die listing assets ausgegeben
            return (element.idFieldValue == null && listing.images.filter(isImageValidForMainGallery).length > 0) || flowData.existsAnyArrayValue(element)
        case 'FlowConfigPage':
            if (INTERNET_SPEED.value <= InternetSpeed.OFFLINE && shouldHidePageWhenOffline(element.type ?? null)) {
                return false
            }

            //wenn type != null, dann handelt es sich um eine custom page, wo die children ignoriert werden
            return (element.type !== null && element.type !== undefined)
                || element.children
                    .filter(e =>
                            context !== 'VIEW' || (
                                //Text-only-Elemente
                                e.__typename !== 'TextUIElement'
                                //Leere ImageGallery-Elemente
                                && (
                                    e.__typename !== 'ImageGalleryUIElement'
                                    //Feld? Dann muss mindestens ein Wert vorhanden sein
                                    || ((e as ImageGalleryUiElement).idFieldValue != null && flowData.existsAnyArrayValue(e))
                                )
                            )
                    )
                    .some(e => isUIElementVisible(
                        e,
                        arrayIndex,
                        context,
                        listing,
                        flowData
                    ))
        default:
            console.warn("Unknown UiHideableElement type", element.__typename, 'in', toRaw(element))
            return false
    }
}

/**
 * Manche pages benötigen zwingend Internet um nutzbar zu sein.
 * Es ist wichtig zu wissen, dass wenn man sich gerade auf der Page befindet und das Internet verliert,
 * die Page auch dann verschwindet.
 */
function shouldHidePageWhenOffline(type: Optional<string>) {
    if (type === null) {
        return false
    }

    return CONFIG.FLOW_CONFIG_PAGES_TO_HIDE_WHEN_OFFLINE.has(type);
}

function isAnyUIElementVisible(
    elements: readonly UiHideableElement[],
    arrayIndex: Optional<number>,
    context: ListingContext,
    listing: Listing,
    flowData: FlowData,
): boolean {
    return elements.some(element => isUIElementVisible(
        element,
        arrayIndex,
        context,
        listing,
        flowData
    ))
}

// noinspection JSClassNamingConvention
export type UIElementTraversalVisitorAbortCondition = (element: UiElement) => boolean

export function createUIElementTraversalVisitorAbortConditionForSameElementType(element: UiElement): UIElementTraversalVisitorAbortCondition {
    let hasFoundSameElementType = false
    return (e) => {
        //this will stop recursion of the same type
        if (e.__typename === element.__typename) {
            hasFoundSameElementType = true
            return false
        }
        return hasFoundSameElementType
    }
}

// noinspection OverlyComplexFunctionJS
export function traverseUIElement(
    element: UiElement | FlowConfigPage | FlowConfig | Subflow,
    visitor: (element: UiElement) => void,
    abortCondition?: UIElementTraversalVisitorAbortCondition
) {
    if (element.__typename !== 'FlowConfigPage' && element.__typename !== 'FlowConfig' && element.__typename !== 'Subflow') {
        if (abortCondition != null && abortCondition(element as UiElement)) {
            return
        }

        visitor(element as UiElement)
    }

    switch (element.__typename) {
        case 'BooleanUIElement':
        case 'NumberUIElement':
        case 'StringUIElement':
        case 'DateUIElement':
            break
        case 'SingleSelectionUIElement':
            if (element.otherUserValue != null) {
                traverseUIElement(element.otherUserValue.element, visitor)
            }
            break
        case 'FileUIElement':
            if (element.nameElement != null) {
                traverseUIElement(element.nameElement, visitor)
            }
            break
        case 'GroupUIElement':
            (element.children ?? []).forEach(e => traverseUIElement(e, visitor)) //(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            break
        case 'ArrayUIElement':
            (element.children ?? []).forEach(e => traverseUIElement(e, visitor))//(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            break
        case 'CustomUIElement':
            (element.children ?? []).forEach(e => traverseUIElement(e, visitor))//(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            break
        case 'ChipGroupUIElement':
            (element.chips ?? []).forEach(e => traverseUIElement(e, visitor))
            break
        case 'KeyValueListUIElement':
        case 'TextUIElement':
        case 'FieldTextUIElement':
        case 'TableUIElement':
        case 'ImageGalleryUIElement':
            break
        case 'Subflow':
        case 'FlowConfigPage':
            (element.children ?? []).forEach(e => traverseUIElement(e, visitor))//(element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen
            break
        case 'FlowConfig':
            [
                ...element.pagesView,
                ...element.pagesEdit,
            ].forEach(p => traverseUIElement(p, visitor))
            break
        default:
            console.warn("Unknown UI element type", element.__typename, 'in', toRaw(element))
            break
    }
}

export function findCustomUIElementById(flowConfig: Optional<FlowConfig>, id: string): Optional<CustomUiElement> {
    if (flowConfig == null) {
        return null
    }

    let customUIElement: Optional<CustomUiElement> = null

    traverseUIElement(flowConfig, element => {
        if (element.__typename === 'CustomUIElement' && element.id === id) {
            customUIElement = element
        }
    })

    //TODO: abbruch, wenn gefunden?!

    return customUIElement
}

export function existsCustomUIElementInPage(page: FlowConfigPage, customUIElementId: string): boolean {
    let exists = false

    traverseUIElement(page, element => {
        if (element.__typename === 'CustomUIElement' && element.id === customUIElementId) {
            exists = true
        }
    })

    return exists
}

export function collectFieldIdsOfUIElement(
    element: UiElement | FlowConfigPage | FlowConfig | Subflow,
    abortCondition?: UIElementTraversalVisitorAbortCondition
): FlowConfigField[] {
    const fields: FlowConfigField[] = []

    traverseUIElement(
        element,
        e => {
            switch (e.__typename) {
                case 'BooleanUIElement':
                    fields.push(e.field)
                    break
                case 'NumberUIElement':
                    fields.push(e.field)
                    break
                case 'StringUIElement':
                    fields.push(e.field)
                    break
                case 'DateUIElement':
                    fields.push(e.field)
                    break
                case 'SingleSelectionUIElement':
                    fields.push(e.field)
                    break
                case 'FileUIElement':
                    fields.push(e.idField)
                    break
                case 'GroupUIElement':
                case 'ArrayUIElement':
                case 'CustomUIElement':
                case 'ChipGroupUIElement':
                    break
                case 'KeyValueListUIElement':
                    fields.push(...e.items.map(i => i.fieldValue.field))
                    break
                case 'TextUIElement':
                    break
                case 'FieldTextUIElement':
                    fields.push(e.fieldValue.field)
                    break
                case 'TableUIElement':
                    fields.push(...e.columns.map(c => c.fieldValue.field))
                    break
                case 'ImageGalleryUIElement':
                    if (e.idFieldValue != null) {
                        fields.push(e.idFieldValue.field)
                    }
                    break
                default:
                    console.warn("Unknown UI element type", e.__typename, 'in', toRaw(e))
                    break
            }
        },
        abortCondition
    )

    //remove duplicates
    return fields.filter((field, index, self) =>
        index === self.findIndex(f => areFlowConfigFieldsEqual(f, field))
    )
}

export function areFlowConfigFieldsEqual(field1: FlowConfigField, field2: FlowConfigField): boolean {
    const f1 = toRaw(field1)
    const f2 = toRaw(field2)

    return f1.id === f2.id
    //to be continued ...
}

export function serializeFlowConfigPageForRoute(page: FlowConfigPage): Optional<string> {
    return serializeFlowConfigPageIdForRoute(page.id)
}

function serializeFlowConfigPageIdForRoute(pageId: string): Optional<string> {
    try {
        return encodeURIComponent(pageId)
    } catch (e) {
        console.warn("Could not serialize flow config page id", pageId, e)
        return null
    }
}

export function pageFromId(flowConfig: Optional<FlowConfig>, pageId: Optional<string>): Optional<FlowConfigPage> {
    if (flowConfig === null) {
        return null
    }
    if (pageId === null) {
        return null
    }
    return flowConfig.pagesView.find(p => p.id === pageId) ?? flowConfig.pagesEdit.find(p => p.id === pageId) ?? null
}

export function pageFromCustomUIElementId(flowConfig: Optional<FlowConfig>, customUIElementId: string): Optional<[FlowConfigPage, ListingContext]> {
    if (flowConfig === null) {
        return null
    }

    for (const page of flowConfig.pagesView) {
        if (existsCustomUIElementInPage(page, customUIElementId)) {
            return [page, "VIEW"]
        }
    }

    for (const page of flowConfig.pagesEdit) {
        if (existsCustomUIElementInPage(page, customUIElementId)) {
            return [page, "EDIT"]
        }
    }

    return null
}

/**
 * @return index of the selected page or null if the page is not found
 */
export function deserializeFlowConfigPageIndexFromRoute(
    pages: readonly FlowConfigPage[],
    route: RouteLocationNormalizedLoaded
): Optional<string> {
    const encodedId = route.hash.substring(1).trim()
    try {
        const id = decodeURIComponent(encodedId)
        if (id === '') {
            return pages.length <= 0 ? null : pages[0].id
        }
        const index = pages.findIndex(page => page.id === id)
        if (index < 0) {
            console.warn("Could not find flow config page with id", id)
            return pages.length <= 0 ? null : pages[0].id
        }
        return id
    } catch (e) {
        console.warn("Could not deserialize flow config page from route", encodedId, e)
        return pages.length <= 0 ? null : pages[0].id
    }
}

export function listingContextRouteForFlowConfigPage(
    listingId: string,
    page: FlowConfigPage,
    targetContext: ListingContext,
    isTargetPage: boolean = false
): RouteLocationNamedRaw {
    const relatedPage: RelatedFlowConfigPage | undefined = isTargetPage
        ? {
            context: targetContext,
            pageId: page.id
        }
        : page.relatedPages.find(p => p.context === targetContext)
    const serializedRelatedPageId = relatedPage === undefined ? null : serializeFlowConfigPageIdForRoute(relatedPage.pageId)
    const hash = serializedRelatedPageId === null ? undefined : `#${serializedRelatedPageId}`

    switch (targetContext) {
        case 'VIEW':
            return {
                name: 'viewListing',
                params: {
                    id: listingId
                },
                hash
            }
        case 'EDIT':
            return {
                name: 'editListing',
                params: {
                    id: listingId
                },
                hash
            }
        default:
            console.warn("Unsupported listing context", targetContext)
            return {
                name: 'root',
            }
    }
}