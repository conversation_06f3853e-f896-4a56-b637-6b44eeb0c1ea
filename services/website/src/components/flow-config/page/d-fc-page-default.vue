<template>
    <d-fc-layout>
        <template v-if="isAnyLeftElementVisible"
                  #left>
            <d-fc-ui-elements :elements="leftElements"/>
        </template>

        <template v-if="isAnyRightElementVisible"
                  #right>
            <d-fc-env-injector v-if="page.layout == null"
                               :env="envRight">
                <d-fc-center-container>
                    <d-fc-ui-elements :elements="rightElements"/>
                </d-fc-center-container>
            </d-fc-env-injector>

            <d-fc-env-injector v-else-if="page.layout === '2_COL_RIGHT_WIDER'"
                               :env="envRight">
                <d-fc-center-container wider>
                    <d-fc-ui-elements :elements="rightElements"/>
                </d-fc-center-container>
            </d-fc-env-injector>

            <!-- only one right element allowed -->
            <d-fc-env-injector v-else-if="page.layout === '2_COL_RIGHT_FILL'"
                               :env="envRightFill">
                <d-fc-ui-element :element="rightElements[0]"
                                 is-first-element
                                 is-last-element/>
            </d-fc-env-injector>

            <d-fc-unknown-type v-else
                               :data="page"
                               :supported-types="['null', '2_COL_RIGHT_FILL', '2_COL_RIGHT_WIDER']"
                               :type="page.layout"
                               clazz="FlowConfigPage.layout"/>
        </template>
    </d-fc-layout>
</template>

<script lang="ts"
        setup>
    import {computed, inject} from "vue";
    import {FlowConfigPage, UiElement} from "@/adapter/graphql/generated/graphql";
    import {isUIElementVisible} from "@/components/flow-config/use-flow-config";
    import {useDisplay} from "vuetify";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import DFcUiElements from "@/components/flow-config/d-fc-ui-elements.vue";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";
    import DFcUiElement from "@/components/flow-config/ui-element/d-fc-ui-element.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import {LFlowDataInjection, LListingContextInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import DFcEnvInjector from "@/components/flow-config/page/d-fc-env-injector.vue";
    import DFcCenterContainer from "@/components/flow-config/d-fc-center-container.vue";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";

    const props = defineProps<{
        page: FlowConfigPage
    }>()

    const listing = inject(LListingInjection)!
    const flowData = inject(LFlowDataInjection)!
    const context = inject(LListingContextInjection)!

    const envRight = computed<ListingEnvironment>(() => ({
        context: context.value,
        layout: {
            position: 'RIGHT',
            fill: false,
        }
    }))
    const envRightFill = computed<ListingEnvironment>(() => ({
        context: context.value,
        layout: {
            position: 'RIGHT',
            fill: true,
        }
    }))

    const {smAndDown} = useDisplay()

    const leftElements = computed<readonly UiElement[]>(() => {
        const elements = props.page.children
        if (smAndDown.value) {
            return elements
        }
        return elements.filter(e => e.preferredLayoutPosition == null
            || e.preferredLayoutPosition === 'LEFT'
            || e.preferredLayoutPosition === 'DEFAULT'
            || e.preferredLayoutPosition === 'MOBILE'
            || e.preferredLayoutPosition === 'PREVIEW'
            || e.preferredLayoutPosition === 'MOBILE_PREVIEW'
        )
    })

    const rightElements = computed<readonly UiElement[]>(() => {
        if (smAndDown.value) {
            return []
        }
        return props.page.children.filter(e => e.preferredLayoutPosition === 'RIGHT' || e.preferredLayoutPosition === 'DETAIL')
    })

    const isAnyLeftElementVisible = computed<boolean>(() => DEBUG_FLOW_CONFIG
        ? leftElements.value.length > 0
        : leftElements.value.some(e => isUIElementVisible(
            e,
            null,
            context.value,
            listing.value,
            flowData.value
        ))
    )
    const isAnyRightElementVisible = computed<boolean>(() => DEBUG_FLOW_CONFIG
        ? rightElements.value.length > 0
        : rightElements.value.some(e => isUIElementVisible(
            e,
            null,
            context.value,
            listing.value,
            flowData.value
        ))
    )
</script>

<style lang="scss"
       scoped>
</style>