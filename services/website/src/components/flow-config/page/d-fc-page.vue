<template>
    <d-fc-page-default v-if="page.type === null || page.type === undefined"
                       :page="page"/>

    <d-fc-page-address v-else-if="page.type === 'ADDRESS'"
                       :page="page"/>

    <d-fc-page-attractivity v-else-if="page.type === 'ATTRACTIVITY'"/>

    <d-fc-page-location v-else-if="page.type === 'LOCATION'"
                        :page="page"/>

    <d-fc-page-admin-boundary v-else-if="page.type === 'ADMIN_BOUNDARY'"/>

    <d-fc-page-project-configurator v-else-if="page.type === 'PROJECT_CONFIGURATOR'"/>

    <d-fc-unknown-type v-else
                       :data="page"
                       :supported-types="[
                           'null',
                           'ADDRESS',
                           'ATTRACTIVITY',
                           'LOCATION',
                           'ADMIN_BOUNDARY',
                           'PROJECT_CONFIGURATOR'
                       ]"
                       :type="page.type"
                       clazz="FlowConfigPage"/>
</template>

<script lang="ts"
        setup>
    import {FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcPageDefault from "@/components/flow-config/page/d-fc-page-default.vue";
    import DFcPageAddress from "@/components/flow-config/page/address/d-fc-page-address.vue";
    import DFcPageAttractivity from "@/components/flow-config/page/attractivity/d-fc-page-attractivity.vue";
    import DFcPageLocation from "@/components/flow-config/page/location/d-fc-page-location.vue";
    import DFcPageAdminBoundary from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary.vue";
    import DFcPageProjectConfigurator from "@/components/flow-config/page/d-fc-page-project-configurator.vue";

    defineProps<{
        page: FlowConfigPage
    }>()
</script>

<style scoped>
</style>