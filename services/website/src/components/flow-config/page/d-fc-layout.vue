<template>
    <div v-if="$slots.left || $slots.right"
         ref="content"
         class="container">
        <div v-if="$slots.left"
             :style="{
                width: $slots.right && mdAndUp ? (lgAndUp ? '600px' : '500px') : '100%',
                height: `${height}px`,
            }"
             class="leftContainer">
            <d-card v-if="$slots.right && mdAndUp"
                    class="h-100 w-100"
                    rounded="0"
                    style="border-left: 0; border-top: 0; border-bottom: 0;"
                    variant="elevated">
                <slot name="left"/>
            </d-card>
            <d-fc-center-container v-else>
                <slot name="left"/>
            </d-fc-center-container>
        </div>

        <div v-if="$slots.right && mdAndUp"
             ref="rightContainer"
             :style="{height: `${height}px`}"
             class="rightContainer">
            <slot v-if="isRightContentVisible"
                  name="right"/>
            <d-loading v-else/>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {useDisplay} from "vuetify";
    import {onMounted, onUnmounted, shallowRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {useElementSize} from "@vueuse/core";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcCenterContainer from "@/components/flow-config/d-fc-center-container.vue";
    import debounce from "debounce";
    import DLoading from "@/components/fragment/d-loading.vue";

    const content = shallowRef<Optional<HTMLDivElement>>(null)
    const {height} = useElementSize(content)

    const {
        mdAndUp,
        lgAndUp
    } = useDisplay()

    const isRightContentVisible = shallowRef<boolean>(false)
    const rightContainer = shallowRef<HTMLElement>();
    const debouncedOnRightContainerResized = debounce(onRightContainerResized, 100)
    const rightContainerResizeObserver = new ResizeObserver(entries => debouncedOnRightContainerResized(entries))

    onMounted(() => {
        const rContainer = rightContainer.value
        if (rContainer) {
            rightContainerResizeObserver.observe(rContainer)
        }
    })

    onUnmounted(() => {
        rightContainerResizeObserver.disconnect()
    })

    function onRightContainerResized(entries: ResizeObserverEntry[]) {
        const {width, height} = entries[0].contentRect
        isRightContentVisible.value = height > 0
    }
</script>

<style lang="scss"
       scoped>
    .container {
        display: flex;
        flex-flow: row;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .leftContainer > * {
        overflow-y: auto;
    }

    .rightContainer {
        flex: 1 1 auto;
        overflow-y: auto;
    }
</style>