import {AttractivenessCategory} from "@/model/listing/AttractivenessCategory";
import {doorbitColors} from "@/adapter/vuetify/theme/doorbit-colors";


export const PRIMARY_CHART_COLOR_LIGHT = doorbitColors.darkestGray
export const PRIMARY_CHART_COLOR_DARK = doorbitColors.lightGray

export const SECONDARY_CHART_COLOR_LIGHT = doorbitColors.green
export const SECONDARY_CHART_COLOR_DARK = doorbitColors.lightGreen

export const SECONDARY_CHART_COLOR_BACKGROUND = "rgba(0,159,100, 0.1)"

export const BORDER_COLOR_LIGHT = doorbitColors.lightestGray
export const BORDER_COLOR_DARK = doorbitColors.darkestGray


export const COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT: Record<AttractivenessCategory, string> = {
    "LOCATION": doorbitColors.darkestGray,
    "LIVING_QUALITY": doorbitColors.green,
    "SOCIODEMOGRAPHY": doorbitColors.darkOrange,
    "ECONOMY": doorbitColors.darkGray,
}
export const COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK: Record<AttractivenessCategory, string> = {
    "LOCATION": doorbitColors.lightGray,
    "LIVING_QUALITY": doorbitColors.lightGreen,
    "SOCIODEMOGRAPHY": doorbitColors.orange,
    "ECONOMY": doorbitColors.blue,
}

export const CHART_COLORS_LIGHT = [
    doorbitColors.darkestGray,
    doorbitColors.green,
    doorbitColors.darkOrange,
    doorbitColors.darkGray,
    doorbitColors.yellow,
]

export const CHART_COLORS_DARK = [
    doorbitColors.lightGray,
    doorbitColors.lightGreen,
    doorbitColors.orange,
    doorbitColors.blue,
    doorbitColors.lightYellow,
]

export const EMPTY_COLOR_LIGHT = doorbitColors.lightGray
export const EMPTY_COLOR_DARK = doorbitColors.darkGray