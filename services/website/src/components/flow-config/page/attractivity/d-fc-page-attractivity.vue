<template>
    <d-fc-layout>
        <template #left>
            <v-container>
                <v-layout v-if="smAndDown"
                          class="justify-center mb-8">
                    <d-fc-page-attractivity-chart v-if="attractiveness"
                                                  :attractiveness="attractiveness"/>
                </v-layout>

                <template v-if="loading">
                    <d-skeleton-loader type="heading"/>
                    <d-skeleton-loader type="text"/>
                    <d-skeleton-loader type="subtitle"/>
                    <d-skeleton-loader type="article"/>
                    <d-skeleton-loader type="subtitle"/>
                    <d-skeleton-loader type="article"/>
                    <d-skeleton-loader type="subtitle"/>
                    <d-skeleton-loader type="article"/>
                </template>
                <template v-else>
                    <d-h5 class="mb-4">
                        <v-layout class="justify-space-between align-center">
                            {{ t('components.listing.detail.attractivity.attractivityScoreTitle') }}
                            <d-listing-attractivness-label v-if="attractiveness"
                                                           :attractiveness="attractiveness"
                                                           size="L"/>
                        </v-layout>
                    </d-h5>
                    <div class="text-body-1">{{ t('components.listing.detail.attractivity.attractivityScoreDescription') }}</div>
                    <template v-if="attractiveness">
                        <v-divider class="my-6"/>
                        <template v-for="(category, categoryIndex) in attractiveness.categories"
                                  :key="categoryIndex">
                            <div v-if="!category.isDataMissing">
                                <v-layout class="align-center">
                                    <v-icon :color="isDark ? COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK[category.key] : COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT[category.key]"
                                            :icon="mdiCircle"
                                            class="me-2"
                                            size="x-small"/>
                                    <v-layout class="align-center">
                                        <d-h5>
                                            {{ t(`enums.attractivenessCategoryType.${category.key}`) }}
                                        </d-h5>
                                        <v-spacer/>
                                        <d-h5>
                                            {{ n(category.score / category.achievableScore, 'percent') }}
                                        </d-h5>
                                    </v-layout>
                                </v-layout>
                                <div v-for="(ratingCritera, ratingCriteriaIndex) in category.ratingCriteria"
                                     :key="ratingCriteriaIndex"
                                     class="mt-6">
                                    <v-layout>
                                        <d-h6>{{ t(`enums.attractivenessCriteriaType.${ratingCritera.key}`) }}</d-h6>
                                        <template v-if="category.key === 'ECONOMY' || category.key === 'SOCIODEMOGRAPHY'">
                                            <v-spacer/>
                                            <d-h6 class="text-subtitle-2">{{ n(ratingCritera.score / ratingCritera.achievableScore, 'percent') }}</d-h6>
                                        </template>
                                    </v-layout>
                                    <div class="text-body-2 mt-2">
                                        {{ t(`components.listing.detail.attractivity.ratingCriteriaDescription.${ratingCritera.key}`) }}
                                    </div>
                                    <v-layout v-if="ratingCritera.pointsOfInterest && ratingCritera.pointsOfInterest.length >0"
                                              class="flex-wrap pois mt-4">
                                        <template v-for="poi in ratingCritera.pointsOfInterest"
                                                  :key="poi.type">
                                            <d-chip
                                                :prepend-icon="poi.available? mdiCheck : undefined"
                                                :text="t(`enums.pointOfInterestType.${poi.type}`)"
                                                :type="poi.available? 'success' : 'default'"/>
                                        </template>
                                    </v-layout>
                                </div>
                                <v-divider v-if="categoryIndex < attractiveness.categories.filter(cat => !cat.isDataMissing).length -1"
                                           class="my-6"/>
                            </div>
                        </template>
                    </template>
                </template>
            </v-container>
        </template>

        <template #right>
            <v-container
                v-if="attractiveness"
                class="h-100"
                fluid>
                <d-sheet class="h-100"
                         rounded="lg">
                    <v-layout class="justify-center pa-4 flex-wrap h-100 overflow-auto">
                        <d-fc-page-attractivity-chart :attractiveness="attractiveness"
                                                      class="mt-16"/>
                        <v-layout class="flex-0-0-100 py-8 mt-16 mb-4">
                            <v-row class="justify-center">
                                <template v-for="(category, index) in attractiveness.categories"
                                          :key="index">
                                    <v-col
                                        v-if="!category.isDataMissing"
                                        class="justify-center"
                                        lg="3"
                                        md="6">
                                        <v-layout class="justify-center">
                                            <d-fc-page-attractivity-category-chart v-if="!category.isDataMissing"
                                                                                   :category="category"/>
                                        </v-layout>
                                        <d-h6 class="text-center my-6">{{ t(`enums.attractivenessCategoryType.${category.key}`) }}</d-h6>
                                        <template v-for="(ratingCriteria, ratingCriteriaIndex) in category.ratingCriteria"
                                                  :key="ratingCriteriaIndex">
                                            <d-fc-page-attractivity-rating-criteria-chart
                                                :category-type="category.key"
                                                :rating-criteria="ratingCriteria"
                                                class="mt-4"/>
                                            <div class="text-body-2 text-truncate mt-1">{{ t(`enums.attractivenessCriteriaType.${ratingCriteria.key}`) }}</div>
                                        </template>
                                    </v-col>
                                </template>
                            </v-row>
                        </v-layout>
                    </v-layout>
                </d-sheet>
            </v-container>
        </template>
    </d-fc-layout>
</template>

<script lang="ts"
        setup>
    import {Attractiveness, DFlowConfigPageAttractivityQueryVariables, useDFlowConfigPageAttractivityQuery} from "@/adapter/graphql/generated/graphql";
    import {computed, inject} from "vue";
    import {Optional} from "@/model/Optional";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {useI18n} from "vue-i18n";
    import DH5 from "@/adapter/vuetify/theme/components/text/headline/d-h5.vue";
    import {mdiCheck, mdiCircle} from "@mdi/js";
    import {useDisplay} from "vuetify";
    import DChip from "@/adapter/vuetify/theme/components/chip/d-chip.vue";
    import DSkeletonLoader from "@/adapter/vuetify/theme/components/skeleton-loader/d-skeleton-loader.vue";
    import DSheet from "@/adapter/vuetify/theme/components/card/d-sheet.vue";
    import DListingAttractivnessLabel from "@/components/listing/old/components/d-listing-attractivness-label.vue";
    import {COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK, COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import DFcPageAttractivityRatingCriteriaChart from "@/components/flow-config/page/attractivity/d-fc-page-attractivity-rating-criteria-chart.vue";
    import DFcPageAttractivityCategoryChart from "@/components/flow-config/page/attractivity/d-fc-page-attractivity-category-chart.vue";
    import DFcPageAttractivityChart from "@/components/flow-config/page/attractivity/d-fc-page-attractivity-chart.vue";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {useColorMode} from "@vueuse/core";

    const listingId = inject(LListingIdInjection)!

    const {t, n} = useI18n()
    const {smAndDown} = useDisplay()
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const queryVariables = computed<DFlowConfigPageAttractivityQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        result: queryResult,
        loading
    } = useDFlowConfigPageAttractivityQuery(queryVariables)

    const attractiveness = computed<Optional<Attractiveness>>(() => queryResult.value?.listing?.attractiveness ?? null)
</script>

<style scoped>
    .pois {
        row-gap: 8px;
        column-gap: 8px;
    }
</style>