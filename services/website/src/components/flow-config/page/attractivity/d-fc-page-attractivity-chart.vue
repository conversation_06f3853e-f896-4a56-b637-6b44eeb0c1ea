<template>
    <div class="chartWrapper">
        <d-h6 class="attractivenessScore position-absolute text-center">
            <div>{{ n(score, 'percent') }}</div>
            <div>{{ t('components.listing.detail.attractivity.percentageTotal') }}</div>
        </d-h6>
        <Doughnut :data="data"
                  :options="options"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {ArcElement, Chart as ChartJS, Tooltip} from 'chart.js'
    import {Doughnut} from 'vue-chartjs'
    import {Attractiveness} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";
    import {useI18n} from "vue-i18n";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {BORDER_COLOR_DARK, BORDER_COLOR_LIGHT, COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK, COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT, EMPTY_COLOR_DARK, EMPTY_COLOR_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(ArcElement, Tooltip)

    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const {attractiveness} = defineProps<{
        attractiveness: Attractiveness,
    }>()

    const {t, n} = useI18n()

    const score = computed<number>(() => attractiveness.score / attractiveness.achievableScore)

    const data = computed(() => {
        const labels: string[][] = []
        const datasets: any[] = []
        const categoriesSorted = attractiveness.categories.toSorted((a, b) => a.achievableScore - a.score < b.achievableScore - b.score ? 1 : -1)
        categoriesSorted.forEach((category, index) => {
            if (!category.isDataMissing) {
                const categoryLabels = [category.key]
                const data = [category.score]
                if (category.score < category.achievableScore) {
                    categoryLabels.push("EMPTY") //TODO: wird eh nicht angezeigt
                    data.push(category.achievableScore - category.score)
                }
                labels.push(categoryLabels)
                datasets.push({
                    backgroundColor: isDark.value ? [COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK[category.key], EMPTY_COLOR_DARK] : [COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT[category.key], EMPTY_COLOR_LIGHT],
                    data
                })
                //console.log(categoryLabels, data)
            }
        })
        return {
            labels,
            datasets
        }
    })

    const options = computed(() => {
        return {
            responsive: true,
            cutout: "50%",
            borderWidth: 1,
            borderColor: isDark.value ? BORDER_COLOR_DARK : BORDER_COLOR_LIGHT,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    })
</script>

<style scoped>
    .chartWrapper, .attractivenessScore {
        width: 260px;
        height: 260px;
    }

    .attractivenessScore {
        padding-top: 110px;
    }
</style>