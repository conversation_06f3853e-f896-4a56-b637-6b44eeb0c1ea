<template>
    <div class="chartWrapper">
        <d-h6 class="attractivenessScore position-absolute text-center">
            <div>{{ n(score, 'percent') }}</div>
        </d-h6>
        <Doughnut :data="data"
                  :options="options"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {ArcElement, Chart as ChartJS, Tooltip} from 'chart.js'
    import {Doughnut} from 'vue-chartjs'
    import {AttractivenessCategory} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";
    import {useI18n} from "vue-i18n";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK, COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT, EMPTY_COLOR_DARK, EMPTY_COLOR_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(ArcElement, Tooltip)

    const {category} = defineProps<{
        category: AttractivenessCategory,
    }>()

    const {n} = useI18n()
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const score = computed<number>(() => category.score / category.achievableScore)

    const data = computed(() => {
        const labels = [category.key]
        const data = [category.score]
        if (category.score < category.achievableScore) {
            labels.push("EMPTY") //TODO: wird eh nicht angezeigt
            data.push(category.achievableScore - category.score)
        }
        return {
            labels,
            datasets: [
                {
                    backgroundColor: isDark.value ? [COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK[category.key], EMPTY_COLOR_DARK] : [COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT[category.key], EMPTY_COLOR_LIGHT],
                    data
                }
            ]
        }
    })

    const options = computed(() => {
        return {
            responsive: true,
            cutout: "75%",
            borderWidth: 0,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    })
</script>


<style scoped>
    .chartWrapper, .attractivenessScore {
        width: 120px;
        height: 120px;
    }

    .attractivenessScore {
        padding-top: 50px;
    }
</style>