<template>
    <div class="chartWrapper">
        <div class="attractivenessScore text-caption position-absolute text-center">
            {{ n(score, 'percent') }}
        </div>
        <Doughnut
            :data="data"
            :options="options"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {ArcElement, Chart as ChartJS, Tooltip} from 'chart.js'
    import {Doughnut} from 'vue-chartjs'
    import {Attractiveness} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";
    import {useI18n} from "vue-i18n";
    import {COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK, COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT, EMPTY_COLOR_DARK, EMPTY_COLOR_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(ArcElement, Tooltip)

    const {attractiveness} = defineProps<{
        attractiveness: Attractiveness,
    }>()

    const {n} = useI18n()
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const score = computed<number>(() => attractiveness.score / attractiveness.achievableScore)

    const data = computed(() => {
        const achievableScore = attractiveness.achievableScore
        const filteredCategories = attractiveness.categories.filter(category => !category.isDataMissing)
        const labels = filteredCategories.map(category => category.key)
        const values = filteredCategories.map(category => category.score)
        if (values.reduce((a, b) => a + b, 0) < achievableScore) {
            labels.push('EMPTY') //TODO: wird eh nicht angezeigt
            values.push(achievableScore - values.reduce((a, b) => a + b, 0))
        }
        return {
            labels: labels,
            datasets: [
                {
                    backgroundColor: isDark.value ?
                        filteredCategories.map(category => COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK[category.key]).concat([EMPTY_COLOR_DARK]) :
                        filteredCategories.map(category => COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT[category.key]).concat([EMPTY_COLOR_LIGHT])

                    ,
                    data: values
                }
            ]
        }
    })

    const options = computed(() => {
        return {
            responsive: true,
            cutout: "75%",
            events: [],
            borderWidth: 0,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    })
</script>


<style scoped>
    .chartWrapper, .attractivenessScore {
        width: 56px;
        height: 56px;
    }

    .attractivenessScore {
        padding-top: 18px;
    }
</style>