<template>
    <div class="chartWrapper">
        <Bar :data="data"
             :options="options"
             class="rounded-lg bg-light"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {Bar} from 'vue-chartjs'
    import {BarElement, BarOptions, CategoryScale, Chart as ChartJS, ChartOptions, LinearScale} from 'chart.js'
    import {AttractivenessRatingCriteria} from "@/adapter/graphql/generated/graphql";
    import {AttractivenessCategory} from "@/model/listing/AttractivenessCategory";
    import {computed} from "vue";
    import {COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK, COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(LinearScale, CategoryScale, BarElement)

    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const {ratingCriteria, categoryType} = defineProps<{
        ratingCriteria: AttractivenessRatingCriteria,
        categoryType: AttractivenessCategory
    }>()

    const color = computed<string>(() => isDark.value ? COLOR_TO_ATTRACTIVENESS_CATEGORY_DARK[categoryType] : COLOR_TO_ATTRACTIVENESS_CATEGORY_LIGHT[categoryType])

    const data = computed(() => ({
        labels: [
            ratingCriteria.key,
        ],
        datasets: [
            {
                backgroundColor: color.value,
                data: [ratingCriteria.score / ratingCriteria.achievableScore]
            }
        ]
    }))

    const options = computed<ChartOptions<"bar"> & BarOptions>(() => ({
        inflateAmount: 1,
        base: 0,
        borderSkipped: false,
        borderRadius: 10,
        borderWidth: 0,
        indexAxis: 'y',
        backgroundColor: color.value,
        borderColor: color.value,
        maintainAspectRatio: false,
        scales: {
            x: {
                max: 1,
                display: false,
                grid: {
                    display: false,
                },
                stacked: true
            },
            y: {
                display: false,
                grid: {
                    display: false,
                },
                stacked: true
            },
        },
        plugins: {
            legend: {
                display: false
            }
        },
        events: []
    }))
</script>

<style lang="scss"
       scoped>
    .chartWrapper {
        width: 100%;
        height: 8px;
    }

    .bg-light {
        background-color: rgb(var(--v-theme-d-outline-default));
    }
</style>