<template>
    <div class="mapContainer">
        <div class="mapWrapper">
            <!-- TODO: center-zoom später wieder aktivieren, wenn zoom gefixt -->
            <lf-map :focused-geolocation="geolocation ?? undefined"
                    :initial-bounds="LISTING_REGION_BOUNDS[listingRegion]"
                    :padding="markerMapPadding"
                    disable-overlay>
                <lf-tile-layer/>

                <lf-circle :border-color="themeColorOutlineError"
                           :border-opacity="1"
                           :color="themeColorOutlineError"
                           :opacity="0.25"
                           :radius="smAndDown ? 2 : 5"
                           center-on-map/>

                <lf-circle v-if="showObfuscationRadius"
                           :border-color="themeColorTextInfo"
                           :border-opacity="0.75"
                           :color="themeColorTextInfo"
                           :opacity="0.25"
                           :radius="CONFIG.ADDRESS_OBFUSCATION_RADIUS_MAX"
                           center-on-map/>

                <template v-if="geolocalization">
                    <lf-circle :border-color="themeColorOutlineSuccess"
                               :border-opacity="0"
                               :color="themeColorOutlineSuccess"
                               :opacity="(1 - geolocalizationPulseFactor1) * geolocalizationPulseMaxOpacity"
                               :radius="geolocalizationPulseSize * geolocalizationPulseFactor1"
                               center-on-map/>
                    <lf-circle :border-color="themeColorOutlineSuccess"
                               :border-opacity="0"
                               :color="themeColorOutlineSuccess"
                               :opacity="(1 - geolocalizationPulseFactor2) * geolocalizationPulseMaxOpacity"
                               :radius="geolocalizationPulseSize * geolocalizationPulseFactor2"
                               center-on-map/>
                </template>

                <d-map-marker-house :center-on-map-zoom="17"
                                    :padding="markerPadding"
                                    :size="markerSize"
                                    center-on-map
                                    @update:geolocation="onGeolocationChangedByMarker">
                    <template #icon>
                        <v-icon :icon="mdiHome"
                                :size="`${markerSize * (smAndDown ? 0.6 : 0.7)}px`"/>
                    </template>
                </d-map-marker-house>
            </lf-map>
        </div>

        <div :style="{
            'padding-top': `${(markerMapPadding).top}px`,
            'padding-bottom': `${markerMapPadding.bottom}px`,
            'padding-left': `${markerMapPadding.left}px`,
            'padding-right': `${markerMapPadding.right}px`,
        }"
             class="mapMarkerInfoLayerWrapper">
            <div class="mapMarkerInfoLayer">
                <d-card :style="{
                    'border-color': markerInfoBorderColor,
                    'background-color': markerInfoBackgroundColor,
                    'margin-bottom': `${markerIconHeight * 2 + markerInfoHeight + 32}px`,
                    'height': `${markerInfoHeight}px`,
                }"
                        class="mapMarkerInfo"
                        elevation="2"
                        variant="elevated">
                    <div :style="{'background-color': markerInfoTextColor}"
                         class="icon">
                        <d-progress-circular v-if="markerInfoStatus === 'geolocalization'"
                                             :bg-color="markerInfoBorderColor"
                                             :color="markerInfoBackgroundColor"
                                             :size="24"
                                             indeterminate/>
                        <v-icon v-else-if="markerInfoStatus === 'invalid'"
                                :color="markerInfoBackgroundColor"
                                :icon="mdiCursorMove"/>
                        <v-icon v-else-if="markerInfoStatus === 'valid'"
                                :color="markerInfoBackgroundColor"
                                :icon="mdiHome"/>
                        <v-icon v-else-if="markerInfoStatus === 'obfuscated'"
                                :color="markerInfoBackgroundColor"
                                :icon="mdiEyeOff"/>
                    </div>
                    <div :style="{color: markerInfoTextColor}"
                         class="status text-caption font-weight-bold">
                        <template v-if="markerInfoStatus === 'geolocalization'">
                            {{ t('components.listing.edit.location.markerInfoStatus.geolocalization') }}
                        </template>
                        <template v-else-if="markerInfoStatus === 'invalid'">
                            {{ t('components.listing.edit.location.markerInfoStatus.invalid') }}
                        </template>
                        <template v-else-if="markerInfoStatus === 'valid'">
                            {{ t('components.listing.edit.location.markerInfoStatus.valid') }}
                        </template>
                        <template v-else-if="markerInfoStatus === 'obfuscated'">
                            {{ t('components.listing.edit.location.markerInfoStatus.obfuscated') }}
                        </template>
                    </div>
                    <div :style="{color: themeColorTextDefault}"
                         class="address text-body-2">
                        <v-skeleton-loader v-if="addressLine === null"
                                           :boilerplate="!geolocalization"
                                           class="skeletonLoaderText"
                                           type="text"/>
                        <template v-else>
                            {{ addressLine }}
                        </template>
                    </div>
                </d-card>
            </div>
        </div>

        <div :class="{'pa-8': !xs, 'pa-4': xs}"
             :style="{bottom: bottomPadding ? `${bottomPadding}px` : '16px'}"
             class="mapGeolocalizationButton">
            <d-btn :icon="geolocalization ? mdiCrosshairsGps : mdiCrosshairsOff"
                   :type="geolocalization ? 'success' : 'default'"
                   variant="elevated"
                   @click="toggleGeolocalization"/>
        </div>

        <div ref="errorWrapper"
             :class="{'pa-0': geolocationError === null, 'ignoreSafeArea': context === 'EDIT'}"
             class="errorWrapper">
            <d-fc-page-address-geolocalization v-model:geolocalization="innerGeolocalization"
                                               @update:geolocation-error="onGeolocationError"
                                               @update:geolocation="onGeolocationChangedByGeolocalization"/>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {mdiCrosshairsGps, mdiCrosshairsOff, mdiCursorMove, mdiEyeOff, mdiHome} from "@mdi/js";
    import LfMap from "@/adapter/leaflet/components/lf-map.vue";
    import DMapMarkerHouse from "@/components/map/d-map-marker-house.vue";
    import LfCircle from "@/adapter/leaflet/components/lf-circle.vue";
    import {CONFIG} from "@/config";
    import {useDisplay} from "vuetify";
    import {LatLng} from "leaflet";
    import {LISTING_REGION_BOUNDS, ListingRegion} from "@/model/listing/ListingRegion";
    import {computed, inject, onUnmounted, shallowRef, watchEffect} from "vue";
    import {Optional} from "@/model/Optional";
    import LfTileLayer from "@/adapter/leaflet/components/lf-tile-layer.vue";
    import {createLfPadding, LfPadding} from "@/adapter/leaflet/LfPadding";
    import anime, {AnimeInstance, AnimeParams} from "animejs";
    import {convert} from "@/utility/converter";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {LfGeolocationWithZoom} from "@/adapter/leaflet/LfGeolocationWithZoom";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useI18n} from "vue-i18n";
    import {useElementSize} from "@vueuse/core";
    import DProgressCircular from "@/adapter/vuetify/theme/components/progress/d-progress-circular.vue";
    import {ListingEditGeolocationWithAccuracyAndZoom} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracyAndZoom";
    import {ListingEditGeolocationWithAccuracy} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracy";
    import DFcPageAddressGeolocalization from "@/components/flow-config/page/address/d-fc-page-address-geolocalization.vue";
    import {LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    const markerInfoHeight = 50 //in px

    const props = defineProps<{
        listingRegion: ListingRegion
        geolocation: Optional<LfGeolocationWithZoom>
        showObfuscationRadius: boolean
        geolocalization: boolean
        geolocalizationZoom: number
        bottomPadding?: number
        addressLine: Optional<string>
    }>()

    const context = inject(LListingContextInjection)!

    const emits = defineEmits<{
        'update:geolocation': [geolocation: ListingEditGeolocationWithAccuracyAndZoom]
        'update:geolocalization': [isGeolocationEnabled: boolean]
    }>()

    const errorWrapper = shallowRef<Optional<HTMLDivElement>>(null)
    const {
        height: errorWrapperHeight
    } = useElementSize(errorWrapper)

    const {t} = useI18n()
    const {xs} = useDisplay()

    type AddressStatus = 'invalid' | 'valid' | 'obfuscated' | 'geolocalization'
    const markerInfoStatus = computed<AddressStatus>(() => {
        if (props.addressLine === null) {
            if (props.geolocalization) {
                return 'geolocalization'
            }
            return 'invalid'
        }
        if (props.showObfuscationRadius) {
            return 'obfuscated'
        }
        return 'valid'
    })
    const markerInfoBorderColor = computed<string>(() => {
        switch (markerInfoStatus.value) {
            case "geolocalization":
            case 'invalid':
                return themeColorOutlineWarning
            case 'valid':
                return themeColorOutlineSuccess
            case 'obfuscated':
                return themeColorOutlineInfo
        }
        return 'magenta'
    })
    const markerInfoBackgroundColor = computed<string>(() => {
        switch (markerInfoStatus.value) {
            case 'geolocalization':
            case 'invalid':
                return themeColorBackgroundWarning
            case 'valid':
                return themeColorBackgroundSuccess
            case 'obfuscated':
                return themeColorBackgroundInfo
        }
        return 'magenta'
    })
    const markerInfoTextColor = computed<string>(() => {
        switch (markerInfoStatus.value) {
            case "geolocalization":
            case 'invalid':
                return themeColorTextWarning
            case 'valid':
                return themeColorTextSuccess
            case 'obfuscated':
                return themeColorTextInfo
        }
        return 'magenta'
    })

    const geolocationError = shallowRef<Optional<GeolocationPositionError>>(null)

    const {smAndDown} = useDisplay()
    const {
        themeColorOutlineInfo,
        themeColorOutlineSuccess,
        themeColorOutlineWarning,
        themeColorOutlineError,
        themeColorBackgroundInfo,
        themeColorBackgroundSuccess,
        themeColorBackgroundWarning,
        themeColorTextInfo,
        themeColorTextSuccess,
        themeColorTextWarning,
        themeColorTextDefault
    } = useDoorbitTheme()

    const markerSize = computed<number>(() => smAndDown.value ? 50 : 100)
    const markerPadding = computed<number>(() => smAndDown.value ? 3 : 6)

    const geolocalizationPulseSize = 500
    const geolocalizationPulseDuration = 5000;
    const geolocalizationPulseMaxOpacity = 0.25;
    const geolocalizationPulseFactor1 = shallowRef<number>(0);
    const geolocalizationPulseFactor2 = shallowRef<number>(0);
    const geolocalizationConfig: AnimeParams = {
        value: 1,
        direction: 'normal',
        easing: 'easeInSine',
        duration: geolocalizationPulseDuration,
        loop: true,
        autoplay: false
    }
    const geolocalizationPulseAnimation1 = anime({
        ...geolocalizationConfig,
        change: (anime: AnimeInstance) => {
            geolocalizationPulseFactor1.value = convert(anime.progress, 0, 100, 0, 1)
        }
    })
    const geolocalizationPulseAnimation2 = anime({
        ...geolocalizationConfig,
        change: (anime: AnimeInstance) => {
            geolocalizationPulseFactor2.value = convert(anime.progress, 0, 100, 0, 1)
        }
    })

    let pulseAnimationTimeoutId: Optional<number> = null

    function startGeolocalizationPulseAnimation() {
        geolocalizationPulseFactor1.value = 0
        geolocalizationPulseFactor2.value = 0

        geolocalizationPulseAnimation1.restart()

        pulseAnimationTimeoutId = setTimeout(() => { //TODO: <<<<<<<<<<<<<<<<< abbrechen
            geolocalizationPulseAnimation2.restart()
        }, geolocalizationPulseDuration / 2)
    }

    function stopGeolocalizationPulseAnimation() {
        geolocalizationPulseAnimation1.pause()
        geolocalizationPulseAnimation2.pause()

        if (pulseAnimationTimeoutId !== null) {
            clearTimeout(pulseAnimationTimeoutId)
            pulseAnimationTimeoutId = null
        }
    }

    onUnmounted(() => {
        stopGeolocalizationPulseAnimation()
    })

    const innerGeolocalization = computed<boolean>({
        get() {
            return props.geolocalization
        },
        set(isEnabled) {
            emits('update:geolocalization', isEnabled)
        }
    })

    watchEffect(() => {
        if (props.geolocalization) {
            startGeolocalizationPulseAnimation()
        } else {
            stopGeolocalizationPulseAnimation()
        }
    })

    function onGeolocationChangedByMarker(geolocation: LatLng) {
        innerGeolocalization.value = false
        emits('update:geolocation', {
            source: 'user',
            latLng: geolocation,
            accuracy: 0,
            zoom: null,
        })
    }

    function onGeolocationChangedByGeolocalization(geolocation: ListingEditGeolocationWithAccuracy) {
        emits('update:geolocation', {
            ...geolocation,
            zoom: props.geolocalizationZoom
        })
    }

    function onGeolocationError(newGeolocationError: Optional<GeolocationPositionError>) {
        geolocationError.value = newGeolocationError
    }

    //TODO: parametrisieren aus d-map-marker.vue
    const markerDiagonal = computed<number>(() => Math.sqrt(2) * markerSize.value)
    const markerBottomTriangleSize = computed<number>(() => (markerDiagonal.value - markerSize.value) / 2)
    const markerIconHeight = computed<number>(() => {
        return markerDiagonal.value - markerBottomTriangleSize.value
    })
    const markerMapPadding = computed<LfPadding>(() => {
        const padding = xs.value ? 16 : 32

        if (props.geolocation === null) {
            return createLfPadding(padding)
        }

        const currentListingMarkerHeight = markerIconHeight.value
        const currentListingMarketHalfWidth = markerSize.value / 2
        return {
            top: currentListingMarkerHeight + padding + (geolocationError.value === null ? 0 : errorWrapperHeight.value),
            bottom: (props.bottomPadding ?? 0) + padding,
            left: currentListingMarketHalfWidth / 2 + padding,
            right: currentListingMarketHalfWidth / 2 + padding
        }
    })

    function toggleGeolocalization() {
        emits('update:geolocalization', !props.geolocalization)
    }
</script>

<style lang="scss"
       scoped>
    .mapContainer {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .mapWrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
    }

    .mapMarkerInfoLayerWrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
        pointer-events: none;
    }

    .mapMarkerInfoLayer {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .mapMarkerInfo {
        width: 220px;
        flex: 0 0 auto;
        display: grid;
        grid-template-areas:
            'icon status'
            'icon address';
        grid-template-columns: 40px 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 0 8px;
        padding-right: 8px;
    }

    .mapMarkerInfo .icon {
        grid-area: icon;
        align-self: stretch;
        justify-self: stretch;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mapMarkerInfo .status {
        grid-area: status;
        align-self: end;
        justify-self: start;
        text-align: start;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: 100%;
    }

    .mapMarkerInfo .address {
        grid-area: address;
        align-self: start;
        justify-self: start;
        text-align: start;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: 100%;
    }

    .mapMarkerInfo .address :deep(.v-skeleton-loader) {
        height: 16px;
        background-color: transparent;
    }

    .mapMarkerInfo .address :deep(.v-skeleton-loader__text) {
        margin: 0;
        height: 8px;
    }

    .mapGeolocalizationButton {
        position: absolute;
        right: 0;
        z-index: 2;
    }

    .mapCardWrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        bottom: 0;
        z-index: 3;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: flex-end;
    }

    .mapCard {
        flex: 1 0 auto;
    }

    .errorWrapper {
        position: relative;
        z-index: 4;
        padding: calc(16px + env(safe-area-inset-top)) 16px 16px;
    }

    .errorWrapper.ignoreSafeArea {
        padding-top: 16px;
    }
</style>