<template>
    <v-slide-y-transition>
        <d-alert v-if="geolocationError"
                 closable
                 type="warning">
            <template v-if="geolocationError.code === geolocationError.PERMISSION_DENIED">
                <template v-if="IS_NATIVE_APP">
                    <p v-if="hasOpenedAppSettings">{{ t('components.listing.edit.location.geolocalizationError.permissionDenied.nativeApp.reloadIntroduction') }}</p>
                    <p v-else>{{ t('components.listing.edit.location.geolocalizationError.permissionDenied.nativeApp.goToSettings.introduction') }}</p>

                    <div class="text-center w-100">
                        <v-btn v-if="!hasOpenedAppSettings"
                               :text="t('components.listing.edit.location.geolocalizationError.permissionDenied.nativeApp.goToSettings.button')"
                               class="mt-4"
                               color="on-warning"
                               variant="text"
                               @click="onOpenAppSettings"/>
                    </div>
                </template>
                <template v-else>
                    <p>{{ t('components.listing.edit.location.geolocalizationError.permissionDenied.reload.introduction') }}</p>
                </template>

                <div class="text-center w-100">
                    <v-btn v-if="!IS_NATIVE_APP || hasOpenedAppSettings"
                           :text="t('components.listing.edit.location.geolocalizationError.permissionDenied.reload.button')"
                           class="mt-4"
                           color="on-warning"
                           variant="text"
                           @click="onPageReload"/>
                </div>
            </template>
            <template v-else-if="geolocationError.code === geolocationError.POSITION_UNAVAILABLE">
                {{ t('components.listing.edit.location.geolocalizationError.positionUnavailable') }}
            </template>
            <template v-else-if="geolocationError.code === geolocationError.TIMEOUT">
                {{ t('components.listing.edit.location.geolocalizationError.timeout') }}
            </template>
            <template v-else>
                {{ t('components.listing.edit.location.geolocalizationError.unknown') }}
            </template>
        </d-alert>
    </v-slide-y-transition>
</template>

<script lang="ts"
        setup>
    import {shallowRef, watch, watchEffect} from "vue";
    import {IS_DEVELOPMENT} from "@/utility/environment";
    import {Optional} from "@/model/Optional";
    import {CONFIG} from "@/config";
    import {useI18n} from "vue-i18n";
    import {IS_NATIVE_APP, NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import {latLng} from "leaflet";
    import {ListingEditGeolocationWithAccuracy} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracy";

    const props = defineProps<{
        geolocalization: boolean
    }>()

    const emit = defineEmits<{
        'update:geolocation': [geolocation: ListingEditGeolocationWithAccuracy]
        'update:geolocalization': [isGeolocalizationEnabled: boolean]
        'update:geolocationError': [geolocationError: Optional<GeolocationPositionError>]
    }>()

    const {t} = useI18n()

    let geolocationWatchId: Optional<number> = null
    const geolocationError = shallowRef<Optional<GeolocationPositionError>>(null)
    const bestGeolocation = shallowRef<Optional<GeolocationCoordinates>>(null)
    const hasOpenedAppSettings = shallowRef<boolean>(false)

    watchEffect(() => {
        if (props.geolocalization) {
            //Spandauer Straße, Berlin
            // emit('update:geolocation', latLng(52.520833, 13.409444))
            startGeolocalization()
        } else {
            //St. Marienkirche, Berlin
            // emit('update:geolocation', latLng(52.521667, 13.410833))
            stopGeolocalization()
        }
    })

    watch(geolocationError, geolocationError => {
        emit('update:geolocationError', geolocationError)
    })

    watch(bestGeolocation, newGeolocation => {
        if (newGeolocation == null) {
            return
        }

        //nur unter https liefert bestGeolocation latitude & longitude zurück (siehe docs)
        const latitude = newGeolocation.latitude === null || newGeolocation.latitude === undefined ? CONFIG.DOORBIT_HEADQUARTER_GEOLOCATION.lat : newGeolocation.latitude;
        const longitude = newGeolocation.longitude === null || newGeolocation.longitude === undefined ? CONFIG.DOORBIT_HEADQUARTER_GEOLOCATION.lng : newGeolocation.longitude;
        const accuracy = newGeolocation.accuracy === null || newGeolocation.accuracy === undefined ? null : newGeolocation.accuracy

        const geolocation = latLng(latitude, longitude)

        const geolocationWithAccuracy: ListingEditGeolocationWithAccuracy = {
            source: 'geolocalization',
            latLng: geolocation,
            accuracy,
        }

        emit('update:geolocation', geolocationWithAccuracy)
    })

    function startGeolocalization() {
        if (IS_DEVELOPMENT) {
            console.log("Starting geolocalization …")
        }

        bestGeolocation.value = null
        geolocationError.value = null

        geolocationWatchId = navigator.geolocation.watchPosition(
            geolocation => {
                if (bestGeolocation.value == null || geolocation.coords.accuracy < bestGeolocation.value.accuracy) {
                    if (IS_DEVELOPMENT) {
                        console.log("Geolocation is accurate enough", geolocation.coords)
                    }

                    bestGeolocation.value = geolocation.coords
                } else if (IS_DEVELOPMENT) {
                    console.log("Geolocation is not accurate enough", geolocation.coords)
                }
            },
            error => {
                if (IS_DEVELOPMENT) {
                    console.warn("An error occurred while geolocalization", error)
                }

                //TODO: tracking

                geolocationError.value = error
                emit('update:geolocalization', false)
            },
            {
                enableHighAccuracy: true,
            }
        )
    }

    //TODO: <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< onUnmounted stoppen?

    function stopGeolocalization() {
        if (IS_DEVELOPMENT) {
            console.log("Stopping geolocalization …")
        }

        if (geolocationWatchId !== null) {
            navigator.geolocation.clearWatch(geolocationWatchId)
        }
    }

    function onOpenAppSettings() {
        NATIVE_APP_SERVICE?.onOpenDeviceSettings()
        hasOpenedAppSettings.value = true
    }

    function onPageReload() {
        location.reload()
    }
</script>

<style scoped>
</style>