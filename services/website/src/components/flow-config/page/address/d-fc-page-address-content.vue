<template>
    <v-form ref="addressForm"
            v-model="isFormValid">
        <v-container fluid>
            <v-slide-y-reverse-transition>
                <v-row v-if="isEditModeEnabled && !isDropdownModeEnabled">
                    <v-col class="pa-0 ps-4">
                        <d-h6>
                            <v-layout class="justify-space-between align-center">
                                <span>{{ t('components.listing.edit.location.editAddress') }}</span>

                                <d-btn :icon="mdiChevronDown"
                                       type="tertiary"
                                       variant="text"
                                       @click="onToggleEditMode"/>
                            </v-layout>
                        </d-h6>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-show="isEditModeEnabled && !isDropdownModeEnabled"
                       :dense="smAndDown">
                    <v-col v-if="streetElement"
                           cols="12"
                           sm="8">
                        <d-fc-ui-element :element="streetElement"
                                         is-first-element
                                         is-last-element/>
                    </v-col>
                    <v-col v-if="houseNumberElement"
                           cols="12"
                           sm="4">
                        <d-fc-ui-element :element="houseNumberElement"
                                         is-first-element
                                         is-last-element/>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-show="isEditModeEnabled && !isDropdownModeEnabled"
                       :dense="smAndDown">
                    <v-col v-if="zipcodeElement"
                           cols="12"
                           sm="4">
                        <d-fc-ui-element :element="zipcodeElement"
                                         is-first-element
                                         is-last-element/>
                    </v-col>
                    <v-col v-if="cityElement"
                           cols="12"
                           sm="8">
                        <d-fc-ui-element :element="cityElement"
                                         is-first-element
                                         is-last-element/>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-if="(isEditModeEnabled || isDropdownModeEnabled) && lastGeocodingAddresses.length > 0"
                       :class="{'mt-0': isDropdownModeEnabled}">
                    <v-col :class="{'pt-4': isEditModeEnabled}"
                           class="py-0">
                        <d-h6>{{ t(lastGeocodingAddresses.length > 1 ? 'components.listing.edit.location.lastAddresses.multiple' : 'components.listing.edit.location.lastAddresses.single') }}</d-h6>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-if="(isEditModeEnabled || isDropdownModeEnabled) && lastGeocodingAddresses.length > 0">
                    <v-col>
                        <d-card rounded="lg"
                                variant="flat">
                            <d-list class="bg-transparent">
                                <template v-for="(lastGeocodingAddress, index) in lastGeocodingAddresses"
                                          :key="index">
                                    <d-divider v-if="index > 0"/>
                                    <d-list-item
                                        :subtitle="addressZipCodeAndCityOf(lastGeocodingAddress)"
                                        :title="addressStreetAndHouseNumberOf(lastGeocodingAddress)"
                                        lines="two"
                                        @click="selectAddress(lastGeocodingAddress, true)"/>
                                </template>
                            </d-list>
                        </d-card>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-show="!isEditModeEnabled && !isDropdownModeEnabled && geolocation !== null"
                       class="mt-0">
                    <v-col class="pt-0">
                        <div class="addressCardText w-100 h-100">
                            <v-icon :color="isFormValid === true && (addressLine1 !== null && addressLine2 !== null) ? themeColorIconSuccess : themeColorIconWarning"
                                    :icon="mdiCircleMedium"
                                    class="addressCardTextIcon"
                                    size="50px"/>
                            <div class="addressCardTextLine1 text-body-1">
                                <v-skeleton-loader v-if="addressLine1 === null"
                                                   :boilerplate="!geolocalization"
                                                   type="text"/>
                                <template v-else>
                                    {{ addressLine1 }}
                                </template>
                            </div>
                            <div class="addressCardTextLine2 text-body-2">
                                <v-skeleton-loader v-if="addressLine2 === null"
                                                   :boilerplate="!geolocalization"
                                                   type="text"/>
                                <template v-else>
                                    {{ addressLine2 }}
                                </template>
                            </div>
                        </div>
                    </v-col>
                    <v-col align-self="center"
                           class="pt-0"
                           cols="auto">
                        <d-btn :icon="mdiPencil"
                               type="tertiary"
                               variant="text"
                               @click="onToggleEditMode"/>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-if="!isDropdownModeEnabled && geolocation !== null && showObfuscatedLocationElement"
                       :class="{'py-1': smAndDown}"
                       :dense="smAndDown">
                    <v-col>
                        <d-fc-ui-element :element="showObfuscatedLocationElement"
                                         is-first-element
                                         is-last-element/>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>

            <v-slide-y-reverse-transition>
                <v-row v-if="isDropdownModeEnabled"
                       :class="{'mt-0' : lastGeocodingAddresses.length <= 0, 'mt-8': lastGeocodingAddresses.length > 0}"
                       dense>
                    <v-col>
                        <d-fc-page-address-input :geolocation="geolocation?.latLng ?? null"
                                                 :listing-region="listingRegion"
                                                 @update:geocoding-address="onGeocodingAddressSelectedFromDropdown"/>
                    </v-col>
                </v-row>
            </v-slide-y-reverse-transition>
        </v-container>
    </v-form>
</template>

<script lang="ts"
        setup>
    import {computed, inject, ref, shallowRef, watch} from "vue";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {DFlowConfigPageAddressReverseGeocodingQueryVariables, FlowConfigPage, GeocodingAddress, UiElement, useDFlowConfigPageAddressReverseGeocodingLazyQuery} from "@/adapter/graphql/generated/graphql";
    import {VForm} from "vuetify/components";
    import {createGeolocationLatLng} from "@/adapter/leaflet/lf-utils";
    import {Address, areAddresesEqual, useAddressService} from "@/utility/address";
    import {mdiChevronDown, mdiCircleMedium, mdiPencil} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import {useDisplay} from "vuetify";
    import DList from "@/adapter/vuetify/theme/components/list/d-list.vue";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {ListingEditGeolocationWithAccuracy} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracy";
    import {ListingEditGeolocation} from "@/components/flow-config/page/address/ListingEditGeolocation";
    import DFcPageAddressInput from "@/components/flow-config/page/address/d-fc-page-address-input.vue";
    import DFcUiElement from "@/components/flow-config/ui-element/d-fc-ui-element.vue";
    import {ListingRegion} from "@/model/listing/ListingRegion";
    import {FlowConfigFieldWithArrayIndex, useFlowDataBoolean} from "@/model/listing/FlowData";
    import {useListingAddress, useMutableListingAddress} from "@/service/use-listing-address";
    import {LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const ACCURACY_NEEDED_TO_END_GEOLOCALIZATION = 10
    const ACCURACY_NEEDED_TO_DISPLAY_STREET_AND_HOUSE_NUMBER = 350

    const props = defineProps<{
        page: FlowConfigPage
        geolocation: Optional<ListingEditGeolocationWithAccuracy>
        geolocalization: boolean
        listingRegion: ListingRegion
    }>()

    const flowData = inject(LMutableFlowDataInjection)!

    type ExtendedAddress = Address & {
        latitude: number
        longitude: number
        accuracy: number
    }

    const emits = defineEmits<{
        'update:geolocation': [geolocation: Optional<ListingEditGeolocation>]
        'update:geolocalization': [isGeolocalizationEnabled: boolean]
        'update:geolocationObfuscation': [isGeolocationObfuscated: boolean]
        'update:addressLine': [addressLine: Optional<string>]
    }>()

    const {
        addressStreetAndHouseNumberOf,
        addressZipCodeAndCityOf,
    } = useAddressService()

    const {
        themeColorIconSuccess,
        themeColorIconWarning,
    } = useDoorbitTheme()
    const {t} = useI18n()
    const {smAndDown} = useDisplay()
    const addressForm = shallowRef<Optional<VForm>>(null)
    const geocodingAddresses = ref<ExtendedAddress[]>([]) //ref okay
    const isEditModeEnabled = shallowRef<boolean>(false)
    const isFormValid = shallowRef<Optional<boolean>>(null)
    const addressLine1 = computed<Optional<string>>(() => addressStreetAndHouseNumberOf(address.value))
    const addressLine2 = computed<Optional<string>>(() => addressZipCodeAndCityOf(address.value))
    const wasAddressSelectedFromDropdown = shallowRef<boolean>(false)
    const isDropdownModeValid = computed<boolean>(() => latitude.value === null && longitude.value === null)
    const isDropdownModeEnabled = computed<boolean>(() =>
        isDropdownModeValid.value &&
        !(isUserInsideProperty.value ?? false) &&
        !wasAddressSelectedFromDropdown.value
    )

    function extractElement(fieldId: string): Optional<UiElement> {
        return props.page.children.find(child => (child as any).field?.id === fieldId) ?? null
    }

    const streetElement = computed<Optional<UiElement>>(() => extractElement('address_street'))
    const houseNumberElement = computed<Optional<UiElement>>(() => extractElement('address_house_number'))
    const zipcodeElement = computed<Optional<UiElement>>(() => extractElement('address_zipcode'))
    const cityElement = computed<Optional<UiElement>>(() => extractElement('address_city'))
    const showObfuscatedLocationElement = computed<Optional<UiElement>>(() => extractElement('address_show_obfuscated_location'))

    const isUserInsideProperty = useFlowDataBoolean(
        flowData,
        computed<FlowConfigFieldWithArrayIndex>(() => ({
            field: {
                id: 'is_user_inside_property'
            },
            arrayIndex: null
        })),
    )

    const {
        street,
        houseNumber,
        zipcode,
        city,
        latitude,
        longitude,
        showObfuscatedLocation
    } = useMutableListingAddress(flowData)

    const {
        address
    } = useListingAddress(flowData)

    async function selectAddress(address: ExtendedAddress, canFinishSelectionFromDropdown: boolean) {
        if (canFinishSelectionFromDropdown && isDropdownModeEnabled.value) {
            finishSelectionFromDropdown()
        }

        street.value = address.street ?? null
        houseNumber.value = address.houseNumber ?? null
        zipcode.value = address.zipcode ?? null
        city.value = address.city ?? null
        latitude.value = address.latitude
        longitude.value = address.longitude

        if (address.longitude !== props.geolocation?.latLng.lng || address.latitude !== props.geolocation?.latLng.lat) {
            const latLng = createGeolocationLatLng(address.latitude, address.longitude)

            emits('update:geolocation', latLng === null ? null : {
                source: 'user',
                latLng: latLng
            })
        }

        await addressForm.value?.validate()
    }

    watch(address, address => {
        const addressLine = addressStreetAndHouseNumberOf(address) ?? addressZipCodeAndCityOf(address)
        emits('update:addressLine', addressLine)
    }, {
        deep: true
    })

    watch(flowData, async () => { //TODO: das muss anders!!!
            const latLng = createGeolocationLatLng(latitude.value, longitude.value)
            emits('update:geolocation', latLng === null ? null : {
                source: 'listing',
                latLng: latLng
            })
            emits('update:geolocalization', latitude.value === null || longitude.value === null)
            emits('update:geolocationObfuscation', showObfuscatedLocation.value === true)

            await addressForm.value?.validate()
        },
        {
            deep: true,
            immediate: true
        }
    )

    const {
        loading: isReverseGeocodedAddressLoading, //TODO use it
        load: triggerReverseGeocodedAddress,
        refetch: refetchReverseGeocodedAddress
    } = useDFlowConfigPageAddressReverseGeocodingLazyQuery({
        latitude: 0,
        longitude: 0
    }, {
        debounce: 100
    })

    async function reverseGeocoding(variables: DFlowConfigPageAddressReverseGeocodingQueryVariables) {
        const response1 = triggerReverseGeocodedAddress(undefined, variables)

        if (response1 !== false) {
            try {
                const result = await response1
                return result.reverseGeocoding ?? null
            } catch (e) {
                console.warn(e) //TODO: logging
                return null
            }
        }

        const response2 = refetchReverseGeocodedAddress(variables)
        if (response2 === undefined) {
            return null
        }

        try {
            const result = await response2
            return result.data.reverseGeocoding ?? null
        } catch (e) {
            console.warn(e) //TODO: logging
            return null
        }
    }

    watch(() => props.geolocation, async geolocation => {
        if (geolocation === null) {
            return
        }
        if (latitude.value === geolocation.latLng.lat && longitude.value === geolocation.latLng.lng) {
            return
        }

        latitude.value = geolocation.latLng.lat
        longitude.value = geolocation.latLng.lng

        const queryVariables: DFlowConfigPageAddressReverseGeocodingQueryVariables = {
            latitude: geolocation.latLng.lat,
            longitude: geolocation.latLng.lng
        }
        const geocodingAddress = await reverseGeocoding(queryVariables)
        if (geocodingAddress === null) {
            return
        }

        if (geolocation.accuracy !== null && geolocation.accuracy <= ACCURACY_NEEDED_TO_END_GEOLOCALIZATION) {
            emits('update:geolocalization', false)
        }

        const accuracy = geolocation.accuracy ?? Number.MAX_VALUE
        const useStreetAndHouseNumber = accuracy <= ACCURACY_NEEDED_TO_DISPLAY_STREET_AND_HOUSE_NUMBER
        const newAddress: ExtendedAddress = {
            street: useStreetAndHouseNumber ? geocodingAddress.street : null,
            houseNumber: useStreetAndHouseNumber ? geocodingAddress.houseNumber : null,
            zipcode: geocodingAddress.zipcode,
            city: geocodingAddress.city,
            latitude: geolocation.latLng.lat,
            longitude: geolocation.latLng.lng,
            accuracy
        }

        cacheNewAddress(newAddress)

        await selectAddress(newAddress, geolocation.source === 'user')
    }, {
        deep: true
    })

    function cacheNewAddress(newAddress: ExtendedAddress) {
        geocodingAddresses.value = geocodingAddresses.value.filter(address => !areAddresesEqual(address, newAddress))
        geocodingAddresses.value.push(newAddress)
    }

    //only 3 addresses, the order must be reversed, skip the first (last) element. if in dropdown mode, take only the first.
    const DISPLAYED_GEOCODING_ADDRESSES_COUNT = 3
    const lastGeocodingAddresses = computed<ExtendedAddress[]>(() => {
        const addresses = geocodingAddresses.value
        if (addresses.length <= 0) {
            return []
        }
        const reversedAddresses = [...addresses].reverse();
        if (isDropdownModeEnabled.value) {
            return reversedAddresses.slice(0, 1)
        }
        return reversedAddresses.slice(1, DISPLAYED_GEOCODING_ADDRESSES_COUNT + 1)
    })

    watch(showObfuscatedLocation, showObfuscatedLocation => {
        emits('update:geolocationObfuscation', showObfuscatedLocation === true)
    }, {
        deep: true
    })

    function onToggleEditMode() {
        isEditModeEnabled.value = !isEditModeEnabled.value
    }

    async function onGeocodingAddressSelectedFromDropdown(geocodingAddress: GeocodingAddress) {
        const newAddress = {
            ...geocodingAddress,
            accuracy: 0
        }

        cacheNewAddress(newAddress)

        await selectAddress(newAddress, true)
    }

    function finishSelectionFromDropdown() {
        emits('update:geolocalization', false)
        wasAddressSelectedFromDropdown.value = true
    }
</script>

<style scoped>
    .addressCardText {
        display: inline-grid;
        grid-template-areas:
            'icon line1'
            'icon line2';
        grid-template-columns: 50px 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .addressCardTextIcon {
        grid-area: icon;
        align-self: center;
        justify-self: center;
    }

    .addressCardTextLine1 {
        grid-area: line1;
        align-self: end;
        justify-self: stretch;
        text-align: start;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .addressCardTextLine1 :deep(.v-skeleton-loader) {
        height: 24px;
    }

    .addressCardTextLine1 :deep(.v-skeleton-loader__text) {
        margin: 0;
        height: 14px;
    }

    .addressCardTextLine2 {
        grid-area: line2;
        align-self: start;
        justify-self: stretch;
        text-align: start;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .addressCardTextLine2 :deep(.v-skeleton-loader) {
        height: 24px;
    }

    .addressCardTextLine2 :deep(.v-skeleton-loader__text) {
        margin: 0;
        height: 14px;
    }
</style>