<template>
    <d-autocomplete-address :geolocation="geolocation"
                            :listing-region="listingRegion"
                            autofocus
                            variant="list"
                            @on-selection="onAddressSelected">
        <template #item="itemData">
            <d-list-item :subtitle="itemData.line2"
                         :title="itemData.line1"
                         @click="itemData.onClick"/>
        </template>
    </d-autocomplete-address>
</template>

<script lang="ts"
        setup>
    import DAutocompleteAddress from "@/adapter/vuetify/theme/components/input/d-autocomplete-address.vue";
    import {GeocodingAddress} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import DListItem from "@/adapter/vuetify/theme/components/list/d-list-item.vue";
    import {LatLng} from "leaflet";

    defineProps<{
        listingRegion: Optional<string>
        geolocation: Optional<LatLng>
    }>()

    const emits = defineEmits<{
        'update:geocodingAddress': [geocodingAddress: GeocodingAddress]
    }>()

    function onAddressSelected(geocodingAddress: GeocodingAddress) {
        emits('update:geocodingAddress', geocodingAddress)
    }
</script>

<style scoped>
</style>