<template>
    <template v-if="listingRegion">
        <d-fc-layout v-if="mdAndUp">
            <!-- ############ -->
            <!-- ### LEFT ### -->
            <!-- ############ -->
            <template #left>
                <d-fc-page-address-content :geolocalization="isGeolocalizationEnabled"
                                           :geolocation="geolocationWithAccuracy"
                                           :listing-region="listingRegion as ListingRegion"
                                           :page="page"
                                           @update:geolocation-obfuscation="onGeolocationObfuscationChanged"
                                           @update:geolocalization="onGeolocalizationChanged"
                                           @update:address-line="onAddressLineChanged"
                                           @update:geolocation="onGeolocationChangedByContent"/>
            </template>

            <!-- ############# -->
            <!-- ### RIGHT ### -->
            <!-- ############# -->
            <template #right>
                <div class="h-100 w-100">
                    <d-fc-page-address-map v-model:geolocalization="isGeolocalizationEnabled"
                                           :address-line="addressLine"
                                           :geolocalization-zoom="zoom"
                                           :geolocation="geolocationWithZoom"
                                           :listing-region="listingRegion as ListingRegion"
                                           :show-obfuscation-radius="isGeolocationObfuscated"
                                           @update:geolocation="onGeolocationChangedByMap"/>
                </div>
            </template>
        </d-fc-layout>

        <!-- ######################### -->
        <!-- ### MOBILE FULLSCREEN ### -->
        <!-- ######################### -->
        <div v-else
             class="mobileWrapper">
            <div>
                <div class="mapWrapper">
                    <d-fc-page-address-map v-model:geolocalization="isGeolocalizationEnabled"
                                           :address-line="addressLine"
                                           :bottom-padding="mapCardWrapperHeight + 32"
                                           :geolocalization-zoom="zoom"
                                           :geolocation="geolocationWithZoom"
                                           :listing-region="listingRegion as ListingRegion"
                                           :show-obfuscation-radius="isGeolocationObfuscated"
                                           @update:geolocation="onGeolocationChangedByMap"/>
                </div>

                <div ref="mapCardWrapper"
                     :class="{
                        'pa-8': smAndUp,
                        'pa-4': !smAndUp,
                        'pb-8': !smAndUp
                     }"
                     class="mapCardWrapper pt-0">
                    <d-card class="mapCard"
                            rounded="xl"
                            variant="elevated">
                        <d-fc-page-address-content :geolocalization="isGeolocalizationEnabled"
                                                   :geolocation="geolocationWithAccuracy"
                                                   :listing-region="listingRegion as ListingRegion"
                                                   :page="page"
                                                   @update:geolocation-obfuscation="onGeolocationObfuscationChanged"
                                                   @update:geolocalization="onGeolocalizationChanged"
                                                   @update:address-line="onAddressLineChanged"
                                                   @update:geolocation="onGeolocationChangedByContent"/>
                    </d-card>
                </div>
            </div>
        </div>
    </template>
</template>

<script lang="ts"
        setup>
    import {inject, shallowRef, toRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {useElementSize} from "@vueuse/core";
    import {LfGeolocationWithZoom} from "@/adapter/leaflet/LfGeolocationWithZoom";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useDisplay} from "vuetify";
    import {useFlowDataString} from "@/model/listing/FlowData";
    import {ListingEditGeolocation} from "@/components/flow-config/page/address/ListingEditGeolocation";
    import {ListingEditGeolocationWithAccuracyAndZoom} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracyAndZoom";
    import {ListingEditGeolocationWithAccuracy} from "@/components/flow-config/page/address/ListingEditGeolocationWithAccuracy";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import {FlowConfigPage} from "@/adapter/graphql/generated/graphql";
    import DFcPageAddressMap from "@/components/flow-config/page/address/d-fc-page-address-map.vue";
    import DFcPageAddressContent from "@/components/flow-config/page/address/d-fc-page-address-content.vue";
    import {ListingRegion} from "@/model/listing/ListingRegion";
    import {LFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const zoom = 16.5

    defineProps<{
        page: FlowConfigPage
    }>()

    const flowData = inject(LFlowDataInjection)!

    const {smAndUp, mdAndUp} = useDisplay()
    const mapCardWrapper = shallowRef<Optional<HTMLDivElement>>(null)
    const {
        height: mapCardWrapperHeight
    } = useElementSize(mapCardWrapper)

    const listingRegion = useFlowDataString(flowData, toRef({
        field: {
            id: 'listing_region',
        },
        arrayIndex: null
    }))

    const geolocationWithAccuracy = shallowRef<Optional<ListingEditGeolocationWithAccuracy>>(null)
    const geolocationWithZoom = shallowRef<Optional<LfGeolocationWithZoom>>(null)
    const isGeolocationObfuscated = shallowRef<boolean>(false)
    const isGeolocalizationEnabled = shallowRef<boolean>(false)
    const addressLine = shallowRef<Optional<string>>(null)

    function onGeolocationChangedByMap(newGeolocation: ListingEditGeolocationWithAccuracyAndZoom) {
        geolocationWithZoom.value = {
            latLng: newGeolocation.latLng,
            zoom: newGeolocation.zoom,
        }
        geolocationWithAccuracy.value = {
            source: newGeolocation.source,
            latLng: newGeolocation.latLng,
            accuracy: newGeolocation.accuracy,
        }
    }

    function onGeolocationChangedByContent(newGeolocation: Optional<ListingEditGeolocation>) {
        if (newGeolocation === null) {
            geolocationWithZoom.value = null
            geolocationWithAccuracy.value = null
            return
        }

        geolocationWithZoom.value = {
            latLng: newGeolocation.latLng,
            zoom: zoom
        }
        geolocationWithAccuracy.value = {
            source: newGeolocation.source,
            latLng: newGeolocation.latLng,
            accuracy: null,
        }
    }

    function onGeolocationObfuscationChanged(newIsGeolocationObfuscated: boolean) {
        isGeolocationObfuscated.value = newIsGeolocationObfuscated
    }

    function onGeolocalizationChanged(newIsGeolocalizationEnabled: boolean) {
        isGeolocalizationEnabled.value = newIsGeolocalizationEnabled
    }

    function onAddressLineChanged(newAddressLine: Optional<string>) {
        addressLine.value = newAddressLine
    }
</script>

<style scoped>
    .mobileWrapper {
        width: 100%;
        height: 100%;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
    }

    .mapWrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
    }

    .mapCardWrapper {
        position: absolute;
        width: 100%;
        bottom: 0;
        z-index: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: flex-end;
    }

    .mapCard {
        flex: 1 1 auto;
    }
</style>