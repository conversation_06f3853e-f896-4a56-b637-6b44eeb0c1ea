<template>
    <div class="chartWrapper">
        <Bar :data="data"
             :options="options"/>
    </div>
</template>

<script lang="ts"
        setup>

    import {BarElement, CategoryScale, Chart as ChartJS, LinearScale, Title, Tooltip} from 'chart.js'
    import {Bar} from 'vue-chartjs'
    import {AgeGroup} from "@/adapter/graphql/generated/graphql";
    import {computed} from "vue";
    import {useI18n} from "vue-i18n";
    import {PRIMARY_CHART_COLOR_DARK, PRIMARY_CHART_COLOR_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip)

    const {t, n} = useI18n()
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const {ageDistribution} = defineProps<{
        ageDistribution: AgeGroup[]
    }>()

    const footer = (tooltipItems: any[]) => {
        if (tooltipItems.length > 0) {
            return t('components.listing.adminBoundary.deviationFromAverageTitle', {
                percent: (tooltipItems[0].raw.deviationFromAverage > 0 ? "+" : "") + n(tooltipItems[0].raw.deviationFromAverage, "percent")
            })
        }
        return ""
    };

    const label = (tooltipItem: any) => {
        return ` ${n(tooltipItem.raw.x, "percent")}`;
    };

    const options = computed<any>(() => ({
        responsive: true,
        maintainAspectRatio: false,
        backgroundColor: isDark.value ? PRIMARY_CHART_COLOR_DARK : PRIMARY_CHART_COLOR_LIGHT,
        borderRadius: 0,
        borderWidth: 0,
        indexAxis: "y",
        scales: {
            x: {
                display: false,
                grid: {
                    display: false,
                },
                stacked: true
            },
            y: {
                display: true,
                grid: {
                    display: false,
                },
                ticks: {
                    color: isDark.value ? PRIMARY_CHART_COLOR_DARK : PRIMARY_CHART_COLOR_LIGHT,
                },
                stacked: true
            },
        },
        plugins: {
            tooltip: {
                callbacks: {
                    footer,
                    label
                }
            },
            legend: {
                display: false
            }
        }
    }))

    const data = computed<any>(() => {
        const labels = ageDistribution.map(ageGroup => t(`components.listing.adminBoundary.population.ageGroup.${ageGroup.type}`))
        const data = ageDistribution.map(ageGroup => ({y: t(`components.listing.adminBoundary.population.ageGroup.${ageGroup.type}`), x: ageGroup.percentage, 'deviationFromAverage': ageGroup.deviationFromAverage}))
        return {
            labels,
            datasets: [{
                data,
            }]
        }
    })
</script>

<style scoped>
    .chartWrapper {
        height: 250px;
        width: 100%;
    }
</style>