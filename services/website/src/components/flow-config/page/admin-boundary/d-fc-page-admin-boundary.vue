<template>
    <d-fc-layout>
        <template #left>
            <v-lazy v-if="smAndDown"
                    class="mb-4">
                <v-responsive :aspect-ratio="5/3"
                              class="max-h-250"
                              @click="goToFullscreenMap">
                    <d-fc-page-admin-boundary-map disable-dragging
                                                  disable-zoom
                                                  hide-scale
                                                  is-embedded
                                                  show-full-screen-button/>
                </v-responsive>
            </v-lazy>

            <v-container fluid>
                <template v-if="loading">
                    <d-skeleton-loader type="heading"/>
                    <d-skeleton-loader type="text"/>
                    <d-skeleton-loader type="subtitle"/>
                    <d-skeleton-loader type="article"/>
                </template>
                <template v-else-if="adminBoundary">
                    <d-h5>{{ adminBoundary.displayName }}</d-h5>
                    <d-expandable-description-card v-if="adminBoundary.longDescription"
                                                   :description="adminBoundary.longDescription"
                                                   class="mt-4"/>
                    <v-layout class="mt-6 align-center">
                        <v-layout v-for="(populationGroup, index) in PopulationGroupValues"
                                  :key="index"
                                  :class="{'active': populationGroup === adminBoundary.populationGroup}"
                                  class="pa-1 py-2 populationGroup rounded-lg flex-grow-1 flex-wrap justify-center">
                            <d-fc-page-admin-boundary-population-group-icon :color="populationGroup === adminBoundary.populationGroup ? 'rgb(var(--v-theme-d-text-default))' : 'rgb(var(--v-theme-d-icon-default))'"
                                                                            :height="xs ? 24: 32"
                                                                            :population-group="populationGroup"
                                                                            :width="xs? 24: 32"
                                                                            class="flex-1-1-100"/>
                            <div :class="{'text-truncate':populationGroup !== adminBoundary.populationGroup, 'text-caption': xs }"
                                 class="flex-1-1-100 text-center px-1">
                                {{ t(`enums.populationGroupType.${populationGroup}`) }}
                            </div>
                            <v-icon v-if="populationGroup === adminBoundary.populationGroup"
                                    :icon="mdiArrowUpThin"
                                    class="flex-1-1-100"
                                    color="rgb(var(--v-theme-d-icon-default))"/>
                        </v-layout>
                    </v-layout>
                    <d-listing-detail-table class="mt-6">
                        <d-listing-detail-table-row>
                            <template #left>
                                {{ t('components.listing.adminBoundary.areaInSquareKilometers.title') }}
                            </template>
                            <template #right>
                                {{ t('units.squareKilometersShort', {squareKilometers: n(adminBoundary.areaInSquareKilometers, 'decimal')}) }}
                            </template>
                        </d-listing-detail-table-row>
                        <template v-if="adminBoundary.population">
                            <d-listing-detail-table-row>
                                <template #left>
                                    {{ t('components.listing.adminBoundary.population.totalPopulation.title') }}
                                </template>
                                <template #right>
                                    <v-container class="pa-0"
                                                 fluid>
                                        <v-row
                                            dense
                                            justify="space-between">
                                            <v-col cols="auto">
                                                {{ t('components.listing.adminBoundary.population.totalPopulation.subtitle') }}
                                            </v-col>
                                            <v-col cols="auto">
                                                {{ n(adminBoundary.population.totalPopulation, 'integer') }}
                                            </v-col>
                                        </v-row>
                                        <v-row
                                            v-if="adminBoundary.population.threeYearsTrend"
                                            dense
                                            justify="space-between">
                                            <v-col cols="auto">
                                                {{ t('components.listing.adminBoundary.population.threeYearsTrend') }}
                                            </v-col>
                                            <v-col cols="auto">
                                                {{ n(adminBoundary.population.threeYearsTrend, 'percentDecimal') }}
                                            </v-col>
                                        </v-row>
                                        <v-row
                                            v-if="adminBoundary.population.fiveYearsTrend"
                                            dense
                                            justify="space-between">
                                            <v-col cols="auto">
                                                {{ t('components.listing.adminBoundary.population.fiveYearsTrend') }}
                                            </v-col>
                                            <v-col cols="auto">
                                                {{ n(adminBoundary.population.fiveYearsTrend, 'percentDecimal') }}
                                            </v-col>
                                        </v-row>
                                        <v-row
                                            v-if="adminBoundary.population.tenYearsTrend"
                                            dense
                                            justify="space-between">
                                            <v-col cols="auto">
                                                {{ t('components.listing.adminBoundary.population.tenYearsTrend') }}
                                            </v-col>
                                            <v-col cols="auto">
                                                {{ n(adminBoundary.population.tenYearsTrend, 'percentDecimal') }}
                                            </v-col>
                                        </v-row>
                                    </v-container>
                                </template>
                            </d-listing-detail-table-row>
                            <d-listing-detail-table-row>
                                <template #left>
                                    {{ t('components.listing.adminBoundary.population.density.title') }}
                                </template>
                                <template #right>
                                    {{ t(`components.listing.adminBoundary.population.density.${adminBoundary.population.density}`) }}
                                </template>
                            </d-listing-detail-table-row>
                            <d-listing-detail-table-row align-left-start>
                                <template #left>
                                    {{ t('components.listing.adminBoundary.population.ageDistribution.title') }}
                                </template>
                                <template #right>
                                    <d-fc-page-admin-boundary-age-distribution-chart :age-distribution="adminBoundary.population.ageDistribution"/>
                                </template>
                            </d-listing-detail-table-row>
                        </template>
                        <d-listing-detail-table-row v-if="adminBoundary.income">
                            <template #left>
                                {{ t('components.listing.adminBoundary.income.title') }}
                            </template>
                            <template #right>
                                <d-fc-page-admin-boundary-trend-number
                                    :description="t(
                                                `components.listing.adminBoundary.income.deviationFromAverageDescription`,
                                                {
                                                    income: n(adminBoundary.income.average, 'currency'),
                                                    deviation: t(
                                                        adminBoundary.income.deviationFromAverage >= 0.03
                                                        ? 'components.listing.adminBoundary.deviationFromAverage.aboveAverage'
                                                        : adminBoundary.income.deviationFromAverage < -0.03
                                                        ? 'components.listing.adminBoundary.deviationFromAverage.belowAverage'
                                                        : 'components.listing.adminBoundary.deviationFromAverage.average',
                                                        {
                                                            percent: n(adminBoundary.income.deviationFromAverage, 'percentDecimal')
                                                        }
                                                    ),
                                                    populationGroup: t(`components.listing.adminBoundary.populationGroup.plural.${adminBoundary.populationGroup}`)
                                                }
                                                            )"
                                    :trend="adminBoundary.income.deviationFromAverage"
                                    :treshold="0.05"
                                    class="justify-end">{{ n(adminBoundary.income.average, 'currency') }}
                                </d-fc-page-admin-boundary-trend-number>
                            </template>
                        </d-listing-detail-table-row>
                        <d-listing-detail-table-row v-if="adminBoundary.perHeadOutput">
                            <template #left>
                                {{ t('components.listing.adminBoundary.perHeadOutput.title') }}
                            </template>
                            <template #right>
                                {{ n(adminBoundary.perHeadOutput.tenYearsTrend, 'percent') }}
                            </template>
                        </d-listing-detail-table-row>
                        <d-listing-detail-table-row v-if="adminBoundary.education"
                                                    align-left-start>
                            <template #left>
                                {{ t('components.listing.adminBoundary.education.title') }}
                            </template>
                            <template #right>
                                <d-fc-page-admin-boundary-education-chart :education="adminBoundary.education"/>
                            </template>
                        </d-listing-detail-table-row>
                        <d-listing-detail-table-row v-if="adminBoundary.unemploymentStatistics"
                                                    align-left-start
                                                    cols12>
                            <template #left>
                                {{ t('components.listing.adminBoundary.unemploymentStatistics.title') }}
                            </template>
                            <template #right>
                                <v-container class="pa-0"
                                             fluid>
                                    <v-row
                                        v-if="adminBoundary.unemploymentStatistics.currentYearTotal !== null && adminBoundary.unemploymentStatistics.currentYearTotal !== undefined"
                                        class="justify-space-between"
                                        dense>
                                        <v-col cols="auto">
                                            {{ t(`components.listing.adminBoundary.unemploymentStatistics.currentYearTotal`) }}
                                        </v-col>
                                        <v-col cols="auto">
                                            {{ n(adminBoundary.unemploymentStatistics.currentYearTotal, 'percentDecimal') }}
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        v-if="adminBoundary.unemploymentStatistics.tenYearsTrend !== null"
                                        class="justify-space-between"
                                        dense>
                                        <v-col cols="auto">
                                            {{ t(`components.listing.adminBoundary.unemploymentStatistics.tenYearsTrend`) }}
                                        </v-col>
                                        <v-col cols="auto">
                                            {{ n(adminBoundary.unemploymentStatistics.tenYearsTrend ?? 0, 'percentDecimal') }}
                                        </v-col>
                                    </v-row>
                                </v-container>
                            </template>
                        </d-listing-detail-table-row>
                        <d-listing-detail-table-row v-if="adminBoundary.crimeStatistics"
                                                    align-left-start>
                            <template #left>
                                {{ t('components.listing.adminBoundary.crimeStatistics.title') }}
                            </template>
                            <template #right>
                                <v-container class="pa-0"
                                             fluid>
                                    <v-row
                                        v-if="adminBoundary.crimeStatistics.fiveYearsTrend"
                                        class="justify-space-between"
                                        dense>
                                        <v-col
                                            cols="auto">
                                            {{ t(`components.listing.adminBoundary.crimeStatistics.fiveYearsTrend`) }}
                                        </v-col>
                                        <v-col cols="auto">
                                            {{ n(adminBoundary.crimeStatistics.fiveYearsTrend, 'percentDecimal') }}
                                        </v-col>
                                    </v-row>
                                    <v-row
                                        v-if="adminBoundary.crimeStatistics.tenYearsTrend"
                                        class="justify-space-between"
                                        dense>
                                        <v-col cols="auto">
                                            {{ t(`components.listing.adminBoundary.crimeStatistics.tenYearsTrend`) }}
                                        </v-col>
                                        <v-col cols="auto">
                                            {{ n(adminBoundary.crimeStatistics.tenYearsTrend, 'percentDecimal') }}
                                        </v-col>
                                    </v-row>
                                </v-container>
                            </template>
                        </d-listing-detail-table-row>
                    </d-listing-detail-table>
                </template>
            </v-container>
        </template>

        <template #right>
            <div class="h-100 w-100">
                <d-fc-page-admin-boundary-map is-embedded
                                              show-full-screen-button/>
            </div>
        </template>
    </d-fc-layout>
</template>

<script lang="ts"
        setup>
    import {AdminBoundary, DFlowConfigPageAdminBoundaryQueryVariables, Listing, useDFlowConfigPageAdminBoundaryQuery} from "@/adapter/graphql/generated/graphql";
    import {computed, inject} from "vue";
    import {Optional} from "@/model/Optional";
    import DH5 from "@/adapter/vuetify/theme/components/text/headline/d-h5.vue";
    import {PopulationGroupValues} from "@/model/listing/PopulationGroup";
    import {mdiArrowUpThin} from "@mdi/js";
    import {useI18n} from "vue-i18n";
    import DListingDetailTable from "@/components/listing/old/components/d-listing-detail-table.vue";
    import DListingDetailTableRow from "@/components/listing/old/components/d-listing-detail-table-row.vue";
    import {useDisplay} from "vuetify";
    import {useRouter} from "vue-router";
    import DSkeletonLoader from "@/adapter/vuetify/theme/components/skeleton-loader/d-skeleton-loader.vue";
    import DFcPageAdminBoundaryMap from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary-map.vue";
    import DFcPageAdminBoundaryEducationChart from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary-education-chart.vue";
    import DFcPageAdminBoundaryAgeDistributionChart from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary-age-distribution-chart.vue";
    import DFcPageAdminBoundaryPopulationGroupIcon from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary-population-group-icon.vue";
    import DExpandableDescriptionCard from "@/components/fragment/d-expandable-description-card.vue";
    import DFcPageAdminBoundaryTrendNumber from "@/components/flow-config/page/admin-boundary/d-fc-page-admin-boundary-trend-number.vue";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const listingId = inject(LListingIdInjection)!

    const {t, n} = useI18n()
    const {xs, smAndDown} = useDisplay()
    const router = useRouter()

    const queryVariables = computed<DFlowConfigPageAdminBoundaryQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        result: queryResult,
        loading
    } = useDFlowConfigPageAdminBoundaryQuery(queryVariables)

    const listing = computed<Optional<Listing>>(() => queryResult.value?.listing as Listing ?? null)
    const adminBoundary = computed<Optional<AdminBoundary>>(() => listing.value?.adminBoundary ?? null)

    function goToFullscreenMap() {
        router.push({
            name: 'listingAdminBoundary',
            params: {
                id: listingId.value
            }
        })
    }
</script>

<style scoped>
    .populationGroup.active {
        background-color: rgb(var(--v-theme-d-outline-default));
    }

    .max-h-250 {
        max-height: 250px;
    }
</style>