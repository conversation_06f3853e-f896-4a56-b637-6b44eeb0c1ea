<template>
    <component :is="icon"
               :color="color"
               :height="height"
               :width="width"/>
</template>

<script lang="ts"
        setup>
    import {PopulationGroup} from "@/model/listing/PopulationGroup";
    import {computed} from "vue";
    import {Optional} from "@/model/Optional";
    import DIconHamlet from "@/components/icon/d-icon-hamlet.vue";
    import DIconVillage from "@/components/icon/d-icon-village.vue";
    import DIconTown from "@/components/icon/d-icon-town.vue";
    import DIconCity from "@/components/icon/d-icon-city.vue";
    import DIconMetropolitanCity from "@/components/icon/d-icon-metropolitan-city.vue";

    const {width, height, color, populationGroup} = defineProps<{
        width: number,
        height: number,
        color: string,
        populationGroup: Optional<PopulationGroup>,
    }>()

    const icon = computed(() => {
        if (populationGroup != null) {
            switch (populationGroup) {
                case "HAMLET":
                    return DIconHamlet
                case "VILLAGE":
                    return DIconVillage
                case "TOWN":
                    return DIconTown
                case "CITY":
                    return DIconCity
                case "METROPOLITAN_CITY":
                    return DIconMetropolitanCity
                default:
                    throw new Error(`Unknown population group: ${populationGroup}`)
            }
        }
        return DIconTown
    })
</script>

<style scoped>
</style>