<script lang="ts"
        setup>
    import {ArcElement, Chart as ChartJ<PERSON>, Legend, Tooltip} from 'chart.js'
    import {Pie} from 'vue-chartjs'
    import {useI18n} from "vue-i18n";
    import {computed} from "vue";
    import {Education} from "@/adapter/graphql/generated/graphql";
    import {BORDER_COLOR_DARK, BORDER_COLOR_LIGHT, CHART_COLORS_DARK, CHART_COLORS_LIGHT, EMPTY_COLOR_DARK, EMPTY_COLOR_LIGHT, PRIMARY_CHART_COLOR_DARK, PRIMARY_CHART_COLOR_LIGHT} from "@/components/flow-config/page/attractivity/chart-colors";
    import {useColorMode} from "@vueuse/core";

    ChartJS.register(ArcElement, Tooltip, Legend)

    const {t, n} = useI18n()
    const colorMode = useColorMode()
    const isDark = computed<boolean>(() => colorMode.state.value === 'dark')

    const {education} = defineProps<{
        education: Education
    }>()

    const label = (tooltipItem: any) => {
        return ` ${n(tooltipItem.raw, "percent")}`;
    };

    const data = computed(() => {
        const labels = [t('components.listing.adminBoundary.education.withHigherEducation'), t('components.listing.adminBoundary.education.withVocationalTraining'), t('components.listing.adminBoundary.education.withoutVocationalTraining')]
        const data = [education.withHigherEducation ?? 0, education.withVocationalTraining ?? 0, education.withoutVocationalTraining ?? 0]
        const rest = 1 - ((education.withHigherEducation ?? 0) + (education.withVocationalTraining ?? 0) + (education.withoutVocationalTraining ?? 0))
        if (rest > 0) {
            labels.push(t('components.listing.adminBoundary.education.rest'))
            data.push(rest)
        }
        return {
            labels,
            datasets: [
                {
                    backgroundColor: isDark.value ? CHART_COLORS_DARK.slice(0, 3).concat(EMPTY_COLOR_DARK) : CHART_COLORS_LIGHT.slice(0, 3).concat(EMPTY_COLOR_LIGHT),
                    data
                }
            ]
        }
    })

    const options = computed(() => ({
        responsive: true,
        maintainAspectRatio: false,
        borderColor: isDark.value ? BORDER_COLOR_DARK : BORDER_COLOR_LIGHT,
        plugins: {
            tooltip: {
                callbacks: {
                    label
                }
            },
            legend: {
                labels: {
                    color: isDark.value ? PRIMARY_CHART_COLOR_DARK : PRIMARY_CHART_COLOR_LIGHT,
                }
            }
        }
    }))
</script>

<template>
    <div class="chartWrapper">
        <Pie :data="data"
             :options="options"/>
    </div>
</template>

<style scoped>
    .chartWrapper {
        width: 300px;
        height: 300px;
    }
</style>