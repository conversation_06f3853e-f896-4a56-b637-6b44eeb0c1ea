<template>
    <div class="contentWrapper">
        <d-listing-detail-toolbar v-if="!isEmbedded">
            <d-listing-button-back :route="{
                                        name: 'viewListing',
                                        params: {
                                            id: listingId
                                        },
                                        hash: '#admin-boundary'
                                   }"
                                   :text="t('components.listing.images.backToListing')"/>
        </d-listing-detail-toolbar>

        <div class="mapWrapper">
            <div :class="{isEmbedded}"
                 class="mapContainer">
                <d-fc-page-location-map :disable-dragging="disableDragging"
                                        :disable-zoom="disableZoom"
                                        :focus-on-geo-shape="geoFence !== undefined"
                                        :focused-point-of-interest-ids="[]"
                                        :full-screen-route="fullScreenRoute"
                                        :geo-shape="geoFence"
                                        :is-loading="loading"
                                        :listing-image="listingImage"
                                        :points-of-interest="[]"
                                        :show-full-screen-button="showFullScreenButton"
                                        :show-scale="!hideScale"
                                        disable-overlay/>
            </div>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {DFlowConfigPageAdminBoundaryMapQueryVariables, ListingImage, useDFlowConfigPageAdminBoundaryMapQuery} from "@/adapter/graphql/generated/graphql";
    import {computed, inject} from "vue";
    import {MultiPolygonArray} from "@/model/MultiPolygonArray";
    import {RouteLocationNamedRaw} from "vue-router";
    import {useI18n} from "vue-i18n";
    import DFcPageLocationMap from "@/components/flow-config/page/location/d-fc-page-location-map.vue";
    import DListingButtonBack from "@/components/listing/fragments/d-listing-button-back.vue";
    import DListingDetailToolbar from "@/components/listing/fragments/d-listing-detail-toolbar.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        disableDragging?: boolean,
        disableZoom?: boolean,
        showFullScreenButton?: boolean,
        isEmbedded?: boolean
        hideScale?: boolean
    }>()

    const listingId = inject(LListingIdInjection)!

    const {t} = useI18n()

    const queryVariables = computed<DFlowConfigPageAdminBoundaryMapQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        loading,
        result: queryResult
    } = useDFlowConfigPageAdminBoundaryMapQuery(queryVariables)

    const listingImage = computed<ListingImage | undefined>(() => queryResult.value?.listing?.mainImage as ListingImage ?? undefined)
    const geoFence = computed<MultiPolygonArray | undefined>(() => queryResult.value?.listing?.adminBoundary?.geofence ?? undefined)

    const fullScreenRoute = computed<RouteLocationNamedRaw>(() => ({
        name: 'listingAdminBoundary',
        params: {
            id: listingId.value
        }
    }))
</script>

<style scoped>
    .contentWrapper {
        position: relative;
        height: 100%;
        width: 100%;
    }

    .mapWrapper {
        height: 100%;
        width: 100%;
    }

    .mapContainer {
        height: 100%;
        width: 100%;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
    }

    .mapContainer.isEmbedded {
        padding-top: 0;
    }
</style>