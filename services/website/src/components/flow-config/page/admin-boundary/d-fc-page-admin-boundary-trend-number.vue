<template>
    <v-layout class="align-center">
        <d-tooltip :open-on-hover="Boolean(description)"
                   location="top"
                   open-delay="300"
                   type="info">
            <template #activator="{props: activatorProps}">
                <div v-bind="activatorProps">
                    <slot/>
                    <v-icon v-if="Math.abs(trend) <= treshold"
                            :icon="mdiMinus"
                            class="ms-1"
                            size="small"/>
                    <v-icon v-else
                            :color="(negativeIsGood && trend < 0) || (!negativeIsGood && trend > 0)? 'green' : 'red'"
                            :icon="trend > 0? mdiArrowUpBold : mdiArrowDownBold"
                            class="ms-1"
                            size="small"/>
                </div>
            </template>
            {{ description }}
        </d-tooltip>
    </v-layout>
</template>

<script lang="ts"
        setup>
    import {mdiArrowDownBold, mdiArrowUpBold, mdiMinus} from "@mdi/js";
    import DTooltip from "@/adapter/vuetify/theme/components/d-tooltip.vue";

    const {trend, treshold = 0.0, negativeIsGood = false, description = ""} = defineProps<{
        trend: number,
        treshold?: number,
        negativeIsGood?: boolean,
        description?: string
    }>()
</script>

<style scoped>
</style>