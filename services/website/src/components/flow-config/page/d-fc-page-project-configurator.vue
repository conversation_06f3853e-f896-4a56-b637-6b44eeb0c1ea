<template>
    <div class="wrapper">
        <iframe :src="iframeSrc"
                frameborder="0"/>
    </div>
</template>

<script lang="ts"
        setup>
    import {PROJECT_CONFIGURATOR_URL} from "@/utility/environment";
    import {computed, inject} from "vue";
    import {LListingContextInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const listingId = inject(LListingIdInjection)!;
    const displayContext = inject(LListingContextInjection)!;
    const pcContext = computed<"BACKOFFICE" | "END_CUSTOMER">(() => displayContext.value === 'EDIT' ? 'BACKOFFICE' : 'END_CUSTOMER');
    const iframeSrc = computed<string>(() => `${PROJECT_CONFIGURATOR_URL}?l=${listingId.value}&c=${pcContext.value}`);

</script>

<style lang="scss"
       scoped>
    .wrapper,
    .wrapper iframe {
        border: none;
        width: 100%;
        height: 100%;
    }
</style>
