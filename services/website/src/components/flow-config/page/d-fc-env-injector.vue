<template>
    <slot/>
</template>

<script lang="ts"
        setup>
    import {provide, toRef} from "vue";
    import {LListingEnvironmentInjection} from "@/components/listing/ListingInjectionKeys";
    import {ListingEnvironment} from "@/model/listing/ListingEnvironment";

    const props = defineProps<{
        env: ListingEnvironment
    }>()

    provide(LListingEnvironmentInjection, toRef(() => props.env))
</script>

<style scoped>
</style>