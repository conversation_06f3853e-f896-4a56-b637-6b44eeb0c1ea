<template>
    <div class="fillWrapper">
        <div class="fillContainer">
            <d-fc-page-location-map :disable-overlay="disableOverlay"
                                    :focused-point-of-interest-ids="focusedPointOfInterestIds"
                                    :is-loading="isLoading"
                                    :listing-image="listingImage"
                                    :points-of-interest="visiblePOIs"
                                    :show-full-screen-button="showFullScreenButton"
                                    :single-element-zoom="singleElementZoom"
                                    show-scale/>
            <!--TODO forced unwrapping?! -->
            <!-- TODO image url -->
        </div>

        <d-sheet v-if="listingPOIs.length > 0"
                 class="elevation-4 toolbar">
            <v-chip-group v-if="availablePOIParentGroupTypes.length > 0"
                          v-model="selectedPOIParentGroupTypes"
                          class="pb-0"
                          multiple>
                <d-chip v-for="poiParentGroupType in availablePOIParentGroupTypes"
                        :key="poiParentGroupType"
                        :text="t(`enums.pointOfInterestParentGroupType.${poiParentGroupType}`)"
                        :value="poiParentGroupType"
                        type="default"/>
            </v-chip-group>

            <v-chip-group v-if="availablePOITypes.length > 0"
                          v-model="selectedPOITypes"
                          class="pb-0"
                          multiple>
                <d-chip v-for="poiType in availablePOITypes"
                        :key="poiType"
                        :prepend-icon="POI_TYPE_ICONS[poiType]"
                        :text="t(`enums.pointOfInterestType.${poiType}`)"
                        :value="poiType"
                        density="compact"
                        type="default"/>
            </v-chip-group>
        </d-sheet>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, inject, ref, watch} from 'vue';
    import {DFlowConfigPageLocationMapWrapperQueryVariables, Listing, ListingImage, PointOfInterest, useDFlowConfigPageLocationMapWrapperQuery} from '@/adapter/graphql/generated/graphql';
    import {useI18n} from "vue-i18n";
    import {groupBy} from "@/utility/arrays";
    import {POI_TYPE_ICONS, PointOfInterestType, PointOfInterestTypeValues} from "@/model/listing/PointOfInterestType";
    import {PointOfInterestParentGroup, PointOfInterestParentGroupValues} from "@/model/listing/PointOfInterestParentGroup";
    import {Optional} from "@/model/Optional";
    import DChip from "@/adapter/vuetify/theme/components/chip/d-chip.vue";
    import DSheet from "@/adapter/vuetify/theme/components/card/d-sheet.vue";
    import DFcPageLocationMap from "@/components/flow-config/page/location/d-fc-page-location-map.vue";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const {t} = useI18n();

    defineProps<{
        showFullScreenButton?: boolean
        disableOverlay?: boolean,
    }>();

    const listingId = inject(LListingIdInjection)!

    const singleElementZoom = 16

    const emits = defineEmits<{
        'update:modelValue': [hasContent: boolean]
    }>();

    //TODO: hier muss noch eigene query her und die felder müssen angepasst werden
    const queryVariables = computed<DFlowConfigPageLocationMapWrapperQueryVariables>(() => ({
        id: listingId.value,
    }));

    const selectedPOIParentGroupTypes = ref<PointOfInterestParentGroup[]>([]) //ref okay
    const selectedPOITypes = ref<PointOfInterestType[]>([]) //ref okay

    const availablePOIParentGroupTypes = computed<PointOfInterestParentGroup[]>(() => {
        const availablePOIParentGroupTypes = listingPOIs.value
            .flatMap(poi => poi.parentGroups)
            .map(parentGroupType => parentGroupType as PointOfInterestParentGroup)
            .filter(parentGroupType => PointOfInterestParentGroupValues.includes(parentGroupType))
        return [...new Set(availablePOIParentGroupTypes)]
    })

    const availablePOITypes = computed<PointOfInterestType[]>(() => {
        const currentListingPOIs = listingPOIs.value;
        const currentSelectedPOITypes = selectedPOITypes.value;

        let visiblePOITypes = listingPOIs.value
            .filter(poi => poi.parentGroups.some(parentGroup => selectedPOIParentGroupTypes.value.includes(parentGroup as PointOfInterestParentGroup)))
            .map(poi => poi.type as PointOfInterestType)
            .filter(poiType => PointOfInterestTypeValues.includes(poiType))
        visiblePOITypes = [...new Set(visiblePOITypes)]

        const availablePOITypes = currentListingPOIs
            .sort((a, b) => t(`enums.pointOfInterestType.${a.type}`).localeCompare(t(`enums.pointOfInterestType.${b.type}`)))
            .map(poi => poi.type as PointOfInterestType)
            .filter(poiType => PointOfInterestTypeValues.includes(poiType) && visiblePOITypes.includes(poiType) || currentSelectedPOITypes.includes(poiType))
        return [...new Set(availablePOITypes)]
    })

    const visiblePOIs = computed<PointOfInterest[]>(() => {
        const currentSelectedPOITypes = selectedPOITypes.value;
        return listingPOIs.value.filter(poi => currentSelectedPOITypes.includes(poi.type as PointOfInterestType))
    })

    const focusedPOIs = computed<PointOfInterest[]>(() => {
        const pointOfInterests = visiblePOIs.value;

        if (pointOfInterests.length === 0) {
            return []
        }

        const poisGroupedByType = groupBy(pointOfInterests, poi => poi.type as PointOfInterestType)
        const poiTypes = Object.keys(poisGroupedByType) as PointOfInterestType[];

        //1 Typ => 3 POIs
        if (poiTypes.length === 1) {
            return poisGroupedByType[poiTypes[0]]
                .sort((a, b) => a.distance - b.distance)
                .slice(0, 3)
        }

        //2 Typen => jeweils 2 POIs
        if (poiTypes.length === 2) {
            return poiTypes
                .flatMap(type => poisGroupedByType[type]
                    .sort((a, b) => a.distance - b.distance)
                    .slice(0, 2)
                )
                .sort((a, b) => a.distance - b.distance)
        }

        //3+ Typen => jeweils 1 POI
        return poiTypes
            .flatMap(type => poisGroupedByType[type]
                .sort((a, b) => a.distance - b.distance)
                .slice(0, 1)
            )
            .sort((a, b) => a.distance - b.distance)
    })

    const focusedPointOfInterestIds = computed<string[]>(() => {
        return focusedPOIs.value.map(poi => poi.id)
    })

    //TODO loading state
    //TODO irgendwie werden öfter mal requests gecancelt
    const {
        result: queryResult,
        loading: isLoading,
    } = useDFlowConfigPageLocationMapWrapperQuery(queryVariables);

    const listing = computed<Optional<Listing>>(() => queryResult.value?.listing as Listing ?? null);
    const listingImage = computed<ListingImage | undefined>(() => listing.value?.mainImage ?? undefined);
    const listingPOIs = computed<PointOfInterest[]>(() => listing.value?.pointsOfInterest ?? []);

    watch(listing, listing => {
        emits('update:modelValue', listing !== null) //TODO kann man noch bissl besser/"sicherer" machen
    }, {
        immediate: true,
    })
</script>

<style scoped>
    .fillWrapper {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: end;
        height: 100%;
        width: 100%;
    }

    .fillContainer {
        flex: 1 1 auto;
    }

    .toolbar {
        position: relative;
        z-index: 400;
        flex: 0 1 auto;
        min-height: calc(var(--v-d-native-app-bottom-bar-height) + env(safe-area-inset-bottom));
        padding: 16px 16px calc(16px + env(safe-area-inset-bottom)) 16px;
        display: flex;
        flex-direction: column;
    }

    .toolbar > * {
        flex: 1 1 auto;
    }
</style>
