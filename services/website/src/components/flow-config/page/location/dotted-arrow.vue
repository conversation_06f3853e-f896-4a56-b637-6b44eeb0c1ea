<template>
    <svg :fill="color"
         :height="height"
         :viewBox="`${127-width} 0 ${width} 12`"
         :width="width"
         xmlns="http://www.w3.org/2000/svg">
        <path d="M127 6L117 0.226497V11.7735L127 6ZM0.896484 7H1.88167V5H0.896484V7ZM3.85203 7H5.8224V5H3.85203V7ZM7.79277 7H9.76314V5H7.79277V7ZM11.7335 7H13.7039V5H11.7335V7ZM15.6742 7H17.6446V5H15.6742V7ZM19.615 7H21.5853V5H19.615V7ZM23.5557 7H25.5261V5H23.5557V7ZM27.4964 7H29.4668V5H27.4964V7ZM31.4372 7H33.4075V5H31.4372V7ZM35.3779 7H37.3483V5H35.3779V7ZM39.3186 7H41.289V5H39.3186V7ZM43.2594 7H45.2297V5H43.2594V7ZM47.2001 7H49.1705V5H47.2001V7ZM51.1408 7H53.1112V5H51.1408V7ZM55.0816 7H57.052V5H55.0816V7ZM59.0223 7H60.9927V5H59.0223V7ZM62.9631 7H64.9334V5H62.9631V7ZM66.9038 7H68.8742V5H66.9038V7ZM70.8445 7H72.8149V5H70.8445V7ZM74.7853 7H76.7556V5H74.7853V7ZM78.726 7H80.6964V5H78.726V7ZM82.6667 7H84.6371V5H82.6667V7ZM86.6075 7H88.5778V5H86.6075V7ZM90.5482 7H92.5186V5H90.5482V7ZM94.4889 7H96.4593V5H94.4889V7ZM98.4297 7H100.4V5H98.4297V7ZM102.37 7H104.341V5H102.37V7ZM106.311 7H108.282V5H106.311V7ZM110.252 7H112.222V5H110.252V7ZM114.193 7H116.163V5H114.193V7ZM118.133 7H120.104V5H118.133V7ZM122.074 7H124.044V5H122.074V7Z"/>
    </svg>
</template>

<script lang="ts"
        setup>

    const {width = 127, height = 12, color = "#36CE6E"} = defineProps<{
        width: number,
        height: number,
        color: string
    }>()
</script>

<style scoped>

</style>