<template>
    <div class="contentWrapper">
        <d-listing-detail-toolbar v-if="!IS_EMBEDDED">
            <d-listing-button-back :route="{
                                        name: 'viewListing',
                                        params: {
                                            id: listingId,
                                        },
                                        hash: '#location'
                                   }"
                                   :text="t('components.listing.images.backToListing')"/>

            <v-spacer/>

            <v-layout class="align-center justify-end me-4">
                <div v-if="showObfuscatedLocation"
                     class="text-body-2 text-end">
                    {{ addressZipCodeAndCityOf(address) }}
                </div>
                <div v-else
                     class="text-body-2 text-end">
                    {{ addressStreetAndHouseNumberOf(address) }}
                    <br>
                    {{ addressZipCodeAndCityOf(address) }}
                </div>

                <!-- TODO: click jumps in map -->
                <!-- TODO check auf überhaupt bild verfügbar -->
                <!-- TODO: alt vom avatar-image nutzen: listing.mainImage.name -->
                <v-avatar :image="listingImage?.thumbnailURL ?? undefined"
                          class="ms-3"
                          size="48"/>
            </v-layout>
        </d-listing-detail-toolbar>

        <div :class="{isEmbedded: IS_EMBEDDED}"
             class="mapWrapper">
            <d-fc-page-location-map-wrapper disable-overlay/>
        </div>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, inject} from 'vue';
    import {DFlowConfigPageLocationDetailQueryVariables, ListingImage, useDFlowConfigPageLocationDetailQuery} from '@/adapter/graphql/generated/graphql';
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {IS_EMBEDDED} from "@/service/embedded-view/embedded-view";
    import DFcPageLocationMapWrapper from "@/components/flow-config/page/location/d-fc-page-location-map-wrapper.vue";
    import DListingButtonBack from "@/components/listing/fragments/d-listing-button-back.vue";
    import DListingDetailToolbar from "@/components/listing/fragments/d-listing-detail-toolbar.vue";
    import {useListingAddress} from "@/service/use-listing-address";
    import {useAddressService} from "@/utility/address";
    import {LFlowDataInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!

    //TODO: hier muss noch eigene query her und die felder müssen angepasst werden
    const queryVariables = computed<DFlowConfigPageLocationDetailQueryVariables>(() => ({
        id: listingId.value,
    }));

    //TODO irgendwie werden öfter mal requests gecancelt
    const {
        result: queryResult,
        loading: isLoading,
    } = useDFlowConfigPageLocationDetailQuery(queryVariables);

    const listingImage = computed<Optional<ListingImage>>(() => queryResult?.value?.listing?.mainImage as ListingImage ?? null);

    const {
        address,
        showObfuscatedLocation
    } = useListingAddress(flowData)

    const {
        addressStreetAndHouseNumberOf,
        addressZipCodeAndCityOf
    } = useAddressService()

    const {t} = useI18n()
</script>

<style scoped>
    .contentWrapper {
        position: relative;
        height: 100%;
        width: 100%;
    }

    .mapWrapper {
        height: 100%;
        width: 100%;
        padding-top: calc(var(--v-d-native-app-top-bar-height) + env(safe-area-inset-top));
    }

    .mapWrapper.isEmbedded {
        padding-top: 0 !important;
    }
</style>
