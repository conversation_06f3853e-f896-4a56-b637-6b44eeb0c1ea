<template>
    <lf-map :disable-dragging="disableDragging"
            :disable-overlay="disableOverlay"
            :disable-zoom="disableZoom"
            :full-screen-route="showFullScreenButton ? finalFullScreenRoute : undefined"
            :initial-bounds="region === null || geoShape || geolocation ? undefined : LISTING_REGION_BOUNDS[region]"
            :is-loading="isLoading"
            :padding="mapPadding"
            :show-scale="showScale">
        <lf-tile-layer/>

        <lf-world-mask v-if="geoShape"
                       :fit-to-bounds="focusOnGeoShape ? 'no-animation' : false"
                       :holes="geoShape"/>

        <d-map-marker-poi v-for="pointOfInterest in unfocusedPOIs"
                          :key="pointOfInterest.id"
                          :point-of-interest="pointOfInterest"/>

        <lf-feature-group :fit-to-bounds="featureGroupFitToBounds"
                          :single-element-zoom="singleElementZoom">
            <d-map-marker-poi v-for="pointOfInterest in focusedPOIs"
                              :key="pointOfInterest.id"
                              :point-of-interest="pointOfInterest"/>
            <d-map-marker-listing-house :flow-data="flowData"
                                        :listing-image="listingImage"
                                        force-image-variant
                                        hide-popup/>
        </lf-feature-group>
    </lf-map>
</template>

<script lang="ts"
        setup>
    import {computed, inject} from 'vue';
    import {ListingImage, PointOfInterest} from '@/adapter/graphql/generated/graphql';
    import LfMap from "@/adapter/leaflet/components/lf-map.vue";
    import {MultiPolygonArray} from "@/model/MultiPolygonArray";
    import {LISTING_REGION_BOUNDS} from "@/model/listing/ListingRegion";
    import LfWorldMask from "@/adapter/leaflet/components/lf-world-mask.vue";
    import DMapMarkerPoi from "@/components/map/d-map-marker-poi.vue";
    import LfFeatureGroup from "@/adapter/leaflet/components/lf-feature-group.vue";
    import DMapMarkerListingHouse from "@/components/map/d-map-marker-listing-house.vue";
    import {RouteLocationNamedRaw} from "vue-router";
    import LfTileLayer from "@/adapter/leaflet/components/lf-tile-layer.vue";
    import {createLfPadding, LfPadding} from "@/adapter/leaflet/LfPadding";
    import {useDisplay} from "vuetify";
    import {useListingAddress} from "@/service/use-listing-address";
    import {LFlowDataInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";

    //TODO; map padding brauchen wir noch für "panTo"-Effekte (gibts bei leaflet). Wenn ich auf ein Popup klicke, dass link oben in der karte liegt …
    //TODO: … scrollt die map zwar zum popup, damit es vollständig sichtbar ist, aber es wird dann verdeckt von den zoom-buttons

    const props = defineProps<{
        listingImage?: ListingImage
        pointsOfInterest: PointOfInterest[]
        focusedPointOfInterestIds: string[]
        showFullScreenButton?: boolean
        disableOverlay?: boolean
        isLoading?: boolean
        geoShape?: MultiPolygonArray,
        disableZoom?: boolean
        focusOnGeoShape?: boolean
        singleElementZoom?: number
        disableDragging?: boolean
        houseMapMarkerSize?: number
        fullScreenRoute?: RouteLocationNamedRaw
        showScale: boolean
    }>();

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!

    const {smAndDown} = useDisplay()

    const focusedPOIs = computed<PointOfInterest[]>(() => {
        return props.pointsOfInterest.filter(poi => props.focusedPointOfInterestIds.includes(poi.id))
    })
    const unfocusedPOIs = computed<PointOfInterest[]>(() => {
        return props.pointsOfInterest.filter(poi => !props.focusedPointOfInterestIds.includes(poi.id))
    })
    const {
        region,
        geolocation
    } = useListingAddress(flowData)

    const featureGroupFitToBounds = computed<boolean | 'no-animation'>(() => {
        if (props.focusOnGeoShape) {
            return false
        }
        return focusedPOIs.value.length <= 0 ? 'no-animation' : true
    })

    //TODO: parametrisieren aus d-map-marker-listing-house
    //ist immer größer als POI-Marker, von daher brauchen wir nur diese Größe zu nehmen
    const listingMarkerSize = computed<number>(() => smAndDown.value ? 50 : 100)

    //TODO: parametrisieren aus d-map-marker.vue
    const listingMarkerDiagonal = computed<number>(() => Math.sqrt(2) * listingMarkerSize.value)
    const listingMarkerBottomTriangleSize = computed<number>(() => (listingMarkerDiagonal.value - listingMarkerSize.value) / 2)
    const listingMarkerIconHeight = computed<number>(() => {
        return listingMarkerDiagonal.value - listingMarkerBottomTriangleSize.value
    })
    const padding = 32
    const mapPadding = computed<LfPadding>(() => {
        if (geolocation.value === null) {
            return createLfPadding(padding)
        }
        const currentListingMarkerHeight = listingMarkerIconHeight.value
        const currentListingMarketHalfWidth = listingMarkerSize.value / 2
        return {
            top: currentListingMarkerHeight + padding,
            bottom: padding,
            left: currentListingMarketHalfWidth / 2 + padding,
            right: currentListingMarketHalfWidth / 2 + padding
        }
    })

    const finalFullScreenRoute = computed<RouteLocationNamedRaw>(() => {
        return props.fullScreenRoute ? props.fullScreenRoute : {
            name: 'listingLocation',
            params: {
                id: listingId.value,
            }
        }
    })
</script>

<style scoped>
</style>
