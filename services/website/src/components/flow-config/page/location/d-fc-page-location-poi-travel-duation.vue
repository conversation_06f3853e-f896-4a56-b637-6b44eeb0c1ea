<template>
    <v-layout class="align-center">
        <v-icon v-if="!hideIcon"
                :icon="transportTypeIcons[travelDuration.transportType as TransportType]"/>
        <div :class="{fill}"
             class="duration text-no-wrap">
            {{ n(travelDuration.maxDuration, 'minutes') }}
        </div>
    </v-layout>
</template>

<script lang="ts"
        setup>
    import {TransportType} from "@/model/listing/TransportType";
    import {computed} from "vue";
    import {mdiBike, mdiCar, mdiWalk} from "@mdi/js";
    import {TravelDuration} from "@/adapter/graphql/generated/graphql";
    import {useI18n} from "vue-i18n";

    const {n} = useI18n()

    const {travelDuration, hideIcon = false, fill = false} = defineProps<{
        travelDuration: TravelDuration,
        hideIcon?: boolean
        fill?: boolean
    }>()

    const transportTypeIcons = computed<Record<TransportType, string>>(() => ({
        "BIKING": mdiBike,
        "DRIVING": mdiCar,
        "WALKING": mdiWalk
    }))
</script>

<style scoped>
    .duration {
        text-align: right;
        width: 100%;
    }

    .duration:not(.fill) {
        width: 7ch;
    }
</style>