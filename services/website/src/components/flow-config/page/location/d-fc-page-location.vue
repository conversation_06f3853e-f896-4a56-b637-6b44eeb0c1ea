<template>
    <d-fc-layout>
        <template #left>
            <v-lazy v-if="smAndDown"
                    class="mb-4">
                <v-responsive :aspect-ratio="5/3"
                              class="max-h-220"
                              @click="goToFullscreenMap">
                    <d-fc-page-location-map :focused-point-of-interest-ids="[]"
                                            :is-loading="loading"
                                            :listing-image="listingImage"
                                            :points-of-interest="[]"
                                            :show-scale="false"
                                            :single-element-zoom="singleElementZoom"
                                            disable-dragging
                                            disable-overlay
                                            disable-zoom
                                            show-full-screen-button/>
                </v-responsive>
            </v-lazy>

            <v-container>
                <template v-if="loading">
                    <d-skeleton-loader type="heading"/>
                    <d-skeleton-loader class="mt-6"
                                       type="card"/>
                    <d-skeleton-loader class="mt-6"
                                       type="article"/>
                    <d-skeleton-loader class="mt-6"
                                       type="table"/>
                </template>
                <template v-else>
                    <d-h5 class="mb-4">{{ t('components.listing.detail.location.locationDescriptionTitle') }}</d-h5>
                    <d-card>
                        <template #text>
                            <div class="text-center text-body-2">
                                <d-icon-location :height="42"
                                                 :width="42"
                                                 color="rgb(var(--v-theme-d-icon-default))"/>
                                <div class="font-weight-bold">
                                    {{ t('components.listing.detail.location.addressTitle') }}
                                </div>
                                <div v-if="showObfuscatedLocation !== false">
                                    {{ addressStreetAndHouseNumberOf(address) }}
                                </div>
                                <div>
                                    {{ addressZipCodeAndCityOf(address) }}
                                </div>
                            </div>
                        </template>
                    </d-card>
                    <v-layout v-if="latitude !== null && longitude !== null"
                              class="justify-end align-center">
                        <d-btn :href="`https://earth.google.com/web/@${latitude},${longitude},0a,5000d`"
                               :icon="mdiGoogleEarth"
                               size="small"
                               target="_blank"
                               type="default"
                               variant="text"/>
                        <d-btn :href="`https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`"
                               :icon="mdiGoogleMaps"
                               size="small"
                               target="_blank"
                               type="default"
                               variant="text"/>
                    </v-layout>
                    <d-expandable-description-card v-if="listingLocationDescription"
                                                   :description="listingLocationDescription"
                                                   class="mt-4"/>
                    <template v-if="nearestMetropolitanCities.length > 0">
                        <d-h5 class="mt-6 mb-4">{{ t('components.listing.detail.location.nearestMetropolitanCitiesTitle') }}</d-h5>
                        <v-layout class="py-4 justify-center align-center nearestMetropolitanCities">
                            <div v-if="smAndUp"
                                 class="markerIconWrapper flex-grow-0 flex-shrink-0">
                                <v-avatar :icon="listing?.mainImage?.thumbnailURL? undefined : mdiHome"
                                          :image="listing?.mainImage?.thumbnailURL ?? undefined"
                                          class="markerIcon"/>
                            </div>
                            <v-layout class="flex-shrink-1 flex-wrap">
                                <v-layout v-for="(poi, index) in nearestMetropolitanCities"
                                          :key="index"
                                          class="mb-2 align-center flex-shrink-1 flex-1-1-100">
                                    <div class="me-4 text-body-2 flex-grow-0 flex-shrink-0 text-center">
                                        <div>
                                            <d-fc-page-location-poi-distance :distance="poi.distance"/>
                                        </div>
                                        <div>
                                            <d-fc-page-location-poi-travel-duation v-if="poi.travelDurations.some(travelDuration => travelDuration.transportType === 'DRIVING')"
                                                                                   :travel-duration="poi.travelDurations.find(travelDuration => travelDuration.transportType === 'DRIVING')!"
                                                                                   fill
                                                                                   hide-icon/>
                                        </div>
                                    </div>
                                    <dotted-arrow :height="12"
                                                  :width="xs? 50: 127"
                                                  class="me-4 flex-shrink-0 flex-grow-0"
                                                  color="rgb(var(--v-theme-secondary))"/>
                                    <v-card class="flex-shrink-0 flex-grow-1"
                                            variant="outlined">
                                        <template #text>
                                            <v-layout class="align-center">

                                                <d-icon-metropolitan-city :height="xs? 24: 32"
                                                                          :width="xs? 24:32"
                                                                          color="rgb(var(--v-theme-primary))"/>
                                                <div class="text-body-2 ms-4">
                                                    {{ poi.name }}
                                                </div>
                                            </v-layout>
                                        </template>
                                    </v-card>
                                </v-layout>
                            </v-layout>
                        </v-layout>
                    </template>
                    <template v-if="poiGroupTypes.length > 0">
                        <d-h5 class="mt-6 mb-4">{{ t('components.listing.detail.location.pointsOfInterestTitle') }}</d-h5>
                        <template v-for="(poiGroupType, index) in poiGroupTypes"
                                  :key="index">
                            <d-h6 :class="{'mt-4': index > 0}"
                                  class="mb-2 text-accent">{{ t(`enums.pointOfInterestParentGroupType.${poiGroupType}`) }}
                            </d-h6>
                            <d-listing-detail-table class="pa-1">
                                <d-listing-detail-table-row v-for="poi in nearestGroupedPois[poiGroupType]"
                                                            :key="poi.id"
                                                            break-on-xs
                                                            dense>
                                    <template #left>
                                        <v-layout>
                                            <d-icon :icon="POI_TYPE_ICONS[poi.type as PointOfInterestType]"
                                                    class="mx-2"
                                                    type="default"/>
                                            <span class="text-truncate"
                                                  style="max-width: 140px;">{{ t(`enums.pointOfInterestType.${poi.type}`) }}</span>
                                            <template v-if="xs">
                                                <v-spacer/>
                                                <d-fc-page-location-poi-distance :distance="poi.distance"/>
                                            </template>
                                        </v-layout>
                                    </template>
                                    <template #right>
                                        <v-layout class="align-center justify-space-between">
                                            <v-layout class="flex-grow-0 text-light text-caption">
                                                <d-fc-page-location-poi-travel-duation v-for="(travelDuration, travelDurationIndex) in poi.travelDurations"
                                                                                       :key="travelDurationIndex"
                                                                                       :travel-duration="travelDuration"
                                                                                       class="me-2"/>
                                            </v-layout>
                                            <template v-if="!xs">
                                                <v-spacer/>
                                                <d-fc-page-location-poi-distance :distance="poi.distance"/>
                                                <d-switch v-if="mdAndUp"
                                                          v-model="selectedPoiTypes"
                                                          :value="poi.type"
                                                          class="flex-grow-0 mx-2 ms-4"
                                                          density="compact"/>
                                            </template>
                                        </v-layout>
                                    </template>
                                </d-listing-detail-table-row>
                            </d-listing-detail-table>
                        </template>
                    </template>
                </template>
            </v-container>
        </template>

        <template #right>
            <div class="h-100 w-100">
                <d-fc-page-location-map :focused-point-of-interest-ids="focusedPois"
                                        :is-loading="loading"
                                        :listing-image="listingImage"
                                        :points-of-interest="displayedPois"
                                        :single-element-zoom="singleElementZoom"
                                        disable-overlay
                                        show-full-screen-button
                                        show-scale/>
            </div>
        </template>
    </d-fc-layout>
</template>

<script lang="ts"
        setup>
    import {DFlowConfigPageLocationQueryVariables, Listing, ListingImage, PointOfInterest, useDFlowConfigPageLocationQuery} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {computed, inject, ref} from "vue";
    import {useI18n} from "vue-i18n";
    import DH5 from "@/adapter/vuetify/theme/components/text/headline/d-h5.vue";
    import {PointOfInterestParentGroup, PointOfInterestParentGroupValues} from "@/model/listing/PointOfInterestParentGroup";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {POI_TYPE_ICONS, PointOfInterestType, PointOfInterestTypeValues} from "@/model/listing/PointOfInterestType";
    import DListingDetailTable from "@/components/listing/old/components/d-listing-detail-table.vue";
    import DListingDetailTableRow from "@/components/listing/old/components/d-listing-detail-table-row.vue";
    import {useDisplay} from "vuetify";
    import {useRouter} from "vue-router";
    import DSwitch from "@/adapter/vuetify/theme/components/switch/d-switch.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DSkeletonLoader from "@/adapter/vuetify/theme/components/skeleton-loader/d-skeleton-loader.vue";
    import DFcPageLocationMap from "@/components/flow-config/page/location/d-fc-page-location-map.vue";
    import DFcPageLocationPoiDistance from "@/components/flow-config/page/location/d-fc-page-location-poi-distance.vue";
    import DFcPageLocationPoiTravelDuation from "@/components/flow-config/page/location/d-fc-page-location-poi-travel-duation.vue";
    import DIconLocation from "@/components/icon/d-icon-location.vue";
    import {useTranslatedListingField} from "@/service/use-listing-translation";
    import {useListingAddress} from "@/service/use-listing-address";
    import {mdiGoogleEarth, mdiGoogleMaps, mdiHome} from "@mdi/js";
    import DExpandableDescriptionCard from "@/components/fragment/d-expandable-description-card.vue";
    import DIconMetropolitanCity from "@/components/icon/d-icon-metropolitan-city.vue";
    import DottedArrow from "@/components/flow-config/page/location/dotted-arrow.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {useAddressService} from "@/utility/address";
    import DFcLayout from "@/components/flow-config/page/d-fc-layout.vue";
    import {LFlowDataInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!

    const {t} = useI18n()
    const {xs, mdAndUp, smAndUp, smAndDown} = useDisplay()
    const singleElementZoom = 16

    const router = useRouter()

    const queryVariables = computed<DFlowConfigPageLocationQueryVariables>(() => ({
        id: listingId.value
    }))
    const {
        result: queryResult,
        loading
    } = useDFlowConfigPageLocationQuery(queryVariables)

    const listing = computed<Optional<Listing>>(() => queryResult.value?.listing as Listing ?? null)
    const listingImage = computed<ListingImage | undefined>(() => listing.value?.mainImage ?? undefined)
    const pois = computed<PointOfInterest[]>(() => listing.value?.pointsOfInterest ?? [])
    const {
        addressStreetAndHouseNumberOf,
        addressZipCodeAndCityOf
    } = useAddressService()

    const poiGroupOrder: PointOfInterestParentGroup[] = ["BASIC_SERVICES", "TRANSPORT", "SHOPPING", "LEISURE", "SPORTS"]

    const poiGroupTypes = computed<PointOfInterestParentGroup[]>(() => pois.value
        .flatMap(poi => (poi.parentGroups ?? []) as PointOfInterestParentGroup[])
        .filter((value, index, array) => array.indexOf(value) === index && PointOfInterestParentGroupValues.includes(value as PointOfInterestParentGroup))
        .sort((a, b) => poiGroupOrder.indexOf(a as PointOfInterestParentGroup) < poiGroupOrder.indexOf(b as PointOfInterestParentGroup) ? -1 : 1)
    )

    const nearestGroupedPois = computed<Record<string, PointOfInterest[]>>(() => {
        const result: Record<string, PointOfInterest[]> = {}
        for (const poiGroupType of poiGroupTypes.value) {
            result[poiGroupType] = pois.value
                .filter(poi => poi.parentGroups?.includes(poiGroupType) && PointOfInterestTypeValues.includes(poi.type as PointOfInterestType))
                .sort((a, b) => a.distance < b.distance ? -1 : 1)
                .filter((value, index, array) => array.findIndex(poi => poi.type === value.type) === index)
                .sort((a, b) => t(`enums.pointOfInterestType.${a.type}`) < t(`enums.pointOfInterestType.${b.type}`) ? -1 : 1)
        }
        return result
    })

    const selectedPoiTypes = ref<PointOfInterestType[]>([]) //ref okay
    const displayedPois = computed<PointOfInterest[]>(() => pois.value.filter(poi => selectedPoiTypes.value.includes(poi.type as PointOfInterestType)))
    const focusedPois = computed<string[]>(() => {
        let result: string[] = []
        selectedPoiTypes.value.forEach(poiType => {
            let displayedPois = pois.value.filter(poi => poi.type === poiType)
            displayedPois = displayedPois.sort((a, b) => a.distance < b.distance ? -1 : 1)
            const sliceAmount = displayedPois[0].distance > 4000 ? 1 : 3
            result = result.concat(displayedPois.slice(0, sliceAmount).map(poi => poi.id))
        })
        return result
    })

    const nearestMetropolitanCities = computed<PointOfInterest[]>(() => pois.value.filter(poi => poi.type === "METROPOLITAN_CITY" && (listing.value?.adminBoundary?.populationGroup !== "METROPOLITAN_CITY" || city.value !== poi.name)).sort((a, b) => a.distance < b.distance ? -1 : 1).slice(0, 3))

    const {listingLocationDescription} = useTranslatedListingField(flowData)
    const {
        city,
        address,
        longitude,
        latitude,
        showObfuscatedLocation
    } = useListingAddress(flowData)

    function goToFullscreenMap() {
        router.push({
            name: 'listingLocation',
            params: {
                id: listingId.value
            }
        })
    }
</script>

<style lang="scss"
       scoped>
    .markerIconWrapper {
        width: 60px;
        height: 60px;
        border-radius: 50% 50% 50% 0;
        background: rgb(var(--v-theme-primary));
        transform: rotate(-45deg);
        padding: 6px;
    }

    .markerIconWrapper::after {
        content: '';
        width: 60px;
        height: 60px;
        background: rgb(var(--v-theme-primary));
        border-radius: 50%;
    }

    .markerIcon {
        color: rgb(var(--v-theme-on-primary));
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        transform: rotate(45deg);
    }

    .nearestMetropolitanCities {
        column-gap: 24px;
    }

    .max-h-220 {
        max-height: 220px;
    }
</style>