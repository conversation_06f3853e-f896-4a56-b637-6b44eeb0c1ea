<template>
    <v-container class="pa-9"
                 fluid>
        <v-row class="counterBar">
            <v-col v-for="(item, index) in items"
                   :key="index"
                   class="text-center">
                <d-h6>
                    <d-fc-field-value :field-value="item.fieldValue"/>
                </d-h6>
                <div class="text-body-2">{{ flowConfigTranslator(item.key) }}</div>
            </v-col>
        </v-row>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {KeyValueListUiElementItem} from "@/adapter/graphql/generated/graphql";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import DFcFieldValue from "@/components/flow-config/ui-element/d-fc-field-value.vue";

    defineProps<{
        items: readonly KeyValueListUiElementItem[]
    }>()

    const {flowConfigTranslator} = useFlowConfig()
</script>

<style scoped>
    .counterBar {
        gap: 36px;
    }
</style>