<template>
    <d-fc-input-wrapper :element="singleSelectionElement">
        <template #unwrapped="{label, hint}">
            <d-select v-model="rawField"
                      :clearable="defaultValue === null"
                      :display-name-supplier="itemName"
                      :hint="hint"
                      :icon-supplier="(item) => flowConfigIconSupplier(item.icon)"
                      :id-supplier="(item) => item.value"
                      :image-supplier="(item) => item.imageURL ?? null"
                      :items="items"
                      :label="label"
                      :persistent-hint="hint !== null"
                      :readonly="context === 'VIEW'"
                      :required="singleSelectionElement.isRequired"
                      :sorted="singleSelectionElement.sorting === 'ASC'"
                      :variant="displayVariant">
                <template v-if="singleSelectionElement.otherUserValue && isUIElementVisible(singleSelectionElement.otherUserValue.element, arrayIndex,context, listing, flowData)"
                          #selectedItemAppend="{item}">
                    <div v-if="singleSelectionElement.otherUserValue.value === item.value"
                         :class="{'mt-4': displayVariant === 'item-group', 'mt-2 mx-4': displayVariant === 'select'}"
                         style="color: rgb(var(--v-theme-d-text-default));">
                        <d-fc-ui-element-string :string-element="singleSelectionElement.otherUserValue.element"
                                                autofocus/>
                    </div>
                </template>

                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="possibleEnumValues"
                                                :unit="null"/>
                </template>
            </d-select>
            <span v-if="DEBUG_FLOW_CONFIG"
                  class="text-caption text-green">{{ (props.singleSelectionElement as any).singleSelectionDefaultValue as (string | undefined) }}</span>
        </template>

        <div class="ma-4">
            <d-select v-model="rawField"
                      :clearable="defaultValue === null"
                      :display-name-supplier="itemName"
                      :icon-supplier="(item) => flowConfigIconSupplier(item.icon)"
                      :id-supplier="(item) => item.value"
                      :image-supplier="(item) => item.imageURL ?? null"
                      :items="items"
                      :readonly="context === 'VIEW'"
                      :required="singleSelectionElement.isRequired"
                      :sorted="singleSelectionElement.sorting === 'ASC'"
                      :variant="displayVariant">
                <template v-if="singleSelectionElement.otherUserValue && isUIElementVisible(singleSelectionElement.otherUserValue.element, arrayIndex,context, listing, flowData)"
                          #selectedItemAppend="{item}">
                    <div v-if="singleSelectionElement.otherUserValue.value === item.value"
                         :class="{'mt-4': displayVariant === 'item-group', 'mt-2 mx-4': displayVariant === 'select'}">
                        <d-fc-ui-element-string :string-element="singleSelectionElement.otherUserValue.element"
                                                autofocus/>
                    </div>
                </template>

                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="possibleEnumValues"
                                                :unit="null"/>
                </template>
            </d-select>
            <span v-if="DEBUG_FLOW_CONFIG"
                  class="text-caption text-green">{{ (props.singleSelectionElement as any).singleSelectionDefaultValue as (string | undefined) }}</span>
        </div>
    </d-fc-input-wrapper>
</template>

<script lang="ts"
        setup>
    import {computed, inject, watchEffect} from "vue";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataString} from "@/model/listing/FlowData";
    import DFcInputWrapper from "@/components/flow-config/ui-element/d-fc-input-wrapper.vue";
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import {SingleSelectionUiElement, SingleSelectionUiElementItem, TranslatedEnumValue} from "@/adapter/graphql/generated/graphql";
    import {isUIElementVisible, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {Optional} from "@/model/Optional";
    import DFcUiElementString from "@/components/flow-config/ui-element/d-fc-ui-element-string.vue";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {LArrayIndexInjection, LListingContextInjection, LListingInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";

    //TODO: type supporten <<<<<<<<<<<<<<<<<<<<<<

    const props = defineProps<{
        singleSelectionElement: SingleSelectionUiElement
    }>()

    const listing = inject(LListingInjection)!
    const flowData = inject(LMutableFlowDataInjection)!
    const context = inject(LListingContextInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const displayVariant = computed<'item-group' | 'select'>(() => {
        return hasAnyItemWithImage.value ? 'item-group' : 'select'
    })

    const {
        flowConfigTranslator,
        flowConfigIconSupplier
    } = useFlowConfig()

    const items = computed<readonly SingleSelectionUiElementItem[]>(() => {
        return props.singleSelectionElement.items.filter(i => isUIElementVisible(
            i,
            arrayIndex.value,
            context.value,
            listing.value,
            flowData.value
        ))
    })

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.singleSelectionElement.field,
        arrayIndex: arrayIndex.value
    }));
    const field = useMutableFlowDataString(flowData, fieldWithArrayIndex)

    const rawField = computed<Optional<SingleSelectionUiElementItem>>({
        get() {
            const fieldValue = field.value
            if (fieldValue === null) {
                return null
            }
            const item = items.value.find(i => i.value === fieldValue)
            if (item === undefined) {
                return null
            }
            return item
        },
        set(item: Optional<SingleSelectionUiElementItem>) {
            field.value = item === null ? null : item.value
        }
    })

    const hasAnyItemWithImage = computed<boolean>(() => {
        return items.value.some(i => i.imageURL !== null && i.imageURL !== undefined)
    })

    function itemName(item: SingleSelectionUiElementItem): Optional<string> {
        return flowConfigTranslator(item.label)
    }

    const possibleEnumValues = computed<readonly TranslatedEnumValue[]>(() => {
        return items.value.map(item => ({
            value: item.value,
            label: item.label
        }))
    })

    const defaultValue = computed<Optional<string>>(() => {
        const defaultValue = (props.singleSelectionElement as any).singleSelectionDefaultValue as (string | undefined) //Ummapping des Feldes in der GraphQL-Query
        return defaultValue === undefined ? null : defaultValue
    })

    watchEffect(() => {
        if (field.value === null) {
            field.value = defaultValue.value
        }
    })
</script>

<style scoped>
</style>