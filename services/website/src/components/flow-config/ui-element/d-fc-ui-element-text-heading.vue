<template>
    <d-h5 :class="{'mt-8': !isFirstElement}">{{ flowConfigTranslator(textElement.text) }}</d-h5>
</template>

<script lang="ts"
        setup>
    import {TextUiElement} from "@/adapter/graphql/generated/graphql";
    import DH5 from "@/adapter/vuetify/theme/components/text/headline/d-h5.vue";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";

    defineProps<{
        textElement: TextUiElement
        isFirstElement: boolean
    }>()

    const {flowConfigTranslator} = useFlowConfig()
</script>

<style scoped>
</style>