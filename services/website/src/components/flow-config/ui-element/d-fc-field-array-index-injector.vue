<template>
    <slot/>
</template>

<script lang="ts"
        setup>
    import {provide, toRef} from "vue";
    import {LArrayIndexInjection} from "@/components/listing/ListingInjectionKeys";
    import {Optional} from "@/model/Optional";

    const props = defineProps<{
        arrayIndex: Optional<number>
    }>()

    provide(LArrayIndexInjection, toRef(() => props.arrayIndex))
</script>

<style scoped>
</style>