<template>
    <v-dialog v-model="isDialogVisible"
              close-on-content-click
              fullscreen>
        <d-card is-in-dialog>
            <template #title>
                <v-layout class="align-center justify-end">
                    <d-btn :icon="mdiClose"
                           type="default"
                           @click.prevent.stop="onDialogClose"/>
                </v-layout>
            </template>

            <d-divider/>

            <div ref="imageWrapper"
                 class="imageWrapper">
                <div :style="{height: `${imageWrapperHeight}px`}">
                    <v-window v-model="selectedImageIndex"
                              :continuous="true"
                              :show-arrows="'hover'"
                              :touch="touchHandlers"
                              class="h-100">
                        <v-window-item v-for="(image, imageIndex) in imageSources"
                                       :key="imageIndex"
                                       class="h-100">
                            <v-img :lazy-src="imageLazySources[imageIndex] ?? undefined"
                                   :src="image"
                                   alt="Blurred Background"
                                   class="blurredImage"
                                   cover/>
                            <v-img :lazy-src="imageLazySources[imageIndex]"
                                   :src="image"
                                   class="h-100"/>
                        </v-window-item>

                        <template #prev>
                            <v-btn v-if="imageSources.length > 1"
                                   :icon="mdiChevronLeft"
                                   class="imageOverlay v-window__left"
                                   @click.prevent.stop="prevImage"/>
                        </template>

                        <template #next>
                            <v-btn v-if="imageSources.length > 1"
                                   :icon="mdiChevronRight"
                                   class="imageOverlay v-window__right"
                                   @click.prevent.stop="nextImage"/>
                        </template>
                    </v-window>
                </div>
            </div>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {mdiChevronLeft, mdiChevronRight, mdiClose} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {computed, shallowRef, watchEffect} from "vue";
    import {Optional} from "@/model/Optional";
    import {useElementSize} from "@vueuse/core";

    const props = defineProps<{
        imageLazySources: readonly string[]
        imageSources: readonly string[]
        index: number
    }>()

    const imageWrapper = shallowRef<Optional<HTMLDivElement>>(null)
    const {height: imageWrapperHeight} = useElementSize(imageWrapper)

    const isDialogVisible = defineModel<boolean>("modelValue", {
        required: true
    })

    function onDialogClose() {
        isDialogVisible.value = false
    }

    const selectedImageIndex = shallowRef<number>(0)

    watchEffect(() => {
        selectedImageIndex.value = props.index
    })

    const touchHandlers = computed(() => {
        return {
            left: (() => nextImage()),
            right: (() => prevImage())
        }
    })

    function nextImage() {
        selectedImageIndex.value = (selectedImageIndex.value + 1) % props.imageSources.length
    }

    function prevImage() {
        const length = props.imageSources.length
        selectedImageIndex.value = (selectedImageIndex.value - 1 + length) % length
    }
</script>

<style scoped>
    .imageWrapper {
        position: relative;
        height: 100%;
        width: 100%;
    }

    .blurredImage {
        position: absolute;
        height: 100%;
        width: 100%;
        object-fit: fill;
        filter: blur(25px);
    }
</style>