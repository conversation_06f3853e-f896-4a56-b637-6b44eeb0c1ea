<template>
    <d-card :to="image.link"
            variant="elevated">
        <template #image>
            <v-img :alt="image.name"
                   :lazy-src="image.base64Image ?? image.lazyUrl"
                   :src="image.base64Image ?? image.url"
                   cover/>
        </template>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {RouteLocationNamedRaw} from "vue-router";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";

    type ImageConfig = {
        url?: string
        name?: string
        lazyUrl?: string
        base64Image?: string
        link: RouteLocationNamedRaw
    }

    defineProps<{
        image: ImageConfig
    }>()
</script>

<style scoped>
</style>