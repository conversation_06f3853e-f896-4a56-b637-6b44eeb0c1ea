<template>
    <!-- TODO: hier fehlen eig. die alt-tags der Bilder innerhalb der v-cards … names wären da, man muss die cards nur umbauen mit "echten" bild-elementen -->
    <!--    {{ displayBreakpointName }}-->
    <div :style="{
            'height': props.narrow ? '250px' : '400px'
        }"
         class="container">
        <template v-if="imageCount > 0">
            <div v-for="(number, index) in bigColumnsCount"
                 :key="index"
                 class="column large">
                <d-card v-if="index < imageCount"
                        :image="listingImages[index].imageAsBase64ForOffline ?? listingImages[index].thumbnailURL ?? undefined"
                        :to="routeForImage(listingImages[index].id)"
                        variant="elevated">
                    <template #image>
                        <v-img :lazy-src="listingImages[index].imageAsBase64ForOffline ?? listingImages[index].miniURL ?? undefined"
                               :name="listingImages[index].name"
                               :src="listingImages[index].imageAsBase64ForOffline ?? listingImages[index].thumbnailURL ?? undefined"
                               cover/>
                    </template>
                </d-card>
            </div>

            <div v-for="(number, index) in smallColumnsCount"
                 :key="index"
                 class="column small">
                <d-card :to="routeForImage(listingImages[bigColumnsCount + (index*2)].id)"
                        variant="elevated">
                    <template #image>
                        <v-img :alt="listingImages[bigColumnsCount + (index*2)].name ?? undefined"
                               :lazy-src="listingImages[bigColumnsCount + (index*2)].imageAsBase64ForOffline ?? listingImages[bigColumnsCount + (index*2)].miniURL ?? undefined"
                               :src="listingImages[bigColumnsCount + (index*2)].imageAsBase64ForOffline ?? listingImages[bigColumnsCount + (index*2)].thumbnailURL ?? undefined"
                               cover/>
                    </template>
                </d-card>
                <d-card :image="listingImages[bigColumnsCount + (index*2) + 1].imageAsBase64ForOffline ?? listingImages[bigColumnsCount + (index*2) + 1].thumbnailURL ?? undefined"
                        :to="routeForImage(listingImages[bigColumnsCount + (index*2) + 1].id)"
                        variant="elevated">
                    <template #image>
                        <v-img :alt="listingImages[bigColumnsCount + (index*2) + 1].name ?? undefined"
                               :lazy-src="listingImages[bigColumnsCount + (index*2) + 1].imageAsBase64ForOffline ?? listingImages[bigColumnsCount + (index*2) + 1].miniURL ?? undefined"
                               :src="listingImages[bigColumnsCount + (index*2) + 1].imageAsBase64ForOffline ?? listingImages[bigColumnsCount + (index*2) + 1].thumbnailURL ?? undefined"
                               cover/>
                    </template>
                </d-card>
            </div>

            <div v-if="showAssets || imageCount > 1"
                 class="allImagesButton ma-4">
                <v-layout class="align-center overflow-visible">
                    <d-listing-images-download-button v-if="showAssets"/>

                    <d-btn v-if="imageCount > 1"
                           :class="{'ms-2': showAssets}"
                           :prepend-icon="mdiImage"
                           :text="t('components.listing.images.more')"
                           :to="imagesRoute"
                           size="large"
                           type="tertiary"
                           variant="elevated"/>
                </v-layout>
            </div>
        </template>

        <d-card v-else
                class="w-100 pa-8 h-100"
                variant="elevated">
            <d-icon-edit-listing-no-images/>
        </d-card>
    </div>
</template>

<script lang="ts"
        setup>
    import {computed, inject, shallowRef} from "vue";
    import {useDisplay} from "vuetify";
    import {IS_DEVELOPMENT} from "@/utility/environment";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {mdiImage} from "@mdi/js";
    import {DFlowConfigUiElementImageGalleryMQueryVariables, ListingImage, useDFlowConfigUiElementImageGalleryMQuery} from "@/adapter/graphql/generated/graphql";
    import {UIElementImageGalleryImageRoutes} from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery";
    import {RouteLocationNamedRaw} from "vue-router";
    import {listingImageRoute} from "@/model/listing/FlowData";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DIconEditListingNoImages from "@/components/icon/d-icon-edit-listing-no-images.vue";
    import {LListingEnvironmentInjection, LListingIdInjection, LSelectedPageInjection} from "@/components/listing/ListingInjectionKeys";
    import DListingImagesDownloadButton from "@/components/listing/d-listing-images-download-button.vue";
    import {isImageValidForFlowConfig, isImageValidForMainGallery, removeDuplicateImages} from "@/components/flow-config/ui-element/image-gallery/image-gallery-helper";

    const {t} = useI18n();
    const testImageCount = shallowRef<Optional<number>>(null)

    const props = defineProps<{
        imageIds: ReadonlySet<string>
        imagesRoute: RouteLocationNamedRaw
        imageIdToImageRoute: UIElementImageGalleryImageRoutes
        showAssets: boolean
        narrow: boolean
    }>()

    const listingId = inject(LListingIdInjection)!
    const selectedPage = inject(LSelectedPageInjection)!
    const env = inject(LListingEnvironmentInjection)!

    const queryVariables = computed<DFlowConfigUiElementImageGalleryMQueryVariables>(() => ({
        id: listingId.value,
    }));
    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
    } = useDFlowConfigUiElementImageGalleryMQuery(queryVariables);

    const listingImages = computed<readonly ListingImage[]>(() => {
        const ids = props.imageIds
        const images = queryResult.value?.listing?.images as ListingImage[] ?? []

        if (props.showAssets) {
            return removeDuplicateImages(images.filter(isImageValidForMainGallery))
        }
        return removeDuplicateImages(images.filter(i => isImageValidForFlowConfig(i) && ids.has(i.id)))
    })

    const {
        name: displayBreakpointName,
    } = useDisplay();

    const imageCount = computed<number>(() => testImageCount.value === null
        ? listingImages.value.length
        : Math.max(0, Math.min(testImageCount.value, listingImages.value.length))
    )

    const bigColumnsCount = computed<number>(() => {
        const maxColumns = imageCount.value
        const layout = env.value.layout

        let columns = 0
        if (layout.position === 'DEFAULT') {
            columns = 1
        } else {
            switch (displayBreakpointName.value) {
                case 'xs':
                    columns = 1
                    break
                case 'sm':
                    columns = 1;
                    break
                case 'md':
                    columns = 1;
                    break
                case 'lg':
                    columns = 1;
                    break
                case 'xl':
                    columns = 2;
                    break
                case "xxl":
                    columns = 2;
                    break
                default:
                    if (IS_DEVELOPMENT) {
                        console.warn("Unknown breakpoint: ", displayBreakpointName.value)
                    }
            }
        }

        return Math.min(columns, maxColumns)
    });

    const smallColumnsCount = computed<number>(() => {
        const finalImageCount = Math.max(0, imageCount.value - bigColumnsCount.value)
        const maxColumns = Math.floor(finalImageCount / 2)
        const layout = env.value.layout

        let columns = 0

        if (layout.position === 'DEFAULT') {
            columns = 1
        } else {
            switch (displayBreakpointName.value) {
                case 'xs':
                    columns = 0
                    break
                case 'sm':
                    columns = 0;
                    break
                case 'md':
                    columns = 1;
                    break
                case 'lg':
                    columns = 2;
                    break
                case 'xl':
                    columns = 2;
                    break
                case "xxl":
                    columns = 3;
                    break
                default:
                    if (IS_DEVELOPMENT) {
                        console.warn("Unknown breakpoint: ", displayBreakpointName.value)
                    }
            }
        }

        return Math.min(columns, maxColumns)
    });

    function routeForImage(imageId: string): RouteLocationNamedRaw {
        if (props.showAssets) {
            const arrayIndex = listingImages.value.findIndex(i => i.id === imageId)!

            return listingImageRoute(
                listingId.value,
                env.value.context,
                selectedPage.value?.id ?? null,
                null,
                arrayIndex
            )
        }
        return props.imageIdToImageRoute.get(imageId)!
    }
</script>

<style scoped>
    .container {
        display: flex;
        gap: 16px;
        position: relative;
    }

    .column {
        flex: auto;
    }

    .column.large, .column.large :deep(.v-card), .column.large :deep(.v-img) {
        height: 100%;
        width: 100%;
    }

    .column.small {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .column.small :deep(.v-card) {
        aspect-ratio: 1;
        height: calc(50% - 8px); /*half gap*/
    }

    .allImagesButton {
        position: absolute;
        bottom: 0;
        right: 0;
    }

    .noImageWrapper {
        aspect-ratio: calc(5 / 3);
        max-height: 600px;
        width: 100%;
    }
</style>