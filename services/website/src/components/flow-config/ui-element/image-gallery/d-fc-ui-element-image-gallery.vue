<template>
    <d-fc-ui-element-image-gallery-s v-if="smAndDown || imageGalleryElement.preferredSize?.startsWith('S')"
                                     :image-id-to-image-route="imageIdToImageRoute"
                                     :image-ids="imageIds"
                                     :show-assets="showAssets"/>

    <d-fc-ui-element-image-gallery-m v-else-if="imageGalleryElement.preferredSize?.startsWith('M')"
                                     :image-id-to-image-route="imageIdToImageRoute"
                                     :image-ids="imageIds"
                                     :images-route="imagesRoute"
                                     :narrow="imageGalleryElement.preferredSize?.includes('NARROW')"
                                     :show-assets="showAssets"/>

    <d-fc-ui-element-image-gallery-l v-else-if="imageGalleryElement.preferredSize?.startsWith('L')"
                                     :image-id-to-image-route="imageIdToImageRoute"
                                     :image-ids="imageIds"
                                     :images-route="imagesRoute"
                                     :show-assets="showAssets"/>

    <d-fc-unknown-type v-else
                       :data="imageGalleryElement"
                       :supported-types="[
                            'S',
                            'M',
                            'L'
                       ]"
                       :type="imageGalleryElement.preferredSize"
                       clazz="ImageGalleryUIElement"/>
</template>

<script lang="ts"
        setup>
    import {ImageGalleryUiElement} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, listingImageRoute} from "@/model/listing/FlowData";
    import {Optional} from "@/model/Optional";
    import DFcUiElementImageGalleryS from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery-s.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementImageGalleryM from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery-m.vue";
    import DFcUiElementImageGalleryL from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery-l.vue";
    import {RouteLocationNamedRaw} from "vue-router";
    import {computed, inject} from "vue";
    import {useDisplay} from "vuetify";
    import {UIElementImageGalleryImageRoutes} from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery";
    import {LFlowDataInjection, LListingContextInjection, LListingIdInjection, LSelectedPageInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        imageGalleryElement: ImageGalleryUiElement
    }>()

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!
    const context = inject(LListingContextInjection)!
    const selectedPage = inject(LSelectedPageInjection)!

    const {smAndDown} = useDisplay()

    const showAssets = computed<boolean>(() => props.imageGalleryElement.idFieldValue == null)
    const arrayIndices = computed<readonly number[]>(() => flowData.value.arrayIndicesOf(props.imageGalleryElement))

    function createIdFieldWithArrayIndex(arrayIndex: Optional<number>): Optional<FlowConfigFieldWithArrayIndex> {
        const field = props.imageGalleryElement.idFieldValue?.field
        if (field == null) {
            return null
        }
        return {
            field,
            arrayIndex
        }
    }

    function idOfElementByArrayIndex(arrayIndex: number): Optional<string> {
        const fieldWithArrayIndex = createIdFieldWithArrayIndex(arrayIndex);
        if (fieldWithArrayIndex === null) {
            return null
        }
        return flowData.value.getString(fieldWithArrayIndex)
    }

    const imageIds = computed<ReadonlySet<string>>(() => {
        const ids = arrayIndices.value
            .map(idOfElementByArrayIndex)
            .filter(id => id !== null)

        return new Set(ids)
    })
    const imagesRoute = computed<RouteLocationNamedRaw>(() => {
        return listingImageRoute(
            listingId.value,
            context.value,
            selectedPage.value?.id ?? null,
            createIdFieldWithArrayIndex(null),
            null
        )
    })
    const imageIdToImageRoute = computed<UIElementImageGalleryImageRoutes>(() => {
        const lId = listingId.value
        const imageRoutes = new Map<string, RouteLocationNamedRaw>()
        arrayIndices.value.forEach(arrayIndex => {
            const id = idOfElementByArrayIndex(arrayIndex)
            if (id === null) {
                return
            }
            const idFieldWithArrayIndex = createIdFieldWithArrayIndex(arrayIndex)
            const route = listingImageRoute(
                lId,
                context.value,
                selectedPage.value?.id ?? null,
                idFieldWithArrayIndex,
                null
            )

            imageRoutes.set(id, route)
        })
        return imageRoutes
    })
</script>

<style scoped>
</style>