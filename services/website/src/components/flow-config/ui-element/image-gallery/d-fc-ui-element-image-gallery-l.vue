<template>
    <v-container v-if="listingImages.length > 0"
                 class="pa-0 position-relative"
                 fluid>
        <v-row justify="center">
            <v-col style="max-width: 800px;">
                <div class="container">
                    <template v-for="(imageConfig, index) in distributedImageConfigs"
                              :key="index">
                        <!--                        <template v-if="IS_TESTING_ENABLED">-->
                        <!--                            {{ imageConfig.templateName }}-->
                        <!--                        </template>-->
                        <template v-if="imageConfig.templateName === 'singleHorizontal'">
                            <div class="row"
                                 style="aspect-ratio: calc(16/9);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'singleVertical'">
                            <div class="row"
                                 style="aspect-ratio: calc(3/4);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                            </div>
                        </template>

                        <template v-else-if="imageConfig.templateName === 'doubleHorizontal'">
                            <div class="row"
                                 style="grid-template-columns: auto auto; aspect-ratio: calc(7/2);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'doubleVertical'">
                            <div class="row"
                                 style="grid-template-columns: auto auto; aspect-ratio: calc(4/3);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"/>
                            </div>
                        </template>

                        <template v-else-if="imageConfig.templateName === 'singleHorizontalSingleVertical'">
                            <div class="row"
                                 style="grid-template-columns: 2fr 1fr; aspect-ratio: calc(2/1);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'singleVerticalSingleHorizontal'">
                            <div class="row"
                                 style="grid-template-columns: 1fr 2fr; aspect-ratio: calc(2/1);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"/>
                            </div>
                        </template>

                        <template v-else-if="imageConfig.templateName === 'doubleHorizontalSingleVertical'">
                            <div class="row"
                                 style="grid-template-columns: auto auto; aspect-ratio: calc(3/2);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 2;"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'singleVerticalDoubleHorizontal'">
                            <div class="row"
                                 style="grid-template-columns: auto auto; aspect-ratio: calc(3/2);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 2;"/>
                            </div>
                        </template>

                        <template v-else-if="imageConfig.templateName === 'tripleVertical1Big'">
                            <div class="row"
                                 style="grid-template-columns: 3fr 2fr; aspect-ratio: calc(5/6);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 2;"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'tripleVertical3Big'">
                            <div class="row"
                                 style="grid-template-columns: 2fr 3fr; aspect-ratio: calc(5/6);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 2;"/>
                            </div>
                        </template>

                        <template v-else-if="imageConfig.templateName === 'doubleHorizontalDoubleVertical1'">
                            <div class="row"
                                 style="grid-template-columns: auto auto; aspect-ratio: calc(11/12);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 2 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[3]"
                                                                    style="grid-row: 3;"/>
                            </div>
                        </template>
                        <template v-else-if="imageConfig.templateName === 'doubleHorizontalDoubleVertical2'">
                            <div class="row"
                                 style="grid-template-columns: auto auto;  aspect-ratio: calc(11/12);">
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[0]"
                                                                    style="grid-row: 1 / span 2;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[1]"
                                                                    style="grid-row: 1;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[2]"
                                                                    style="grid-row: 3;"/>
                                <d-fc-ui-element-image-gallery-card :image="imageConfig.elements[3]"
                                                                    style="grid-row: 2 / span 2;"/>
                            </div>
                        </template>
                    </template>
                </div>
                <!--    <pre>-->
                <!--        {{ listing?.images }}-->
                <!--    </pre>-->
                <!--    <pre>-->
                <!--        {{ imageConfigs }}-->
                <!--    </pre>-->
                <!--                <pre>-->
                <!--                        {{ bestCombination }}-->
                <!--                    </pre>-->
                <!--    <pre>-->
                <!--        {{ distributedImageConfigs }}-->
                <!--    </pre>-->
                <!--                <pre>-->
                <!--                    A{{ collectFieldIdsOfUIElement(imageGalleryElement) }}-->
                <!--                    <br>-->
                <!--                    B{{ arrayIndices }}-->
                <!--                    <br>-->
                <!--                    C{{ imageGalleryElement }}-->
                <!--                </pre>-->
            </v-col>
        </v-row>

        <div v-if="showAssets && listingImages.length > 0"
             class="downloadButton my-7 mx-4">
            <d-listing-images-download-button/>
        </div>
    </v-container>

    <d-card v-else
            class="w-100 pa-8 noImageWrapper"
            variant="elevated">
        <d-icon-edit-listing-no-images/>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {DFlowConfigUiElementImageGalleryLQueryVariables, ListingImage, useDFlowConfigUiElementImageGalleryLQuery} from "@/adapter/graphql/generated/graphql";
    import {computed, inject} from "vue";
    import {calculateBestTemplateCombination, distributeToImageGalleryTemplates, IMAGE_GALLERY_TEMPLATES, ImageGalleryTemplateCombination, ImageGalleryTemplateCombinationElements, ImageOrientation} from "@/utility/image-gallery";
    import {createRandomGenerator} from "@/utility/random";
    import {Optional} from "@/model/Optional";
    import {RouteLocationNamedRaw} from "vue-router";
    import {chunkify} from "@/utility/arrays";
    import {UIElementImageGalleryImageRoutes} from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery";
    import DFcUiElementImageGalleryCard from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery-card.vue";
    import {listingImageRoute} from "@/model/listing/FlowData";
    import DIconEditListingNoImages from "@/components/icon/d-icon-edit-listing-no-images.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {LListingContextInjection, LListingIdInjection, LSelectedPageInjection} from "@/components/listing/ListingInjectionKeys";
    import DListingImagesDownloadButton from "@/components/listing/d-listing-images-download-button.vue";
    import {isImageValidForFlowConfig, isImageValidForMainGallery, removeDuplicateImages} from "@/components/flow-config/ui-element/image-gallery/image-gallery-helper";

    const IS_TESTING_ENABLED = false
    const USE_CUSTOM_TEST_DATA = false

    const props = defineProps<{
        imageIds: ReadonlySet<string>
        imagesRoute: RouteLocationNamedRaw
        imageIdToImageRoute: UIElementImageGalleryImageRoutes
        showAssets: boolean
    }>()

    const listingId = inject(LListingIdInjection)!
    const context = inject(LListingContextInjection)!
    const selectedPage = inject(LSelectedPageInjection)!

    type ImageConfig = {
        url?: string
        name?: string
        lazyUrl?: string
        base64Image?: string
        orientation: ImageOrientation
        link: RouteLocationNamedRaw
    }

    const queyVariables = computed<DFlowConfigUiElementImageGalleryLQueryVariables>(() => ({
        id: listingId.value,
    }));
    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
    } = useDFlowConfigUiElementImageGalleryLQuery(queyVariables);

    const listingImages = computed<readonly ListingImage[]>(() => {
        const ids = props.imageIds
        const images = queryResult.value?.listing?.images as ListingImage[] ?? []

        if (props.showAssets) {
            return removeDuplicateImages(images.filter(isImageValidForMainGallery))
        }
        return removeDuplicateImages(images.filter(i => isImageValidForFlowConfig(i) && ids.has(i.id)))
    })

    const imageConfigs = computed<ImageConfig[]>(() => {
        if (IS_TESTING_ENABLED) {
            const imagesOrientations: ImageOrientation[] = USE_CUSTOM_TEST_DATA
                ? [
                    '-',
                    '|',
                    '|',
                    '-',
                    '-',
                    '-',
                    '|',
                    '-',
                    '|',
                    '-',
                    '-',
                    '|',
                    '|',
                    '|',
                    '-'
                ]
                : Object.values(IMAGE_GALLERY_TEMPLATES).flat()

            const images = listingImages.value
            const imageConfigs: ImageConfig[] = []

            if (images.length > 0) {
                for (let i = 0; i < imagesOrientations.length; ++i) {
                    const image = images[i % (images.length)]
                    const orientation = imagesOrientations[i];

                    imageConfigs.push({
                        url: image.thumbnailURL ?? undefined,
                        name: image.name ?? undefined,
                        lazyUrl: image.miniURL ?? undefined,
                        base64Image: image.imageAsBase64ForOffline ?? undefined,
                        orientation: orientation,
                        link: props.imagesRoute
                    })
                }
            }

            return imageConfigs
        }

        return listingImages.value.map((image, imageIndex) => {
            return {
                url: image.originalURL ?? undefined,
                name: image.name ?? undefined,
                lazyUrl: image.miniURL ?? undefined,
                base64Image: image.imageAsBase64ForOffline ?? undefined,
                orientation: image.isVertical ? '|' : '-',
                link: routeForImage(image.id, imageIndex)
            }
        })
    })

    const imagesOrientations = computed<ImageOrientation[]>(() => imageConfigs.value.map(imageConfig => imageConfig.orientation))

    const IMAGE_GALLERY_CHUNK_SIZE = 20
    const bestCombination = computed<Optional<ImageGalleryTemplateCombination>>(() => {
        if (IS_TESTING_ENABLED && !USE_CUSTOM_TEST_DATA) {
            return Object.keys(IMAGE_GALLERY_TEMPLATES).flat() as ImageGalleryTemplateCombination
        }

        const randomGenerator = createRandomGenerator(listingId.value)

        const orientations = imagesOrientations.value
        if (orientations.length <= IMAGE_GALLERY_CHUNK_SIZE) {
            return calculateBestTemplateCombination(randomGenerator, orientations)
        }

        return chunkify(imagesOrientations.value, IMAGE_GALLERY_CHUNK_SIZE)
            .map(orientationsChunk => calculateBestTemplateCombination(randomGenerator, orientationsChunk))
            .filter(c => c !== null)
            .map(c => c!)
            .flat()
    })

    const distributedImageConfigs = computed<ImageGalleryTemplateCombinationElements<ImageConfig>[]>(() => {
        if (bestCombination.value === null) {
            return []
        }
        let distributedImageConfigs = distributeToImageGalleryTemplates(imageConfigs.value, bestCombination.value!)

        if (IS_TESTING_ENABLED) {
            distributedImageConfigs = distributedImageConfigs.map(distributedImageConfig => {
                return {
                    ...distributedImageConfig,
                    elements: distributedImageConfig.elements.map((element, index) => {
                        const isHorizontal = element.orientation === '-'
                        const width = isHorizontal ? 400 : 300
                        const height = isHorizontal ? 300 : 400
                        const text = `${index + 1}${element.orientation === '-' ? 'H' : 'V'}`
                        return {
                            ...element,
                            url: `https://dummyimage.com/${width}x${height}/000/fff&text=${text}`
                        }
                    })
                }
            })
        }

        return distributedImageConfigs
    })

    function routeForImage(imageId: string, arrayIndex: number): RouteLocationNamedRaw {
        if (props.showAssets) {
            return listingImageRoute(
                listingId.value,
                context.value,
                selectedPage.value?.id ?? null,
                null,
                arrayIndex
            )
        }
        return props.imageIdToImageRoute.get(imageId)!
    }
</script>

<style scoped>
    /*.wrapper {
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }*/

    .container {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .row {
        display: grid;
        gap: 16px;
    }

    /*.row, .row :deep(.v-card), .row :deep(.v-img) {
        height: 100%;
        width: 100%;
    }*/

    .downloadButton {
        position: absolute;
        top: 0;
        right: 0;
    }

    .noImageWrapper {
        aspect-ratio: calc(5 / 3);
        max-height: 600px;
        width: 100%;
    }
</style>