<template>
    <d-card variant="elevated">
        <div class="gallery">
            <d-skeleton-loader v-if="isLoading"
                               :boilerplate="!isLoading"
                               height="100%"
                               type="image"
                               width="100%"/>

            <div v-else-if="listingImages.length <= 0"
                 class="pa-8 h-100 w-100">
                <d-icon-edit-listing-no-images/>
            </div>

            <v-window v-else-if="listingImages.length > 0"
                      v-model="selectedImageIndex"
                      :continuous="true"
                      :touch="touchHandlers"
                      class="h-100"
                      show-arrows>
                <v-window-item v-for="(image, imageIndex) in listingImages"
                               :key="imageIndex"
                               class="h-100 galleryPage"
                               @click="onImageClicked(image, imageIndex)">
                    <v-img :lazy-src="image.imageAsBase64ForOffline ?? image.miniURL ?? undefined"
                           :src="image.imageAsBase64ForOffline ?? image.thumbnailURL ?? undefined"
                           alt="Blurred Background"
                           class="blurredImage"
                           cover/>
                    <v-img :alt="image.name ?? undefined"
                           :lazy-src="image.imageAsBase64ForOffline ?? image.miniURL ?? undefined"
                           :src="image.imageAsBase64ForOffline ?? image.thumbnailURL ?? undefined"
                           class="h-100"/>
                </v-window-item>

                <template #prev>
                    <d-btn v-if="listingImages.length > 1"
                           :icon="mdiChevronLeft"
                           class="imageOverlay v-window__left"
                           size="large"
                           type="tertiary"
                           variant="elevated"
                           @click.prevent="prevImage"/>
                </template>

                <template #next>
                    <d-btn v-if="listingImages.length > 1"
                           :icon="mdiChevronRight"
                           class="imageOverlay v-window__right"
                           size="large"
                           type="tertiary"
                           variant="elevated"
                           @click.prevent="nextImage"/>
                </template>

                <div v-if="listingImages.length > 1"
                     class="counterChipWrapper">
                    <v-tabs v-model="selectedImageIndex"
                            center-active
                            class="pa-1 counterChip ma-4"
                            hide-slider>
                        <v-tab v-for="(image, imageIndex) in listingImages"
                               :key="imageIndex"
                               class="pa-0"
                               style="min-width: 0;"
                               @click.prevent="selectedImageIndex = imageIndex">
                            <v-icon :icon="imageIndex === selectedImageIndex ? mdiCircle : mdiCircleMedium"/>
                        </v-tab>
                    </v-tabs>
                </div>
            </v-window>

            <div v-if="showAssets && listingImages.length > 0"
                 class="downloadButton ma-4">
                <d-listing-images-download-button/>
            </div>
        </div>
    </d-card>
</template>
<script lang="ts"
        setup>
    import {computed, inject, shallowRef} from "vue";
    import {mdiChevronLeft, mdiChevronRight, mdiCircle, mdiCircleMedium} from "@mdi/js";
    import {DFlowConfigUiElementImageGallerySQueryVariables, ListingImage, useDFlowConfigUiElementImageGallerySQuery} from "@/adapter/graphql/generated/graphql";
    import DSkeletonLoader from "@/adapter/vuetify/theme/components/skeleton-loader/d-skeleton-loader.vue";
    import {RouteLocationNamedRaw, useRouter} from "vue-router";
    import {UIElementImageGalleryImageRoutes} from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery";
    import {listingImageRoute} from "@/model/listing/FlowData";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DIconEditListingNoImages from "@/components/icon/d-icon-edit-listing-no-images.vue";
    import {LListingContextInjection, LListingIdInjection, LSelectedPageInjection} from "@/components/listing/ListingInjectionKeys";
    import DListingImagesDownloadButton from "@/components/listing/d-listing-images-download-button.vue";
    import {isImageValidForFlowConfig, isImageValidForMainGallery, removeDuplicateImages} from "@/components/flow-config/ui-element/image-gallery/image-gallery-helper";

    const props = defineProps<{
        imageIds: ReadonlySet<string>
        imageIdToImageRoute: UIElementImageGalleryImageRoutes
        showAssets: boolean
    }>()

    const listingId = inject(LListingIdInjection)!
    const context = inject(LListingContextInjection)!
    const selectedPage = inject(LSelectedPageInjection)!

    const queryVariables = computed<DFlowConfigUiElementImageGallerySQueryVariables>(() => ({
        id: listingId.value,
    }));
    const {
        result: queryResult,
        loading: isLoading, //TODO use loading state
    } = useDFlowConfigUiElementImageGallerySQuery(queryVariables);

    const listingImages = computed<readonly ListingImage[]>(() => {
        const ids = props.imageIds
        const images = queryResult.value?.listing?.images as ListingImage[] ?? []

        if (props.showAssets) {
            return removeDuplicateImages(images.filter(isImageValidForMainGallery))
        }
        return removeDuplicateImages(images.filter(i => isImageValidForFlowConfig(i) && ids.has(i.id)))
    })

    const router = useRouter()
    const selectedImageIndex = shallowRef<number>(0)

    const touchHandlers = computed(() => {
        return {
            left: (() => nextImage()),
            right: (() => prevImage())
        }
    })

    function nextImage() {
        selectedImageIndex.value = (selectedImageIndex.value + 1) % listingImages.value.length
    }

    function prevImage() {
        const length = listingImages.value.length
        selectedImageIndex.value = (selectedImageIndex.value - 1 + length) % length
    }

    function onImageClicked(image: ListingImage, imageIndex: number) {
        const route = routeForImage(image.id, imageIndex)
        router.push(route)
    }

    function routeForImage(imageId: string, arrayIndex: number): RouteLocationNamedRaw {
        if (props.showAssets) {
            return listingImageRoute(
                listingId.value,
                context.value,
                selectedPage.value?.id ?? null,
                null,
                arrayIndex
            )
        }
        return props.imageIdToImageRoute.get(imageId)!
    }
</script>

<style scoped>
    .gallery {
        aspect-ratio: calc(5 / 3);
        max-height: 600px;
        width: 100%;
        position: relative;
    }

    .galleryPage {
        cursor: pointer;
    }

    /* TODO: das wird auch in d-listing-image-banner.vue verwendet */
    .imageOverlay {
        background-color: rgba(var(--v-theme-background), 0.9) !important;
        color: rgb(var(--v-theme-on-background)) !important;
    }

    .counterChipWrapper {
        display: flex;
        position: absolute;
        height: 100%;
        width: 100%;
        align-items: end;
        justify-content: center;
        pointer-events: none;
    }

    .counterChipWrapper :deep(.v-tabs) {
        background-color: rgba(0, 0, 0, 0.5) !important;
        border-radius: 16px !important;
        pointer-events: auto;
    }

    .counterChipWrapper :deep(.v-slide-group__content), .counterChipWrapper :deep(.v-tabs--density-default) {
        --v-tabs-height: unset;
    }

    .counterChipWrapper :deep(.v-tab), .counterChipWrapper :deep(.v-slide-group-item--active) * {
        color: white;
    }

    :deep(.v-slide-group__prev), :deep(.v-slide-group__next) {
        display: none;
    }

    .blurredImage {
        position: absolute;
        height: 100%;
        width: 100%;
        object-fit: fill;
        filter: blur(25px);
    }

    .downloadButton {
        position: absolute;
        top: 0;
        right: 0;
    }
</style>