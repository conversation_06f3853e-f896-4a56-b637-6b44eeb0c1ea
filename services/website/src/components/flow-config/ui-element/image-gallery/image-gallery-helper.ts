import {ListingImage} from "@/adapter/graphql/generated/graphql";

/**
 * Da wir in vue-apollo.ts für 24h das offline erzeugte Image behalten, kommt es zwangsläufig zu Duplikaten bei ein und
 * demselben Foto. Hiermit bereinigen wir die Duplikate und bevorzugen das offline erzeugte Foto, damit der User keine
 * Ladezeiten für seine eigenen Fotos hat.
 */
export function removeDuplicateImages(items: ListingImage[]): ListingImage[] {
    const map = new Map<string, ListingImage>();

    items.forEach(item => {
        if (map.has(item.id)) {
            const existingItem = map.get(item.id);
            // Prefer base64 variant in case the offline image and the image from server are both present.
            // Keeping the base64 one is advantageous because the user faces no loading times.
            if (!existingItem?.imageAsBase64ForOffline && item.imageAsBase64ForOffline) {
                map.set(item.id, item);
            }
        } else {
            map.set(item.id, item);
        }
    });

    return Array.from(map.values());
}

export function isImageValidForMainGallery(image: ListingImage): boolean {
    return image.type === 'USER_UPLOAD'
}

export function isImageValidForFlowConfig(image: ListingImage): boolean {
    return image.type === 'LISTING_FIELD'
}