<template>
    <v-table :class="themeLayerClass"
             class="bg-transparent rounded-lg"
             density="compact">
        <tbody>
            <tr v-for="(item, index) in items"
                :key="index">
                <td v-if="existsItemWithIcon"
                    style="width: 0%;">
                    <d-icon v-if="item.icon"
                            :icon="flowConfigIconSupplier(item.icon)"
                            type="default"/>
                </td>
                <th class="selectable">
                    {{ flowConfigTranslator(item.key) }}
                </th>
                <td class="text-end">
                    <d-fc-field-value :field-value="item.fieldValue"/>
                </td>
            </tr>
        </tbody>
    </v-table>
</template>

<script lang="ts"
        setup>
    import {KeyValueListUiElementItem} from "@/adapter/graphql/generated/graphql";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {computed} from "vue";
    import DFcFieldValue from "@/components/flow-config/ui-element/d-fc-field-value.vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";

    const props = defineProps<{
        items: readonly KeyValueListUiElementItem[]
    }>()

    const {
        flowConfigTranslator,
        flowConfigIconSupplier
    } = useFlowConfig()
    const {themeLayerClass} = useDoorbitTheme()

    const existsItemWithIcon = computed<boolean>(() => props.items.some(i => i.icon !== null && i.icon !== undefined))
</script>

<style scoped>
    .v-table tr {
        color: rgb(var(--v-theme-d-text-default));
    }

    .v-table.onBackground thead tr,
    .v-table.onBackground tfoot tr {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .v-table.onBackground tbody tr:nth-child(even) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .v-table.onBackground tbody tr:nth-child(odd) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .v-table.onSurface thead tr,
    .v-table.onSurface tfoot tr {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .v-table.onSurface tbody tr:nth-child(even) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .v-table.onSurface tbody tr:nth-child(odd) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .selectable {
        user-select: text !important;
    }
</style>