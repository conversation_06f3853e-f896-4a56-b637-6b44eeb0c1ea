<template>
    <v-fab-transition>
        <d-btn v-if="suggestedValue !== null"
               :icon="isSuggestionApplied ? mdiCreation : mdiAutoFix"
               :type="isSuggestionApplied ? 'magic' : 'tertiary'"
               size="small"
               variant="text"
               @click.prevent.stop="onOpenDialog"/>
    </v-fab-transition>

    <v-dialog v-model="isDialogVisible"
              width="auto">
        <d-card max-width="600">
            <v-table class="bg-transparent"
                     fixed-header
                     style="max-height: 300px;">
                <thead>
                    <tr>
                        <th style="width: 49%;">
                            {{ t('flowConfig.uiElement.suggestion.currentValue') }}
                        </th>
                        <th class="text-center"
                            style="width: 2%;">
                            <d-divider vertical/>
                        </th>
                        <th style="width: 49%;">
                            <v-layout class="align-center justify-end">
                                <d-icon :icon="mdiAutoFix"
                                        class="me-2"
                                        type="default"/>
                                <span class="text-end">{{ t('flowConfig.uiElement.suggestion.suggestedValue') }}</span>
                            </v-layout>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="py-3"
                            style="width: 49%;">
                            <code>
                                <d-fc-field-value-raw :field-value="fieldValueReference"
                                                      :raw-value="userValue"/>
                            </code>
                        </td>
                        <td class="text-center position-relative"
                            style="width: 2%;">
                            <d-divider vertical/>
                            <div class="position-absolute"
                                 style="top: calc(50% - 16px); left: calc(50% - 16px);">
                                <d-icon v-if="isSuggestionApplied"
                                        :icon="mdiEqual"
                                        size="32px"
                                        type="success"/>
                                <d-icon v-else
                                        :icon="mdiNotEqualVariant"
                                        size="32px"
                                        type="error"/>
                            </div>
                        </td>
                        <td class="py-3 text-end"
                            style="width: 49%;">
                            <code>
                                <d-fc-field-value-raw :field-value="fieldValueReference"
                                                      :raw-value="suggestedValue"/>
                            </code>
                        </td>
                    </tr>
                </tbody>
            </v-table>

            <template #actions>
                <v-layout :class="isSuggestionApplied ? 'justify-center' : 'justify-space-between'"
                          class="ma-2">
                    <d-btn :text="isSuggestionApplied ? t('flowConfig.uiElement.suggestion.closeButton') : t('flowConfig.uiElement.suggestion.abortButton')"
                           :type="isSuggestionApplied ? 'primary' : 'default'"
                           :variant="isSuggestionApplied ? undefined : 'tonal'"
                           size="large"
                           @click="onCloseDialog"/>
                    <d-btn v-if="!isSuggestionApplied"
                           :prepend-icon="mdiCreation"
                           :text="t('flowConfig.uiElement.suggestion.applyButton')"
                           size="large"
                           type="primary"
                           @click="onApplySuggestion"/>
                </v-layout>
            </template>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {areFlowConfigFieldRawDataEqual, FlowConfigFieldWithArrayIndex, useFlowDataValue, useMutableFlowDataValue} from "@/model/listing/FlowData";
    import {computed, inject, shallowRef, toRef, watchEffect} from "vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {mdiAutoFix, mdiCreation, mdiEqual, mdiNotEqualVariant} from "@mdi/js";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {useI18n} from "vue-i18n";
    import DFcFieldValueRaw from "@/components/flow-config/ui-element/d-fc-field-value-raw.vue";
    import {FlowConfigFieldValueReference, TranslatedEnumValue} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";
    import {LMutableFlowDataInjection, LSuggestionsInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        unit: Optional<ListingFieldUnit>
        possibleEnumValues: readonly TranslatedEnumValue[]
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const suggestions = inject(LSuggestionsInjection)!

    const {t} = useI18n()

    const fieldValueReference = computed<FlowConfigFieldValueReference>(() => ({
        field: {
            id: props.fieldWithArrayIndex.field.id,
        },
        unit: props.unit,
        possibleEnumValues: [...props.possibleEnumValues]
    }))

    const propsFieldWithArrayIndex = toRef(() => props.fieldWithArrayIndex)

    const userValue = useMutableFlowDataValue(flowData, propsFieldWithArrayIndex)
    const suggestedValue = useFlowDataValue(suggestions, propsFieldWithArrayIndex)
    const previousSuggestionValue = shallowRef<Optional<any>>(null)
    const isSuggestionApplied = computed<boolean>(() => areFlowConfigFieldRawDataEqual(userValue.value, suggestedValue.value))

    watchEffect(() => {
        if (canSetSuggestion()) {
            userValue.value = suggestedValue.value
            previousSuggestionValue.value = suggestedValue.value
        } else if (areFlowConfigFieldRawDataEqual(userValue.value, suggestedValue.value)) {
            previousSuggestionValue.value = suggestedValue.value
        }
    })

    function canSetSuggestion() {
        if (userValue.value === null) {
            // User hasn't set the value yet, so we can set the suggestion.
            return true
        }

        if (areFlowConfigFieldRawDataEqual(userValue.value, previousSuggestionValue.value)) {
            // User' entered a value but he has relied on the previously suggested value, so we can set the suggestion.
            return true
        }

        return false
    }

    const isDialogVisible = shallowRef<boolean>(false)

    function onOpenDialog() {
        isDialogVisible.value = true
    }

    function onCloseDialog() {
        isDialogVisible.value = false
    }

    function onApplySuggestion() {
        userValue.value = suggestedValue.value
        isDialogVisible.value = false
    }
</script>

<style scoped>
</style>