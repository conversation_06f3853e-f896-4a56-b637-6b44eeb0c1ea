<template>
    <d-fc-input-wrapper :element="booleanElement">
        <template #unwrapped="{label, hint}">
            <d-switch-input v-model="field"
                            :hint="hint"
                            :icon="booleanElement.icon"
                            :label="label">
                <template #prepend>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                    <span v-if="DEBUG_FLOW_CONFIG"
                          class="text-caption text-green">{{ (props.booleanElement as any).booleanDefaultValue as boolean }}</span>
                </template>
            </d-switch-input>
        </template>

        <div class="pa-4">
            <d-switch-input v-model="field">
                <template #prepend>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-switch-input>
            <span v-if="DEBUG_FLOW_CONFIG"
                  class="text-caption text-green">{{ (props.booleanElement as any).booleanDefaultValue as boolean }}</span>
        </div>
    </d-fc-input-wrapper>
</template>

<script lang="ts"
        setup>
    import {computed, inject, watchEffect} from "vue";
    import {BooleanUiElement} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataBoolean} from "@/model/listing/FlowData";
    import DFcInputWrapper from "@/components/flow-config/ui-element/d-fc-input-wrapper.vue";
    import DSwitchInput from "@/adapter/vuetify/theme/components/input/d-switch-input.vue";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {LArrayIndexInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";

    const props = defineProps<{
        booleanElement: BooleanUiElement
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.booleanElement.field,
        arrayIndex: arrayIndex.value
    }));
    const field = useMutableFlowDataBoolean(
        flowData,
        fieldWithArrayIndex,
    )

    watchEffect(() => {
        if (field.value === null) {
            field.value = (props.booleanElement as any).booleanDefaultValue as boolean //Ummapping des Feldes in der GraphQL-Query
        }
    })
</script>

<style scoped>
</style>