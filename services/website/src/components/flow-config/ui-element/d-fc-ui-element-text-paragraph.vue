<template>
    <p>{{ flowConfigTranslator(textElement.text) }}</p>
</template>

<script lang="ts"
        setup>
    import {TextUiElement} from "@/adapter/graphql/generated/graphql";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";

    defineProps<{
        textElement: TextUiElement
    }>()

    const {flowConfigTranslator} = useFlowConfig()
</script>

<style scoped>
</style>