<template>
    <d-fc-input-wrapper :element="stringElement">
        <template #unwrapped="{label, hint}">
            <d-text-field v-model="field"
                          :autofocus="autofocus"
                          :hint="hint"
                          :label="label"
                          :length-max="stringElement.lengthMaximum"
                          :length-min="stringElement.lengthMinimum"
                          :persistent-hint="hint !== null"
                          :readonly="context === 'VIEW'"
                          :required="stringElement.isRequired">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-text-field>
        </template>

        <d-text-field v-model="field"
                      :autofocus="autofocus"
                      :length-max="stringElement.lengthMaximum"
                      :length-min="stringElement.lengthMinimum"
                      :readonly="context === 'VIEW'"
                      :required="stringElement.isRequired"
                      class="ma-4">
            <template #appendInner>
                <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                            :possible-enum-values="[]"
                                            :unit="null"/>
            </template>
        </d-text-field>
    </d-fc-input-wrapper>
</template>

<script lang="ts"
        setup>
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {StringUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcInputWrapper from "@/components/flow-config/ui-element/d-fc-input-wrapper.vue";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {computed, inject} from "vue";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataString} from "@/model/listing/FlowData";
    import {LArrayIndexInjection, LListingContextInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        stringElement: StringUiElement
        autofocus?: boolean
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!
    const context = inject(LListingContextInjection)!

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.stringElement.field,
        arrayIndex: arrayIndex.value
    }));

    const field = useMutableFlowDataString(flowData, fieldWithArrayIndex)
</script>

<style scoped>
</style>