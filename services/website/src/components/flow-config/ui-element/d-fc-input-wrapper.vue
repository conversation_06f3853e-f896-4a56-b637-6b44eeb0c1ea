<template>
    <template v-if="element.hintImageURL">
        <d-card :subtitle="hint"
                rounded="xl">
            <template v-if="label"
                      #title>
                <d-h6 class="cardTitle">{{ label }}</d-h6>
            </template>

            <v-img :src="element.hintImageURL"
                   max-height="300"/>

            <slot :hint="hint"
                  :label="label"/>
        </d-card>
    </template>
    <template v-else>
        <slot :hint="hint"
              :label="label"
              name="unwrapped"/>
    </template>

    <!-- TODO: icon support -->
</template>

<script lang="ts"
        setup>
    import {computed} from "vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {UiInput} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";

    const props = defineProps<{
        element: UiInput
    }>()

    const {flowConfigTranslator} = useFlowConfig()

    const label = computed<Optional<string>>(() => {
        const label = flowConfigTranslator(props.element.label)

        if (props.element.isRequired && props.element.hintImageURL !== null) {
            return `${label} *`
        }
        return label
    })
    const hint = computed<Optional<string>>(() => {
        return flowConfigTranslator(props.element.hint)
    })
</script>

<style scoped>
    .cardTitle {
        color: rgb(var(--v-theme-d-text-default));
    }
</style>