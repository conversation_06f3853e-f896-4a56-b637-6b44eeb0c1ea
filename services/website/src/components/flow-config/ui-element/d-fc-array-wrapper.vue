<template>
    <d-card :loading="loading"
            :subtitle="hint"
            rounded="xl">
        <template v-if="label"
                  #title>
            <d-h6 class="cardTitle">{{ label }}</d-h6>
        </template>

        <v-container fluid>
            <v-row v-if="element.hintImageURL"
                   class="mb-4">
                <v-col>
                    <v-img :src="element.hintImageURL"
                           max-height="300"/>
                </v-col>
            </v-row>

            <slot :array-indices="arrayIndices"
                  :array-indices-with-new-element="arrayIndicesWithNewElement"
                  :new-element-array-index="newElementArrayIndex"
                  :show-add-button="showAddButton"
                  :size="size"
                  :size-with-new-element="sizeWithNewElement"/>

            <v-row align="center"
                   justify="end">
                <v-col v-if="validationError">
                    <d-alert :text="validationError"
                             class="text-caption"
                             hide-icon
                             type="error"
                             variant="text"/>
                </v-col>
                <v-col v-if="$slots.footer"
                       class="pa-0"
                       cols="auto">
                    <slot name="footer"/>
                </v-col>
                <v-col v-if="context === 'EDIT'"
                       class="text-caption"
                       cols="auto">
                    {{
                        t('flowConfig.uiElement.arrayWrapper.size', {
                            size: n(size, 'integer'),
                            sizeMaximum: n(sizeMaximum, 'integer'),
                        })
                    }}
                </v-col>
            </v-row>
        </v-container>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {computed, inject, toRef} from "vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useValidation, VuetifyValidationRule} from "@/adapter/vuetify/theme/components/input/validation-rules";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import DH6 from "@/adapter/vuetify/theme/components/text/headline/d-h6.vue";
    import {UiElement, UiInput} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {useI18n} from "vue-i18n";
    import {LFlowDataInjection, LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        element: UiInput & UiElement
        sizeMinimum: number
        sizeMaximum: number
        loading?: boolean
    }>()

    const flowData = inject(LFlowDataInjection)!
    const context = inject(LListingContextInjection)!

    const {t, n} = useI18n()

    const {
        flowConfigTranslator,
    } = useFlowConfig()

    //TODO duplicated
    const label = computed<Optional<string>>(() => {
        const label = flowConfigTranslator(props.element.label)

        if (props.element.isRequired && props.element.hintImageURL !== null) {
            return `${label} *`
        }
        return label
    })
    const hint = computed<Optional<string>>(() => {
        return flowConfigTranslator(props.element.hint)
    })

    const arrayIndices = computed<readonly number[]>(() => flowData.value.arrayIndicesOf(props.element))
    const newElementArrayIndex = computed<number>(() => flowData.value.nextArrayIndex(props.element))
    const arrayIndicesWithNewElement = computed<readonly number[]>(() => {
        const uniqueArrayIndices = new Set<number>(arrayIndices.value)
        uniqueArrayIndices.add(newElementArrayIndex.value)
        return Array.from(uniqueArrayIndices).toSorted()
    })
    const size = computed<number>(() => arrayIndices.value.length)
    const sizeWithNewElement = computed<number>(() => arrayIndicesWithNewElement.value.length)
    const showAddButton = computed<boolean>(() => sizeWithNewElement.value <= props.sizeMaximum)

    const validationError = computed<string | undefined>(() => {
        const rules = validationRules.value

        for (const rule of rules) {
            const handler = typeof rule === 'function' ? rule : () => rule;
            const result = handler(arrayIndices.value);
            if (result === true) {
                continue;
            }
            if (result !== false && typeof result !== 'string') {
                console.warn(`${result} is not a valid value. Rule functions must return boolean true or a string.`);
                continue;
            }
            return result || ''
        }

        return undefined
    })

    const validationRules = computed<readonly VuetifyValidationRule[]>(() => [
        validationRuleArraySizeMin(props.sizeMinimum),
        validationRuleArraySizeMax(props.sizeMaximum),
    ])

    const {
        validationRuleArraySizeMin,
        validationRuleArraySizeMax,
    } = useValidation(toRef(true))
</script>

<style scoped>
    .cardTitle {
        color: rgb(var(--v-theme-d-text-default));
    }
</style>