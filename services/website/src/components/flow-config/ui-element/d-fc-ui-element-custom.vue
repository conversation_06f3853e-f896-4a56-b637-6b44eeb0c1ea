<template>
    <d-fc-ui-element-custom-scanner v-if="customElement.type === 'SCANNER'"
                                    :custom-element="customElement"/>

    <d-fc-unknown-type v-else
                       :data="customElement"
                       :supported-types="[
                           'SCANNER'
                       ]"
                       :type="customElement.type"
                       clazz="CustomUIElement"/>
</template>

<script lang="ts"
        setup>
    import {CustomUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementCustomScanner from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner.vue";

    defineProps<{
        customElement: CustomUiElement
    }>()
</script>

<style scoped>
</style>