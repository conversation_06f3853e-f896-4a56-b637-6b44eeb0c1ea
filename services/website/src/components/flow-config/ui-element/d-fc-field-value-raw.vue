<template>
    <template v-if="rawValue === null">
        <!--null-->
    </template>

    <template v-else-if="typeof rawValue === 'boolean'">
        {{ rawValue ? t('boolean.yes') : t('boolean.no') }}
    </template>

    <template v-else-if="typeof rawValue === 'string'">
        <template v-if="fieldValue.possibleEnumValues.some(e => e.value === rawValue)">
            {{ flowConfigTranslator(fieldValue.possibleEnumValues.find(e => e.value === rawValue)!.label) }}
        </template>
        <template v-else-if="matchesDateTimeString(rawValue)">
            {{ toDateOrTimeStringFromString(rawValue) }}
        </template>
        <template v-else>
            {{ rawValue }}
        </template>
    </template>

    <template v-else-if="rawValue.type === 'INTEGER'">
        <span class="text-no-wrap">{{ n(rawValue.value, 'integer') }}<span v-if="fieldValue.unit"
                                                                           class="ms-1">{{ t(`enums.listingFieldUnit.${fieldValue.unit}`) }}</span></span>
    </template>

    <template v-else-if="rawValue.type === 'DOUBLE'">
        <span class="text-no-wrap">{{ n(rawValue.value, 'decimal') }}<span v-if="fieldValue.unit"
                                                                           class="ms-1">{{ t(`enums.listingFieldUnit.${fieldValue.unit}`) }}</span></span>
    </template>

    <d-fc-unknown-type v-else
                       :data="rawValue"
                       :supported-types="[
                            'boolean',
                            'string',
                            'number.INTEGER',
                            'number.DOUBLE'
                       ]"
                       :type="rawValue.type"
                       clazz="FlowConfigField"/>
</template>

<script lang="ts"
        setup>
    import {FlowDataRawData} from "@/model/listing/FlowData";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import {useI18n} from "vue-i18n";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {matchesDateTimeString, useDateService} from "@/utility/date-converter";
    import {FlowConfigFieldValueReference} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";

    const {toDateOrTimeStringFromString} = useDateService()

    const {t, n} = useI18n()
    const {flowConfigTranslator} = useFlowConfig()

    defineProps<{
        fieldValue: FlowConfigFieldValueReference
        rawValue: Optional<FlowDataRawData>
    }>()
</script>

<style scoped>
</style>