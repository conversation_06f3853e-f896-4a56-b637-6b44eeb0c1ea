<template>
    <v-data-table
        v-model:items-per-page="itemsPerPage"
        :class="themeLayerClass"
        :headers="headers"
        :height="itemsPerPage > minItemsPerPage ? '410px' : 'auto'"
        :hide-default-footer="tableItems.length <= 8"
        :items="tableItems"
        :items-per-page-options="[minItemsPerPage, maxItemsPerPage]"
        class="border rounded-lg bg-transparent"
        density="compact">
        <!-- Slot für Index-Spalte -->
        <template v-if="tableElement.hideIndexColumn !== true"
                  #[`item.index`]="{ internalItem }">
            {{ internalItem.index + 1 }}
        </template>

        <!-- Dynamische Slots für jede Spalte -->
        <template v-for="column in columns"
                  :key="column.fieldValue.field.id"
                  #[slotNameOf(column)]="{ item }">
            <d-fc-field-array-index-injector :array-index="item.arrayIndex">
                <d-fc-field-value :field-value="column.fieldValue"/>
            </d-fc-field-array-index-injector>
        </template>
    </v-data-table>
</template>

<script lang="ts"
        setup>
    import {TableUiElement, TableUiElementColumn} from "@/adapter/graphql/generated/graphql";
    import {isUIElementVisible, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import {computed, inject, shallowRef, toRef} from "vue";
    import {useDoorbitTheme} from "@/adapter/vuetify/theme/doorbit-theme-provider";
    import DFcFieldValue from "@/components/flow-config/ui-element/d-fc-field-value.vue";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import DFcFieldArrayIndexInjector from "@/components/flow-config/ui-element/d-fc-field-array-index-injector.vue";
    import {useFlowDataValue} from "@/model/listing/FlowData";

    const minItemsPerPage = 8
    const maxItemsPerPage = 20

    const props = defineProps<{
        tableElement: TableUiElement;
    }>();

    const listing = inject(LListingInjection)!;
    const flowData = inject(LFlowDataInjection)!;
    const context = inject(LListingContextInjection)!;
    const arrayIndex = inject(LArrayIndexInjection)!;

    const itemsPerPage = shallowRef<number>(minItemsPerPage);

    const {flowConfigTranslator} = useFlowConfig();
    const {themeLayerClass} = useDoorbitTheme();
    const arrayIndices = computed<readonly number[]>(() => flowData.value.arrayIndicesOf(props.tableElement));

    type SlotName = `item.${string}`;

    function slotNameOf(column: TableUiElementColumn): SlotName {
        return `item.${column.fieldValue.field.id}`;
    }

    const columns = computed<readonly TableUiElementColumn[]>(() =>
        props.tableElement.columns.filter((c) =>
            isUIElementVisible(c, arrayIndex.value, context.value, listing.value, flowData.value)
        )
    );

    type TableHeader = {
        title: string
        key: string
        value: string
    }

    const headers = computed<readonly TableHeader[]>(() => {
        const baseHeaders = columns.value.map(column => ({
            title: flowConfigTranslator(column.header) ?? "",
            key: column.fieldValue.field.id,
            value: column.fieldValue.field.id,
        }));

        if (props.tableElement.hideIndexColumn !== true) {
            return [
                {
                    title: '#',
                    value: 'index',
                    key: 'index'
                },
                ...baseHeaders
            ];
        }

        return baseHeaders;
    });

    const tableItems = computed<readonly any[]>(() => {
        const sortedArrayIndices = [...arrayIndices.value].sort((a, b) => b - a);

        return sortedArrayIndices.map((rowArrayIndex, rowIndex) => {
            const item: Record<string, any> = {
                index: rowIndex + 1,
                arrayIndex: rowArrayIndex
            };

            for (const column of columns.value) {
                //TODO: hier brauchen wir eine hilfsfunktion, die die fertigen String-Werte von d-fc-field-value printed

                const value = useFlowDataValue(
                    flowData,
                    toRef({
                        field: column.fieldValue.field,
                        arrayIndex: rowArrayIndex
                    })
                ).value

                if (typeof value === "boolean" || typeof value === "string") {
                    item[column.fieldValue.field.id] = value
                } else {
                    item[column.fieldValue.field.id] = value?.value
                }
            }

            return item;
        });
    });
</script>

<style scoped>
    :deep(tr) {
        color: rgb(var(--v-theme-d-text-default));
    }

    .onBackground :deep(thead tr),
    .onBackground :deep(tfoot tr) {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .onBackground :deep(tbody tr:nth-child(even)) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .onBackground :deep(tbody tr:nth-child(odd)) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .onBackground :deep(.v-data-table-footer) {
        background-color: rgb(var(--v-theme-d-background-highlight));
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    .onSurface :deep(thead tr),
    .onSurface :deep(tfoot tr) {
        background-color: rgb(var(--v-theme-d-background-highlight));
    }

    .onSurface :deep(tbody tr:nth-child(even)) {
        background-color: rgb(var(--v-theme-d-background-surface));
    }

    .onSurface :deep(tbody tr:nth-child(odd)) {
        background-color: rgb(var(--v-theme-d-background-default));
    }

    .onSurface :deep(.v-data-table-footer) {
        background-color: rgb(var(--v-theme-d-background-highlight));
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }
</style>
