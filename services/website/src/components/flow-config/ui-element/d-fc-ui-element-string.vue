<template>
    <d-fc-ui-element-string-text v-if="stringElement.type === 'TEXT'"
                                 :autofocus="autofocus"
                                 :string-element="stringElement"/>

    <d-fc-ui-element-string-text-area v-else-if="stringElement.type === 'TEXT_AREA'"
                                      :autofocus="autofocus"
                                      :string-element="stringElement"/>

    <d-fc-unknown-type v-else
                       :data="stringElement"
                       :supported-types="[
                            'TEXT',
                            'TEXT_AREA'
                       ]"
                       :type="stringElement.type"
                       clazz="StringUIElement"/>
</template>

<script lang="ts"
        setup>
    import {StringUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementStringTextArea from "@/components/flow-config/ui-element/d-fc-ui-element-string-text-area.vue";
    import DFcUiElementStringText from "@/components/flow-config/ui-element/d-fc-ui-element-string-text.vue";

    defineProps<{
        stringElement: StringUiElement
        autofocus?: boolean
    }>()
</script>

<style scoped>
</style>