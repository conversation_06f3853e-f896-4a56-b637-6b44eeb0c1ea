<template>
    <d-fc-input-wrapper :element="numberElement">
        <template #unwrapped="{label, hint}">
            <!-- Ummapping des min- und max-Feldes in der GraphQL-Query -->
            <d-text-field v-model="rawField"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          :readonly="context === 'VIEW'"
                          :required="numberElement.isRequired"
                          :unit="numberElement.unit as ListingFieldUnit"
                          :value-max="(numberElement as any).numberMaximum as number"
                          :value-min="(numberElement as any).numberMinimum as number"
                          type="number">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="numberElement.unit as ListingFieldUnit"/>
                </template>
            </d-text-field>
        </template>

        <!-- Ummapping des min- und max-Feldes in der GraphQL-Query -->
        <d-text-field v-model="rawField"
                      :readonly="context === 'VIEW'"
                      :required="numberElement.isRequired"
                      :unit="numberElement.unit as ListingFieldUnit"
                      :value-max="(numberElement as any).numberMaximum as number"
                      :value-min="(numberElement as any).numberMinimum as number"
                      class="ma-4"
                      type="number">
            <template #appendInner>
                <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                            :possible-enum-values="[]"
                                            :unit="numberElement.unit as ListingFieldUnit"/>
            </template>
        </d-text-field>

        <!-- TODO: step="0.05" -->
    </d-fc-input-wrapper>
</template>

<script lang="ts"
        setup>
    import {computed, inject} from "vue";
    import {NumberUiElement} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataDouble} from "@/model/listing/FlowData";
    import DFcInputWrapper from "@/components/flow-config/ui-element/d-fc-input-wrapper.vue";
    import {ListingFieldUnit} from "@/model/listing/ListingFieldUnit";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {Optional} from "@/model/Optional";
    import {stringToDouble} from "@/utility/converter";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {LArrayIndexInjection, LListingContextInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        numberElement: NumberUiElement
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!
    const context = inject(LListingContextInjection)!

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.numberElement.field,
        arrayIndex: arrayIndex.value
    }));

    const field = useMutableFlowDataDouble(flowData, fieldWithArrayIndex)

    const rawField = computed<Optional<string>>({
        get() {
            return field.value?.toString() ?? null
        },
        set(value: Optional<string>) {
            field.value = value === null ? null : stringToDouble(value)
        }
    })
</script>

<style scoped>
</style>