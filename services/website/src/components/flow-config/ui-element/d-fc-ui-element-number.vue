<template>
    <d-fc-ui-element-number-integer v-if="numberElement.type === 'INTEGER'"
                                    :number-element="numberElement"/>

    <d-fc-ui-element-number-double v-else-if="numberElement.type === 'DOUBLE'"
                                   :number-element="numberElement"/>

    <d-fc-unknown-type v-else
                       :data="numberElement"
                       :supported-types="[
                            'INTEGER',
                            'DOUBLE'
                       ]"
                       :type="numberElement.type"
                       clazz="NumberUIElement"/>
</template>

<script lang="ts"
        setup>
    import {NumberUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementNumberInteger from "@/components/flow-config/ui-element/d-fc-ui-element-number-integer.vue";
    import DFcUiElementNumberDouble from "@/components/flow-config/ui-element/d-fc-ui-element-number-double.vue";

    defineProps<{
        numberElement: NumberUiElement
    }>()
</script>

<style scoped>
</style>