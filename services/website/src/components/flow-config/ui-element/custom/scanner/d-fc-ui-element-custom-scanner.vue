<template>
    <!-- ### DEFAULT ### -->
    <template v-if="smAndDown || !env.layout.fill">
        <v-responsive v-if="hasBuildingScan"
                      :aspect-ratio="4/3"
                      class="buildingWrapper">
            <template v-if="!isAppClipsMode">
                <d-listing-building v-if="isBuildingScanCompleted"
                                    :context="env.context"
                                    :custom-ui-element-id="customElement.id"
                                    :show-back-button="env.context === 'EDIT'"
                                    hide-controls
                                    is-embedded
                                    show-full-screen-button/>
                <d-listing-building-scan-renderer-default v-else
                                                          :key="buildingScanRenderKey"
                                                          hide-controls
                                                          is-embedded/>
            </template>

            <div class="buildingGalleryOverlay"/>

            <div class="actionArea ma-4">
                <d-btn v-if="canDeleteBuilding"
                       :disabled="isDeleteBuildingDisabled"
                       :icon="mdiDelete"
                       :loading="isDeleteLoading"
                       type="tertiary"
                       variant="elevated"
                       @click="onDelete"/>
            </div>

            <div v-if="!isBuildingScanCompleted"
                 class="actionArea bottom ma-4">
                <div v-if="canCompleteBuilding"
                     class="d-inline-block">
                    <d-fc-ui-element-custom-scanner-complete-button/>
                </div>

                <d-fc-ui-element-custom-scanner-scan-button v-if="IS_NATIVE_IOS_APP && HAS_LIDAR && canScanBuilding"
                                                            v-model:is-native-app-building-scanner-active="isNativeAppBuildingScannerActive"
                                                            :building-token="buildingToken"
                                                            :can-continue="existsScanDataOnDevice"
                                                            :custom-ui-element-id="customElement.id"
                                                            :force-continue="false"
                                                            :is-app-clips-mode="isAppClipsMode"
                                                            :is-building-scan-completed="false"/>

                <d-btn :icon="mdiFullscreen"
                       :size="env.context === 'EDIT' ? 'large' : 'default'"
                       :to="{
                          name: 'listingBuildingScan',
                          params: {
                            id: listingId,
                          }
                       }"
                       type="tertiary"
                       variant="elevated"/>
            </div>
        </v-responsive>
        <d-loading v-else-if="isListingLoading"/>
        <d-fc-ui-element-custom-scanner-intro v-else-if="env.context === 'EDIT'"
                                              v-model:is-native-app-building-scanner-active="isNativeAppBuildingScannerActive"
                                              :building-token="buildingToken"
                                              :custom-ui-element-id="customElement.id"
                                              :force-continue="existsScanDataOnDevice"
                                              :is-app-clips-mode="isAppClipsMode"
                                              class="mx-auto"/>
        <d-fc-ui-element-custom-scanner-no-scan v-else/>
    </template>

    <!-- ### FULLSCREEN ### -->
    <template v-else>
        <div v-if="hasBuildingScan"
             class="h-100 w-100 position-relative">
            <div v-if="!isAppClipsMode"
                 class="h-100 w-100 position-relative rendererWrapper">
                <d-listing-building v-if="isBuildingScanCompleted"
                                    :context="env.context"
                                    :custom-ui-element-id="customElement.id"
                                    is-embedded
                                    show-back-button
                                    show-full-screen-button/>
                <d-listing-building-scan-renderer-default v-else
                                                          :key="buildingScanRenderKey"
                                                          is-embedded
                                                          show-full-screen-button/>
            </div>

            <div v-if="canCompleteBuilding || canScanBuilding || canDeleteBuilding"
                 class="actionArea ma-4">
                <div v-if="!isBuildingScanCompleted && canCompleteBuilding"
                     class="d-inline-block">
                    <d-fc-ui-element-custom-scanner-complete-button/>
                </div>

                <d-fc-ui-element-custom-scanner-scan-button v-if="!isBuildingScanCompleted && IS_NATIVE_IOS_APP && HAS_LIDAR && canScanBuilding"
                                                            v-model:is-native-app-building-scanner-active="isNativeAppBuildingScannerActive"
                                                            :building-token="buildingToken"
                                                            :can-continue="existsScanDataOnDevice"
                                                            :custom-ui-element-id="customElement.id"
                                                            :force-continue="false"
                                                            :is-app-clips-mode="isAppClipsMode"
                                                            :is-building-scan-completed="false"/>

                <d-btn v-if="canDeleteBuilding"
                       :disabled="isDeleteBuildingDisabled"
                       :icon="mdiDelete"
                       :loading="isDeleteLoading"
                       type="tertiary"
                       variant="elevated"
                       @click="onDelete"/>
            </div>
        </div>
        <d-loading v-else-if="isListingLoading"/>
        <v-container v-else
                     class="h-100"
                     fluid>
            <v-row align="center"
                   class="h-100"
                   justify="center">
                <v-col cols="auto">
                    <d-fc-ui-element-custom-scanner-intro v-if="env.context === 'EDIT'"
                                                          v-model:is-native-app-building-scanner-active="isNativeAppBuildingScannerActive"
                                                          :building-token="buildingToken"
                                                          :custom-ui-element-id="customElement.id"
                                                          :force-continue="existsScanDataOnDevice"
                                                          :is-app-clips-mode="isAppClipsMode"/>
                    <d-fc-ui-element-custom-scanner-no-scan v-else
                                                            style="width: 400px;"/>
                </v-col>
            </v-row>
        </v-container>
    </template>

    <v-dialog v-model="isDeleteConfirmDialogVisible"
              width="500">
        <d-card :text="t('flowConfig.uiElement.custom.scanner.deleteBuildingDialog.description')"
                :title="t('flowConfig.uiElement.custom.scanner.deleteBuildingDialog.title')"
                is-in-dialog>
            <template #actions>
                <v-layout class="justify-space-between">
                    <d-btn :text="t('flowConfig.uiElement.custom.scanner.deleteBuildingDialog.cancel')"
                           type="tertiary"
                           variant="text"
                           @click="onCancelDeleteBuilding"/>
                    <d-btn :loading="isDeleteLoading"
                           :text="t('flowConfig.uiElement.custom.scanner.deleteBuildingDialog.confirm')"
                           type="error"
                           @click="onConfirmDeleteBuilding"/>
                </v-layout>
            </template>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onMounted, onUnmounted, ref, shallowRef} from "vue";
    import {Building, CustomUiElement, DFlowConfigUiElementCustomScannerLoadListingQueryVariables, DListingBuildingLoadBuildingScanDocument, Listing, ListingBuildingScanInput, useDFlowConfigUiElementCustomScannerDeleteBuildingMutation, useDFlowConfigUiElementCustomScannerDeleteBuildingScanMutation, useDFlowConfigUiElementCustomScannerLoadListingQuery, useDFlowConfigUiElementCustomScannerSaveBuildingScanMutation} from "@/adapter/graphql/generated/graphql";
    import {mdiDelete, mdiFullscreen} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {useDisplay} from "vuetify";
    import {HAS_LIDAR, IS_NATIVE_APP, IS_NATIVE_IOS_APP, NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import DFcUiElementCustomScannerIntro from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-intro.vue";
    import DFcUiElementCustomScannerNoScan from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-no-scan.vue";
    import {LListingEnvironmentInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import DListingBuilding from "@/components/listing/building/d-listing-building.vue";
    import DListingBuildingScanRendererDefault from "@/components/listing/building-scan/renderer/default/d-listing-building-scan-renderer-default.vue";
    import {Optional} from "@/model/Optional";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import {buildingScanInputToBuildingScan} from "@/adapter/graphql/mapper/buildingscaninput-to-buildingscan-mapper";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {useI18n} from "vue-i18n";
    import {AUTH_SERVICE} from "@/service/auth/AuthService";
    import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
    import {reportMessageToBff} from "@/adapter/graphql/apollo-links/MonitoringReporter";
    import DLoading from "@/components/fragment/d-loading.vue";
    import DFcUiElementCustomScannerScanButton from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-scan-button.vue";
    import DFcUiElementCustomScannerCompleteButton from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-complete-button.vue";

    const {smAndDown} = useDisplay()

    const hasBuildingScan = shallowRef<boolean>(false)
    const isBuildingScanCompleted = shallowRef<boolean>(false)
    const existsScanDataOnDevice = shallowRef<boolean>(false)
    const buildingScanRenderKey = shallowRef<number>(-1)
    const isDeleteConfirmDialogVisible = ref<boolean>(false)
    const {t} = useI18n()

    const props = withDefaults(defineProps<{
        customElement: CustomUiElement
        isAppClipsMode?: boolean
        listingBuildingToken?: string
    }>(), {
        isAppClipsMode: false,
        listingBuildingToken: undefined
    })

    const emits = defineEmits<{
        onScanDone: [boolean]
    }>();

    const listingId = inject(LListingIdInjection)!
    const env = inject(LListingEnvironmentInjection)!

    const isDeleteLoading = shallowRef<boolean>(false)
    const isNativeAppBuildingScannerActive = shallowRef<boolean>(false)

    const nativeAppMethodScanFinished = "nativeAppBuildingScanFinished"
    const nativeAppMethodExistsScanResponse = "nativeAppBuildingScanExistsResponse"

    if (IS_NATIVE_APP) {
        onMounted(() => {
            //TODO: default value für deleteBuilding entfernen, das ist nur für abwärtskompatibilität
            createNativeAppMethod(nativeAppMethodScanFinished, (buildingToken: string, buildingScan: Optional<ListingBuildingScanInput>, deleteBuilding: boolean = false) => {
                reportMessageToBff("Building Scan", `Finished scan for listing ${listingId.value}. App Clip: ${props.isAppClipsMode}, isComplete: ${buildingScan?.isComplete ?? "null"}, Building Token: ${buildingToken}, Delete Building: ${deleteBuilding}, Scan: ${JSON.stringify(buildingScan)}`)

                isNativeAppBuildingScannerActive.value = false

                //scan cancelled by iOS user, do nothing
                if (buildingScan === null) {
                    console.log("Building scan cancelled by user")
                    reportMessageToBff("Building Scan", `Cancelled scan for listing ${listingId.value}.`)

                    if (deleteBuilding) {
                        existsScanDataOnDevice.value = false
                        reportMessageToBff("Building Scan", `Delete building for listing ${listingId.value}.`)
                        triggerDelete(true).then()
                    }

                    emits("onScanDone", true)
                    return
                }

                onSaveBuildingScan(buildingToken, buildingScan)
            })

            if (props.isAppClipsMode) {
                existsScanDataOnDevice.value = false
            } else {
                createNativeAppMethod(nativeAppMethodExistsScanResponse, (givenListingId: string, exists: boolean) => {
                    if (givenListingId !== listingId.value) {
                        console.warn(`Received scan exists response for wrong listing ${givenListingId}. Expected ${listingId.value}`)
                        reportMessageToBff("Building Scan", `Received scan exists response for wrong listing ${givenListingId}. Expected ${listingId.value}`)
                        return
                    }
                    existsScanDataOnDevice.value = exists
                })

                NATIVE_APP_SERVICE?.onBuildingScanExistsRequested(listingId.value)
            }
        })

        onUnmounted(() => {
            destroyNativeAppMethod(nativeAppMethodScanFinished)

            if (!props.isAppClipsMode) {
                destroyNativeAppMethod(nativeAppMethodExistsScanResponse)
            }
        })
    }

    const loadListingQueryVariables = computed<DFlowConfigUiElementCustomScannerLoadListingQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        onResult: onListingLoaded,
        loading: isListingLoading
    } = useDFlowConfigUiElementCustomScannerLoadListingQuery(loadListingQueryVariables)

    const buildingToken = ref<Optional<string>>(props.listingBuildingToken ?? null)
    const buildingScanId = ref<Optional<string>>(null)
    const isDeleteBuildingDisabled = computed<boolean>(() => buildingScanId.value !== null && INTERNET_SPEED.value <= InternetSpeed.OFFLINE)
    const canDeleteBuilding = computed<boolean>(() => buildingScanId.value !== null && env.value.context === 'EDIT' && AUTH_SERVICE.isLoggedIn.value)
    const canScanBuilding = computed<boolean>(() => env.value.context === 'EDIT' && AUTH_SERVICE.isLoggedIn.value)
    const canCompleteBuilding = computed<boolean>(() => env.value.context === 'EDIT' && AUTH_SERVICE.isLoggedIn.value)

    onListingLoaded(result => {
        const listing = result.data?.listing as (Listing | null | undefined) ?? null
        if (listing === null) {
            return
        }

        buildingToken.value = listing.buildingToken ?? props.listingBuildingToken ?? null
        buildingScanId.value = listing.buildingScanId ?? null

        if (listing?.hasBuilding || listing?.hasBuildingScan) {
            hasBuildingScan.value = true
            isBuildingScanCompleted.value = result.data?.listing?.isBuildingScanComplete ?? false
        }
    })

    const {
        mutate: saveBuildingScan
    } = useDFlowConfigUiElementCustomScannerSaveBuildingScanMutation()

    const {
        mutate: deleteBuildingScan
    } = useDFlowConfigUiElementCustomScannerDeleteBuildingScanMutation()

    const {
        mutate: deleteBuilding
    } = useDFlowConfigUiElementCustomScannerDeleteBuildingMutation()

    async function onSaveBuildingScan(buildingToken: string, buildingScan: ListingBuildingScanInput) {
        console.log(`Saving building scan of listing ${listingId.value} …`, buildingScan)
        reportMessageToBff("Building Scan", `Saving scan of listing ${listingId.value} …`)

        try {
            const mutationResult = await saveBuildingScan({
                listingBuildingScanData: {
                    listingId: listingId.value,
                    buildingScan,
                    buildingToken,
                },
            }, {
                update: (cache, result) => {
                    console.log(`Executing update function for building scan of listing ${listingId.value} …`, buildingScan)
                    reportMessageToBff("Building Scan", `Executing update function for scan of listing ${listingId.value} …`)

                    //Wenn wir online sind, dann kommt ein Boolean als Result zurück, wenn wir offline sind, dann kommt null zurück, d.h. die folgenden Prüfungen werden übersprungen
                    // => bekommen wir "false" zurück, war höchstwarscheinlich das Token falsch, dann brechen wir hier ab, weil sonst wir sonst den Cache corrupten würden
                    if (result.data?.saveListingBuildingScan === false) {
                        console.warn(`Building scan of listing ${listingId.value} couldn't be saved (online)!`)
                        reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be saved (online)! False returned.`)
                        emits("onScanDone", false)
                        return
                    }
                    // => bekommen wir "true" zurück, dann war der Scan erfolgreich und wir können weitermachen
                    if (result.data?.saveListingBuildingScan === true) {
                        console.log(`Building scan of listing ${listingId.value} successfully saved (online).`, buildingScan)
                        reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} successfully saved (online).`)

                        if (props.isAppClipsMode) {
                            setScanAsDone(buildingScan)
                            return
                        }
                    }

                    console.log(`Writing building scan of listing ${listingId.value} to cache …`)
                    reportMessageToBff("Building Scan", `Writing scan of listing ${listingId.value} to cache …`)

                    try {
                        cache.modify({
                            id: cache.identify({
                                __typename: "Listing",
                                id: listingId.value
                            }),
                            fields: {
                                buildingScanId(): Optional<string> {
                                    return buildingScan.id
                                },
                                hasBuildingScan(): boolean {
                                    return true
                                },
                                isBuildingScanComplete(): boolean {
                                    return buildingScan.isComplete
                                },
                                hasFloorPlan(): boolean {
                                    return true
                                }
                            }
                        });

                        const listingBuildingScan = buildingScanInputToBuildingScan(buildingScan, false)
                        console.log("Writing building scan to cache", listingBuildingScan)

                        cache.writeQuery({
                            query: DListingBuildingLoadBuildingScanDocument,
                            variables: {
                                listingId: listingId.value
                            },
                            data: {
                                listingBuildingScan
                            }
                        });

                        console.log(`Building scan of listing ${listingId.value} successfully written to cache.`, buildingScan)
                        reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} successfully written to cache.`)

                        setScanAsDone(buildingScan)
                    } catch (error) {
                        console.warn(`Building scan of listing ${listingId.value} couldn't be written to cache!`, error)
                        reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be written to cache!`, JSON.stringify(error))
                        emits("onScanDone", false)
                        return
                    }
                }
            });

            if (mutationResult?.errors) {
                console.warn(`Building scan of listing ${listingId.value} couldn't be saved! (result: errors)`, mutationResult.errors)
                reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be saved! (result: errors)`, JSON.stringify(mutationResult.errors))
            } else {
                if (mutationResult?.data?.saveListingBuildingScan === false) {
                    console.warn(`Building scan of listing ${listingId.value} couldn't be saved! (result: false)`)
                    reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be saved! (result: false)`)
                } else if (mutationResult?.data?.saveListingBuildingScan === true) {
                    console.log(`Building scan of listing ${listingId.value} successfully saved! (result: true)`)
                    reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} successfully saved! (result: true)`)
                } else {
                    console.warn(`Building scan of listing ${listingId.value} couldn't be saved! (result: null)`)
                    reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be saved! (result: null)`)
                }
            }
        } catch (error) {
            console.warn(`Building scan of listing ${listingId.value} couldn't be saved! (catch)`, error)
            reportMessageToBff("Building Scan", `Scan of listing ${listingId.value} couldn't be saved! (catch)`, JSON.stringify(error))
            emits("onScanDone", false)
        }
    }

    function setScanAsDone(buildingScan: ListingBuildingScanInput) {
        hasBuildingScan.value = true
        isBuildingScanCompleted.value = buildingScan.isComplete ?? false //TODO: <<<<<<<<<<<<<< das kann nach dem nächsten app update nicht mehr nullable sein

        if (!props.isAppClipsMode) {
            NATIVE_APP_SERVICE?.onBuildingScanExistsRequested(listingId.value)
        }

        emits("onScanDone", true)
    }

    function onDelete() {
        isDeleteConfirmDialogVisible.value = true
    }

    async function onConfirmDeleteBuilding() {
        await triggerDelete()
        isDeleteConfirmDialogVisible.value = false
    }

    function onCancelDeleteBuilding() {
        isDeleteConfirmDialogVisible.value = false
    }

    async function triggerDelete(force: boolean = false) {
        isDeleteLoading.value = true
        try {
            if (!force && buildingScanId.value === null) {
                console.log("Cannot delete building because buildingScanId on listing is null")
                return
            }

            await deleteBuildingScan({
                listingId: listingId.value
            }, {
                update: cache => {
                    cache.evict({
                        id: cache.identify({
                            __typename: "ListingBuildingScan",
                            id: buildingScanId.value
                        })
                    });
                }
            });

            await deleteBuilding({
                listingId: listingId.value
            }, {
                update: cache => {
                    cache.modify({
                        id: cache.identify({
                            __typename: "Listing",
                            id: listingId.value
                        }),
                        fields: {
                            building(): Optional<EnsureDefined<Building>> {
                                return null
                            },
                            hasBuildingScan(): boolean {
                                return false
                            },
                            isBuildingScanComplete(): boolean {
                                return false
                            },
                            hasBuilding(): boolean {
                                return false
                            },
                            buildingScanId(): Optional<string> {
                                return null
                            },
                            hasFloorPlan(): boolean {
                                return false
                            }
                        }
                    });
                }
            });

            hasBuildingScan.value = false
            isBuildingScanCompleted.value = false
        } catch (error) {
            console.warn("Error while deleting building", error)
        } finally {
            isDeleteLoading.value = false
        }
    }
</script>

<style lang="scss"
       scoped>
    .buildingWrapper {
        position: relative;
        border-radius: 8px;
        max-height: 300px;
    }

    .buildingGalleryOverlay {
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
    }

    .actionArea {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: end;
        gap: 8px;
        flex-wrap: wrap;
        pointer-events: none;
    }

    .actionArea.bottom {
        top: auto;
        bottom: 0;
    }

    .actionArea > * {
        pointer-events: auto;
    }

    .imageOverlay {
        background-color: rgba(0, 0, 0, 0.75);
        color: white;
    }

    .rendererWrapper {
        cursor: move;
    }
</style>