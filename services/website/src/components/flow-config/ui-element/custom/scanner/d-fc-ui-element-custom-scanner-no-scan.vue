<template>
    <v-img class="mx-auto"
           rounded="xl"
           src="@/assets/appstore/app_clip_scan_blurred_small.webp">
        <div class="w-100 h-100 position-relative">
            <v-layout class="position-absolute h-100 w-100 align-center justify-center">
                <div class="rounded-circle d-flex align-center justify-center imageOverlay"
                     style="width: 100px; height: 100px;">
                    <v-icon :icon="mdiFloorPlan"
                            size="75px"/>
                </div>
            </v-layout>

            <v-layout class="align-end w-100 h-100 position-relative">
                <div class="w-100 text-center pa-4 text-body-1 imageOverlay position-absolute font-weight-bold">
                    {{ t('flowConfig.uiElement.custom.scanner.buildingNotScannedYet') }}
                </div>
            </v-layout>
        </div>
    </v-img>
</template>

<script lang="ts"
        setup>
    import {mdiFloorPlan} from "@mdi/js";
    import {useI18n} from "vue-i18n";

    const {t} = useI18n()
</script>

<style lang="scss"
       scoped>
    .imageOverlay {
        background-color: rgba(0, 0, 0, 0.75);
        color: white;
    }
</style>