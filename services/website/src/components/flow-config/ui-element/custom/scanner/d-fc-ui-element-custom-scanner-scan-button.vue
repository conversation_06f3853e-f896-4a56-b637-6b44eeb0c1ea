<template>
    <d-btn v-if="IS_NATIVE_IOS_APP && HAS_LIDAR"
           :disabled="buildingToken === null || (!isBuildingScanCompleted && !canContinue)"
           :loading="isNativeAppBuildingScannerActive"
           :prepend-icon="mdiCamera"
           :text="isBuildingScanCompleted && !forceContinue ? t('components.listing.edit.buildingPlan.startScannerButton') : t('components.listing.edit.buildingPlan.continueScanButton')"
           size="x-large"
           type="secondary"
           variant="elevated"
           @click="onNativeAppBuildingScannerStarted"/>
    <d-btn v-else-if="!IS_NATIVE_IOS_APP && IS_IOS && isBuildingScanCompleted"
           :disabled="iOSAppClipBuildingScannerURL === null"
           :href="iOSAppClipBuildingScannerURL"
           :prepend-icon="mdiCamera"
           :text="t('components.listing.edit.buildingPlan.startScannerButton')"
           size="x-large"
           type="secondary"
           variant="elevated"/>
</template>

<script lang="ts"
        setup>
    import {HAS_LIDAR, IS_NATIVE_IOS_APP, NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {IOS_APP_CLIP_BUILDING_SCANNER_URL, IS_IOS} from "@/utility/environment";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {computed, inject, onMounted} from "vue";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {mdiCamera} from "@mdi/js";

    const props = defineProps<{
        buildingToken: Optional<string>
        isBuildingScanCompleted: boolean
        canContinue: boolean
        customUiElementId: string
        isAppClipsMode: boolean
        forceContinue: boolean
    }>()

    const listingId = inject(LListingIdInjection)!

    const {t} = useI18n()

    const isNativeAppBuildingScannerActive = defineModel<boolean>("isNativeAppBuildingScannerActive", {
        required: true
    })

    const iOSAppClipBuildingScannerURL = computed<Optional<string>>(() => {
        const token = props.buildingToken
        if (token === null) {
            return null
        }
        return IOS_APP_CLIP_BUILDING_SCANNER_URL
            .replace("{listingId}", listingId.value)
            .replace("{customUIElementId}", props.customUiElementId)
            .replace("{token}", token)
    })

    function onNativeAppBuildingScannerStarted() {
        const listingBuildingToken = props.buildingToken
        if (listingBuildingToken === null) {
            console.warn("Building token is null")
            return
        }

        isNativeAppBuildingScannerActive.value = true;

        NATIVE_APP_SERVICE?.onBuildingScannerStartedV2({
            listingBuildingToken,
            listingId: listingId.value,
            customUIElementId: props.customUiElementId
        })
    }

    if (props.isAppClipsMode) {
        onMounted(() => {
            onNativeAppBuildingScannerStarted()
        })
    }
</script>

<style lang="scss"
       scoped>
</style>