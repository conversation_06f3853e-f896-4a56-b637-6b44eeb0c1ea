<template>
    <v-dialog v-model="isDialogVisible"
              close-on-content-click
              max-width="500">
        <d-card>
            <v-container fluid>
                <v-row>
                    <v-col cols="6"
                           order="1">
                        <v-img v-if="isLight"
                               src="@/assets/listing/building-plan/door.svg"/>
                        <v-img v-else
                               src="@/assets/listing/building-plan/door_white.svg"/>
                    </v-col>
                    <v-col class="text-caption text-center"
                           cols="6"
                           order="3">
                        {{ t('components.listing.edit.buildingPlan.intro.tutorial.steps.closeWindowsAndDoors') }}
                    </v-col>
                    <v-col cols="6"
                           order="2">
                        <v-img v-if="isLight"
                               src="@/assets/listing/building-plan/light.svg"/>
                        <v-img v-else
                               src="@/assets/listing/building-plan/light_white.svg"/>
                    </v-col>
                    <v-col class="text-caption text-center"
                           cols="6"
                           order="4">
                        {{ t('components.listing.edit.buildingPlan.intro.tutorial.steps.lighting') }}
                    </v-col>
                    <v-col cols="6"
                           order="5">
                        <v-img v-if="isLight"
                               src="@/assets/listing/building-plan/room.svg"/>
                        <v-img v-else
                               src="@/assets/listing/building-plan/room_white.svg"/>
                    </v-col>
                    <v-col class="text-caption text-center"
                           cols="6"
                           order="7">
                        {{ t('components.listing.edit.buildingPlan.intro.tutorial.steps.roomByRoom') }}
                    </v-col>
                    <v-col cols="6"
                           order="6">
                        <v-img v-if="isLight"
                               src="@/assets/listing/building-plan/smartphone.svg"/>
                        <v-img v-else
                               src="@/assets/listing/building-plan/smartphone_white.svg"/>
                    </v-col>
                    <v-col class="text-caption text-center"
                           cols="6"
                           order="8">
                        {{ t('components.listing.edit.buildingPlan.intro.tutorial.steps.cameraCover') }}
                    </v-col>
                </v-row>
                <v-row align="center"
                       class="mt-8"
                       justify="center">
                    <v-col cols="auto">
                        <d-btn :text="t('components.listing.edit.buildingPlan.intro.tutorial.closeDialogButton')"
                               size="large"
                               type="primary"
                               @click="onCloseDialog"/>
                    </v-col>
                </v-row>
            </v-container>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {useColorMode} from "@vueuse/core";
    import {computed} from "vue";

    const {t} = useI18n()

    const isDialogVisible = defineModel<boolean>('modelValue')
    const colorMode = useColorMode()
    const isLight = computed<boolean>(() => colorMode.state.value === 'light')

    function onCloseDialog() {
        isDialogVisible.value = false
    }
</script>

<style lang="scss"
       scoped>
</style>