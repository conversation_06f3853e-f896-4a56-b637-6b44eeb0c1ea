<template>
    <d-confirmation-wrapper @onconfirm="onCompleteBuildingScan">
        <template #default="{props: confirmationProps}">
            <d-btn :disabled="INTERNET_SPEED <= InternetSpeed.OFFLINE"
                   :loading="isBuildingScanLoading || isSaveBuildingScanLoading"
                   :prepend-icon="mdiCheck"
                   :text="t('components.listing.edit.buildingPlan.completeScanButton')"
                   size="small"
                   type="tertiary"
                   v-bind="confirmationProps"
                   variant="elevated"/>
        </template>
    </d-confirmation-wrapper>
</template>

<script lang="ts"
        setup>
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {computed, inject} from "vue";
    import {useI18n} from "vue-i18n";
    import {LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {mdiCheck} from "@mdi/js";
    import {deepCopyListingBuildingScanInput} from "@/adapter/graphql/mapper/buildingscaninput-to-buildingscaninput-mapper";
    import {DFlowConfigUiElementCustomScannerCompleteButtonLoadBuildingScanQueryVariables, DListingBuildingLoadBuildingScanDocument, ListingBuildingScan, useDFlowConfigUiElementCustomScannerCompleteButtonLoadBuildingScanLazyQuery, useDFlowConfigUiElementCustomScannerCompleteButtonSaveBuildingScanMutation} from "@/adapter/graphql/generated/graphql";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import {Optional} from "@/model/Optional";
    import {buildingScanInputToBuildingScan} from "@/adapter/graphql/mapper/buildingscaninput-to-buildingscan-mapper";
    import {reportMessageToBff} from "@/adapter/graphql/apollo-links/MonitoringReporter";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";

    const listingId = inject(LListingIdInjection)!

    const {t} = useI18n()

    const buildingScanQueryVariables = computed<DFlowConfigUiElementCustomScannerCompleteButtonLoadBuildingScanQueryVariables>(() => ({
        listingId: listingId.value
    }))
    const {
        load: loadBuildingScan,
        refetch: refetchBuildingScan,
        loading: isBuildingScanLoading,
    } = useDFlowConfigUiElementCustomScannerCompleteButtonLoadBuildingScanLazyQuery(buildingScanQueryVariables)

    const {
        mutate: saveBuildingScan,
        loading: isSaveBuildingScanLoading
    } = useDFlowConfigUiElementCustomScannerCompleteButtonSaveBuildingScanMutation()

    async function onCompleteBuildingScan() {
        let buildingScan: Optional<ListingBuildingScan> = null

        const loadResult = loadBuildingScan()
        if (loadResult === false) {
            const refetchResult = await refetchBuildingScan()
            buildingScan = refetchResult?.data.listingBuildingScan as (undefined | null | ListingBuildingScan) ?? null
        } else {
            buildingScan = (await loadResult)?.listingBuildingScan as (undefined | null | ListingBuildingScan) ?? null
        }

        if (buildingScan === null) {
            console.warn("Building scan not found")
            return
        }

        const newBuildingScan = deepCopyListingBuildingScanInput(buildingScan)
        newBuildingScan.isComplete = true

        console.log(`Saving building scan (completion) of listing ${listingId.value} …`, buildingScan)
        reportMessageToBff("Building Scan (completion)", `Saving scan of listing ${listingId.value} …`)

        try {
            const mutationResult = await saveBuildingScan({
                listingBuildingScanData: {
                    listingId: listingId.value,
                    buildingScan: newBuildingScan
                }
            }, {
                update: (cache, result) => {
                    console.log(`Executing update function for building scan (completion) of listing ${listingId.value} …`, buildingScan)
                    reportMessageToBff("Building Scan (completion)", `Executing update function for scan of listing ${listingId.value} …`)

                    //Wenn wir online sind, dann kommt ein Boolean als Result zurück, wenn wir offline sind, dann kommt null zurück, d.h. die folgenden Prüfungen werden übersprungen
                    // => bekommen wir "false" zurück, war höchstwarscheinlich das Token falsch, dann brechen wir hier ab, weil sonst wir sonst den Cache corrupten würden
                    if (result.data?.saveListingBuildingScan === false) {
                        console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be saved (online)!`)
                        reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be saved (online)! False returned.`)
                        return
                    }
                    // => bekommen wir "true" zurück, dann war der Scan erfolgreich und wir können weitermachen
                    if (result.data?.saveListingBuildingScan === true) {
                        console.log(`Building scan (completion) of listing ${listingId.value} successfully saved (online).`, buildingScan)
                        reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} successfully saved (online).`)
                    }

                    console.log(`Writing building scan (completion) of listing ${listingId.value} to cache …`)
                    reportMessageToBff("Building Scan (completion)", `Writing scan of listing ${listingId.value} to cache …`)

                    try {
                        cache.modify({
                            id: cache.identify({__typename: "ListingBuildingScan", id: newBuildingScan.id}),
                            fields: {
                                isComplete(): boolean {
                                    return true
                                }
                            }
                        })

                        cache.modify({
                            id: cache.identify({
                                __typename: "Listing",
                                id: listingId.value
                            }),
                            fields: {
                                isBuildingScanComplete(): boolean {
                                    return true
                                },
                            }
                        });

                        const listingBuildingScan = buildingScanInputToBuildingScan(newBuildingScan, false)
                        console.log("Writing building scan to cache (completion)", listingBuildingScan)

                        cache.writeQuery({
                            query: DListingBuildingLoadBuildingScanDocument,
                            variables: {
                                listingId: listingId.value
                            },
                            data: {
                                listingBuildingScan
                            }
                        });

                        console.log(`Building scan (completion) of listing ${listingId.value} successfully written to cache.`, buildingScan)
                        reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} successfully written to cache.`)
                    } catch (error) {
                        console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be written to cache!`, error)
                        reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be written to cache!`, JSON.stringify(error))
                    }
                }
            })

            if (mutationResult?.errors) {
                console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be saved! (result: errors)`, mutationResult.errors)
                reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be saved! (result: errors)`, JSON.stringify(mutationResult.errors))
            } else {
                if (mutationResult?.data?.saveListingBuildingScan === false) {
                    console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be saved! (result: false)`)
                    reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be saved! (result: false)`)
                } else if (mutationResult?.data?.saveListingBuildingScan === true) {
                    console.log(`Building scan (completion) of listing ${listingId.value} successfully saved! (result: true)`)
                    reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} successfully saved! (result: true)`)
                } else {
                    console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be saved! (result: null)`)
                    reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be saved! (result: null)`)
                }
            }
        } catch (error) {
            console.warn(`Building scan (completion) of listing ${listingId.value} couldn't be saved! (catch)`, error)
            reportMessageToBff("Building Scan (completion)", `Scan of listing ${listingId.value} couldn't be saved! (catch)`, JSON.stringify(error))
        }
    }
</script>

<style lang="scss"
       scoped>
</style>