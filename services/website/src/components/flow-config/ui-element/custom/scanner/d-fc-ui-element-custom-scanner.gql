mutation dFlowConfigUIElementCustomScannerDeleteBuildingScan(
    $listingId: String!
){
    deleteListingBuildingScan(
        listingId: $listingId
    )
}

mutation dFlowConfigUIElementCustomScannerDeleteBuilding(
    $listingId: String!
){
    deleteListingBuilding(
        listingId: $listingId
    )
}

mutation dFlowConfigUIElementCustomScannerSaveBuildingScan(
    $listingBuildingScanData: ListingBuildingScanDataInput!
){
    saveListingBuildingScan(
        listingBuildingScanData: $listingBuildingScanData
    )
}

query dFlowConfigUIElementCustomScannerLoadListing(
    $listingId: String!
){
    listing(
        id: $listingId
    ) {
        id
        buildingToken
        hasBuilding
        hasBuildingScan
        isBuildingScanComplete
        buildingScanId
    }
}