<template>
    <d-card :text="t('components.listing.edit.buildingPlan.intro.description')"
            :title="t('components.listing.edit.buildingPlan.intro.title')"
            class="text-center"
            style="max-width: 400px;">
        <v-container fluid>
            <v-row class="mb-8">
                <v-col>
                    <d-btn :text="t('components.listing.edit.buildingPlan.intro.tutorial.question')"
                           size="small"
                           type="tertiary"
                           variant="tonal"
                           @click="onTutorialButtonClicked"/>
                </v-col>
            </v-row>

            <!-- NO LIDAR -->
            <v-row v-if="IS_NATIVE_IOS_APP && !HAS_LIDAR"
                   justify="center">
                <v-col cols="auto">
                    <d-alert :text="t('components.listing.edit.buildingPlan.intro.noLIDAR')"
                             type="warning"/>
                </v-col>
            </v-row>

            <!-- START/CONTINUE SCAN BUTTON -->
            <v-row v-if="(IS_NATIVE_IOS_APP && HAS_LIDAR) || (!IS_NATIVE_IOS_APP && IS_IOS)"
                   justify="center">
                <v-col class="pb-8"
                       cols="auto">
                    <d-fc-ui-element-custom-scanner-scan-button v-model:is-native-app-building-scanner-active="isNativeAppBuildingScannerActive"
                                                                :building-token="buildingToken"
                                                                :can-continue="false"
                                                                :custom-ui-element-id="customUiElementId"
                                                                :force-continue="forceContinue"
                                                                :is-app-clips-mode="isAppClipsMode"
                                                                is-building-scan-completed/>
                </v-col>
            </v-row>

            <!-- QR-CODE -->
            <template v-else>
                <template v-if="INTERNET_SPEED > InternetSpeed.VERY_SLOW">
                    <v-row v-if="iOSAppClipBuildingScannerURL">
                        <v-col>
                            <v-img :src="buildServerUrl(`/bff/qr-code?text=${encodeURIComponent(iOSAppClipBuildingScannerURL)}`)"
                                   class="mx-auto"
                                   max-width="250"/>
                        </v-col>
                    </v-row>

                    <v-row v-else>
                        <v-col>
                            <d-alert :text="t('components.listing.edit.buildingPlan.intro.qrCode.error')"
                                     type="error"/>
                        </v-col>
                    </v-row>
                </template>
                <!-- TODO: fallback für langsames internet... einen auf: lasse jemand anderes scannen, QR gibts gerade nicht, teilen link geht aber -->

                <v-row v-if="iOSAppClipBuildingScannerURL"
                       class="mt-0">
                    <v-col>
                        <d-share-button :share-data="shareData"
                                        size="S"/>
                    </v-col>
                </v-row>

                <v-row>
                    <v-col class="text-caption">
                        {{ t('components.listing.edit.buildingPlan.intro.qrCode.caption') }}
                    </v-col>
                </v-row>
            </template>
        </v-container>

        <d-fc-ui-element-custom-scanner-tutorial-dialog v-model="isTutorialVisible"/>
    </d-card>
</template>

<script lang="ts"
        setup>
    import {HAS_LIDAR, IS_NATIVE_IOS_APP} from "@/service/native-app/native-app";
    import {IOS_APP_CLIP_BUILDING_SCANNER_URL, IS_IOS} from "@/utility/environment";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import {computed, inject, shallowRef, watch} from "vue";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";
    import DShareButton from "@/components/fragment/share/d-share-button.vue";
    import {urlToFile} from "@/utility/file";
    import {useAddressService} from "@/utility/address";
    import {buildServerUrl} from "@/utility/server";
    import DFcUiElementCustomScannerTutorialDialog from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-tutorial-dialog.vue";
    import {useListingAddress} from "@/service/use-listing-address";
    import {LFlowDataInjection, LListingIdInjection} from "@/components/listing/ListingInjectionKeys";
    import {DFlowConfigUiElementCustomScannerIntroLoadListingForShareQueryVariables, Listing, useDFlowConfigUiElementCustomScannerIntroLoadListingForShareQuery} from "@/adapter/graphql/generated/graphql";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
    import DFcUiElementCustomScannerScanButton from "@/components/flow-config/ui-element/custom/scanner/d-fc-ui-element-custom-scanner-scan-button.vue";

    const props = defineProps<{
        buildingToken: Optional<string>
        customUiElementId: string
        isAppClipsMode: boolean
        forceContinue: boolean
    }>()

    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LFlowDataInjection)!

    const isNativeAppBuildingScannerActive = defineModel<boolean>("isNativeAppBuildingScannerActive", {
        required: true
    })

    const {t} = useI18n()
    const {
        addressDisplayNameOf
    } = useAddressService()
    const isTutorialVisible = shallowRef<boolean>(false)

    const qrCodeURL = computed<Optional<string>>(() => {
        const url = iOSAppClipBuildingScannerURL.value
        if (url === null) {
            return null
        }
        const urlEncodedText = encodeURIComponent(url)
        return `${window.location.origin}/qr/?t=${urlEncodedText}`
    })

    const {
        address
    } = useListingAddress(flowData)

    const listingForShareQueryVariables = computed<DFlowConfigUiElementCustomScannerIntroLoadListingForShareQueryVariables>(() => ({
        id: listingId.value,
    }))
    const {
        result: listingResult,
    } = useDFlowConfigUiElementCustomScannerIntroLoadListingForShareQuery(listingForShareQueryVariables)

    const listing = computed<Optional<Listing>>(() => listingResult.value?.listing as Listing ?? null)

    const shareMessage = computed<Optional<string>>(() => {
        const message = t('components.listing.edit.buildingPlan.intro.qrCode.share.message');

        let addressLine = addressDisplayNameOf(address.value)
        if (addressLine !== null) {
            addressLine = `\n${addressLine}\n`
        }
        const addressLineOrEmpty = addressLine ?? ''

        return `${message}\n${addressLineOrEmpty}`
    })

    const shareData = computed<ShareData>(() => {
        const text = shareMessage.value

        return {
            title: t('components.listing.edit.buildingPlan.intro.qrCode.share.title'),
            text: text ?? undefined,
            url: iOSAppClipBuildingScannerURL.value ?? undefined,
            files: [...shareFiles.value]
        }
    })

    const iOSAppClipBuildingScannerURL = computed<Optional<string>>(() => {
        const token = props.buildingToken
        if (token === null) {
            return null
        }
        return IOS_APP_CLIP_BUILDING_SCANNER_URL
            .replace("{listingId}", listingId.value)
            .replace("{customUIElementId}", props.customUiElementId)
            .replace("{token}", token)
    })

    function onTutorialButtonClicked() {
        isTutorialVisible.value = true
    }

    const shareFiles = shallowRef<readonly File[]>([])
    watch([qrCodeURL, listing], parameters => {
        const [qrCodeURL, listing] = parameters
        const urls = [
            qrCodeURL,
            listing?.mainImage?.thumbnailURL
        ].filter(url => url != null)

        const filePromises = urls.map(url => urlToFile(url))
        Promise
            .all(filePromises)
            .then(files => {
                shareFiles.value = files
            })
    })
</script>

<style lang="scss"
       scoped>
</style>