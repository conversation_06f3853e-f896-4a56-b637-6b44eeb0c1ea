<template>
    <d-fc-ui-element-file-image v-if="fileElement.fileType === 'IMAGE'"
                                :file-element="fileElement"/>

    <d-fc-unknown-type v-else
                       :data="fileElement"
                       :supported-types="[
                            'IMAGE'
                       ]"
                       :type="fileElement.fileType"
                       clazz="FileUIElement"/>
</template>

<script lang="ts"
        setup>
    import {FileUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUiElementFileImage from "@/components/flow-config/ui-element/d-fc-ui-element-file-image.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";

    defineProps<{
        fileElement: FileUiElement
    }>()
</script>

<style scoped>
</style>