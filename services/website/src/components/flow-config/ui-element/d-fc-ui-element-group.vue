<template>
    <d-h3 v-if="title && groupElement.nestingDepth <= 0"
          :class="{'mt-2': !isFirstElement}"
          class="mb-2">{{ title }}
    </d-h3>
    <d-h4 v-else-if="title && groupElement.nestingDepth > 0"
          class="mb-1 mt-0">{{ title }}
    </d-h4>

    <p v-if="description"
       class="text-caption mb-4">{{ description }}</p>

    <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
    <d-fc-ui-elements :elements="groupElement.children ?? []"
                      no-margin/>

    <!-- TODO: icon, image, collapsible support -->
</template>

<script lang="ts"
        setup>
    import {computed} from "vue";
    import {GroupUiElement} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DH3 from "@/adapter/vuetify/theme/components/text/headline/d-h3.vue";
    import DFcUiElements from "@/components/flow-config/d-fc-ui-elements.vue";
    import DH4 from "@/adapter/vuetify/theme/components/text/headline/d-h4.vue";

    const props = defineProps<{
        groupElement: GroupUiElement
        isFirstElement: boolean
        isLastElement: boolean
    }>()

    const {flowConfigTranslator} = useFlowConfig()

    const title = computed<Optional<string>>(() => flowConfigTranslator(props.groupElement.title))
    const description = computed<Optional<string>>(() => flowConfigTranslator(props.groupElement.description))
</script>

<style scoped>
</style>