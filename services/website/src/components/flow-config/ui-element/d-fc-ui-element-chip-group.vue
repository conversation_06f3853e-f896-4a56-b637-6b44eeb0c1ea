<template>
    <d-h3 v-if="title"
          :class="{'mb-2': description !== null, 'mb-4': description === null, 'mt-8': !isFirstElement}">{{ title }}
    </d-h3>

    <p v-if="description"
       class="text-caption mb-4">{{ description }}</p>

    <v-layout class="chips flex-wrap">
        <v-fade-transition v-for="(chip, chipIndex) in chipGroupElement.chips"
                           :key="chipIndex">
            <d-fc-ui-element-chip-group-chip v-if="DEBUG_FLOW_CONFIG || isUIElementVisible(chip, arrayIndex, context, listing, flowData)"
                                             :boolean-element="chip"/>
        </v-fade-transition>
    </v-layout>
</template>

<script lang="ts"
        setup>
    import {computed, inject} from "vue";
    import {ChipGroupUiElement} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {isUIElementVisible, useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DH3 from "@/adapter/vuetify/theme/components/text/headline/d-h3.vue";
    import DFcUiElementChipGroupChip from "@/components/flow-config/ui-element/d-fc-ui-element-chip-group-chip.vue";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";

    const props = defineProps<{
        chipGroupElement: ChipGroupUiElement
        isFirstElement: boolean
        isLastElement: boolean
    }>()

    const listing = inject(LListingInjection)!
    const flowData = inject(LFlowDataInjection)!
    const context = inject(LListingContextInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const {flowConfigTranslator} = useFlowConfig()

    const title = computed<Optional<string>>(() => flowConfigTranslator(props.chipGroupElement.title))
    const description = computed<Optional<string>>(() => flowConfigTranslator(props.chipGroupElement.description))
</script>

<style scoped>
    .chips {
        row-gap: 8px;
        column-gap: 8px;
    }
</style>