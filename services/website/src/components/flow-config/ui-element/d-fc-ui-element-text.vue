<template>
    <d-fc-ui-element-text-heading v-if="textElement.type === 'HEADING'"
                                  :is-first-element="isFirstElement"
                                  :text-element="textElement"/>

    <d-fc-ui-element-text-paragraph v-else-if="textElement.type === 'PARAGRAPH'"
                                    :text-element="textElement"/>

    <d-fc-unknown-type v-else
                       :data="textElement"
                       :supported-types="[
                           'HEADING',
                           'PARAGRAPH'
                       ]"
                       :type="textElement.type"
                       clazz="TextUIElement"/>
</template>

<script lang="ts"
        setup>
    import {TextUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUiElementTextHeading from "@/components/flow-config/ui-element/d-fc-ui-element-text-heading.vue";
    import DFcUiElementTextParagraph from "@/components/flow-config/ui-element/d-fc-ui-element-text-paragraph.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";

    defineProps<{
        textElement: TextUiElement
        isFirstElement: boolean
    }>()
</script>

<style scoped>
</style>