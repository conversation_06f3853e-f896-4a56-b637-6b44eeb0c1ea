<template>
    <d-fc-field-value-raw :field-value="fieldValue"
                          :raw-value="rawValue"/>
</template>

<script lang="ts"
        setup>
    import {FlowConfigFieldValueReference} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, useFlowDataValue} from "@/model/listing/FlowData";
    import {computed, inject} from "vue";
    import DFcFieldValueRaw from "@/components/flow-config/ui-element/d-fc-field-value-raw.vue";
    import {LArrayIndexInjection, LFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        fieldValue: FlowConfigFieldValueReference
    }>()

    const flowData = inject(LFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const rawValue = useFlowDataValue(
        flowData,
        computed<FlowConfigFieldWithArrayIndex>(() => ({
            field: props.fieldValue.field,
            arrayIndex: arrayIndex.value
        }))
    )
</script>

<style scoped>
</style>