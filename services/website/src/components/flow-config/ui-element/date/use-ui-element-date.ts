import {computed, Ref, shallowRef} from "vue";
import {Optional} from "@/model/Optional";
import {stringToInt} from "@/utility/converter";
import dayjs, {Dayjs} from "dayjs";
import {DATE_TIME_DISTANCES_REGEX, forcedUTCDateFromYMDT} from "@/utility/date-converter";

export type UIElementDateProvider = {
    year: Ref<Optional<number>>,
    month: Ref<Optional<number>>,
    day: Ref<Optional<number>>
    hour: Ref<Optional<number>>,
    minute: Ref<Optional<number>>,
    dayjsDate: Readonly<Ref<Optional<Dayjs>>>
    destroyUIElementDate: () => void
}

/**
 {year}.{month}.{day} {hour}:{minute}" //####.##.## ##:##
 */
export function useUIElementDate(
    timestampField: Ref<Optional<string>>,
    allowDistancesFromToday: boolean = false
): UIElementDateProvider {
    const now = shallowRef<Date>(new Date());

    let intervalId: Optional<number> = null
    if (allowDistancesFromToday) {
        intervalId = setInterval(() => {
            now.value = new Date();
        }, 1000);
    }

    const destroy = () => {
        if (intervalId !== null) {
            clearInterval(intervalId);
        }
    }

    // noinspection NestedFunctionJS
    function timestampToTimeUnit(timeUnitIdentifier: string, timeUnitCalculator: (date: Date) => number): Optional<number> {
        const timestamp = timestampField.value;

        if (timestamp === null || !DATE_TIME_DISTANCES_REGEX.test(timestamp) || !timestamp.endsWith(timeUnitIdentifier)) {
            return null
        }

        let timeUnit = timeUnitCalculator(now.value)

        const operator = timestamp.charAt(0);
        const value = stringToInt(timestamp.substring(1, timestamp.length - 1))!;

        if (operator === "+") {
            timeUnit += value;
        } else if (operator === "-") {
            timeUnit -= value;
        }

        return timeUnit
    }

    const nowYear = computed<Optional<number>>(() => timestampToTimeUnit("Y", date => date.getFullYear()));
    const nowMonth = computed<Optional<number>>(() => timestampToTimeUnit("M", date => date.getMonth() + 1));
    const nowDay = computed<Optional<number>>(() => timestampToTimeUnit("D", date => date.getDate()));
    const nowHour = computed<Optional<number>>(() => timestampToTimeUnit("H", date => date.getHours()));
    const nowMinute = computed<Optional<number>>(() => timestampToTimeUnit("m", date => date.getMinutes()));

    const year = computed<Optional<number>>({
        get() {
            const timestamp = timestampField.value;
            if (timestamp === null) {
                return null;
            }
            return stringToInt(timestamp.substring(0, 4));
        },
        set(value) {
            const valueString = value === null ? "####" : Math.max(0, Math.min(3000, value)).toString().padStart(4, "0");
            const timestamp = timestampField.value;
            if (timestamp === null) {
                timestampField.value = `${valueString}.##.## ##:##`;
            } else {
                timestampField.value = `${valueString}${timestamp.substring(4)}`;
            }
        }
    });

    const month = computed<Optional<number>>({
        get() {
            const timestamp = timestampField.value;
            if (timestamp === null) {
                return null;
            }
            return stringToInt(timestamp.substring(5, 7));
        },
        set(value) {
            const valueString = value === null ? "##" : Math.max(1, Math.min(12, value)).toString().padStart(2, "0");
            const timestamp = timestampField.value;
            if (timestamp === null) {
                timestampField.value = `####.${valueString}.## ##:##`;
            } else {
                timestampField.value = `${timestamp.substring(0, 5)}${valueString}${timestamp.substring(7)}`;
            }
        }
    });

    const day = computed<Optional<number>>({
        get() {
            const timestamp = timestampField.value;
            if (timestamp === null) {
                return null;
            }
            return stringToInt(timestamp.substring(8, 10));
        },
        set(value) {
            const valueString = value === null ? "##" : Math.max(1, Math.min(31, value)).toString().padStart(2, "0");
            const timestamp = timestampField.value;
            if (timestamp === null) {
                timestampField.value = `####.##.${valueString} ##:##`;
            } else {
                timestampField.value = `${timestamp.substring(0, 8)}${valueString}${timestamp.substring(10)}`;
            }
        }
    });

    const hour = computed<Optional<number>>({
        get() {
            const timestamp = timestampField.value;
            if (timestamp === null) {
                return null;
            }
            return stringToInt(timestamp.substring(11, 13));
        },
        set(value) {
            const valueString = value === null ? "##" : Math.max(0, Math.min(23, value)).toString().padStart(2, "0");
            const timestamp = timestampField.value;
            if (timestamp === null) {
                timestampField.value = `####.##.## ${valueString}:##`;
            } else {
                timestampField.value = `${timestamp.substring(0, 11)}${valueString}${timestamp.substring(13)}`;
            }
        }
    });

    const minute = computed<Optional<number>>({
        get() {
            const timestamp = timestampField.value;
            if (timestamp === null) {
                return null;
            }
            return stringToInt(timestamp.substring(14, 16));
        },
        set(value) {
            const valueString = value === null ? "##" : Math.max(0, Math.min(59, value)).toString().padStart(2, "0");
            const timestamp = timestampField.value;
            if (timestamp === null) {
                timestampField.value = `####.##.## ##:${valueString}`;
            } else {
                timestampField.value = `${timestamp.substring(0, 14)}${valueString}`;
            }
        }
    });

    const dayjsDate = computed<Optional<Dayjs>>(() => {
        const nYear = nowYear.value
        const nMonth = nowMonth.value
        const nDay = nowDay.value
        const nHour = nowHour.value
        const nMinute = nowMinute.value
        if (
            nYear !== null ||
            nMonth !== null ||
            nDay !== null ||
            nHour !== null ||
            nMinute !== null
        ) {
            let dayjsDate = dayjs(now.value)
            if (nYear !== null) {
                dayjsDate = dayjsDate.year(nYear)
            }
            if (nMonth !== null) {
                dayjsDate = dayjsDate.month(nMonth - 1)
            }
            if (nDay !== null) {
                dayjsDate = dayjsDate.date(nDay)
            }
            if (nHour !== null) {
                dayjsDate = dayjsDate.hour(nHour)
            }
            if (nMinute !== null) {
                dayjsDate = dayjsDate.minute(nMinute)
            }
            return dayjsDate
        }

        return forcedUTCDateFromYMDT(
            year.value,
            month.value,
            day.value,
            hour.value,
            minute.value
        )
    });

    return {
        year,
        month,
        day,
        hour,
        minute,
        dayjsDate,
        destroyUIElementDate: destroy
    }
}