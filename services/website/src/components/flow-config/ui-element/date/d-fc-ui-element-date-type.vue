<template>
    <!--    <pre>Ergebnis: {{ timestampField }}</pre>-->

    <!--    <d-text-field v-model="rawYear"-->
    <!--                  label="Year"-->
    <!--                  type="number"/>-->

    <!--    <d-text-field v-model="rawMonth"-->
    <!--                  label="Month"-->
    <!--                  type="number"/>-->

    <!--    <d-text-field v-model="rawDay"-->
    <!--                  label="Day"-->
    <!--                  type="number"/>-->

    <!--    <d-text-field v-model="rawHour"-->
    <!--                  label="Hour"-->
    <!--                  type="number"/>-->

    <!--    <d-text-field v-model="rawMinute"-->
    <!--                  label="Minute"-->
    <!--                  type="number"/>-->

    <d-fc-ui-element-date-type-y v-if="dateElement.type === 'Y'"
                                 v-model:year="year"
                                 :field-with-array-index="fieldWithArrayIndex"
                                 :hint="hint"
                                 :label="label"
                                 :maximum="maximum"
                                 :minimum="minimum"/>

    <d-fc-ui-element-date-type-ym v-else-if="dateElement.type === 'YM'"
                                  v-model:month="month"
                                  v-model:year="year"
                                  :field-with-array-index="fieldWithArrayIndex"
                                  :hint="hint"
                                  :label="label"
                                  :maximum="maximum"
                                  :minimum="minimum"/>

    <d-fc-ui-element-date-type-ymd v-else-if="dateElement.type === 'YMD'"
                                   v-model:day="day"
                                   v-model:month="month"
                                   v-model:year="year"
                                   :field-with-array-index="fieldWithArrayIndex"
                                   :hint="hint"
                                   :label="label"
                                   :maximum="maximum"
                                   :minimum="minimum"/>

    <d-fc-ui-element-date-type-ymdt v-else-if="dateElement.type === 'YMDT'"
                                    v-model:day="day"
                                    v-model:hour="hour"
                                    v-model:minute="minute"
                                    v-model:month="month"
                                    v-model:year="year"
                                    :field-with-array-index="fieldWithArrayIndex"
                                    :hint="hint"
                                    :label="label"
                                    :maximum="maximum"
                                    :minimum="minimum"/>

    <!--    <d-fc-ui-element-date-type-t v-else-if="dateElement.type === 'T'"-->
    <!--                                 v-model:hour="hour"-->
    <!--                                 v-model:minute="minute"-->
    <!--                                 :hint="hint"-->
    <!--                                 :label="label"/>-->

    <d-fc-unknown-type v-else
                       :data="dateElement"
                       :supported-types="[
                            'Y',
                            'YM',
                            'YMD',
                            'YMDT',
                            'T'
                       ]"
                       :type="dateElement.type"
                       clazz="DateUIElement"/>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onUnmounted} from "vue";
    import {DateUiElement} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataString} from "@/model/listing/FlowData";
    import {Optional} from "@/model/Optional";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import {useUIElementDate} from "@/components/flow-config/ui-element/date/use-ui-element-date";
    import DFcUiElementDateTypeY from "@/components/flow-config/ui-element/date/d-fc-ui-element-date-type-y.vue";
    import DFcUiElementDateTypeYmd from "@/components/flow-config/ui-element/date/d-fc-ui-element-date-type-ymd.vue";
    import DFcUiElementDateTypeYmdt from "@/components/flow-config/ui-element/date/d-fc-ui-element-date-type-ymdt.vue";
    import DFcUiElementDateTypeYm from "@/components/flow-config/ui-element/date/d-fc-ui-element-date-type-ym.vue";
    import {LArrayIndexInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        dateElement: DateUiElement
        label: Optional<string>
        hint: Optional<string>
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.dateElement.field,
        arrayIndex: arrayIndex.value
    }));
    const timestampField = useMutableFlowDataString(flowData, fieldWithArrayIndex)

    const {
        year,
        month,
        day,
        hour,
        minute,
        destroyUIElementDate: destroyUIElementDate1,
    } = useUIElementDate(timestampField)


    const minimumRaw = computed<Optional<string>>(() => (props.dateElement as any).dateMinimum as (string | undefined | null) ?? null)
    const maximumRaw = computed<Optional<string>>(() => (props.dateElement as any).dateMaximum as (string | undefined | null) ?? null)

    const {
        dayjsDate: minimum,
        destroyUIElementDate: destroyUIElementDate2,
    } = useUIElementDate(minimumRaw, true)

    const {
        dayjsDate: maximum,
        destroyUIElementDate: destroyUIElementDate3,
    } = useUIElementDate(maximumRaw, true)

    onUnmounted(() => {
        destroyUIElementDate1()
        destroyUIElementDate2()
        destroyUIElementDate3()
    })
    // const rawYear = computed<Optional<string>>({
    //     get() {
    //         return year.value?.toString() ?? null
    //     },
    //     set(value: Optional<string>) {
    //         year.value = value === null ? null : stringToInt(value)
    //     }
    // })
    // const rawMonth = computed<Optional<string>>({
    //     get() {
    //         return month.value?.toString() ?? null
    //     },
    //     set(value: Optional<string>) {
    //         month.value = value === null ? null : stringToInt(value)
    //     }
    // })
    // const rawDay = computed<Optional<string>>({
    //     get() {
    //         return day.value?.toString() ?? null
    //     },
    //     set(value: Optional<string>) {
    //         day.value = value === null ? null : stringToInt(value)
    //     }
    // })
    // const rawHour = computed<Optional<string>>({
    //     get() {
    //         return hour.value?.toString() ?? null
    //     },
    //     set(value: Optional<string>) {
    //         hour.value = value === null ? null : stringToInt(value)
    //     }
    // })
    // const rawMinute = computed<Optional<string>>({
    //     get() {
    //         return minute.value?.toString() ?? null
    //     },
    //     set(value: Optional<string>) {
    //         minute.value = value === null ? null : stringToInt(value)
    //     }
    // })
</script>

<style scoped>
</style>