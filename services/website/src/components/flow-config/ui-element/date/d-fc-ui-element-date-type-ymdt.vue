<template>
    <v-menu v-model="isMenuVisible"
            :close-on-content-click="false"
            :disabled="context === 'VIEW'"
            min-width="290px"
            open-on-click
            transition="scale-transition">
        <template #activator="{props: menuProps}">
            <d-text-field v-model="rawData"
                          :clearable="context === 'EDIT'"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          hide-readonly
                          readonly
                          v-bind="menuProps"
                          @click:clear="onClear">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-text-field>
        </template>

        <d-card class="pa-2"
                rounded="xl"
                variant="flat">
            <v-date-picker v-model="date"
                           :max="maximum ?? undefined"
                           :min="minimum ?? undefined"
                           hide-header
                           show-adjacent-months/>

            <d-select v-model="timeString"
                      :display-name-supplier="time => time"
                      :id-supplier="time => time"
                      :items="timeSuggestions"
                      :label="t('flowConfig.uiElement.date.ymdt.time')"/>
        </d-card>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {Optional} from "@/model/Optional";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {computed, inject, shallowRef, watch} from "vue";
    import {Dayjs} from "dayjs";
    import {useDateService, utcDateFromYMD} from "@/utility/date-converter";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import {VDatePicker, VMenu} from 'vuetify/components';
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import {stringToInt} from "@/utility/converter";
    import {useI18n} from "vue-i18n";
    import {LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        label: Optional<string>
        hint: Optional<string>
        minimum: Optional<Dayjs>
        maximum: Optional<Dayjs>
    }>()

    const context = inject(LListingContextInjection)!
    const {t} = useI18n()

    const year = defineModel<Optional<number>>("year", {
        required: true
    })
    const month = defineModel<Optional<number>>("month", {
        required: true
    })
    const day = defineModel<Optional<number>>("day", {
        required: true
    })
    const hour = defineModel<Optional<number>>("hour", {
        required: true
    })
    const minute = defineModel<Optional<number>>("minute", {
        required: true
    })

    const rawData = computed<string | undefined>({
        get() {
            const value = dateTime.value
            if (value === undefined) {
                return undefined
            }
            return toDateOrTimeStringFromDatejs(value, true, true)
        },
        set(value: string | undefined) {
            dateTime.value = undefined
        }
    })

    const date = computed<Dayjs | undefined>({
        get() {
            return utcDateFromYMD(year.value, month.value, day.value) ?? undefined
        },
        set(value: Dayjs | undefined) {
            if (value === undefined) {
                year.value = null
                month.value = null
                day.value = null
                return
            }
            year.value = value.year()
            month.value = value.month() + 1
            day.value = value.date()
        }
    })

    const dateTime = computed<Dayjs | undefined>({
        get() {
            return date.value ? date.value.hour(hour.value ?? 0).minute(minute.value ?? 0) : undefined
        },
        set(value: Dayjs | undefined) {
            if (value === undefined) {
                year.value = null
                month.value = null
                day.value = null
                hour.value = null
                minute.value = null
                return
            }
            year.value = value.year()
            month.value = value.month() + 1
            day.value = value.date()
            hour.value = value.hour()
            minute.value = value.minute()
        }
    })

    const isMenuVisible = shallowRef<boolean>(false)

    const timeString = computed<Optional<string>>({
        get() {
            if (hour.value === null || minute.value === null) {
                return ''
            }
            return `${hour.value.toString().padStart(2, '0')}:${minute.value.toString().padStart(2, '0')}`
        },
        set(value: Optional<string>) {
            if (value === null) {
                hour.value = null
                minute.value = null
                return
            }
            const [h, m] = value.split(':').map(stringToInt)
            hour.value = h
            minute.value = m
        }
    })

    // 06:00 - 23:00
    const timeSuggestions = Array.from({length: 35}, (_, i) => {
        const h = Math.floor(i / 2) + 6
        const m = i % 2 === 0 ? '00' : '30'
        return `${h.toString().padStart(2, '0')}:${m}`
    })

    function onClear() {
        isMenuVisible.value = false
        rawData.value = undefined
    }

    watch([date, timeString], () => {
        if (year.value !== null && month.value !== null && day.value !== null && hour.value !== null && minute.value !== null) {
            isMenuVisible.value = false;
        }
    })

    const {toDateOrTimeStringFromDatejs} = useDateService()
</script>

<style scoped>
</style>
