<template>
    <v-menu v-model="isMenuVisible"
            :close-on-content-click="false"
            :disabled="context === 'VIEW'"
            min-width="1px"
            open-on-click
            transition="scale-transition"
            @click="onClick">
        <template #activator="{props: menuProps}">
            <d-text-field v-model="rawData"
                          :clearable="context === 'EDIT'"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          hide-readonly
                          readonly
                          v-bind="menuProps"
                          @click:clear="onClear">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-text-field>
        </template>

        <d-card rounded="xl"
                variant="flat">
            <v-date-picker-years v-if="selectionMode === 'YEAR'"
                                 v-model="rawYear"
                                 :max="maximum ?? undefined"
                                 :min="minimum ?? undefined"/>
            <v-date-picker-months v-else-if="selectionMode === 'MONTH'"
                                  v-model="rawMonth"
                                  :max="maximum ?? undefined"
                                  :min="minimum ?? undefined"
                                  :year="rawYear"/>
        </d-card>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {computed, inject, shallowRef, watch} from "vue";
    import {Optional} from "@/model/Optional";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {Dayjs} from "dayjs";
    import {useDateService, utcDateFromYM} from "@/utility/date-converter";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import {LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        label: Optional<string>
        hint: Optional<string>
        minimum: Optional<Dayjs>
        maximum: Optional<Dayjs>
    }>()

    const context = inject(LListingContextInjection)!

    const year = defineModel<Optional<number>>("year", {
        required: true
    })
    const month = defineModel<Optional<number>>("month", {
        required: true
    })

    const rawYear = computed<number | undefined>({
        get() {
            return year.value ?? undefined
        },
        set(value: number | undefined) {
            year.value = value ?? null
        }
    })
    const rawMonth = computed<number | undefined>({
        get() {
            const m = month.value
            return m === null ? undefined : (m - 1)
        },
        set(value: number | undefined) {
            month.value = value === undefined ? null : (value + 1)
        }
    })
    const rawData = computed<string | undefined>({
        get() {
            const value = date.value
            if (value === undefined) {
                return undefined
            }
            return toDateOrTimeStringFromDatejs(value, false, false)
        },
        set(value: string | undefined) {
            date.value = undefined
        }
    })

    const date = computed<Dayjs | undefined>({
        get() {
            return utcDateFromYM(year.value, month.value) ?? undefined
        },
        set(value: Dayjs | undefined) {
            if (value === undefined) {
                year.value = null
                month.value = null
                return
            }
            year.value = value.year()
            month.value = value.month() + 1
        }
    })

    const isMenuVisible = shallowRef<boolean>(false)
    const selectionMode = shallowRef<false | 'YEAR' | 'MONTH'>(false)

    watch(selectionMode, selectionMode => {
        isMenuVisible.value = selectionMode !== false
    })

    watch(isMenuVisible, isMenuVisible => {
        if (isMenuVisible) {
            selectionMode.value = 'YEAR'
        }
    })

    function onClear() {
        isMenuVisible.value = false
        rawYear.value = undefined
    }

    function onClick() {
        if (selectionMode.value === 'YEAR') {
            selectionMode.value = 'MONTH'
        } else if (selectionMode.value === 'MONTH') {
            selectionMode.value = false
        }
    }

    const {toDateOrTimeStringFromDatejs} = useDateService()
</script>

<style scoped>
</style>