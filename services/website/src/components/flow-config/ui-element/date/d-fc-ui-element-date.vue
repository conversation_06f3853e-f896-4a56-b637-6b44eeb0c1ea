<template>
    <d-fc-input-wrapper :element="dateElement">
        <template #unwrapped="{label, hint}">
            <d-fc-ui-element-date-type :date-element="dateElement"
                                       :hint="hint"
                                       :label="label"/>
        </template>

        <div class="ma-4">
            <d-fc-ui-element-date-type :date-element="dateElement"
                                       :hint="null"
                                       :label="null"/>
        </div>
    </d-fc-input-wrapper>
</template>

<script lang="ts"
        setup>
    import {DateUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcInputWrapper from "@/components/flow-config/ui-element/d-fc-input-wrapper.vue";
    import DFcUiElementDateType from "@/components/flow-config/ui-element/date/d-fc-ui-element-date-type.vue";

    defineProps<{
        dateElement: DateUiElement
    }>()
</script>

<style scoped>
</style>