<template>
    T

    <!--<d-fc-input-wrapper :element="dateElement">
        <template #unwrapped="{label, hint}">
            <d-date-input v-model="rawField"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          :required="dateElement.isRequired"/>
        </template>

        <d-date-input v-model="rawField"
                      :required="dateElement.isRequired"
                      class="ma-4"/>
    </d-fc-input-wrapper>-->

    <!-- TODO: date anpassen -->

    <!--    field: FlowConfigField! #implizit string feld, format: "YYYY.MM.DD HH:mm", leere Werte werden mit '#' aufgefüllt, die Zeit wird im 24 Stundenformat angegeben, z.B. "2020.##.## ##:##" oder "####.##.## 14:30"-->
    <!--    type: String! #Y, YM, YMD, YMDT, T => <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>, <PERSON>ahr-<PERSON><PERSON>-<PERSON>-<PERSON>eit, Zeit-->

    <!--
    default
    <br>
    <v-date-picker/>
    <br>

    month
    <br>
    <v-date-picker view-mode="month"/>
    <br>

    months
    <br>
    <v-date-picker view-mode="months"/>
    <br>

    year
    <br>
    <v-date-picker view-mode="year"/>
    <br>
    -->
</template>

<script lang="ts"
        setup>
    import {Optional} from "@/model/Optional";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import {Dayjs} from "dayjs";

    defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        label: Optional<string>
        hint: Optional<string>
        minimum: Optional<Dayjs>
        maximum: Optional<Dayjs>
    }>()

    const hour = defineModel<Optional<number>>("hour", {
        required: true
    })
    const minute = defineModel<Optional<number>>("minute", {
        required: true
    })

    // const rawField = computed<Optional<Dayjs>>({
    //     get() {
    //         const value = field.value
    //         return value === null ? null : utcDate(value)
    //     },
    //     set(value: Optional<Dayjs>) {
    //         field.value = value === null ? null : value.format("YYYY-MM-DD")
    //     }
    // })
</script>

<style scoped>
</style>