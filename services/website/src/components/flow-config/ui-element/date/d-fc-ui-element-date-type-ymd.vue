<template>
    <v-menu v-model="isMenuVisible"
            :close-on-content-click="false"
            :disabled="context === 'VIEW'"
            min-width="1px"
            open-on-click
            transition="scale-transition">
        <template #activator="{props: menuProps}">
            <d-text-field v-model="rawData"
                          :clearable="context === 'EDIT'"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          hide-readonly
                          readonly
                          v-bind="menuProps"
                          @click:clear="onClear">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-text-field>
        </template>

        <v-date-picker v-model="date"
                       :max="maximum ?? undefined"
                       :min="minimum ?? undefined"
                       hide-header
                       rounded="xl"
                       show-adjacent-months
                       @update:model-value="onChange"/>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {Optional} from "@/model/Optional";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import {computed, inject, shallowRef} from "vue";
    import {Dayjs} from "dayjs";
    import {useDateService, utcDateFromYMD} from "@/utility/date-converter";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import {LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        label: Optional<string>
        hint: Optional<string>
        minimum: Optional<Dayjs>
        maximum: Optional<Dayjs>
    }>()

    const context = inject(LListingContextInjection)!

    const year = defineModel<Optional<number>>("year", {
        required: true
    })
    const month = defineModel<Optional<number>>("month", {
        required: true
    })
    const day = defineModel<Optional<number>>("day", {
        required: true
    })

    const rawData = computed<string | undefined>({
        get() {
            const value = date.value
            if (value === undefined) {
                return undefined
            }
            return toDateOrTimeStringFromDatejs(value, true, false)
        },
        set(value: string | undefined) {
            date.value = undefined
        }
    })

    const date = computed<Dayjs | undefined>({
        get() {
            return utcDateFromYMD(year.value, month.value, day.value) ?? undefined
        },
        set(value: Dayjs | undefined) {
            if (value === undefined) {
                year.value = null
                month.value = null
                day.value = null
                return
            }
            year.value = value.year()
            month.value = value.month() + 1
            day.value = value.date()
        }
    })

    const isMenuVisible = shallowRef<boolean>(false)

    function onClear() {
        isMenuVisible.value = false
        rawData.value = undefined
    }

    function onChange() {
        isMenuVisible.value = false
    }

    const {toDateOrTimeStringFromDatejs} = useDateService()
</script>

<style scoped>
</style>