<template>
    <v-menu v-model="isMenuVisible"
            :disabled="context === 'VIEW'"
            open-on-click>
        <template #activator="{props: menuProps}">
            <d-text-field v-model="rawYear"
                          :clearable="context === 'EDIT'"
                          :hint="hint"
                          :label="label"
                          :persistent-hint="hint !== null"
                          :readonly="context === 'VIEW'"
                          hide-readonly
                          type="number"
                          v-bind="menuProps"
                          @click:clear="onClear">
                <template #appendInner>
                    <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                                :possible-enum-values="[]"
                                                :unit="null"/>
                </template>
            </d-text-field>
        </template>

        <d-card rounded="xl"
                variant="flat">
            <v-date-picker-years v-model="rawYear"
                                 :max="maximum ?? undefined"
                                 :min="minimum ?? undefined"/>
        </d-card>
    </v-menu>
</template>

<script lang="ts"
        setup>
    import {computed, inject, shallowRef} from "vue";
    import {Optional} from "@/model/Optional";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import {Dayjs} from "dayjs";
    import {LListingContextInjection} from "@/components/listing/ListingInjectionKeys";

    defineProps<{
        fieldWithArrayIndex: FlowConfigFieldWithArrayIndex
        label: Optional<string>
        hint: Optional<string>
        minimum: Optional<Dayjs>
        maximum: Optional<Dayjs>
    }>()

    const context = inject(LListingContextInjection)!

    const year = defineModel<Optional<number>>("year", {
        required: true
    })

    const rawYear = computed<number | undefined>({
        get() {
            return year.value ?? undefined
        },
        set(value: number | undefined) {
            year.value = value ?? null
        }
    })

    const isMenuVisible = shallowRef<boolean>(false)

    function onClear() {
        isMenuVisible.value = false
        rawYear.value = undefined
    }
</script>

<style scoped>
</style>