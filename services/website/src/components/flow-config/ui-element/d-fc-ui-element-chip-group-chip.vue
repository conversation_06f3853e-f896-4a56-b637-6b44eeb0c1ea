<template>
    <d-chip :size="chipSize"
            :style="{height: DEBUG_FLOW_CONFIG ? 'auto' : undefined}"
            :type="field === true ? 'success' : 'default'"
            v-on="{ click: shouldAddClickListener ? toggle : null }">
        <v-layout :class="{'d-inline-block': DEBUG_FLOW_CONFIG}"
                  class="align-center">
            <span v-if="DEBUG_FLOW_CONFIG"
                  class="me-1">
                <span class="text-body-2 text-red me-1">{{ booleanElement.__typename }}</span><span v-if="(booleanElement as any).field?.id !== undefined"
                                                                                                    class="text-body-2">[{{ (booleanElement as any).field.id }}]</span><span v-if="booleanElement.visibilityCondition"
                                                                                                                                                                             class="text-caption"><span class="mx-1">:</span><span class="text-blue"><d-fc-playground-be :expression="booleanElement.visibilityCondition"/></span></span>
            </span>

            <div class="ms-1">
                {{ flowConfigTranslator(booleanElement.label) }}
            </div>

            <span v-if="DEBUG_FLOW_CONFIG"
                  class="text-caption text-green ms-1">{{ (props.booleanElement as any).booleanDefaultValue as boolean }}</span>
        </v-layout>

        <template #prepend>
            <v-slide-x-transition>
                <v-icon v-if="field === true"
                        :icon="mdiCheck"/>
            </v-slide-x-transition>

            <d-icon v-if="booleanElement.icon"
                    :icon="flowConfigIconSupplier(booleanElement.icon)"
                    type="default"/>
        </template>

        <template #close>
            <d-fc-ui-element-suggestion :field-with-array-index="fieldWithArrayIndex"
                                        :possible-enum-values="[]"
                                        :unit="null"/>
        </template>
    </d-chip>
</template>

<script lang="ts"
        setup>
    import {computed, inject, watchEffect} from "vue";
    import DChip from "@/adapter/vuetify/theme/components/chip/d-chip.vue";
    import {mdiCheck} from "@mdi/js";
    import {BooleanUiElement} from "@/adapter/graphql/generated/graphql";
    import {FlowConfigFieldWithArrayIndex, useMutableFlowDataBoolean} from "@/model/listing/FlowData";
    import {useFlowConfig} from "@/components/flow-config/use-flow-config";
    import DFcUiElementSuggestion from "@/components/flow-config/ui-element/d-fc-ui-element-suggestion.vue";
    import {LArrayIndexInjection, LListingContextInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";
    import DFcPlaygroundBe from "@/components/flow-config/playground/d-fc-playground-be.vue";
    import DIcon from "@/adapter/vuetify/theme/components/d-icon.vue";

    const props = defineProps<{
        booleanElement: BooleanUiElement
    }>()

    const flowData = inject(LMutableFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!
    const flowContext = inject(LListingContextInjection)!
    const chipSize = flowContext.value === 'VIEW' ? 'small' : 'default'
    const shouldAddClickListener = flowContext.value === 'EDIT'

    const fieldWithArrayIndex = computed<FlowConfigFieldWithArrayIndex>(() => ({
        field: props.booleanElement.field,
        arrayIndex: arrayIndex.value
    }));

    const field = useMutableFlowDataBoolean(
        flowData,
        fieldWithArrayIndex,
    )

    watchEffect(() => {
        if (field.value === null) {
            field.value = (props.booleanElement as any).booleanDefaultValue as boolean //Ummapping des Feldes in der GraphQL-Query
        }
    })

    function toggle() {
        if (field.value !== null) {
            field.value = !field.value
        }
    }

    const {
        flowConfigTranslator,
        flowConfigIconSupplier,
    } = useFlowConfig()
</script>

<style lang="scss"
       scoped>
    .label {
        color: rgb(var(--v-theme-d-text-default));
    }
</style>