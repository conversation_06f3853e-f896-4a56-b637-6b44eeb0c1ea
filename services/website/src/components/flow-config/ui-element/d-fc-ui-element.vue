<template>
    <d-fc-ui-element-custom v-if="element.__typename === 'CustomUIElement'"
                            :custom-element="element as CustomUiElement"/>

    <d-fc-ui-element-string v-else-if="element.__typename === 'StringUIElement'"
                            :string-element="element as StringUiElement"/>

    <d-fc-ui-element-number v-else-if="element.__typename === 'NumberUIElement'"
                            :number-element="element as NumberUiElement"/>

    <d-fc-ui-element-date v-else-if="element.__typename === 'DateUIElement'"
                          :date-element="element as DateUiElement"/>

    <d-fc-ui-element-boolean v-else-if="element.__typename === 'BooleanUIElement'"
                             :boolean-element="element as BooleanUiElement"/>

    <d-fc-ui-element-single-selection v-else-if="element.__typename === 'SingleSelectionUIElement'"
                                      :single-selection-element="element as SingleSelectionUiElement"/>

    <d-fc-ui-element-array v-else-if="element.__typename === 'ArrayUIElement'"
                           :array-element="element as ArrayUiElement"/>

    <d-fc-ui-element-file v-else-if="element.__typename === 'FileUIElement'"
                          :file-element="element as FileUiElement"/>

    <d-fc-ui-element-group v-else-if="element.__typename === 'GroupUIElement'"
                           :group-element="element as GroupUiElement"
                           :is-first-element="isFirstElement"
                           :is-last-element="isLastElement"/>

    <d-fc-ui-element-chip-group v-else-if="element.__typename === 'ChipGroupUIElement'"
                                :chip-group-element="element as ChipGroupUiElement"
                                :is-first-element="isFirstElement"
                                :is-last-element="isLastElement"/>

    <d-fc-ui-element-key-value-list v-else-if="element.__typename === 'KeyValueListUIElement'"
                                    :key-value-list-element="element as KeyValueListUiElement"/>

    <d-fc-ui-element-text v-else-if="element.__typename === 'TextUIElement'"
                          :is-first-element="isFirstElement"
                          :text-element="element"/>

    <d-fc-ui-element-field-text v-else-if="element.__typename === 'FieldTextUIElement'"
                                :field-text-element="element"
                                :is-first-element="isFirstElement"/>

    <d-fc-ui-element-table v-else-if="element.__typename === 'TableUIElement'"
                           :table-element="element"/>

    <d-fc-ui-element-image-gallery v-else-if="element.__typename === 'ImageGalleryUIElement'"
                                   :image-gallery-element="element as ImageGalleryUiElement"/>

    <d-fc-unknown-type v-else
                       :data="element"
                       :supported-types="[
                            'CustomUIElement',
                            'StringUIElement',
                            'NumberUIElement',
                            'DateUIElement',
                            'BooleanUIElement',
                            'SingleSelectionUIElement',
                            'ArrayUIElement',
                            'FileUIElement',
                            'GroupUIElement',
                            'ChipGroupUIElement',
                            'KeyValueListUIElement',
                            'TextUIElement',
                            'FieldTextUIElement',
                            'TableUIElement',
                            'ImageGalleryUIElement',
                       ]"
                       :type="element.__typename ?? '???'"
                       clazz="UIElement"/>
</template>

<script lang="ts"
        setup>
    import {ArrayUiElement, BooleanUiElement, ChipGroupUiElement, CustomUiElement, DateUiElement, FileUiElement, GroupUiElement, ImageGalleryUiElement, KeyValueListUiElement, NumberUiElement, SingleSelectionUiElement, StringUiElement, UiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUiElementCustom from "@/components/flow-config/ui-element/d-fc-ui-element-custom.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementString from "@/components/flow-config/ui-element/d-fc-ui-element-string.vue";
    import DFcUiElementNumber from "@/components/flow-config/ui-element/d-fc-ui-element-number.vue";
    import DFcUiElementDate from "@/components/flow-config/ui-element/date/d-fc-ui-element-date.vue";
    import DFcUiElementBoolean from "@/components/flow-config/ui-element/d-fc-ui-element-boolean.vue";
    import DFcUiElementSingleSelection from "@/components/flow-config/ui-element/d-fc-ui-element-single-selection.vue";
    import DFcUiElementArray from "@/components/flow-config/ui-element/d-fc-ui-element-array.vue";
    import DFcUiElementFile from "@/components/flow-config/ui-element/d-fc-ui-element-file.vue";
    import DFcUiElementGroup from "@/components/flow-config/ui-element/d-fc-ui-element-group.vue";
    import DFcUiElementKeyValueList from "@/components/flow-config/ui-element/d-fc-ui-element-key-value-list.vue";
    import DFcUiElementText from "@/components/flow-config/ui-element/d-fc-ui-element-text.vue";
    import DFcUiElementFieldText from "@/components/flow-config/ui-element/d-fc-ui-element-field-text.vue";
    import DFcUiElementTable from "@/components/flow-config/ui-element/d-fc-ui-element-table.vue";
    import DFcUiElementImageGallery from "@/components/flow-config/ui-element/image-gallery/d-fc-ui-element-image-gallery.vue";
    import DFcUiElementChipGroup from "@/components/flow-config/ui-element/d-fc-ui-element-chip-group.vue";

    defineProps<{
        element: UiElement
        isFirstElement: boolean
        isLastElement: boolean
    }>()
</script>

<style scoped>
</style>