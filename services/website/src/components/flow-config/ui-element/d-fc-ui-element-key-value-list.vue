<template>
    <d-fc-ui-element-key-value-list-counter-bar v-if="keyValueListElement.type === 'COUNTER_BAR'"
                                                :items="items"/>

    <d-fc-ui-element-key-value-list-table v-else-if="keyValueListElement.type === 'TABLE'"
                                          :items="items"/>

    <d-fc-unknown-type v-else
                       :data="keyValueListElement"
                       :supported-types="[
                            'COUNTER_BAR',
                            'TABLE'
                       ]"
                       :type="keyValueListElement.type"
                       clazz="KeyValueListUIElement"/>
</template>

<script lang="ts"
        setup>
    import {KeyValueListUiElement, KeyValueListUiElementItem} from "@/adapter/graphql/generated/graphql";
    import DFcUiElementKeyValueListCounterBar from "@/components/flow-config/ui-element/d-fc-ui-element-key-value-list-counter-bar.vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementKeyValueListTable from "@/components/flow-config/ui-element/d-fc-ui-element-key-value-list-table.vue";
    import {computed, inject} from "vue";
    import {isUIElementVisible} from "@/components/flow-config/use-flow-config";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";

    const props = defineProps<{
        keyValueListElement: KeyValueListUiElement
    }>()

    const listing = inject(LListingInjection)!
    const flowData = inject(LFlowDataInjection)!
    const context = inject(LListingContextInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!

    const items = computed<readonly KeyValueListUiElementItem[]>(() => props.keyValueListElement.items.filter(e => isUIElementVisible(
        e,
        arrayIndex.value,
        context.value,
        listing.value,
        flowData.value
    )))
</script>

<style scoped>
</style>