<template>
    <d-fc-ui-element-field-text-heading v-if="fieldTextElement.type === 'HEADING'"
                                        :class="horizontalAlignmentClasses"
                                        :field-text-element="fieldTextElement">
        <d-fc-field-value :field-value="fieldTextElement.fieldValue"/>
    </d-fc-ui-element-field-text-heading>

    <d-fc-ui-element-field-text-paragraph v-else-if="fieldTextElement.type === 'PARAGRAPH'"
                                          :class="horizontalAlignmentClasses"
                                          :field-text-element="fieldTextElement"
                                          :is-first-element="isFirstElement">
        <d-fc-field-value :field-value="fieldTextElement.fieldValue"/>
    </d-fc-ui-element-field-text-paragraph>

    <d-fc-unknown-type v-else
                       :data="fieldTextElement"
                       :supported-types="[
                           'HEADING',
                           'PARAGRAPH'
                       ]"
                       :type="fieldTextElement.type"
                       clazz="FieldTextUIElement"/>
</template>

<script lang="ts"
        setup>
    import {FieldTextUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcFieldValue from "@/components/flow-config/ui-element/d-fc-field-value.vue";
    import {computed} from "vue";
    import DFcUnknownType from "@/components/flow-config/d-fc-unknown-type.vue";
    import DFcUiElementFieldTextHeading from "@/components/flow-config/ui-element/d-fc-ui-element-field-text-heading.vue";
    import DFcUiElementFieldTextParagraph from "@/components/flow-config/ui-element/d-fc-ui-element-field-text-paragraph.vue";

    const props = defineProps<{
        fieldTextElement: FieldTextUiElement
        isFirstElement: boolean
    }>()

    const horizontalAlignmentClasses = computed<Record<string, boolean>>(() => {
        return {
            'text-left': props.fieldTextElement.horizontalAlignment === 'LEFT',
            'text-center': props.fieldTextElement.horizontalAlignment === 'CENTER'
        }
    })
</script>

<style scoped>
</style>
