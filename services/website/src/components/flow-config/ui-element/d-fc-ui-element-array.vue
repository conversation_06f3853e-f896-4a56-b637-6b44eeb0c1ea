<template>
    <d-fc-array-wrapper :element="arrayElement"
                        :size-maximum="arrayElement.sizeMaximum"
                        :size-minimum="arrayElement.sizeMinimum">
        <template #default="{arrayIndicesWithNewElement, newElementArrayIndex, showAddButton}">
            <v-slide-y-transition v-for="(arrayIndex, elementNumber) in arrayIndicesWithNewElement"
                                  :key="arrayIndex">
                <v-row v-if="arrayIndex !== newElementArrayIndex || newElementArrayIndex === lastShownNewElementArrayIndex">
                    <v-col>
                        <d-card>
                            <template #subtitle>
                                <v-layout class="justify-space-between align-center">
                                    {{ t('flowConfig.uiElement.array.itemName', {number: elementNumber + 1}) }}

                                    <d-confirmation-wrapper @onconfirm="onElementRemoved(arrayIndex, arrayIndex === newElementArrayIndex)">
                                        <template #default="{props: menuProps}">
                                            <d-btn :icon="mdiDelete"
                                                   type="default"
                                                   v-bind="menuProps"
                                                   variant="text"/>
                                        </template>
                                    </d-confirmation-wrapper>
                                </v-layout>
                            </template>

                            <!-- visibility will be handled in component -->
                            <!-- (element.children ?? []) weil wir nur bis zu einer begrenzten Tiefe die Daten abfragen -->
                            <d-fc-field-array-index-injector :array-index="arrayIndex">
                                <d-fc-ui-elements :elements="props.arrayElement.children ?? []"/>
                            </d-fc-field-array-index-injector>
                        </d-card>
                    </v-col>
                </v-row>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <v-row v-if="showAddButton && lastShownNewElementArrayIndex !== newElementArrayIndex"
                       justify="center">
                    <v-col cols="auto">
                        <d-btn :icon="mdiPlus"
                               type="tertiary"
                               @click="onElementAdded(newElementArrayIndex)"/>
                    </v-col>
                </v-row>
            </v-slide-y-transition>
        </template>
    </d-fc-array-wrapper>
</template>

<script lang="ts"
        setup>
    import {ArrayUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcArrayWrapper from "@/components/flow-config/ui-element/d-fc-array-wrapper.vue";
    import {mdiDelete, mdiPlus} from "@mdi/js";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {inject, shallowRef} from "vue";
    import {Optional} from "@/model/Optional";
    import {useI18n} from "vue-i18n";
    import DFcUiElements from "@/components/flow-config/d-fc-ui-elements.vue";
    import {LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import DFcFieldArrayIndexInjector from "@/components/flow-config/ui-element/d-fc-field-array-index-injector.vue";

    const {t} = useI18n()

    const props = defineProps<{
        arrayElement: ArrayUiElement
    }>()

    const flowData = inject(LMutableFlowDataInjection)!

    const lastShownNewElementArrayIndex = shallowRef<Optional<number>>(null)

    function onElementAdded(newElementArrayIndex: number) {
        lastShownNewElementArrayIndex.value = newElementArrayIndex
    }

    function onElementRemoved(index: number, isNewElement: boolean) {
        if (isNewElement || lastShownNewElementArrayIndex.value === index) {
            lastShownNewElementArrayIndex.value = null
        }
        if (!isNewElement) {
            flowData.value.deleteArrayElement(props.arrayElement, index)
        }
    }
</script>

<style scoped>
</style>