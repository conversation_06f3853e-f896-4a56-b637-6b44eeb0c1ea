<template>
    <p :class="{'simple-paragraph': !isFirstElement}">
        <d-fc-field-value :field-value="fieldTextElement.fieldValue"/>
    </p>
</template>

<script lang="ts"
        setup>
    import {FieldTextUiElement} from "@/adapter/graphql/generated/graphql";
    import DFcFieldValue from "@/components/flow-config/ui-element/d-fc-field-value.vue";

    defineProps<{
        fieldTextElement: FieldTextUiElement
        isFirstElement: boolean
    }>()
</script>

<style scoped>
    .simple-paragraph {
        margin-top: -20px;
    }
</style>
