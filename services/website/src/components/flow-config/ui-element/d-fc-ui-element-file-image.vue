<template>
    <!-- TODO required !!! ---->
    <!-- TODO: changes direkt speichern, mit debounce -->
    <d-fc-array-wrapper :element="fileElement"
                        :size-maximum="fileElement.fileCountMaximum"
                        :size-minimum="fileElement.fileCountMinimum">
        <v-row :justify="images.length <= 0 ? 'center' : undefined">
            <v-col v-for="image in visibleImages"
                   :key="image._internalId"
                   class="h-100 pa-2"
                   cols="12"
                   sm="6">
                <d-card class="h-100">
                    <d-card :rounded="false"
                            class="border-none bg-transparent"
                            variant="flat"
                            @click="openImageInDialog(image)">
                        <v-img :aspect-ratio="1"
                               :lazy-src="determineImgSrc(image, true) ?? undefined"
                               :src="determineImgSrc(image) ?? undefined"/>
                    </d-card>
                    <d-divider/>
                    <template #actions>
                        <v-layout class="align-center justify-space-between">
                            <d-fc-field-array-index-injector v-if="fileElement.nameElement"
                                                             :array-index="image.arrayIndex">
                                <d-fc-ui-element-string :string-element="fileElement.nameElement"/>
                            </d-fc-field-array-index-injector>
                            <template v-if="context === 'EDIT'">
                                <v-scale-transition>
                                    <d-btn v-if="image._state === 'COMPRESSION_ERROR' || image._state === 'UPLOAD_ERROR'"
                                           :icon="mdiSyncAlert"
                                           class="ms-1"
                                           density="compact"
                                           type="error"
                                           variant="text"
                                           @click="onRetryCompressionOrUpload(image)"/>
                                </v-scale-transition>
                                <d-confirmation-wrapper @onconfirm="onDeleteImage(images.indexOf(image))">
                                    <template #default="{props: menuProps}">
                                        <d-btn :class="{'ms-1': image._state !== 'COMPRESSION_ERROR' && image._state !== 'UPLOAD_ERROR'}"
                                               :icon="mdiDelete"
                                               density="compact"
                                               type="default"
                                               v-bind="menuProps"
                                               variant="text"/>
                                    </template>
                                </d-confirmation-wrapper>
                            </template>
                        </v-layout>
                    </template>
                </d-card>
            </v-col>

            <v-slide-y-transition>
                <v-col v-if="!isImageListExpanded && images.length > maxVisibleImages"
                       class="text-center pa-0 pt-1"
                       cols="12">
                    <v-btn v-if="images.length > maxVisibleImages"
                           :text="t('flowConfig.uiElement.file.image.showAllButton')"
                           variant="text"
                           @click="onShowAllImages"/>
                </v-col>
            </v-slide-y-transition>

            <v-slide-y-transition>
                <v-col v-if="images.length < fileElement.fileCountMaximum && context === 'EDIT'"
                       align-self="center"
                       class="h-100 pa-0"
                       cols="12">
                    <v-container class="h-100"
                                 fluid>
                        <v-row align="center"
                               justify="center">
                            <v-col class="text-center"
                                   cols="12">
                                <d-btn :icon="IS_NATIVE_IOS_APP && isBuildingScanner ? mdiCamera : mdiUpload"
                                       size="x-large"
                                       type="tertiary"
                                       @click="onImageSelectionTriggered"/>
                            </v-col>
                            <v-col v-if="!IS_NATIVE_IOS_APP || !isBuildingScanner"
                                   class="text-center text-caption pt-0"
                                   cols="12"
                                   style="color: rgb(var(--v-theme-d-text-default));">
                                {{ props.fileElement.allowedMimeTypes.map(mt => mt.split("/").slice(1)).join(", ") }}
                            </v-col>
                        </v-row>
                    </v-container>
                </v-col>
            </v-slide-y-transition>
        </v-row>

        <d-fc-ui-element-file-image-dialog v-model="isDialogVisible"
                                           :image-lazy-sources="imageLazySources"
                                           :image-sources="imageSources"
                                           :index="selectedDialogIndex"/>

        <v-slide-y-transition>
            <p v-if="images.length <= 0 && context === 'VIEW'"
               class="text-caption pt-2">
                {{ t('flowConfig.uiElement.file.image.noImagesAvailable') }}
            </p>
        </v-slide-y-transition>

        <template v-if="images.length > 0"
                  #footer>
            <d-btn :icon="mdiImageMultiple"
                   type="default"
                   variant="text"
                   @click="openImageInDialog(null)"/>
        </template>
    </d-fc-array-wrapper>
</template>

<script lang="ts"
        setup>
    import {computed, inject, onMounted, onUnmounted, ref, shallowRef, watch} from "vue";
    import {DFlowConfigUiElementFileImageLoadListingImagesQueryVariables, FileUiElement, ListingImage, useDFlowConfigUiElementFileImageLoadListingImagesQuery} from "@/adapter/graphql/generated/graphql";
    import {useFileDialog} from "@vueuse/core";
    import {Optional} from "@/model/Optional";
    import Compressor from "compressorjs";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {mdiCamera, mdiDelete, mdiImageMultiple, mdiSyncAlert, mdiUpload} from "@mdi/js";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DConfirmationWrapper from "@/components/fragment/d-confirmation-wrapper.vue";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import {v4 as uuidv4} from 'uuid';
    import {areSetsEqual} from "@/utility/set";
    import DFcArrayWrapper from "@/components/flow-config/ui-element/d-fc-array-wrapper.vue";
    import DFcUiElementString from "@/components/flow-config/ui-element/d-fc-ui-element-string.vue";
    import {LListingContextInjection, LListingIdInjection, LMutableFlowDataInjection} from "@/components/listing/ListingInjectionKeys";
    import DFcFieldArrayIndexInjector from "@/components/flow-config/ui-element/d-fc-field-array-index-injector.vue";
    import {deleteImageFromCache, uploadImageGraphQl} from "@/service/image-upload/upload-image";
    import {INTERNET_SPEED, InternetSpeed} from "@/service/pwa/internet-speed";
    import {useI18n} from "vue-i18n";
    import {isImageValidForFlowConfig} from "@/components/flow-config/ui-element/image-gallery/image-gallery-helper";
    import {IS_NATIVE_IOS_APP, NATIVE_APP_SERVICE} from "@/service/native-app/native-app";
    import {createNativeAppMethod, destroyNativeAppMethod} from "@/service/native-app/native-app-function";
    import {FlowConfigFieldWithArrayIndex} from "@/model/listing/FlowData";
    import DFcUiElementFileImageDialog from "@/components/flow-config/ui-element/d-fc-ui-element-file-image-dialog.vue";

    //suppress LocalVariableNamingConventionJS
    const COMPRESSOR_MIME_TYPE = 'image/jpeg'

    const props = defineProps<{
        fileElement: FileUiElement
    }>()

    const context = inject(LListingContextInjection)!
    const listingId = inject(LListingIdInjection)!
    const flowData = inject(LMutableFlowDataInjection)!

    const selectedDialogIndex = shallowRef<number>(-1)
    const isDialogVisible = shallowRef<boolean>(false)

    watch(selectedDialogIndex, index => {
        isDialogVisible.value = index >= 0
    })
    watch(isDialogVisible, isVisible => {
        if (!isVisible) {
            selectedDialogIndex.value = -1
        }
    })

    const maxVisibleImages = 2
    const isImageListExpanded = shallowRef<boolean>(false)
    const visibleImages = computed<readonly Image[]>(() => {
        return isImageListExpanded.value ? images.value : images.value.slice(0, maxVisibleImages)
    })

    function onShowAllImages() {
        isImageListExpanded.value = true
    }

    function openImageInDialog(image: Optional<Image>) {
        selectedDialogIndex.value = image ? imageToDialogIndex(image) : 0
    }

    watch(INTERNET_SPEED, internetSpeed => {
        if (internetSpeed > InternetSpeed.OFFLINE) {
            for (const image of images.value) {
                onRetryCompressionOrUpload(image)
            }
        }
    })

    const imageSources = computed<readonly string[]>(() => images.value
        .map(image => determineImgSrc(image))
        .filter(src => src !== null)
    )
    const imageLazySources = computed<readonly string[]>(() => images.value
        .map(image => determineImgSrc(image, true))
        .filter(src => src !== null)
    )

    function imageToDialogIndex(image: Image): number {
        let index = 0
        for (const img of images.value) {
            if (determineImgSrc(img) === null) {
                continue
            }
            if (img.id === image.id) {
                return index
            }
            ++index
        }
        return 0
    }

    function getIdOfImage(image: Image): Optional<string> {
        return idOfElementByArrayIndex(image.arrayIndex);
    }

    const queryVariables = computed<DFlowConfigUiElementFileImageLoadListingImagesQueryVariables>(() => ({
        id: listingId.value,
    }))
    const {
        result: imagesDataResult,
    } = useDFlowConfigUiElementFileImageLoadListingImagesQuery(queryVariables)
    const imagesData = computed<readonly ListingImage[]>(() => imagesDataResult.value?.listing?.images as ListingImage[] ?? [])

    function determineImgSrc(image: Image, miniUrl: boolean = false): Optional<string> {
        const imageId = getIdOfImage(image)
        if (imageId === null) {
            return null
        }

        if (image._base64Image) {
            return image._base64Image
        }

        const listingImage = imagesData.value
            .filter(isImageValidForFlowConfig)
            .find(imageData => imageData.id === imageId) ?? null

        if (listingImage === null) {
            return null
        }

        if (listingImage.imageAsBase64ForOffline) {
            return listingImage.imageAsBase64ForOffline
        }

        return (miniUrl ? listingImage.miniURL : listingImage.thumbnailURL) ?? null
    }

    type ImageState = 'SELECTED' | 'COMPRESSING' | 'COMPRESSED' | 'COMPRESSION_ERROR' | 'UPLOADED' | 'UPLOAD_ERROR'

    type Image = {
        id: string
        readonly arrayIndex: number

        readonly _internalId: string
        _state: ImageState
        _data: Optional<File | Blob>
        _compressor: Optional<Compressor>
        _base64Image: Optional<string>
    }
    const images = ref<Image[]>([]) //ref okay
    const imageIds = computed<ReadonlySet<string>>(() => createImageIdSet(images.value))

    function createImageIdSet(images: Image[]): Set<string> {
        const ids = images
            .map(getIdOfImage)
            .filter(id => id !== null);
        return new Set<string>(...ids)
    }

    const {t} = useI18n()

    const arrayIndices = computed<readonly number[]>(() => flowData.value.arrayIndicesOf(props.fileElement))

    watch(flowData, flowData => {
        const currentArrayIndices = arrayIndices.value
        const ids = currentArrayIndices
            .map(idOfElementByArrayIndex)
            .filter(id => id !== null)
        const newImageIds = new Set<string>(ids)

        const oldImages = images.value
        const oldImageIds = imageIds.value

        if (areSetsEqual(oldImageIds, newImageIds)) {
            if (newImageIds.size <= 0) {
                images.value = []
            }
            return
        }

        //add new images, remove old images, don't touch the rest
        images.value = currentArrayIndices
            .map(arrayIndex => {
                const newImageId = idOfElementByArrayIndex(arrayIndex)
                if (newImageId === null) {
                    return null
                }
                const oldImage = oldImages.find(oldImage => oldImage.id === newImageId)
                if (oldImage !== undefined) {
                    return oldImage
                }
                const newImage: Image = {
                    id: newImageId,
                    arrayIndex,
                    _internalId: uuidv4(),
                    _state: 'UPLOADED',
                    _data: null,
                    _compressor: null,
                    _base64Image: null
                }
                return newImage
            })
            .filter(image => image !== null)
    }, {
        deep: true,
        immediate: true
    })

    const queryString = window.location.search
    const urlParameters = new URLSearchParams(queryString)
    const isBuildingScanner = urlParameters.get("isBuildingScanner") === 'true'

    function onImageSelectionTriggered() {
        if (IS_NATIVE_IOS_APP && isBuildingScanner) {
            NATIVE_APP_SERVICE?.onBuildingScannerImageUploadRequested(listingId.value)
        } else {
            onImageSelectionOpened()
        }
    }

    if (IS_NATIVE_IOS_APP && isBuildingScanner) {
        const nativeAppBuildingScannerImageMimeType = "image/jpeg"
        const nativeAppMethod = "nativeAppBuildingScannerImageUpload"
        let imageData = ""
        let imageCameraTransformationMatrix: Optional<readonly number[][]> = null
        let imageCameraProjectionMatrix: Optional<readonly number[][]> = null
        onMounted(() => {
            createNativeAppMethod(nativeAppMethod, (
                imageId: string,
                cameraTransformationMatrix: Optional<readonly number[][]>,
                cameraProjectionMatrix: Optional<readonly number[][]>,
                imageDataChunk: Optional<string>
            ) => {
                if (cameraTransformationMatrix !== null) {
                    imageCameraTransformationMatrix = cameraTransformationMatrix
                }
                if (cameraProjectionMatrix !== null) {
                    imageCameraProjectionMatrix = cameraProjectionMatrix
                }
                if (imageDataChunk !== "#EOF#") {
                    imageData += imageDataChunk
                    return
                }
                const byteCharacters = atob(imageData);

                imageData = ""
                const metadata: ImageMetadata = {
                    id: imageId,
                    cameraTransformationMatrix: imageCameraTransformationMatrix!,
                    cameraProjectionMatrix: imageCameraProjectionMatrix!
                }

                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i) => byteCharacters.charCodeAt(i));
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], {type: nativeAppBuildingScannerImageMimeType});
                const file = new File([blob], uuidv4(), {type: nativeAppBuildingScannerImageMimeType});
                const fileList: FileList = {
                    0: file,
                    length: 1,
                    item: (index: number) => {
                        return index === 0 ? file : null
                    }
                }
                processNewFiles(fileList, [metadata])
            })
        })

        onUnmounted(() => {
            destroyNativeAppMethod(nativeAppMethod)
        })
    }

    const {
        open: onImageSelectionOpened,
        onChange: onImagesSelected
    } = useFileDialog({
        accept: props.fileElement.allowedMimeTypes.join(", "),
        reset: true,
    })

    onImagesSelected(files => processNewFiles(files, null))

    type ImageMetadata = {
        id: string,
        cameraTransformationMatrix: readonly number[][],
        cameraProjectionMatrix: readonly number[][]
    }

    function processNewFiles(files: Optional<FileList>, metadata: Optional<readonly ImageMetadata[]>) {
        if (files === null || files.length <= 0) {
            return
        }

        isImageListExpanded.value = true

        const data = flowData.value
        const fileElement = props.fileElement
        const nameField = fileElement.nameElement?.field ?? null

        handleFileUpload(files)

        // noinspection NestedFunctionJS
        async function handleFileUpload(files: FileList) {
            for (let i = 0; i < files.length; ++i) {
                await new Promise(resolve => setTimeout(resolve, 500)) // delay to prevent too many parallel compressions and uploads

                const file = files.item(i)
                if (file === null) {
                    continue
                }

                const newArrayIndex = data.nextArrayIndex(fileElement)

                const base64Image = await blobToBase64(file)

                const imageMetadata = metadata === null ? null : metadata[i]

                if (imageMetadata !== null) {
                    data.setString(
                        {
                            field: {
                                id: "photo_camera_transformation_matrix",
                            },
                            arrayIndex: newArrayIndex
                        },
                        JSON.stringify(imageMetadata.cameraTransformationMatrix),
                        false
                    )
                    data.setString(
                        {
                            field: {
                                id: "photo_camera_projection_matrix",
                            },
                            arrayIndex: newArrayIndex
                        },
                        JSON.stringify(imageMetadata.cameraProjectionMatrix),
                        false
                    )
                }

                const newId = imageMetadata === null ? uuidv4() : imageMetadata.id
                const image: Image = {
                    id: newId,
                    arrayIndex: newArrayIndex,
                    _internalId: uuidv4(),
                    _state: 'SELECTED',
                    _data: file,
                    _compressor: null,
                    _base64Image: base64Image
                }

                images.value.push(image)

                compressImage(image)

                if (nameField !== null) {
                    data.setString(
                        {
                            field: nameField,
                            arrayIndex: newArrayIndex
                        },
                        file.name,
                        false
                    )
                }

                data.setString(
                    createIdFieldWithArrayIndex(newArrayIndex),
                    newId,
                    false,
                )
            }
        }
    }

    function blobToBase64(blob: Blob): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = () => {
                if (reader.result) {
                    resolve(reader.result as string); // base 64 string
                } else {
                    reject('Error converting blob to Base64');
                }
            };
            reader.onerror = () => {
                reject('Error reading blob');
            };
        });
    }

    function compressImage(image: Image) {
        console.log("Triggering compression of image with id", image.id)

        if (image._state !== 'SELECTED' || !(image._data instanceof File)) {
            console.warn('Image is not in SELECTED state or data is not a file', image)
            return
        }

        image._state = 'COMPRESSING'

        if (props.fileElement.allowedMimeTypes.indexOf(COMPRESSOR_MIME_TYPE) <= -1) {
            console.warn('Compression mime type', COMPRESSOR_MIME_TYPE, "is not allowed in file element's mime types", props.fileElement.allowedMimeTypes, props.fileElement)
            return
        }

        console.log("Compressing image with id …", image.id)

        image._compressor = new Compressor(image._data, {
            quality: 0.80, //https://sirv.com/help/articles/jpeg-quality-comparison/
            maxWidth: 1920,
            maxHeight: 1920,
            retainExif: false,
            mimeType: COMPRESSOR_MIME_TYPE,
            success: file => {
                console.log("Compression of image with id", image.id, "successful")

                image._data = file
                image._compressor = null
                image._state = 'COMPRESSED'
                uploadImage(image, t)
            },
            error: error => {
                console.warn("Compression of image with id", image.id, "failed", error)

                image._compressor = null
                image._state = 'COMPRESSION_ERROR'
            },
        })
    }

    async function uploadImage(image: Image, t: (key: string) => string) {
        console.log("Triggering upload of image with id", image.id)

        if (image._state !== 'COMPRESSED' || image._data === null) {
            console.warn('Image is not in COMPRESSED state or data is null', image)
            return
        }

        console.log("Uploading image with id …", image.id)

        try {

            /**
             * Leads to re-fetching image data for showing it in the UI.
             * We want to show the image immediately after upload is initiated.
             */
            const onCacheWritten = function () {
                console.log("Cache written for image with id", image.id)
            };

            const onMutationFinished = function () {
                console.log("Mutation finished for image with id", image.id)

                image._state = 'UPLOADED'
                flowData.value.setString(
                    createIdFieldWithArrayIndex(image.arrayIndex),
                    image.id,
                    false,
                );
                image._data = null // Speicher wieder freigeben
            }

            const onError = function () {
                console.warn("Error uploading image with id", image.id)
                image._state = 'UPLOAD_ERROR'
            }

            await uploadImageGraphQl(listingId, image.id, 'LISTING_FIELD', image._data as Blob, t, onCacheWritten, onError, onMutationFinished);
        } catch (error) {
            console.warn("Error uploading image with id", image.id, error)
            image._state = 'UPLOAD_ERROR'
        }
    }

    function onDeleteImage(index: number) {
        const removedImage = images.value.splice(index, 1)
        if (removedImage.length <= 0) {
            return
        }
        const image = removedImage[0]
        image._compressor?.abort()
        image._compressor = null
        image._data = null

        flowData.value.deleteArrayElement(props.fileElement, image.arrayIndex)

        deleteImageFromCache(listingId.value, image.id)
    }

    function onRetryCompressionOrUpload(image: Image) {
        switch (image._state) {
            case 'COMPRESSION_ERROR':
                image._state = 'SELECTED'
                compressImage(image)
                break
            case 'UPLOAD_ERROR':
                if (INTERNET_SPEED.value >= InternetSpeed.FAST) {
                    image._state = 'COMPRESSED';
                    uploadImage(image, t)
                }
                break
        }
    }

    function createIdFieldWithArrayIndex(arrayIndex: Optional<number>): FlowConfigFieldWithArrayIndex {
        const fileElement = props.fileElement
        const idField = fileElement.idField
        return {
            field: idField,
            arrayIndex
        }
    }

    function idOfElementByArrayIndex(arrayIndex: number): Optional<string> {
        return flowData.value.getString(createIdFieldWithArrayIndex(arrayIndex))
    }
</script>

<style scoped>
    :deep(.v-btn + .v-btn) {
        margin-inline-start: 0 !important;
    }
</style>