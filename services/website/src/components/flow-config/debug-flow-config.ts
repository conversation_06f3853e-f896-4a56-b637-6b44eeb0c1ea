import {LS__DEBUG_FLOW_CONFIG} from "@/service/local-storage/local-storage";

const queryString = window.location.search
const urlParameters = new URLSearchParams(queryString)

const FLOW_CONFIG_URL_PARAMETER_DEBUG = "debugFlowConfig"

const existsURLParameter = urlParameters.has(FLOW_CONFIG_URL_PARAMETER_DEBUG)
const isURLParameterEnabled = urlParameters.get(FLOW_CONFIG_URL_PARAMETER_DEBUG) === 'true'
const isLocalStorageEnabled = LS__DEBUG_FLOW_CONFIG.state.value
export const DEBUG_FLOW_CONFIG = (existsURLParameter && isURLParameterEnabled) || isLocalStorageEnabled

if (existsURLParameter) {
    LS__DEBUG_FLOW_CONFIG.state.value = isURLParameterEnabled
}