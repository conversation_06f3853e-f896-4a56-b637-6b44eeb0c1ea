<template>
    <d-alert type="error">
        <span class="text-red font-weight-bold">Unknown type of {{ clazz }}: {{ type }}</span>
        <br>
        <span>Supported types: {{ supportedTypes.join(', ') }}</span>

        <d-fc-raw-data :raw-data="data"/>
    </d-alert>
</template>

<script lang="ts"
        setup>
    import DFcRawData from "@/components/flow-config/d-fc-raw-data.vue";
    import DAlert from "@/adapter/vuetify/theme/components/alert/d-alert.vue";

    defineProps<{
        clazz: string
        type: string
        data: any
        supportedTypes: string[]
    }>()
</script>

<style scoped>
</style>