<template>
    <v-container :class="{'pa-0 ma-0': noMargin && !DEBUG_FLOW_CONFIG}"
                 fluid>
        <v-scroll-y-transition v-for="(element, elementIndex) in elements"
                               :key="elementIndex">
            <v-row v-if="DEBUG_FLOW_CONFIG || isUIElementVisible(element, arrayIndex, context, listing, flowData)"
                   :class="{'my-1': DEBUG_FLOW_CONFIG}"
                   :style="{
                        opacity: DEBUG_FLOW_CONFIG
                            ? (isUIElementVisible(element, arrayIndex, context, listing, flowData) ? 1 : 0.5)
                            : undefined,

                        border: DEBUG_FLOW_CONFIG
                            ? (isUIElementVisible(element, arrayIndex, context, listing, flowData) ? '1px solid red' : '1px dashed red')
                            : undefined
                    }">
                <v-col>
                    <p v-if="DEBUG_FLOW_CONFIG"
                       class="pb-4">
                        <span class="text-body-2 text-red">{{ element.__typename }}</span><span v-if="(element as any).field?.id !== undefined"
                                                                                                class="text-body-2">[{{ (element as any).field.id }}]</span><span v-if="element.visibilityCondition"
                                                                                                                                                                  class="text-caption"><span class="mx-1">:</span><span class="text-blue"><d-fc-playground-be :expression="element.visibilityCondition"/></span></span>
                    </p>
                    <d-fc-ui-element :element="element"
                                     :is-first-element="elementIndex === firstVisibleElementIndex"
                                     :is-last-element="elementIndex === lastVisibleElementIndex"/>
                    <d-fc-raw-data v-if="DEBUG_FLOW_CONFIG"
                                   :raw-data="element"/>
                </v-col>
            </v-row>
        </v-scroll-y-transition>
    </v-container>
</template>

<script lang="ts"
        setup>
    import {UiElement} from "@/adapter/graphql/generated/graphql";
    import DFcUiElement from "@/components/flow-config/ui-element/d-fc-ui-element.vue";
    import {isUIElementVisible} from "@/components/flow-config/use-flow-config";
    import {computed, inject} from "vue";
    import DFcPlaygroundBe from "@/components/flow-config/playground/d-fc-playground-be.vue";
    import DFcRawData from "@/components/flow-config/d-fc-raw-data.vue";
    import {LArrayIndexInjection, LFlowDataInjection, LListingContextInjection, LListingInjection} from "@/components/listing/ListingInjectionKeys";
    import {DEBUG_FLOW_CONFIG} from "@/components/flow-config/debug-flow-config";

    const props = defineProps<{
        elements: readonly UiElement[]
        noMargin?: boolean
    }>()

    const listing = inject(LListingInjection)!
    const flowData = inject(LFlowDataInjection)!
    const arrayIndex = inject(LArrayIndexInjection)!
    const context = inject(LListingContextInjection)!

    const firstVisibleElementIndex = computed<number>(() => props.elements.findIndex(e => isUIElementVisible(
        e,
        arrayIndex.value,
        context.value,
        listing.value,
        flowData.value
    )))
    const lastVisibleElementIndex = computed<number>(() => props.elements.findLastIndex(e => isUIElementVisible(
        e,
        arrayIndex.value,
        context.value,
        listing.value,
        flowData.value
    )))
</script>

<style scoped>
</style>