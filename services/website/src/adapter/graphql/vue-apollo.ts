import {<PERSON><PERSON>lient, ApolloLink, createHtt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>questH<PERSON><PERSON>, split} from '@apollo/client/core';
import {createApolloProvider} from '@vue/apollo-option';
import {provideApolloClient} from '@vue/apollo-composable';
import {buildServerUrl, buildServerWsUrl} from '@/utility/server';
import {AUTH_SERVICE} from "@/service/auth/AuthService";
import {v4 as uuidv4} from 'uuid';
import {BUILD_VERSION, CLIENT_SECRET, DEBUG_APOLLO_WATCHES, IS_DEVELOPMENT} from "@/utility/environment";
import {getMainDefinition} from "@apollo/client/utilities";
import {GraphQLWsLink} from "@apollo/client/link/subscriptions";
import {createClient} from "graphql-ws";
import {loadDevMessages, loadErrorMessages} from "@apollo/client/dev";
import possibleTypes from '@/adapter/graphql/generated/fragmentTypes.json';
// @ts-expect-error("js dependency")
import apolloLogger from "apollo-link-logger";
import {FlowConfigFieldValue, ListingImage} from "@/adapter/graphql/generated/graphql";
import {setContext} from "@apollo/client/link/context";
import {CONFIG} from "@/config";
import {ApolloOfflineMutationsLink} from "@/adapter/graphql/apollo-links/ApolloOfflineMutationsLink";
import {ApolloKeycloakTokenRefreshLink} from "@/adapter/graphql/apollo-links/ApolloKeycloakTokenRefreshLink";
import {ApolloCustomTimeoutLink} from "@/adapter/graphql/apollo-links/ApolloCustomTimeoutLink";

// cacheSizes["cache.fragmentQueryDocuments"] = 10
// cacheSizes["documentTransform.cache"] = 10
// cacheSizes['fragmentRegistry.findFragmentSpreads'] = 10
// cacheSizes["fragmentRegistry.lookup"] = 10
// cacheSizes["fragmentRegistry.transform"] = 10
// cacheSizes["inMemoryCache.executeSelectionSet"] = 10
// cacheSizes["inMemoryCache.executeSubSelectedArray"] = 10
// cacheSizes["inMemoryCache.maybeBroadcastWatch"] = 10
// cacheSizes["PersistedQueryLink.persistedQueryHashes"] = 10
// cacheSizes['queryManager.getDocumentInfo'] = 10
// cacheSizes['removeTypenameFromVariables.getVariableDefinitions'] = 10
// cacheSizes.canonicalStringify = 10
// cacheSizes.parser = 10
// cacheSizes.print = 10

const GRAPHQL_HOST = buildServerUrl('/graphql');
const GRAPHQL_WS_HOST = buildServerWsUrl('/subscriptions');

if (IS_DEVELOPMENT) {
    loadDevMessages();
    loadErrorMessages();
    console.log('GraphQL Host = ', GRAPHQL_HOST, "GraphQL WS Host = ", GRAPHQL_WS_HOST);
}

export function constructHttpHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
        'X-Client-Version': BUILD_VERSION,
        'X-Client-Id': 'Website',
        'X-Client-Context': location.pathname,
        'X-Request-Id': uuidv4(),
        'X-Request-Payload': CLIENT_SECRET,
    };

    if (AUTH_SERVICE.accessToken.value !== null) {
        headers['Authorization'] = `Bearer ${AUTH_SERVICE.accessToken.value}`;
    }

    return headers;
}

/**
 * Defines the WebSocker link for the Apollo Client.
 * It is used for subscriptions.
 */
const wsLink = new GraphQLWsLink(createClient({
    url: GRAPHQL_WS_HOST,
    lazy: true,
    connectionParams: () => {
        return {
            headers: constructHttpHeaders()
        };
    }
}));

const httpLink = createHttpLink({
    uri: GRAPHQL_HOST,
});

const dynamicTimeoutLink = setContext((request, _) => {
    let timeout = CONFIG.APOLLO_TIMEOUT_DEFAULT_MS;

    const isImageUpload = request.operationName === 'dFlowConfigUIElementFileImageUploadImage';
    if (isImageUpload) {
        timeout = CONFIG.APOLLO_TIMEOUT_FILEUPLOAD_MS;
    }

    const isCreateListing = request.operationName === 'dListingListCreateListing';
    if (isCreateListing) {
        timeout = CONFIG.APOLLO_TIMEOUT_CREATE_LISTING_MS;
    }

    const isEvexExport = request.operationName === 'dListingBuildingExportDialogDownloadEvex';
    if (isEvexExport) {
        timeout = CONFIG.APOLLO_TIMEOUT_EXPORT_EVEX_MS;
    }

    return {timeout};
});


/**
 * Splits the link based on the operation type.
 * If the operation is a subscription, it uses the wsLink.
 * Otherwise, it uses the httpLink.
 */
const httpAndSubscriptionSplitLink = split(
    // split based on operation type
    ({query}) => {
        const definition = getMainDefinition(query);
        return definition.kind === 'OperationDefinition' &&
            definition.operation === 'subscription';
    },
    wsLink,
    httpLink
);

const headersLink = new ApolloLink((operation, forward) => {
    const headers = constructHttpHeaders();

    operation.setContext({headers});

    return forward(operation);
});

/**
 * Wenn man ein Foto mit sehr langsamen Internet hochlädt oder ganz offline ist,
 * dann wird das hochgeladene Foto in Apollo erstmal gecached und liegt dann da drin.
 *
 * Wenn man wieder Internet hat und dann auf der Plattform aber herrumnavigiert, dann
 * trifft man zwangsläufig auch auf GraphQL Queries, die das Listing, für das man Bilder hochgeladen hat,
 * abfragen. Hier kommt dann das Listing vom Server zurück und die Bilder sind nicht dabei, da sie ja noch nicht hochgeladen werden konnten.
 *
 * Aus diesem Grund behalten wir Bilder, die innerhalb der letzten 24h hochgeladen wurden, im Cache.
 * Das hat für die Löschung von Fotos keinen Effekt, da Fotos zusätzlich als Feld im Flow relatiert sind.
 * Das "asset" Foto ist somit zwar noch im Cache vorhanden, aber bei Löschung schon nicht mehr im Flow.
 */
function shouldKeepExistingImage(existingImage: ListingImage) {
    if (existingImage.uploadDate) {
        // Behalten wenn das Bild vor 24h hochgeladen wurde (offline Fotos)
        return Date.now() - new Date(existingImage.uploadDate).getTime() < 24 * 60 * 60 * 1000;
    }
}

export const APOLLO_IN_MEMORY_CACHE = new InMemoryCache({
    possibleTypes: possibleTypes.possibleTypes,
    typePolicies: {
        // TODO das muss irgendwie besser gehen
        // Disables normalization for each of the following types and subtypes
        // Otherwise the cache will be filled with unnecessary cache fragmentation
        // while these fragments are not needed anyway
        Address: {
            keyFields: false
        },
        Building: {
            keyFields: false
        },
        ConstructionPart: {
            keyFields: false
        },
        Wall: {
            keyFields: false
        },
        Room: {
            keyFields: false
        },
        Furniture: {
            keyFields: false
        },
        FlowConfigField: {
            keyFields: false
        },
        FlowConfigPage: {
            keyFields: false
        },
        CustomUIElement: {
            keyFields: false
        },
        Floor: {
            keyFields: false
        },
        ListingImage: {
            keyFields: false
        },
        ListingBuildingScanRoom: {
            keyFields: false
        },
        ListingBuildingScanObject: {
            keyFields: false
        },
        ListingBuildingScanSurface: {
            keyFields: false
        },
        GeocodingAddress: {
            keyFields: false
        },
        PointOfInterest: {
            keyFields: false
        },
        UserGroup: {
            keyFields: false
        },
        ValuationFieldDefinition: {
            keyFields: false
        },
        Listing: {
            fields: {
                images: {
                    merge(existing = [], incoming: ListingImage[]) {
                        const existingToKeep = existing.filter((existingImage: ListingImage) => {
                            return shouldKeepExistingImage(existingImage);
                        });

                        return [...existingToKeep, ...incoming];
                    }
                },
                fieldValues: {
                    merge(existing = [], incoming: FlowConfigFieldValue[]) {
                        let existingToKeep = []
                        if (APOLLO_OFFLINE_MUTATIONS_LINK.hasPendingMutations()) {
                            const incomingSet = new Set(incoming.map(item => `${item.field.id}_${item.arrayIndex}`));
                            existingToKeep = existing.filter((item: FlowConfigFieldValue) => !incomingSet.has(`${item.field.id}_${item.arrayIndex}`));
                        }

                        return [...existingToKeep, ...incoming];
                    }
                }
            }
        },
        Query: {
            fields: {
                listing: {
                    read(_, {args, toReference}) {
                        return toReference({
                            __typename: 'Listing',
                            id: args?.id,
                        });
                    }
                },
                flowConfig: {
                    read(_, {args, toReference}) {
                        return toReference({
                            __typename: 'FlowConfig',
                            id: args?.id,
                        });
                    }
                },
            },
        },
    },
});

export const APOLLO_OFFLINE_MUTATIONS_LINK = new ApolloOfflineMutationsLink()

const apolloLinks: (ApolloLink | RequestHandler)[] = [
    IS_DEVELOPMENT ? apolloLogger : undefined,
    // new ApolloErrorLink().createErrorLink(),
    APOLLO_OFFLINE_MUTATIONS_LINK,
    headersLink,
    new ApolloKeycloakTokenRefreshLink(),
    dynamicTimeoutLink,
    new ApolloCustomTimeoutLink(),
    httpAndSubscriptionSplitLink
].filter(l => l !== undefined)

const apolloClient = new ApolloClient({
    link: ApolloLink.from(apolloLinks),
    cache: APOLLO_IN_MEMORY_CACHE,
    devtools: {
        enabled: IS_DEVELOPMENT,
    },
    defaultOptions: {
        watchQuery: {
            fetchPolicy: "cache-first",
            refetchWritePolicy: 'merge',
        },
        query: {
            fetchPolicy: "cache-first",
        },
        mutate: {
            fetchPolicy: "no-cache",
            awaitRefetchQueries: false,
            errorPolicy: "all",
            onQueryUpdated: IS_DEVELOPMENT && DEBUG_APOLLO_WATCHES
                ? (previousQuery, newQuery) => {
                    console.log("Watches: ", (APOLLO_IN_MEMORY_CACHE as any).watches)
                } : undefined
        }
    },
});

if (IS_DEVELOPMENT && apolloClient.getMemoryInternals) {
    console.log("Apollo memory", apolloClient.getMemoryInternals())
}

provideApolloClient(apolloClient);

export default createApolloProvider({
    defaultClient: apolloClient,
});

