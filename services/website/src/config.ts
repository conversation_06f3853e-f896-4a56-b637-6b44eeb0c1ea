import {LanguageCode} from '@/adapter/vue-i18n/LanguageCode';
import {DoorbitThemeId} from "@/adapter/vuetify/theme/doorbit-theme-id";
import {latLng, LatLng} from "leaflet";

export const CONFIG: {
    LANGUAGE_CODE: LanguageCode
    THEME: DoorbitThemeId
    DOORBIT_HEADQUARTER_GEOLOCATION: LatLng
    ADDRESS_OBFUSCATION_RADIUS_MIN: number //in meters
    ADDRESS_OBFUSCATION_RADIUS_MAX: number //in meters
    NATIVE_APP_TOP_BAR_HEIGHT: number //in pixels, excluding safe area
    NATIVE_APP_BOTTOM_BAR_HEIGHT: number //in pixels, excluding safe area
    /**
     * After this amount of time after the onMounted event,
     * the user listings are force-fetched.
     */
    APOLLO_FETCH_USERLISTINGS_AFTER_LOAD_MS: number
    /**
     * After this amount of time after the onMounted event,
     * the flow configs are force-fetched, if needed.
     */
    APOLLO_FETCH_FLOWCONFIGS_AFTER_LOAD_MS: number
    /**
     * We have to wait until the flow config was initially fetched at least, otherwise
     * refetch is not available yet.
     */
    APOLLO_REFETCH_FLOW_CONFIG_AFTER_LOAD_MS: number
    APOLLO_TIMEOUT_FILEUPLOAD_MS: number
    APOLLO_TIMEOUT_CREATE_LISTING_MS: number
    APOLLO_TIMEOUT_EXPORT_EVEX_MS: number
    APOLLO_TIMEOUT_DEFAULT_MS: number
    FLOW_CONFIG_PAGES_TO_HIDE_WHEN_OFFLINE: ReadonlySet<string>
} = {
    LANGUAGE_CODE: 'de',
    THEME: 'light',
    DOORBIT_HEADQUARTER_GEOLOCATION: latLng(51.507222, -0.1275),
    ADDRESS_OBFUSCATION_RADIUS_MIN: 75, //in meters
    ADDRESS_OBFUSCATION_RADIUS_MAX: 250, //in meters
    NATIVE_APP_TOP_BAR_HEIGHT: 64, //in pixels, excluding safe area //TODO: <<<<<<<<<<<<<<<<<<<<< umbenennen zu einfacher HEADER height... auch wichtig. die css variablen umbenennen
    NATIVE_APP_BOTTOM_BAR_HEIGHT: 68, //in pixels, excluding safe area //TODO: <<<<<<<<<<<<<<<<<<<<< umbenennen zu einfacher HEADER height... auch wichtig. die css variablen umbenennen
    APOLLO_FETCH_USERLISTINGS_AFTER_LOAD_MS: 300,
    APOLLO_FETCH_FLOWCONFIGS_AFTER_LOAD_MS: 500,
    APOLLO_REFETCH_FLOW_CONFIG_AFTER_LOAD_MS: 2500,
    APOLLO_TIMEOUT_FILEUPLOAD_MS: 47 * 1000,
    APOLLO_TIMEOUT_CREATE_LISTING_MS: 14 * 1000,
    APOLLO_TIMEOUT_EXPORT_EVEX_MS: 60 * 1000,
    APOLLO_TIMEOUT_DEFAULT_MS: 10 * 1000,
    FLOW_CONFIG_PAGES_TO_HIDE_WHEN_OFFLINE: new Set([
        /**
         * No geocoding when offline
         */
        "ADDRESS"
    ])
};
