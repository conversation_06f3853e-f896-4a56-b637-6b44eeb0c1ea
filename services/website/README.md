# Website Portal

This is the frontend for the doorbit portal.
It is a Vue3 project that communicates via GraphQL with our "bff" backend.

## Project setup

This should do the job:

```
# install dependencies
yarn install

# generate graphql types out of grapql schemas
yarn --cwd graphql-generator generate
```

Compiles, starts local webserver and hot-reloads for development.
We are using Vite with Vite-Proxy (to circumvent CORS issues) to connect to the BFF application on integ.doorbit.com
directly.

```
yarn dev
```

IN CASE OF authorization problems with cloudflare (Most likely you get a 302 by Cloudflare) its because
the CF_Authorization cookie is expired. You can find the cookie in VITE_CLOUDFLARE_AUTHORIZATION_KEY and you should
refresh it after freshly logging in to integ.doorbit.com. Make sure you got that redirect to Cloudflare-Access. If not, clear all website data using Developer Tools -> Application -> Clear website data.
When you do, <PERSON>flare is creating a new cookie and you should copy the value from the browser dev tools and commit it.

```
yarn build
```

See also: [package.json](package.json).

### Connecting to backend

## PWA

We use `vite-plugin-pwa` to configure the ServiceWorker to achieve Offline-Capability for all assets the PWA serves.
**It is important to know** that the ServiceWorker only really works when the PWA is served by a **real** web-server. Vite is not a real webserver, thus `yarn dev` won't cache into the ServiceWorker! You have to run `yarn build-dev` and then `yarn serve`.

## Apollo Client Cache

We use Apollo Client Caching to speed up UI performance as well as for offline capability.
In order to be fully offline compliant the platform needs to prefetch all data that is supposed to work in offline mode as well.

Prefetching is achieved by `d-offline-capability.vue`.
To test the offline capability, goto `.env.dev` and set `VITE_APOLLO_FETCH_POLICY` to `cache-only`. This will fetch data from cache only and never go to the server. There is an additional `CacheMissLink` that logs warnings when a query experienced a cache-miss.

### Fetch-Policies and debugging

`cache-only` is the ultimate solution when you want to test for offline-capability. Because the PWA really shows problems only if you rely on cache only. If you use `cache-first` or even `cache-and-network` the PWA will behave okay, because if there is no cache entry found Apollo will fetch data from Network.

### Consistency throughout the platform

Since most places of the platform are offline-capable, all data related to these places is cached upfront.
Caching means that data is stale by design, since there is no Internet available to update it. However when Internet is available, that cached-data must be updated for maintaining a responsive and good User experience.

At the moment there are some strategies to achieve consistency, while maintaining 0-loading times even for Online-users:

1. When entering the PWA, `d-offline-capability` prefetches data in a `cache-and-network` manner, i. e. data is served from cache but fetched from network asynchronously after. This will refresh data of the entire platform upon PWA entry.
2. When accessing the UserListings list (`d-listing-list`), UserListings are fetched in a `cache-and-network` manner as well. In addition, if a user remains on this page for a longer period, periodic re-fetching ensures fresh UserListings as well.
3. When accessing a Listing Detail Page (`d-listing`), the Listing is fetched in a `cache-and-network` manner as well. On each page transition the listing is fetched synchronously to get latest changes.

### Debugging the cache

Please install the [Apollo DevTools browser plugin](https://www.apollographql.com/docs/react/development-testing/developer-tooling/#apollo-client-devtools). Under "Cache" you can see all cache entries.

### Common Mistakes

#### Numbers in Query/Mutation-Names in .gql-Files

This will result into cryptic errors and a long debugging session. It's currently not supported by the GraphQL-Generator.

## Native App

Es gibt 3 versteckte URL-Query-Parameter:

```
isIOSApp=true
isAndroidApp=true
isNativeApp=true
```

`isIOSApp` und `isAndroidApp` sorgen dafür, dass `isNativeApp` (bzw. `IS_NATIVE_APP`) im Code `true` ist. Wenn einer dieser Parameter gesetzt wurde, bleiben diese bestehen im Local Storage. Man kann diese Parameter alle einzeln auf `false` zurücksetzen.

Mit `isIOSApp` und `isAndroid` sollte man lieber nicht rumspielen, die Parameter sorgen für keine Initialisierung von Keycloak, weil das die nativen Apps mit Bridge Calls selber machen. Wenn ihr trotzdem das Frontend ausprobieren wollt, wie es hybrid aussieht, könnt ihr dafür dann `isNativeApp=true` setzen.

Das Bearbeiten dieser Parameter direkt in der URL löst kein Neu-Rendern der Seite an, ein Page Reload ist der sichere Weg.
