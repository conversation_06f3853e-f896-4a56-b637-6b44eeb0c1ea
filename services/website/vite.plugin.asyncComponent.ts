import {createFilter, FilterPattern} from "@rollup/pluginutils";
import {PluginOption} from "vite";

export type AsyncComponentPluginOptions = {
    include?: FilterPattern
    exclude?: FilterPattern
}

export function asyncComponentPlugin(options: AsyncComponentPluginOptions): PluginOption {
    const filter = createFilter(options.include, options.exclude);

    return {
        name: 'async-component-plugin',
        apply: 'build',
        enforce: 'pre',
        transform(code, id) {
            let newCode = code

            if (filter(id) && newCode.includes('from') && newCode.includes('.vue')) {
                // console.log()
                // console.log(`Async-Import-Checks: ${id}`)

                newCode = newCode.replace(/import\s+(\S+)\s+from\s+(['"].+\.vue['"])/g, (match, importName, importPath) => {
                    // console.log(`\t${importName} ✅ `)

                    return `const ${importName} = defineAsyncComponent(() => import('${importPath}'))`;
                });
            }

            return {
                newCode,
                map: null
            };
        },
    }
}