{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "VITE_BUILD_GIT_SHORT_SHA=$(git show -s --format='%h') VITE_BUILD_DATE=$(date -Iseconds) vite --host --mode dev", "dev-renaldo": "VITE_BUILD_GIT_SHORT_SHA=$(git show -s --format='%h') VITE_BUILD_DATE=$(date -Iseconds) VITE_FORCED_WHITE_LABEL_CONTEXT=renaldo vite --host --mode dev", "dev-windows": "vite --host --mode dev", "build": "vue-tsc --noEmit && printenv && vite build --mode integ", "serve": "serve dist", "build-dev": "vue-tsc --noEmit && printenv && vite build --mode dev", "build-live": "vue-tsc --noEmit && printenv && vite build --mode live", "preview": "vite preview --port 3000", "lint": "eslint . --fix --ignore-path .gitignore"}, "dependencies": {"@apollo/client": "==3.13.8", "@date-io/dayjs": "==3.2.0", "@fontsource/montserrat": "==5.1.1", "@fontsource/roboto": "==5.1.1", "@mdi/js": "==7.4.47", "@mdi/svg": "==7.4.47", "@turf/turf": "==7.2.0", "@vue/apollo-composable": "==4.2.1", "@vue/apollo-option": "==4.2.0", "@vueuse/core": "==12.7.0", "@vueuse/router": "==12.7.0", "animejs": "==3.2.2", "apollo-link-logger": "==2.0.1", "apollo-link-timeout": "==5.0.0", "apollo3-cache-persist": "==0.15.0", "async-mutex": "==0.5.0", "chart.js": "==4.4.8", "clipper2-js": "==1.2.4", "compressorjs": "==1.2.1", "concaveman": "==1.2.1", "core-js": "==3.40.0", "crypto-js": "==4.2.0", "d3": "==7.9.0", "dayjs": "==1.11.13", "debounce": "==2.2.0", "dexie": "==4.0.11", "earcut": "==3.0.1", "flatbush": "==4.4.1", "graphql": "==16.10.0", "graphql-ws": "==6.0.4", "js-confetti": "==0.12.0", "jspdf": "==3.0.1", "keycloak-js": "==26.2.0", "klaro": "==0.7.21", "leaflet": "==1.9.4", "planar-face-discovery": "==2.0.7", "seedrandom": "==3.0.5", "svg2pdf.js": "==2.5.0", "three": "==0.178.0", "three-bvh-csg": "==0.0.17", "three-mesh-bvh": "==0.9.1", "throttle-debounce": "==5.0.2", "uuid": "==11.1.0", "vue": "==3.5.13", "vue-chartjs": "==5.3.2", "vue-i18n": "==11.1.1", "vue-router": "==4.5.0", "vuetify": "==3.7.13", "web-ifc": "==0.0.68"}, "devDependencies": {"@eslint/js": "==9.21.0", "@graphql-codegen/cli": "==5.0.5", "@intlify/unplugin-vue-i18n": "==6.0.3", "@rollup/pluginutils": "==5.1.4", "@tsconfig/node23": "==23.0.2", "@types/animejs": "==3.1.13", "@types/concaveman": "==1.1.6", "@types/crypto-js": "==4.2.2", "@types/d3": "==7.4.3", "@types/debounce": "==1.2.4", "@types/earcut": "==3.0.0", "@types/flatbush": "==4.2.2", "@types/geojson": "==7946.0.16", "@types/leaflet": "==1.9.16", "@types/node": "==24.0.13", "@types/seedrandom": "==3.0.8", "@types/three": "==0.178.1", "@types/throttle-debounce": "==5.0.2", "@vitejs/plugin-vue": "==5.2.1", "@vue/eslint-config-typescript": "==14.4.0", "@vue/tsconfig": "==0.7.0", "eslint": "==9.21.0", "eslint-plugin-vue": "==9.32.0", "rollup-plugin-copy": "==3.5.0", "rollup-plugin-visualizer": "==5.14.0", "sass": "==1.85.1", "typescript": "==5.8.3", "vite": "==6.2.0", "vite-plugin-pwa": "==0.21.1", "vite-plugin-vuetify": "==2.1.0", "vue-tsc": "==2.2.4"}}