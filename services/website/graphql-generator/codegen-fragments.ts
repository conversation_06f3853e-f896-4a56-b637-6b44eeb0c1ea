import type {CodegenConfig} from '@graphql-codegen/cli';

const config: CodegenConfig = {
    overwrite: true,
    schema: [
        {
            '../../../api/graphql/schema/**/*.graphqls': {
                commentDescriptions: true,
                assumeValidSDL: true,
            },
        },
    ],
    documents: '../src/**/*.gql',
    ignoreNoDocuments: true,
    debug: true,
    verbose: true,
    generates: {
        '../src/adapter/graphql/generated/fragmentTypes.json': {
            plugins: [
                'fragment-matcher'
            ]
        },
    },
};

export default config;
