import type {CodegenConfig} from '@graphql-codegen/cli';

const config: CodegenConfig = {
    overwrite: true,
    schema: [
        {
            '../../../api/graphql/schema/**/*.graphqls': {
                commentDescriptions: true,
                assumeValidSDL: true,
            },
        },
    ],
    documents: '../src/**/*.gql',
    ignoreNoDocuments: true,
    debug: true,
    verbose: true,
    generates: {
        '../src/adapter/graphql/generated/': {
            preset: 'client',
            plugins: [
                'typescript-vue-apollo',
            ],
            config: { //
                //https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-vue-apollo
                useTypeImport: true, //
                // skipTypename: true,
                strictScalars: true,
                pureMagicComment: true,
                addDocBlocks: true,
                withCompositionFunctions: true,
                vueCompositionApiImportFrom: 'vue',
                vueApolloComposableImportFrom: 'vue', //
                //https://the-guild.dev/graphql/codegen/plugins/typescript/typescript
                enumsAsTypes: true,
                immutableTypes: true,
            },
        },
    },
};

export default config;
