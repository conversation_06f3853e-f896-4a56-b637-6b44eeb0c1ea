const fs = require('fs');
const path = require('path');

const loadGeneratedPath = path.resolve(__dirname, '../src/adapter/graphql/generated/graphql.ts');

fs.writeFileSync(loadGeneratedPath, fs
    .readFileSync(loadGeneratedPath, 'utf8')
    .replace(/@vue\/composition-api/m, 'vue')
    .replaceAll(/export const [A-Z][a-zA-Z]*Document = \{[^;]+;/mg, '')
    .replaceAll(/export const [A-Z][a-zA-Z_0-9]*FragmentDoc = \{[^;]+;/mg, '')
);
