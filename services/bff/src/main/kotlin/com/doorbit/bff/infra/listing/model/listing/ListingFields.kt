package com.doorbit.bff.infra.listing.model.listing

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.i
import com.doorbit.bff.infra.listing.applicationservice.flowconfig.ListingFlowConfigUtil
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import org.springframework.data.annotation.Transient

class ListingFields(private var fields: MutableSet<ListingField> = mutableSetOf()) {

    /**
     * Adds new fields or updates existing ones.
     * Returns true if the fields have changed.
     */
    fun update(changeSet: Map<FieldName, List<ListingField>>) {
        changeSet.forEach { (fieldName, newValue) ->
            val existingValues = fieldMap().getOrPut(fieldName) { mutableListOf() }.toMutableList()
            if (existingValues.isEmpty()) {
                existingValues.addAll(newValue)
            } else {
                replaceNewValues(existingValues, newValue)
            }

            fieldMap()[fieldName] = existingValues
        }

        fields = updateFields()
        /**
         * Since the filter logic before can remove fields, we need to rebuild the field map to reflect that change as well.
         */
        this.fieldMap = rebuildFieldMap()
    }

    fun clearArrayFieldsWithNames(fieldNames: Collection<FieldName>) {
        val fieldsToRemove = fields.filter { fieldNames.contains(it.fieldName) }
        fields.removeAll(fieldsToRemove)
        this.fieldMap = rebuildFieldMap()
    }

    private fun replaceNewValues(existing: MutableList<ListingField>, newValues: List<ListingField>) {
        newValues.forEach { newValue ->
            val existingField = existing.find { it.index == newValue.index }
            if (existingField != null) existing.remove(existingField)

            existing.add(newValue)
        }
    }

    private fun updateFields(): MutableSet<ListingField> {
        val newFieldsList = fieldMap().values.flatten()
            .distinct()
            .toMutableSet()

        return newFieldsList
    }

    operator fun <T : Any> get(fieldSpec: FieldSpec<T>, index: Int? = null): T? {
        return getOrNull(fieldSpec, index)
    }

    fun <T : Any> getOrNull(fieldSpec: FieldSpec<T>, index: Int? = null): T? {
        val field = fieldMap()[fieldSpec.fieldName] ?: return null

        validateFieldAccess(field, index, fieldSpec)

        val listingField = if (index == null) field.first().value else field.first { it.index == index }.value
        val fieldType = fieldSpec.validationSpec.dataType

        return fieldType.rawToTypedValue(listingField)
    }

    fun <T : Any> getAllOrNull(fieldSpec: FieldSpec<T>): List<T> {
        val field = fieldMap()[fieldSpec.fieldName] ?: return emptyList()

        return field.map { it.value }.map { fieldSpec.validationSpec.dataType.rawToTypedValue(it) }
            .filterNotNull()
    }

    fun isFieldSet(fieldName: FieldName): Boolean = fieldMap().containsKey(fieldName)

    fun isFieldsSet(fieldNames: List<FieldName>? = null, fieldSpecs: List<FieldSpec<*>>? = null): Boolean {
        if (fieldNames != null) {
            return fieldNames.all { isFieldSet(it) }
        }

        if (fieldSpecs != null) {
            return fieldSpecs.all { isFieldSet(it.fieldName) }
        }

        return false
    }

    fun data(): Map<FieldName, List<ListingField>> = fieldMap().toMap()

    /**
     * Removes fields that are invisible according to the flow configuration.
     * Invisible is a kind of validation, making sure that certain fields are only requested by the client if a certain condition is true.
     * Thus, if a field is invisible, it is not needed and should be removed.
     */
    fun deleteInvisibleFields(flowConfig: ListingFlowConfigUtil) {
        val invisibleFields = flowConfig.invisibleFields(fields)

        this.fields = fields.filter { field ->
            if (invisibleFields.contains(field.fieldName)) {
                LOGGER.i { "Field ${field.fieldName} is not visible in flow and will be removed.\nOther fields: $fields" }
                false
            } else true
        }.toMutableSet()
        this.fieldMap = rebuildFieldMap()
    }

    fun fieldMap(): MutableMap<FieldName, List<ListingField>> {
        if (fieldMap == null) {
            this.fieldMap = rebuildFieldMap()
        }

        return fieldMap!!
    }

    private fun rebuildFieldMap(): MutableMap<FieldName, List<ListingField>> {
        return fields.groupBy { it.fieldName }.toMutableMap()
    }

    @Transient
    private var fieldMap: MutableMap<FieldName, List<ListingField>>? = null

    private fun <T : Any> validateFieldAccess(field: List<ListingField>, index: Int?, fieldSpec: FieldSpec<T>) {
        check(field.size == 1 || index != null) { "Field $fieldSpec is an array field and requires giving an index on selection" }
    }

    fun isFieldSetByUserInput(fieldName: FieldName): Boolean {
        return fields.any { it.fieldName == fieldName && it.isUserInput == true }
    }

    companion object : WithLogger()
}
