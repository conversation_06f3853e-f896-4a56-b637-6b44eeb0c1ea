package com.doorbit.bff.infra.adapter.contactform

import com.doorbit.bff.core.domain.extension.WithLogger
import com.doorbit.bff.core.domain.extension.e
import com.doorbit.bff.infra.api.dto.ClientDebugInfo
import com.doorbit.bff.infra.api.dto.ContactFormInput
import com.doorbit.bff.infra.api.dto.ContactReason
import com.sendgrid.Client
import com.sendgrid.Method
import com.sendgrid.Request
import com.sendgrid.SendGrid
import com.sendgrid.helpers.mail.Mail
import com.sendgrid.helpers.mail.objects.Content
import com.sendgrid.helpers.mail.objects.Email
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class ContactFormAdapter(
    @Value(value = "\${application.mailingservice.api-key}") private val apiKey: String,
    @Value(value = "\${application.mailingservice.contact-form-email}") private val feedbackEmail: String,
) {
    private val apiClient = SendGrid(a<PERSON><PERSON><PERSON>, <PERSON>lient())

    fun sendContactMessage(contactFormInput: ContactFormInput, clientDebugInfo: ClientDebugInfo) {
        val content = """
            Hallo Doorbit Team,
                
            über das Kontaktformular wurde folgende Anfrage übermittelt:
                
            ${contactFormInput.message}
            ${nameAndEmail(contactFormInput)}
                
            Hier noch ein paar Debug-Informationen:
            $clientDebugInfo
        """.trimIndent()

        sendMail(apiClient, feedbackEmail, feedbackEmail, subject(contactFormInput), content)
    }

    fun subject(contactFormInput: ContactFormInput): String =
        "${contactReason(contactFormInput)} von ${contactFormInput.name}"

    companion object : WithLogger() {
        fun contactReason(contactFormInput: ContactFormInput): String =
            when (contactFormInput.reason) {
                ContactReason.FEEDBACK -> "Feedback"
                ContactReason.SUPPORT -> "Support-Anfrage"
                ContactReason.BUSINESS -> "Business-Anfrage"
                ContactReason.GENERAL -> "Allgemeine Anfrage"
                ContactReason.OTHER -> "Sonstige Anfrage"
            }

        fun nameAndEmail(contactFormInput: ContactFormInput): String =
            """
            Folgende Kontaktdaten wurden vom Nutzer angegeben:
            Name: ${contactFormInput.name}
            Email: ${contactFormInput.email}
            """

        fun sendMail(apiClient: SendGrid, fromEmail: String, toEmail: String, subject: String, content: String) {
            val sendgridMail = Mail(
                Email(fromEmail),
                subject,
                Email(toEmail),
                Content(
                    "text/plain",
                    content
                )
            )

            try {
                val request = Request()

                request.method = Method.POST
                request.endpoint = "mail/send"
                request.body = sendgridMail.build()

                apiClient.api(request)
            } catch (e: Exception) {
                LOGGER.e(e) { "Error while sending email with SendGrid." }
                throw e
            }
        }
    }
}