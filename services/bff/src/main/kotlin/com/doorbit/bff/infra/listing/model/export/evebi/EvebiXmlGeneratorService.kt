package com.doorbit.bff.infra.listing.model.export.evebi

import com.doorbit.bff.core.domain.model.user.profile.*
import com.doorbit.bff.infra.adapter.api.geo.dto.AdminBoundaryDto
import com.doorbit.bff.infra.listing.model.export.evebi.EvebiXmlHelper.createDummyTreeViewState
import com.doorbit.bff.infra.listing.model.export.evebi.EvebiXmlHelper.createGuid
import com.doorbit.bff.infra.listing.model.export.evebi.model.EveFloat
import com.doorbit.bff.infra.listing.model.export.evebi.model.EveInteger
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAdresse
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAusstattungsstandard.AS_GUT
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAusstellgrund
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoBoolean
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTDenkmalArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTFeuchteAnforderung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTLCAKlasse
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTLuftVerbundFunktion
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTSchlagregengruppe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoEnumTSchwere
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoInteger
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoSingle
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutoString
import com.doorbit.bff.infra.listing.model.export.evebi.model.TAutomationEveFloatArr
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBatterieDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBaubegleitung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBearbeitung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBegFoerderData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBeleuchtArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBerechVerfahren.PBV_DIN_18599
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBilderStreamListe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBtlDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TBundesland
import com.doorbit.bff.infra.listing.model.export.evebi.model.TDIBtKontrolldatei
import com.doorbit.bff.infra.listing.model.export.evebi.model.TDachKurz
import com.doorbit.bff.infra.listing.model.export.evebi.model.TDenaModus
import com.doorbit.bff.infra.listing.model.export.evebi.model.TDenkmalArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEALueftungSet
import com.doorbit.bff.infra.listing.model.export.evebi.model.TETraeger
import com.doorbit.bff.infra.listing.model.export.evebi.model.TETraegerKat
import com.doorbit.bff.infra.listing.model.export.evebi.model.TETraegerNutzSet
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEbbKomponenteList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEingabe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEnergieausweisData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TEnevVersion.GEG_2024
import com.doorbit.bff.infra.listing.model.export.evebi.model.TFeuchteAnforderung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGebaeudeArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGebaeudeLage
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGebaeudeText
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGebaeudeTexte
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGebaeudeteil
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGegHzData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGeschoss
import com.doorbit.bff.infra.listing.model.export.evebi.model.TGeschossList
import com.doorbit.bff.infra.listing.model.export.evebi.model.THauptnutzungslist
import com.doorbit.bff.infra.listing.model.export.evebi.model.THdrData
import com.doorbit.bff.infra.listing.model.export.evebi.model.THeizkoerperDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.THeizlastData
import com.doorbit.bff.infra.listing.model.export.evebi.model.THydrAbgleichDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.THzErzDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.THzStrDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TImportSourceType
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKErzDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKaelteBereichDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKaelteSpeicherDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKaelteVerteilerDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKalkLeistungList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKalkSpesenList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKalkulation
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKellerKurz
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKellerKurz.KK_KEINKELLER
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKellerKurz.KK_KELLER_BEH
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKellerKurz.KK_KELLER_TEILW_BEH
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKellerKurz.KK_KELLER_UNBEH
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKfWEffHaus
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKfWEffHausData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKfWFoerderArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKfWKreditArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKlimaData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TKuehlungDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCABerechnungsweg
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAEEKAufzug
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAKlasse
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatFugenband
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatInnentuer
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatLeitDaemm
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatLeitung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatLuftkanal
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatSchornstein
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCAMatTreppe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLCANutzKatAufzug
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLKGrundprinzip
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLampeData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLampeDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLampenArt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLastProfilDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLeerstandsart
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLichtData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLichtDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLichtLstgVerfahren
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLtgDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLueftKonzeptDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLuftDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TLuftVerbundFunktion
import com.doorbit.bff.infra.listing.model.export.evebi.model.TManAutoBoolean
import com.doorbit.bff.infra.listing.model.export.evebi.model.TMassnListe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TModernisierungList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TNHNachweis
import com.doorbit.bff.infra.listing.model.export.evebi.model.TNotizList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TNutzungData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TOBDSonderelemente
import com.doorbit.bff.infra.listing.model.export.evebi.model.TOekoBauList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TPVDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TPrjBrStList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TProjekt
import com.doorbit.bff.infra.listing.model.export.evebi.model.TProjektManagement
import com.doorbit.bff.infra.listing.model.export.evebi.model.TProjektSettings
import com.doorbit.bff.infra.listing.model.export.evebi.model.TRLTDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TRaumData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TRaumDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TRelistungsart
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSFPETraeger
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSanierungsfahrplan
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSchlagregengruppe.SRG_MITTEL
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSchlagregengruppe.SRG_SCHWACH
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSchwere
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSectionBearbeiter
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSectionBrowser
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSectionRaumbuchGroessen
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSectionReport
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSommerWSDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TSpeicherDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TTAB
import com.doorbit.bff.infra.listing.model.export.evebi.model.TTWErzDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TTWHeizlastDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TTWStrDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TTageslichtKontrolle
import com.doorbit.bff.infra.listing.model.export.evebi.model.TVariantenListe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TVariantenListeID
import com.doorbit.bff.infra.listing.model.export.evebi.model.TVerbrauchsListe
import com.doorbit.bff.infra.listing.model.export.evebi.model.TVorschaltGeraete
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWBrueckDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWbrVerfAttributSet
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWbrVerfahren
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWbrVerfahrenData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWindDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWohnNutzung
import com.doorbit.bff.infra.listing.model.export.evebi.model.TWohnungDataList
import com.doorbit.bff.infra.listing.model.export.evebi.model.TZoneData
import com.doorbit.bff.infra.listing.model.export.evebi.model.TZoneDataList
import com.doorbit.bff.infra.listing.model.listing.Listing
import com.doorbit.bff.infra.listing.model.listing.building.Floor
import com.doorbit.bff.infra.listing.model.listing.building.FloorLevelType
import com.doorbit.bff.infra.listing.model.listing.building.Room
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec
import com.doorbit.bff.infra.listing.model.listing.fields.RoomCategory
import com.doorbit.bff.infra.listing.model.listing.fields.SubType
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatter.ISO_LOCAL_DATE

private const val EVEBI_VERSION = "13.4.6"

/**
 * Not a Spring Bean
 */
class EvebiXmlGeneratorService(
    private val listing: Listing,
    private val userProfile: UserProfile,
    private val adminBoundaries: List<AdminBoundaryDto>,
    private val idGenerator: EvebiIdGenerator = EvebiIdGenerator()
) {

    private val zoneHeated = createWohnraumZone()
    private var floorCache = mapOf<String, Pair<TGeschoss, Floor>>()
    private var roomCache = mapOf<String, Pair<TRaumData, Room>>()

    fun generateXml(): TProjekt {
        return TProjekt().apply {
            xsdVersion = "2024-02-14"
            guid = createGuid()
            createProgVer = EVEBI_VERSION
            programmVersion = EVEBI_VERSION
            eing = createEingabe()
            bilderStreamsListe = createBilderStreamsListe()
            massnahmenListe = createMassnahmenListe()
            varDefinitionenListe = createVarDefinitionenListe()
            settings = createSettings()
            isReadOnly = false
            management = createManagement()
            hdr = createHdr()
        }
    }

    private fun createHdr(): THdrData {
        return THdrData().apply {
            guid = createGuid()
            isDemoMode = false
            klima = TKlimaData().apply {
                guid = createGuid()
                legStationGruppe = 0.toEveInteger()
                legStation = 52.toEveInteger()
                legStationPlz = (listing.fields.getOrNull(FieldSpec.AddressZipcodeField) ?: "")
                legStationHoehe = 158.0.toEveFloat("m")
                wetterOrt = (listing.fields.getOrNull(FieldSpec.AddressCityField) ?: "")
                heizperiode = 252.toEveInteger("d/a")
                aussenTemp = 5.0.toEveFloat("°C")
                tiefstTemp = (determineMinTemp() * -1).toEveFloat("°C")
                sxKlimaIndex = 1.toAutoInteger(auto = true, setToCalc = true)
                windfaktorLEG = 1.0.toEveFloat("-")
                schlagregengruppe = TAutoEnumTSchlagregengruppe().apply {
                    isAuto = true
                    value = SRG_MITTEL
                    calc = SRG_MITTEL
                    man = SRG_SCHWACH
                }
            }
            gebaeudeKategorie = 0.toEveInteger()
            berechVerfahren = PBV_DIN_18599
            isLuftdichtheit = listing.fields.getOrNull(FieldSpec.IsBuildingTightField) ?: listing.fields.getOrNull(FieldSpec.IsBlowerDoorTestedField) ?: false
            schweresGeb = TSchwere.BTS_LEICHT
            isNichtWohngeb = false
            isOeffentlich = false
            isKommunal = false
            isNeubau = false
            isErweiterungsBau = false
            isFlGrundwasser = false
            grundwasserTiefe = 10.0.toAutoSingle(auto = true, setToCalc = true)
            isNurVerbrAusweis = false
            isDatenErfasserEigentuemer = false
            isDatenErfasserAussteller = true
            enevVersion = GEG_2024
            isSommerErfuelltMan = false
            isSommerErfuelltAuto = true
            personen = 1.toAutoInteger(unit = "Personen", auto = true, setToCalc = true)
            wetterOrt = (listing.fields.getOrNull(FieldSpec.AddressCityField) ?: "").toAutoString(auto = true, setToCalc = true)
            heizPeriode = 252.toAutoInteger(unit = "d/a", auto = true, setToCalc = true)
            aussenTemp = 5.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
            tiefstTemp = (determineMinTemp() * -1).toAutoSingle(unit = "°C", auto = true, setToCalc = true)
            gebaeudeArt = determineGebaeudeArt()
            lage = determinePopulationDensity()
            exposition = 0.toEveInteger()
            objektKategorie = 0.toEveInteger()
            bundesland = determineBundesland()
            gemark = ""
            flur = ""
            isHat100TsdEw = determineHat100TsdEinwohner()
            beobEVerbrauch = 0.0.toAutoSingle(unit = "kWh/a", auto = true, setToCalc = true)
            beobEVerbrauchKosten = 0.0.toAutoSingle(unit = "€/a", auto = true, setToCalc = true)
            beobEVerbrauchBegruendung = "Verbrauchsmessung liegt nicht vor".toAutoString(auto = true, setToCalc = true)
            wwBedarf = 0.0.toAutoSingle(unit = "l/d", auto = true, setToCalc = true)
            elektrisch = 0.0.toEveFloat("kWh/Personen*a")
            hitzewirkung = 0.0.toEveFloat("-")
            personenAbwaerme = 100.0.toEveFloat("W")
            personenAufenthalt = 0.0.toEveFloat("Personen")
            kwBedarfpP = 0.0.toEveFloat("l/Person*d")
            wwBedarfpP = 0.0.toEveFloat("l/Person*d")
            nachtabsenkungFuer = 0.toEveFloat("h/d")
            nachtabsenkungUm = 0.0.toEveFloat("K")
            bjBrennerTausch = brennerBaujahr()
            bjFensterAustausch = fensterBaujahr()
            isKellerBeheizt = false
            daemmstandard = 0.toEveInteger()
            nachtraeglDaemmungKeller = 0.toEveFloat("cm") // TODO haben wir auch
            nachtraeglDaemmungDach = 0.toEveFloat("cm")
            nachtraeglDaemmungWand = 0.toEveFloat("cm")
            nachtraeglDaemmungFenster = 0.toEveFloat("cm")
            baujahr = (listing.fields.getOrNull(FieldSpec.YearOfConstructionField)?.value?.year ?: 2025).toEveInteger()
            sanierungsjahr = 0.toEveInteger() // TODO Sanierungsjahr
            rechtsstand = null
            lg = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            bg = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            geschosse = 0.toAutoInteger(unit = "Geschosse", auto = true, setToCalc = true)
            vollgeschosse = 0.toAutoInteger(unit = "Vollgeschosse", auto = true, setToCalc = true)
            we = 1.toAutoInteger(unit = "Wohneinheiten", auto = false, setToCalc = false) // <!-- Wohneinheiten -->
            weSelbstgenutzt = 1.toAutoInteger(unit = "Wohneinheiten", auto = false, setToCalc = false) // <!-- Wohneinheiten -->
            n50 = 4.0.toAutoSingle(unit = "l/h", auto = true, setToCalc = true) // <!-- Luftwechselrate -->
            tNutzD = 0.0.toEveFloat(unit = "h/d") // Hint: durchschnittliche Aufenthaltsdauer jeder Person
            dNutzA = 0.0.toEveInteger(unit = "d/a")
            isEinZonenModell = false
            isTeilbeheizung = false
            serverraum = 0.0.toEveFloat("m²")
            kuehlraum = 0.0.toEveFloat("m²")
            isKurzVerfahren = false
            aktenZeichen = ""
            objektAdr = mapObjektAdresse()
            kundenAdr = mapObjektAdresse()
            planerAdr = mapPlanerAdresse()
            ausstattung = AS_GUT
            isDenkmal = false // TODO
            isSichtFachwerk = false
            windfaktorLEG = 1.0.toEveFloat("-")
            standortHoehe = 46.7.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            investition = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            enevRegistriernummer = ""
            eaKontrolldatei = TDIBtKontrolldatei.DKD_NONE
            nutzungenListe = THauptnutzungslist().apply {
                guid = createGuid()
            }
            eaLueftungSet = TEALueftungSet().apply {
            }
            eavKuehlFlaeche = 0.0.toEveFloat("m²")
            isEavHatTwSolar = false // TODO
            isEavHatHzSolar = false // TODO
            isEavHatTwWaermepumpe = false // TODO
            isEavHatHzWaermepumpe = false // TODO
            wfl = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            technikflaeche = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            verkehrsflaeche = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            gesamtNRF = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            beheiztNRF = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            dachAF = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            bgf = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true) // Wird automatisch berechnet solange auto an ist
            bri = 0.0.toAutoSingle(unit = "m³", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            ve = 0.0.toAutoSingle(unit = "m³", auto = true, setToCalc = true)  // Wird automatisch berechnet solange auto an ist
            isUseRaeumeFuerVe = true
            raumHoehe = listing.building!!.averageRoomHeight().toAutoSingle(unit = "m", auto = true, setToCalc = true)
            isBodenplatteNachDIN13370 = false
            isHTuNachDIN13789 = false
            isHeiztageBerechnen = false
            isMitOpak = false
            isVereinfFensteraufmass = false
            isVereinfTuer = false
            isVereinfRollladen = false
            isVereinfVorspruenge = false
            isVereinfTreppenabgang = false
            isVereinfTreppenaufgang = false
            isVereinfHKN = false
            isVereinfLueftungsschacht = false
            isVereinfMarginal = false
            isVereinfOrient = false
            isVereinfNeig = false
            isVereinfUWerte = false
            dachKurz = TDachKurz.DK_DACH_UNBEH // TODO
            kellerKurz = determineKellerKurz()
            isGaubenVorhanden = false
            kfwDatumFoerderAntrag = LocalDate.now().format(ISO_LOCAL_DATE)
            bafaDatumFoerderAntrag = LocalDate.now().format(ISO_LOCAL_DATE)
            haushaltsEinkommen = "HEK_MAX"
            isHatiSFP = false
            isRollstuhlgerecht = false
            rolladenhoeheStd = 0.25.toEveFloat(unit = "m")
            drempelhoeheStd = 1.0.toEveFloat(unit = "m")
            heizkoerpernischenhoeheStd = 0.85.toEveFloat(unit = "m")
            wsv95 = false.toAutoBoolean(auto = true, setToCalc = true)
            id = 42.toEveInteger()
            name = (listing.fields.getOrNull(FieldSpec.ExternalIdField) ?: listing.createAddressString()).toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {// TODO <!--Hier müssen die Bilder rein, die im Workflow als unter Cover Foto und Gebäudefotos aufgenommen worden sind-->
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
        }
    }

    private fun determineHat100TsdEinwohner(): Boolean {
        val populationBoundary = adminBoundaries.find { it.population != null } ?: return true
        return (populationBoundary.population?.populationTotal ?: 100_001) > 100_000
    }

    private fun determineBundesland(): TBundesland {
        val stateBoundary = adminBoundaries.find { it.type == "STATE" } ?: return TBundesland.BL_NONE
        return when (stateBoundary.name.lowercase()) {
            "baden-württemberg" -> TBundesland.BL_BW
            "bayern" -> TBundesland.BL_BY
            "berlin" -> TBundesland.BL_BE
            "brandenburg" -> TBundesland.BL_BB
            "bremen" -> TBundesland.BL_HB
            "hamburg" -> TBundesland.BL_HH
            "hessen" -> TBundesland.BL_HE
            "mecklenburg-vorpommern" -> TBundesland.BL_MV
            "niedersachsen" -> TBundesland.BL_NI
            "nordrhein-westfalen" -> TBundesland.BL_NW
            "rheinland-pfalz" -> TBundesland.BL_RP
            "saarland" -> TBundesland.BL_SL
            "sachsen" -> TBundesland.BL_SN
            "sachsen-anhalt" -> TBundesland.BL_ST
            "schleswig-holstein" -> TBundesland.BL_SH
            "thüringen" -> TBundesland.BL_TH
            else -> TBundesland.BL_NONE
        }

    }

    private fun determinePopulationDensity(): TGebaeudeLage {
        val adminBoundary = adminBoundaries.find { it.population != null } ?: return TGebaeudeLage.GEBL_INNERORTS
        return when (adminBoundary.population!!.populationGroupType) {
            "HAMLET", "VILLAGE" -> TGebaeudeLage.GEBL_AUSSERORTS
            else -> TGebaeudeLage.GEBL_INNERORTS
        }
    }

    private fun determineKellerKurz(): TKellerKurz {
        val floors = Floors(listing.building!!.floors)
        if (!floors.hasCellarFloor()) return KK_KEINKELLER

        val cellarFloor = floors.cellarFloor()
        val unheatedRooms = cellarFloor.rooms.count { !it.isHeated() }
        val heatedRooms = cellarFloor.rooms.count { it.isHeated() }
        return if (unheatedRooms == 0) {
            KK_KELLER_BEH
        } else if (heatedRooms != 0) {
            KK_KELLER_TEILW_BEH
        } else {
            KK_KELLER_UNBEH
        }
    }

    private fun fensterBaujahr(): EveInteger {
        return listing.fields.getOrNull(FieldSpec.WindowYearOfConstructionPeriodField)?.averageYear()?.let {
            return it.toEveInteger()
        } ?: (listing.fields.getOrNull(FieldSpec.YearOfConstructionField)?.value?.year ?: 1900).toEveInteger()
    }

    /**
     * Entweder das ist gepflegt oder wir gehen von 10 Jahre alte Heizanlage aus
     */
    private fun brennerBaujahr(): EveInteger {
        return ((listing.fields.getOrNull(FieldSpec.HeatGeneratorYearOfConstructionField)?.value?.year ?: (LocalDateTime.now().year - 10))).toEveInteger()
    }

    private fun determineGebaeudeArt(): TGebaeudeArt {
        val subType = listing.fields.getOrNull(FieldSpec.SubTypeField) ?: return TGebaeudeArt.GEBV_FREISTEHEND

        return when (subType) {
            SubType.DETACHED -> TGebaeudeArt.GEBV_FREISTEHEND
            SubType.TOWNHOUSE_MIDDLE -> TGebaeudeArt.GEBV_REIHENBEBAUUNG
            SubType.TOWNHOUSE_END -> TGebaeudeArt.GEBV_ANGEBAUT
            else -> TGebaeudeArt.GEBV_FREISTEHEND // Illegal
        }
    }

    private fun determineMinTemp(): Double {
        val latitude = listing.fields.getOrNull(FieldSpec.AddressLongitudeField) ?: return 12.0

        return when {
            latitude < 45 -> 0.0 // Alles unter Parma friert es so gut wie nicht mehr
            latitude < 50 -> 12.0 // Kassel bis München
            latitude < 55 -> 8.0 // Flensburg bis Kassel
            latitude > 55 -> 12.0 // Flensburg und nördlich davon
            else -> 8.0
        }
    }

    private fun mapObjektAdresse(): TAdresse {
        val street = listing.fields.getOrNull(FieldSpec.AddressStreetField) ?: ""
        val number = listing.fields.getOrNull(FieldSpec.AddressHouseNumberField) ?: ""
        val zip = listing.fields.getOrNull(FieldSpec.AddressZipcodeField) ?: ""
        val city = listing.fields.getOrNull(FieldSpec.AddressCityField) ?: ""
        val lat = listing.fields.getOrNull(FieldSpec.AddressLatitudeField) ?: 0.0
        val lon = listing.fields.getOrNull(FieldSpec.AddressLongitudeField) ?: 0.0

        return TAdresse().apply {
            guid = createGuid()
            anrede = ""
            titel = ""
            firma = ""
            firma2 = ""
            vorname = ""
            nachname = ""
            briefanrede = "Sehr geehrte Damen und Herren".toAutoString(auto = true, setToCalc = false)
            plz = zip
            ort = city
            strasse = street
            hausnr = number
            telefon = ""
            land = "Deutschland"
            telefax = ""
            eMail = ""
            www = ""
            beruf = ""
            bafaBeraterNr = ""
            latitude = lat.toEveFloat(unit = "°")
            longitude = lon.toEveFloat(unit = "°")
            memo = ""
        }
    }

    private fun mapPlanerAdresse(): TAdresse {
        val companyName = userProfile.additionalFields?.get(CompanyNameField) ?: ""
        val firstname = userProfile.firstName ?: ""
        val lastName = userProfile.lastName ?: ""
        val profession = userProfile.additionalFields?.get(ProfessionField) ?: ""
        val beraterNummer = userProfile.additionalFields?.get(ConsultantNumberField) ?: ""

        val address = userProfile.additionalFields?.get(CompanyAddressField) ?: emptyMap()
        val street = address["street"] ?: ""
        val number = address["houseNumber"] ?: ""
        val zip = address["zipCode"] ?: ""
        val city = address["city"] ?: ""
        val country = address["country"] ?: ""

        val contactInformation = userProfile.additionalFields?.get(ContactInformationField)

        val phoneNumber = findFieldValue(contactInformation, "TELEPHONE")
        val email = findFieldValue(contactInformation, "EMAIL")
        val website = findFieldValue(contactInformation, "WEBSITE")

        return TAdresse().apply {
            guid = createGuid()
            anrede = ""
            titel = ""
            firma = companyName
            firma2 = ""
            vorname = firstname
            nachname = lastName
            briefanrede = "Sehr geehrte Damen und Herren".toAutoString(auto = true, setToCalc = false)
            plz = zip
            ort = city
            strasse = street
            hausnr = number
            telefon = phoneNumber
            land = country
            telefax = ""
            eMail = email
            www = website
            beruf = profession
            bafaBeraterNr = beraterNummer
            latitude = 0.toEveFloat(unit = "°") // TODO geolocation
            longitude = 0.toEveFloat(unit = "°")
            memo = ""
        }
    }

    private fun findFieldValue(contactInformation: List<Map<String, String?>>?, fieldName: String): String? {
        return contactInformation?.firstOrNull { it["contactType"] == fieldName }?.get("contactValue")
    }

    private fun createManagement(): TProjektManagement {
        return TProjektManagement().apply {
            guid = createGuid()
            kalk = TKalkulation().apply {
                guid = createGuid()
                we = 1.toEveInteger()
                gesZonen = 0.toEveInteger()
                rltZonen = 0.toEveInteger()
                ngf = 0.toEveInteger()
                frei1 = 0.0000000.toEveFloat("-")
                frei2 = 0.0000000.toEveFloat("-")
                leistungenListe = TKalkLeistungList().apply {
                    guid = createGuid()
                }
                spesenListe = TKalkSpesenList().apply {
                    guid = createGuid()
                }
                regionID = 0.toEveInteger()
                aufwandID = 0.toEveInteger()
                druckDatum = "1899-12-30T00:00:00"
                rabatt = 0.0000000.toEveFloat("%")
                isBefristet = false
                freitext = ""
            }
            notizenListe = TNotizList().apply {
                guid = createGuid()
            }
        }
    }

    private fun createSettings(): TProjektSettings {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
        val currentTime = ZonedDateTime.now(ZoneId.of("CET")).format(formatter)

        return TProjektSettings().apply {
            guid = createGuid()
            bearbeiterListe = TSectionBearbeiter().apply {
                guid = createGuid()
                item = listOf(
                    TBearbeitung().apply {
                        guid = createGuid()
                        username = "-"
                        startTime = currentTime
                        endTime = currentTime
                    }
                )
            }
            explGebaeude = createDummyTreeViewState()
            explVarianten = createDummyTreeViewState()
            explBeratung = createDummyTreeViewState()
            explErgebnisse = createDummyTreeViewState()
            explMassnahmen = createDummyTreeViewState()

            letzterReiter = TTAB.TAB_IST
            raumbuch = TSectionRaumbuchGroessen().apply {
                guid = createGuid()
                raumBreite = 0.0.toEveFloat("m")
                raumTiefe = 0.0.toEveFloat("m")
                raumHoehe = 0.0.toEveFloat("m")
                fensterBreite = 1.23.toEveFloat("m")
                fensterHoehe = 1.48.toEveFloat("m")
                fensterBruest = 0.8.toEveFloat("m")
                richtungErste = 0.toEveInteger("°")
                isBruttoBezug = false
                wandDicke = 0.36.toEveFloat("m")
                deckenDicke = 0.24.toEveFloat("m")
            }
            browser = TSectionBrowser().apply {
                guid = createGuid()
                konstrSpalten = 0.toEveInteger()
                qualitaetHuelle = 0.toEveInteger()
                qualitaetAnlage = 0.toEveInteger()
            }
            report = TSectionReport().apply {
                guid = createGuid()
                anzahlZeilenNWGDoku = 0.toEveInteger()
                isZustaende = false
                isKapitel = false
                isVariablen = false
                isBilderAbfrage = false
                isReport = false
            }
        }
    }

    private fun createVarDefinitionenListe(): TVariantenListe {
        return TVariantenListe().apply {
            guid = createGuid()
            zielvarianteID = ""
        }
    }

    private fun createMassnahmenListe(): TMassnListe {
        return TMassnListe().apply {
            guid = createGuid()
        }
    }

    private fun createBilderStreamsListe(): TBilderStreamListe {
        return TBilderStreamListe().apply {
            guid = createGuid()
        }
    }

    private fun createEingabe(): TEingabe {
        val bauteilGenerator = BauteilEvexGenerator(listing, idGenerator) { roomCache }
        return TEingabe().apply {
            guid = createGuid()
            eTraegerListe = createETraegerListe()
            zdListe = createZdListe()
            weListe = TWohnungDataList().apply {
                guid = createGuid()
            }
            lueftKonzeptListe = TLueftKonzeptDataList().apply {
                guid = createGuid()
            }
            geschosseListe = createGeschosseListe()
            rmListe = createRoomListe()
            lichtListe = TLichtDataList().apply {
                guid = createGuid()
                item.add(createSingleLichtItem(zoneHeated.guid))
            }
            konstruktionenListe = bauteilGenerator.createConstructionListe()
            konstrFensterListe = bauteilGenerator.createConstructionFensterListe()
            btlListe = bauteilGenerator.createBauteilListe(zoneHeated.guid)
            nBtlListe = TBtlDataList().apply {
                guid = createGuid()
            }
            tflListe = bauteilGenerator.createTeilflaechenListe()
            wbv = createWbv()
            wbrListe = TWBrueckDataList().apply {
                guid = createGuid()
            }
            ltgListe = TLtgDataList().apply {
                guid = createGuid()
            }
            speicherListeListe = TSpeicherDataList().apply {
                guid = createGuid()
            }
            hzListe = THzStrDataList().apply {
                guid = createGuid()
            }
            hzErzListe = THzErzDataList().apply {
                guid = createGuid()
            }
            twListe = TTWStrDataList().apply {
                guid = createGuid()
            }
            twErzListe = TTWErzDataList().apply {
                guid = createGuid()
            }
            luftListe = TLuftDataList().apply {
                guid = createGuid()
            }
            wksListe = TKuehlungDataList().apply {
                guid = createGuid()
            }
            rltListe = TRLTDataList().apply {
                guid = createGuid()
            }
            kStrListe = TKaelteBereichDataList().apply {
                guid = createGuid()
            }
            kErzListe = TKErzDataList().apply {
                guid = createGuid()
            }
            kVerteilerListe = TKaelteVerteilerDataList().apply {
                guid = createGuid()
            }
            kSpeicherListe = TKaelteSpeicherDataList().apply {
                guid = createGuid()
            }
            pvListe = TPVDataList().apply {
                guid = createGuid()
            }
            windListe = TWindDataList().apply {
                guid = createGuid()
            }
            batterienListe = TBatterieDataList().apply {
                guid = createGuid()
            }
            texte = createTexte()
            gegHz = TGegHzData().apply {
                guid = createGuid()
                erfuelltAnford = TManAutoBoolean().apply {
                    isAuto = true
                    isValue = true
                    isMan = true
                }
            }
            kfW = createKfw()
            kfwBaubegleitung = createKfwBaubegleitung()
            kfW433 = createKfw433()
            heizlastListe = THeizlastData().apply {
                guid = createGuid()
            }
            twHeizlastListe = TTWHeizlastDataList().apply {
                guid = createGuid()
            }
            sommerWSListe = TSommerWSDataList().apply {
                guid = createGuid()
            }
            hydrAbgleichListe = THydrAbgleichDataList().apply {
                guid = createGuid()
                isBerichtEinzelmassnahme = true
            }
            ea = createEa()
            lastprofileListe = TLastProfilDataList().apply {
                guid = createGuid()
            }
            isfp = createIsfp()
            ebb = createEbb()
            lca = createLca()
        }
    }

    private fun createLca(): TLCAData {
        return TLCAData().apply {
            guid = createGuid()
            lcaBerechMode = TLCABerechnungsweg.LCA_QNG
            lcaKlasse = TAutoEnumTLCAKlasse().apply {
                isAuto = true
                value = TLCAKlasse.LCA_NONE
                man = TLCAKlasse.LCA_NONE
                calc = TLCAKlasse.LCA_K_1
            }
            isAbschneideKriterien = false
            isKostenGruppieren = false
            fundamentVolumen = 0.0.toAutoSingle(unit = "m³", auto = true, setToCalc = true)
            fliesenflaeche = 25.0454077.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            sanitaerobjekte = 0.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            wannen = 2.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            treppenhaeuser = 1.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            treppenbreite = 1.1.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            treppeBauart = TLCAMatTreppe.MTREP_BETON
            fenster = 0.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            materialITuer = TLCAMatInnentuer.MITUER_KARTON
            materialFBand = TLCAMatFugenband.MFBAND_POLYURETHAN
            aufzuege = 0.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            aufzugGrundGewicht = 1600.0.toEveFloat(unit = "kg")
            aufzugEtagenGewicht = 330.0.toEveFloat(unit = "kg")
            aufzugNutzKat = TLCANutzKatAufzug.NKAT_NONE
            aufzugEEK = TLCAEEKAufzug.EEK_NONE
            aufzugNennlast = 0.toEveInteger(unit = "kg")
            aufzugGeschwindigkeit = 0.0.toEveFloat(unit = "m/s")
            fahrtreppen = 0.toEveInteger(unit = "Stk")
            fahrtreppGrundGewicht = 5500.0.toEveFloat(unit = "kg")
            fahrtreppEtagenGewicht = 2000.0.toEveFloat(unit = "kg")
            schwachstromanlagen = 0.toEveInteger(unit = "Stk")
            videoanlagen = 0.toEveInteger(unit = "Stk")
            videoflaeche = 0.toEveInteger(unit = "m²")
            hzKMenge = 0.toAutoInteger(unit = "Stk", auto = true, setToCalc = true)
            aFlHz = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            kabelLaenge = 500.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            kaelteMittelMenge = 0.0.toAutoSingle(unit = "kg", auto = true, setToCalc = true)
            schornsteine = 0.toEveInteger(unit = "Stk")
            schornsteinGewicht = 50.0.toEveFloat(unit = "kg")
            schornsteinbauart = TLCAMatSchornstein.MSCH_NONE
            materialHL = TLCAMatLeitung.ML_KUPFER
            materialTWL = TLCAMatLeitung.ML_KUPFER
            materialLL = TLCAMatLuftkanal.MLK_ZINKBLECH
            materialLDaemm = TLCAMatLeitDaemm.MLD_PUR
            btlMitKonstr = false.toAutoBoolean(auto = true, setToCalc = true)
            unbehKeller = true.toAutoBoolean(auto = true, setToCalc = true)
            unbehDach = false.toAutoBoolean(auto = true, setToCalc = true)
            innenBtl = false.toAutoBoolean(auto = true, setToCalc = true)
            aussenElemente = false.toAutoBoolean(auto = true, setToCalc = true)
            flaechenDin277 = true.toAutoBoolean(auto = true, setToCalc = true)
            oekoBauDataListe = TOekoBauList().apply {
                guid = createGuid()
            }
            id = 1.toEveInteger() // TODO
            name = "Projektelement".toAutoString(auto = true, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
        }
    }

    private fun createEbb(): TBaubegleitung {
        return TBaubegleitung().apply {
            guid = createGuid()
            varianteID = ""
            komponentenListe = TEbbKomponenteList().apply { // TODO ?
                guid = createGuid()
            }
            id = 1.toEveInteger() // TODO
            name = "Baubegleitung".toAutoString(auto = true, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
            sourceID = ""
        }
    }

    private fun createIsfp(): TSanierungsfahrplan {
        return TSanierungsfahrplan().apply {
            guid = createGuid()
            schadstellenBilderListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            anschreiben = "heute erhalten Sie Ihren persönlichen Sanierungsfahrplan für Ihr Wohnhaus.\nDer Sanierungsfahrplan wurde erstellt, da Sie im Zuge bevorstehender Reparaturen und damit verbundenen Investitionen an Ihrer Heizung über weitere sinnvolle Maßnahmen informiert werden möchten. Unserem Gespräch konnte ich entnehmen, dass Sie vorrangig an der Verbesserung des Wohnkomforts und einer Verringerung der Heizkosten interessiert sind.\nMit der Entscheidung zur energetischen Sanierung Ihres Zuhauses leisten Sie einen Beitrag zum Einsparen an Energie und an CO₂-Emissionen. Damit haben Sie einen persönlichen Anteil am Gelingen der Energiewende.\nKoppeln Sie die vorgeschlagenen Effizienzmaßnahmen am besten".toAutoString(auto = true, setToCalc = true)
            grussformel = ""
            raumtemp = ""
            anwesenheit = ""
            raumnutzung = ""
            warmwasser = ""
            lueftungsverhalten = ""
            fazit = ""
            svArchitekt = ""
            isUseSvArchitekt = false
            svStatiker = ""
            isUseSvStatiker = false
            svSchornsteinfeger = ""
            isUseSvSchornsteinfeger = false
            svHolzschutzgutachter = ""
            isUseSvHolzschutzgutachter = false
            svFachplHaustechnik = ""
            isUseSvFachplHaustechnik = false
            svSonstiges = ""
            isUseSvSonstiges = false
            ausgangsSituationFuerSanierung = ""
            hausZukunftBeschreibungVorteile = ""
            nutzerHinweise = ""
            heizungsOptimierung = ""
            gebaeudeArt = TAutoEnumTDenkmalArt().apply {
                isAuto = true
                value = TDenkmalArt.GA_NORMAL
                man = TDenkmalArt.GA_NORMAL
                calc = TDenkmalArt.GA_NORMAL
            }
            dachArt = "beheizt bis OGD".toAutoString(auto = true, setToCalc = true)
            kellerArt = "ja / teilbeheizt".toAutoString(auto = true, setToCalc = true)
            sanierungen = ""
            empfehlungen = ""
            schritte = ""
            sachverstaendige = ""
            baulicheInvestGesamt = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            anlagenInvest = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            baulicheSowiesoGesamt = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            anlagenInstandsetzung = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            investitionGesamtsanierungOhneAnu = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            instandsetzungGesamtOhneAnu = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            synergiepauschale = 0.1.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            foerderGesamt = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            wartungIst = 0.0.toAutoSingle(unit = "€/a", auto = true, setToCalc = true)
            wartungGesamt = 0.0.toAutoSingle(unit = "€/a", auto = true, setToCalc = true)
            etrStrom = TSFPETraeger().apply {
                guid = createGuid()
                arbeitspreis = 0.0.toEveFloat(unit = "€/kWh")
            }
            etrIst = TSFPETraeger().apply {
                guid = createGuid()
                arbeitspreis = 0.0.toEveFloat(unit = "€/kWh")
            }
            etrZiel = TSFPETraeger().apply {
                guid = createGuid()
                arbeitspreis = 0.0.toEveFloat(unit = "€/kWh")
            }
            kellerText = "".toAutoString(auto = true, setToCalc = true)
            kellerabgangText = ""
            wandText = "".toAutoString(auto = true, setToCalc = true)
            fensterText = "".toAutoString(auto = true, setToCalc = true)
            dachText = "".toAutoString(auto = true, setToCalc = true)
            heizungText = "".toAutoString(auto = true, setToCalc = true)
            verteilungText = "".toAutoString(auto = true, setToCalc = true)
            warmwasserText = "".toAutoString(auto = true, setToCalc = true)
            lueftungText = "".toAutoString(auto = true, setToCalc = true)
            qeAnmerkung = ""
            isKostenAnzeigen = true
            isKostenNachHandbuch = false
            isBestaetigungRichtigeDaten = false
            isBestaetigungMassnahmen = false
            isBestaetigungAbweichungenBegruendet = false
            schadstellenBilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
        }
    }

    private fun createEa(): TEnergieausweisData {
        return TEnergieausweisData().apply {
            guid = createGuid()
            ausstellgrund = TAusstellgrund.AG_NEUBAU
            variantenListe = TVariantenListeID().apply {
                guid = createGuid()
                zielvarianteID = ""
            }
            varianteID = ""
            ergaenzendeErlaeuterungen = ""
            isMitAushang = true
            isAutoMassnahmen = true
            modernisierungenListe = TModernisierungList().apply {
                guid = createGuid()
            }
            leerstandsart = TLeerstandsart.LSA_KEIN
            isZusatzInfosEnergQualitaet = false
            isErneuerbareEnergienArtAuto = true
            erneuerbareEnergienArt = ""
            isErneuerbareEnergienVerwendungAuto = true
            erneuerbareEnergienVerwendung = ""
            sommerlicherWaermeschutz = false.toAutoBoolean(auto = true, setToCalc = true)
            isVereinfachungenNachP9Abs2 = false
            isVereinfachungenNachP21Abs2 = false
            angabenerhaeltlich = "gebaeudeforum.de".toAutoString(auto = true, setToCalc = true)
            isAngabenerhaeltlichAuto = true
            denaRelistungArtLeistungsnachweis = TRelistungsart.RLA_BAFA
            isDenaFoerderungKfW = false
            isDenaFoederungBAFA = true
            denaIstGuetesiegelAusweis = TDenaModus.DENA_RELISTUNG
            gebaeudeteil = TGebaeudeteil.GT_GANZES_GEB
            baujahr = "".toAutoString(auto = true, setToCalc = true)
            baujahrHzErz = "".toAutoString(auto = true, setToCalc = true)
            ausstelldatum = "2025-01-24"
            passiveKuehlung = false.toAutoBoolean(auto = true, setToCalc = true)
            fernKuehlung = false.toAutoBoolean(auto = true, setToCalc = true)
            stromKuehlung = false.toAutoBoolean(auto = true, setToCalc = true)
            waermeKuehlung = false.toAutoBoolean(auto = true, setToCalc = true)
            kaiAnzahl = 0.toAutoInteger(unit = "Klimaanlagen", auto = true, setToCalc = true)
            kaiDatum = "1899-12-30"
            kaiAnlGroesser12KWohneGA = false
            kaiAnlGroesser12KWmitGA = false
            isStromAufzugEnthalten = false
            isWwSchwimmbadEnthalten = false
        }
    }

    private fun createKfw433(): TBegFoerderData {
        return TBegFoerderData().apply {
            guid = createGuid()
            we = 1.toAutoInteger(unit = "Wohneinheiten", auto = true, setToCalc = true)
            angf = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            isMassnAuto = true
            programmnummer = ""
            kreditArt = TKfWKreditArt.KFW_KA_LZ_10
            foerderArt = TKfWFoerderArt.KF_W_DARLEHEN
            zins = 1.84.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            laufzeit = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            zinsBindung = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            tilgungsfrei = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            forceTilgProz = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            forceTilgProzBonus = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            zuschussProz = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            zuschussProzBonus = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            bonusNH = TNHNachweis.NH_NONE
            bonusEE = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusWPB = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusBiomasse = false.toAutoBoolean(auto = true, setToCalc = true)
            isBonusSerSan = false
            bonusGeschwind = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusEinkommen = false.toAutoBoolean(auto = true, setToCalc = true)
            maxMoeglicheFoerder = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            investition = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
        }
    }

    private fun createKfwBaubegleitung(): TBegFoerderData {
        return TBegFoerderData().apply {
            guid = createGuid()
            we = 1.toAutoInteger(unit = "Wohneinheiten", auto = true, setToCalc = true)
            angf = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            isMassnAuto = true
            programmnummer = ""
            kreditArt = TKfWKreditArt.KFW_KA_LZ_10
            foerderArt = TKfWFoerderArt.KF_W_DARLEHEN
            zins = 1.84.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            laufzeit = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            zinsBindung = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            tilgungsfrei = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            forceTilgProz = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            forceTilgProzBonus = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            zuschussProz = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            zuschussProzBonus = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            bonusNH = TNHNachweis.NH_NONE
            bonusEE = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusWPB = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusBiomasse = false.toAutoBoolean(auto = true, setToCalc = true)
            isBonusSerSan = false
            bonusGeschwind = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusEinkommen = false.toAutoBoolean(auto = true, setToCalc = true)
            maxMoeglicheFoerder = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
        }
    }

    private fun createKfw(): TKfWEffHausData {
        return TKfWEffHausData().apply {
            guid = createGuid()
            forceEffHaus = TKfWEffHaus.KF_W_NONE
            programmnummer = ""
            kreditArt = TKfWKreditArt.KFW_KA_LZ_10
            foerderArt = TKfWFoerderArt.KF_W_DARLEHEN
            zins = 1.84.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            laufzeit = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            zinsBindung = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            tilgungsfrei = 0.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            forceTilgProz = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            forceTilgProzBonus = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            zuschussProz = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            zuschussProzBonus = 0.0.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            bonusNH = TNHNachweis.NH_NONE
            bonusEE = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusWPB = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusBiomasse = false.toAutoBoolean(auto = true, setToCalc = true)
            isBonusSerSan = false
            bonusGeschwind = false.toAutoBoolean(auto = true, setToCalc = true)
            bonusEinkommen = false.toAutoBoolean(auto = true, setToCalc = true)
            maxMoeglicheFoerder = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
            investition = 0.0.toAutoSingle(unit = "€", auto = true, setToCalc = true)
        }
    }

    private fun createTexte(): TGebaeudeTexte {
        return TGebaeudeTexte().apply {
            zusammenfassung = createTextItem("Zusammenfassung")
            sanierung = createTextItem("Sanierung")
            sanierungWeitere = createTextItem("Weitere Sanierung")
            sanierungSchritte = createTextItem("Sanierung in Schritten")
            rechte = createTextItem("Rechtliche Hinweise")
            allgemeines = createTextItem("Allgemeines zum Gebäude")
            nutzung = createTextItem("Nutzung")
            schwachstellen = createTextItem("Schwachstellen")
            keller = createTextItem("Keller")
            dach = createTextItem("Dach")
            wand = createTextItem("Wand")
            fenster = createTextItem("Fenster")
            wbruecken = createTextItem("Wärmebrücken")
            heizung = createTextItem("Heizung")
            trinkwasser = createTextItem("Trinkwasser")
            lueftung = createTextItem("Lüftung")
            everbrauch = createTextItem("Energieverbrauchsdaten")
            binvest = createTextItem("bisherige Investitionen/Umbauten")
            thermografie = createTextItem("Thermografie")
            luftdichtigkeitspruefung = createTextItem("Luftdichtigkeitsprüfung")
            entsorgung = createTextItem("Entsorgung")
            kuehlung = createTextItem("Kühlung")
            beleuchtung = createTextItem("Beleuchtung")
            rlt = createTextItem("RLT")
            kaelteversorgung = createTextItem("Kälteversorgung")
            sommer = createTextItem("Sommer")
        }
    }

    private fun createTextItem(s: String): TGebaeudeText {
        return TGebaeudeText().apply {
            guid = createGuid()
            id = Math.random().toEveInteger()
            name = s.toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
            sourceID = ""
        }
    }

    private fun createWbv(): TWbrVerfahrenData {
        return TWbrVerfahrenData().apply {
            guid = createGuid()
            art = TWbrVerfahren.WBRV_PAUSCHALE
            deltaUWB = 0.1.toAutoSingle(unit = "W/(m²K)", auto = true, setToCalc = true)
            isInnengedaemmtUndMassivdecke = false
            kurzAttrSet = TWbrVerfAttributSet()
            isHolzleichtbau = false
            eingehalten = false.toAutoBoolean(auto = true, setToCalc = true)
            id = 97.toEveInteger()
            name = "Wärmebrückenverfahren".toAutoString(auto = true, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
            sourceID = ""
            aHuelle = 0.0.toEveFloat("m²")
        }
    }

    private fun createSingleLichtItem(zoneId: String): TLichtData {
        val lichtItemId = createGuid()
        return TLichtData().apply {
            guid = lichtItemId
            zoneID = zoneId
            raumID = ""
            a = 77.8.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            isVereinfachteGeometrie = true
            aRaum = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            bRaum = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            fensterID = ""
            bFenster = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            bFassade = 0.0.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            hBruestung = 0.8.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            hSt = 2.28.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            oberlichtID = ""
            isEinzelneObl = false
            anzahlObl = 1.toEveInteger()
            asObl = 0.0.toEveFloat("m")
            bsObl = 0.0.toEveFloat("m")
            hsObl = 0.0.toEveFloat("m")
            verfahren = TLichtLstgVerfahren.LLV_FACHPLANUNG
            isHatPraesenzmelder = false
            isHatKonstantlichtKontrolle = false
            tageslichtKontr = TTageslichtKontrolle.TLK_MANUELL
            isPFpAuto = true
            pFpMan = 0.0.toEveFloat("W/m²")
            leuchtenListe = TLampeDataList().apply {
                guid = createGuid()
                item.add(TLampeData().apply {
                    guid = createGuid()
                    belID = lichtItemId
                    lampArt = TLampenArt.LMP_LEUCHT_INT_VG
                    vorschalt = TVorschaltGeraete.VSG_EVG
                    deckung = 1.0.toEveFloat("-")
                    belArt = TBeleuchtArt.BEL_DIREKT
                    hl = 2.5.toEveFloat("m")
                    wf = 0.67.toEveFloat("-")
                    etaLB = 0.7.toEveFloat("-")
                    etaS = 55.0.toAutoSingle(unit = "lm/W", auto = true, setToCalc = true)
                    anzahl = 1.toEveInteger()
                    nennleistung = 36.0.toEveFloat("W")
                    id = 1.toEveInteger()
                    name = "Leuchtstofflampe kompakt, integr. EVG".toAutoString(auto = true, setToCalc = false)
                    bilderListeListe = TBilderStreamListe().apply {
                        guid = createGuid()
                    }
                    memo = "".toAutoString(auto = true, setToCalc = true)
                    sourceType = TImportSourceType.IMP_SRC_NONE
                })
            }
            id = 1.toEveInteger()
            name = "Licht".toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
        }

    }

    private fun createRoomListe(): TRaumDataList {

        val raumDataList = listing.building!!.floors.flatMap { floor ->
            val geschossGuid = floorCache[floor.id]!!.first.guid
            floor.rooms.map {
                createSingleRoom(geschossGuid, it) to it
            }
        }

        this.roomCache = raumDataList.associateBy { it.second.id }

        return TRaumDataList().apply {
            guid = createGuid()
            item = raumDataList.map { it.first }
        }
    }

    private fun createSingleRoom(geschossId: String, room: Room): TRaumData {
        return TRaumData().apply {
            guid = createGuid()
            code = room.roomNumber
            geschossID = geschossId
            luftVerbund = TAutoEnumTLuftVerbundFunktion().apply {// Relevant für Lüftungsanlage
                value = TLuftVerbundFunktion.LVF_NONE // Relevant für Lüftungsanlage
                isAuto = true
                man = TLuftVerbundFunktion.LVF_NONE // Relevant für Lüftungsanlage
            }
            lichteTiefe = 0.0.toEveFloat("m")
            lichteBreite = 0.0.toEveFloat("m")
            lichteHoehe = room.calculations.lichteHoehe.toEveFloat("m")
            ngf = room.calculations.din277.nettoRaumflaeche.toAutoSingle(unit = "m²", auto = false, setToCalc = false)
            bgf = room.calculations.din277.bruttoGrundflaeche.toAutoSingle(unit = "m²", auto = false, setToCalc = false)
            v = room.calculations.din277.nettoRauminhalt.toAutoSingle(unit = "m³", auto = false, setToCalc = false)
            ve = room.calculations.din277.bruttoRauminhalt.toAutoSingle(unit = "m³", auto = false, setToCalc = false)
            zoneID = if (room.isHeated()) zoneHeated.guid else ""
            isBeheiztOhneZone = false
            wohnNutzung = mapRoomCategory(room, room.isHeated())
            isFensterlos = !room.hasWindows() // Relevant für Lüftungsanlage
            isBedarfsfuehrung = false // Relevant für Lüftungsanlage
            isOhneTuerDichtung = false
            personen = 1.toEveInteger(unit = "-")
            iTmp = room.temperature().toAutoSingle(unit = "°C", auto = true, setToCalc = true)
            luftWechsel = 0.5.toAutoSingle(unit = "1/h", auto = true, setToCalc = true)
            mitAufheizlast = false.toAutoBoolean(auto = true, setToCalc = true)
            frZu = 1.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            anfLuftVol = 0.0.toAutoSingle(unit = "m³/h", auto = true, setToCalc = true)
            lueftTyp = TLKGrundprinzip.LKG_QUER
            heizkoerperListe = THeizkoerperDataList().apply {
                guid = createGuid()
            }
            grenzTempSWS = 25.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
            isSWSBericht = true
            id = (room.displayId?.toInt() ?: idGenerator.next()).toEveInteger()
            name = room.roomName().toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
            wohnungID = ""
        }
    }

    private fun mapRoomCategory(room: Room, heated: Boolean): TWohnNutzung {
        return when (room.roomCategory()) {
            RoomCategory.LIVING_ROOM,
            RoomCategory.OTHER_LIVING_SPACE,
            RoomCategory.OPEN_KITCHEN -> TWohnNutzung.WNTZ_WOHNEN

            RoomCategory.KITCHEN -> TWohnNutzung.WNTZ_KUECHE
            RoomCategory.BEDROOM -> TWohnNutzung.WNTZ_SCHLAFEN
            RoomCategory.BATHROOM_GUEST_WC -> TWohnNutzung.WNTZ_BAD
            RoomCategory.DINING_ROOM -> TWohnNutzung.WNTZ_ESSEN
            RoomCategory.HOMEOFFICE -> TWohnNutzung.WNTZ_ARBEITS
            RoomCategory.HALLWAY_STAIRS -> TWohnNutzung.WNTZ_FLUR
            RoomCategory.UTILITY_ROOM -> TWohnNutzung.WNTZ_HAUSARBEIT
            RoomCategory.STORAGE,
            RoomCategory.GARAGE -> TWohnNutzung.WNTZ_ABSTELL

            RoomCategory.HEATING_ROOM -> TWohnNutzung.WNTZ_TECHNIK
            RoomCategory.CELLAR -> TWohnNutzung.WNTZ_KELLER
            RoomCategory.WINTERGARDEN -> TWohnNutzung.WNTZ_WOHNEN
            RoomCategory.ATTIC -> TWohnNutzung.WNTZ_ABSTELL
            null -> if (heated) TWohnNutzung.WNTZ_WOHNEN else TWohnNutzung.WNTZ_ABSTELL
        }
    }

    private fun createGeschosseListe(): TGeschossList {

        val geschosse = listing.building!!.floors.map(::createSingleGeschoss)
        val idOfErdgeschoss = geschosse.find { it.second.levelInfo.levelType == FloorLevelType.EG }?.first?.guid

        this.floorCache = geschosse.associateBy { it.second.id }

        return TGeschossList().apply {
            guid = createGuid()
            item.addAll(geschosse.map { it.first })
            erdgeschossID = idOfErdgeschoss
        }
    }

    private val defaultSlabThickness = 0.24

    private fun createSingleGeschoss(floor: Floor): Pair<TGeschoss, Floor> {
        return TGeschoss().apply {
            guid = createGuid()
            hg = floor.calculations.floorHeight.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            hgMax = floor.calculations.floorHeight.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            hd = defaultSlabThickness.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            id = (floor.displayId?.toInt() ?: idGenerator.next()).toEveInteger()
            name = floor.levelInfo.createDisplayName().toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                this.guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
        } to floor
    }

    private fun createZdListe(): TZoneDataList {
        return TZoneDataList().apply {
            guid = createGuid()
            item.addAll(listOf(zoneHeated))
            isGesamtesGebaeude = false
        }
    }

    private fun createWohnraumZone(): TZoneData {
        val averageRoomHeight = listing.building!!.averageRoomHeight()
        val averageStoreyHeight = listing.building.averageStoreyHeight()
        return TZoneData().apply {
            guid = createGuid()
            geschossHoehe = averageStoreyHeight.toEveFloat("m")
            personen = 2.toAutoInteger(unit = "Personen", auto = false, setToCalc = false)
            twNutzung = 0.toEveInteger()
            twNutzungMenge = 0.0.toEveFloat("TODO")
            schwere = TAutoEnumTSchwere().apply {
                value = TSchwere.BTS_MITTEL
                man = TSchwere.BTS_MITTEL
                calc = TSchwere.BTS_MITTEL
                isAuto = true
            }
            cWirk = 50.0.toAutoSingle(unit = "Wh/(m²K)", auto = true, setToCalc = true)
            isALD = false
            isFestverglast = false
            zusWaermequelle = 0.0.toEveFloat("kWh/a")
            zusWaermesenke = 0.0.toEveFloat("kWh/a")
            iTmp = 21.0.toEveFloat("°C")
            isSpeziellesProfil = false
            nutzung = TNutzungData().apply {
                guid = createGuid()
                profil = "99"
                kurzname = "Wohnnutzung"
                basisID = 99.toEveInteger()
                nutzungVon = "00:00:00".toAutoString(auto = true, setToCalc = true)
                nutzungBis = "23:59:59".toAutoString(auto = true, setToCalc = true)
                heizenVon = "06:00:00".toAutoString(auto = true, setToCalc = true)
                heizenBis = "23:00:00".toAutoString(auto = true, setToCalc = true)
                kuehlenVon = "00:00:00".toAutoString(auto = true, setToCalc = true)
                kuehlenBis = "23:59:59".toAutoString(auto = true, setToCalc = true)
                dNutzA = 365.toAutoInteger(unit = "d/a", auto = true, setToCalc = true)
                dRltOpA = 365.toAutoInteger(unit = "d/a", auto = true, setToCalc = true)
                dcOpA = 365.toAutoInteger(unit = "d/a", auto = true, setToCalc = true)
                dhOpA = 365.toAutoInteger(unit = "d/a", auto = true, setToCalc = true)
                thetaIHSoll = 20.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
                thetaICSoll = 25.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
                thetaIHMin = 20.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
                thetaICMax = 26.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
                deltaThetaINA = 4.0.toAutoSingle(unit = "°C", auto = true, setToCalc = true)
                feuchteAnf = TAutoEnumTFeuchteAnforderung().apply {
                    value = TFeuchteAnforderung.FANF_KEINE
                    isAuto = true
                    man = TFeuchteAnforderung.FANF_KEINE
                    calc = TFeuchteAnforderung.FANF_KEINE
                }
                vxAPers = 24.8153619.toEveFloat("m³/(h*Person)")
                vxA = 1.0.toAutoSingle(unit = "m³/(h*m²)", auto = true, setToCalc = true)
                vxAGeb = 1.0.toAutoSingle(unit = "m³/(h*m²)", auto = true, setToCalc = true)
                crlt = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                frlt = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                nMechMin = 0.0.toEveFloat("TODO")
                nMechMax = 0.0.toEveFloat("TODO")
                nMechCMin = 0.0.toEveFloat("TODO")
                nMechCMax = 0.0.toEveFloat("TODO")
                em = 200.toAutoInteger(unit = "lx", auto = true, setToCalc = true)
                hNe = 0.8.toAutoSingle(unit = "m", auto = true, setToCalc = true)
                ka = 1.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                ca = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                k = 1.25.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                ft = 1.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                fv = 1.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
                kvb = 1.0.toEveFloat("-")
                pip = 70.0.toAutoSingle(unit = "W/Person", auto = true, setToCalc = true)
                piFac = 0.0.toAutoSingle(unit = "W/m²", auto = true, setToCalc = true)
                tNutzDP = 24.0.toAutoSingle(unit = "h/d", auto = true, setToCalc = true)
                tNutzDFac = 0.0.toAutoSingle(unit = "h/d", auto = true, setToCalc = true)
                etaIPLow = 24.8888889.toEveFloat("m²/Person")
                etaIPMid = 24.8888889.toEveFloat("m²/Person")
                etaIPHigh = 24.8888889.toEveFloat("m²/Person")
                pipLow = 2.8125.toEveFloat("W/m²")
                pipMid = 2.8125.toEveFloat("W/m²")
                pipHigh = 2.8125.toEveFloat("W/m²")
                piFacLow = 0.0.toEveFloat("W/m²")
                piFacMid = 0.0.toEveFloat("W/m²")
                piFacHigh = 0.0.toEveFloat("W/m²")
                deltaThetaEMS = TAutomationEveFloatArr().apply {
                    akld = 0.0
                    aklc = 0.0
                    aklb = -0.5
                    akla = -1.0
                }
                fAdapt = TAutomationEveFloatArr().apply {
                    akld = 1.0
                    aklc = 1.0
                    aklb = 1.35
                    akla = 1.35
                }
                id = idGenerator.next().toEveInteger()
                name = "Wohnnutzung".toAutoString(auto = false, setToCalc = false)
                bilderListeListe = TBilderStreamListe().apply {
                    guid = createGuid()
                }
                memo = "".toAutoString(auto = true, setToCalc = true)
                sourceType = TImportSourceType.IMP_SRC_NONE
            }
            n50 = 4.0.toAutoSingle(unit = "1/h", auto = true, setToCalc = true)
            luftDichtheitTyp = 3.toEveInteger()
            raumHoehe = averageRoomHeight.toAutoSingle(unit = "m", auto = true, setToCalc = true)
            flaeche = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true) // Wird von Evebi automatisch berechnet solange auto auf true steht
            v = 0.0.toAutoSingle(unit = "m³", auto = true, setToCalc = true)  // Wird von Evebi automatisch berechnet solange auto auf true steht
            id = 1.toEveInteger()
            name = "Wohnnutzung".toAutoString(auto = false, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply {
                guid = createGuid()
            }
            memo = "".toAutoString(auto = true, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_GREEN_BUILDING
            sourceID = "zo_1"
        }
    }

    private fun createETraegerListe(): TPrjBrStList {
        return TPrjBrStList().apply {
            guid = createGuid()
            item.add(createSIngleETraeger())
        }

    }

    private fun createSIngleETraeger(): TETraeger {
        return TETraeger().apply {
            guid = createGuid()
            umr = 1.0.toEveFloat(unit = "-")
            abgasVKonst = 0.0.toEveFloat(unit = "%")
            preisPE = 0.418.toEveFloat(unit = "€/Einheit")
            cO2 = 560.0.toAutoSingle(unit = "g/kWh", auto = true, setToCalc = true)
            sO2 = 500.0.toAutoSingle(unit = "mg/kWh", auto = true, setToCalc = true)
            nOx = 500.0.toAutoSingle(unit = "mg/kWh", auto = true, setToCalc = true)
            staub = 50.0.toAutoSingle(unit = "mg/kWh", auto = true, setToCalc = true)
            brStKategorie = TETraegerKat.BK_STROM
            quelle = ""
            setKea(0.0.toEveFloat(unit = "-"))
            setKea1(0.0.toEveFloat(unit = "-"))
            setKea2(0.0.toEveFloat(unit = "-"))
            cO2A = 0.0.toEveFloat(unit = "-")
            altPlus = 1.73.toAutoSingle(unit = "%", auto = true, setToCalc = true)
            nhf = 0.0.toEveFloat(unit = "-")
            kaT2 = 0.toEveInteger()
            kaT3 = 0.toEveInteger()
            fpnErneuerbar = 1.8.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            anteilErneuerbar = 0.0.toEveFloat(unit = "-")
            anteilBiomasse = 0.0.toEveFloat(unit = "-")
            anteilKWK = 0.0.toEveFloat(unit = "-")
            owner = ""
            nutzungSet = TETraegerNutzSet().apply { guid = createGuid() }
            grundpreis = 0.0.toEveFloat(unit = "€/a")
            verbraeucheListe = TVerbrauchsListe().apply { guid = createGuid() }
            zuschlagDezWW = false.toAutoBoolean(auto = true, setToCalc = false)
            klimafaktorDezWW = 0.0.toAutoSingle(unit = "-", auto = true, setToCalc = true)
            flaecheDezWW = 0.0.toAutoSingle(unit = "m²", auto = true, setToCalc = true)
            einheit = "kWh"
            isGebaeudeNahErzeugt = false
            isIsFw = false
            oekoBauKategorieID = ""
            oekoBauElement = TOBDSonderelemente.OBD_NONE
            kostengruppe = 300.toAutoInteger(auto = false, setToCalc = true)
            nutzungsdauer = 50.toAutoInteger(unit = "a", auto = true, setToCalc = true)
            menge = 0.0.toEveFloat(unit = "-")
            masse = 0.0.toEveFloat(unit = "kg")
            inOekobilanz = true.toAutoBoolean(auto = true, setToCalc = true)
            bestand = false.toAutoBoolean(auto = true, setToCalc = false)
            id = 1.toEveInteger()
            name = "Strom".toAutoString(auto = true, setToCalc = false)
            bilderListeListe = TBilderStreamListe().apply { guid = createGuid() }
            memo = "".toAutoString(auto = false, setToCalc = true)
            sourceType = TImportSourceType.IMP_SRC_NONE
            sourceID = ""
        }
    }


    private fun Double.toAutoSingle(unit: String? = null, auto: Boolean, setToCalc: Boolean): TAutoSingle {
        val number = this.toFloat()
        return TAutoSingle().apply {
            value = number
            isAuto = auto
            this.unit = unit
            calc = if (setToCalc) number else 0.0f
            man = if (!setToCalc) number else 0.0f
        }
    }

    private fun Number.toAutoInteger(unit: String? = null, auto: Boolean, setToCalc: Boolean = true): TAutoInteger {
        val number = this.toInt()
        return TAutoInteger().apply {
            value = BigInteger.valueOf(number.toLong())
            this.unit = unit
            isAuto = auto
            calc = if (setToCalc) BigInteger.valueOf(number.toLong()) else BigInteger.ZERO
            man = if (!setToCalc) BigInteger.valueOf(number.toLong()) else BigInteger.ZERO
        }
    }

    private fun Number.toEveInteger(unit: String? = null): EveInteger {
        val number = this.toInt()
        return EveInteger().apply {
            value = BigInteger.valueOf(number.toLong())
            this.unit = unit
        }
    }

    private fun Number.toEveFloat(unit: String? = null): EveFloat {
        val number = this.toFloat()
        return EveFloat().apply {
            value = number
            this.unit = unit
        }
    }

    private fun String.toAutoString(unit: String? = null, auto: Boolean, setToCalc: Boolean): TAutoString {
        val string = this
        return TAutoString().apply {
            value = string
            this.unit = unit
            isAuto = auto
            calc = if (setToCalc) string else ""
            man = if (!setToCalc) string else ""
        }
    }

    private fun Boolean.toAutoBoolean(auto: Boolean, setToCalc: Boolean): TAutoBoolean {
        val bool = this
        return TAutoBoolean().apply {
            isValue = bool
            isAuto = auto
            isCalc = if (setToCalc) bool else false
            isMan = if (!setToCalc) bool else false
        }
    }
}
