package com.doorbit.bff.infra.listing.model.listing.building

import com.doorbit.bff.infra.listing.extensions.nullIfZero
import com.doorbit.bff.infra.listing.extensions.rounded
import com.doorbit.bff.infra.listing.model.listing.ListingField
import com.doorbit.bff.infra.listing.model.listing.building.ListingFieldHelper.createSingleFieldForMap
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldName
import com.doorbit.bff.infra.listing.model.listing.fielddefinition.FieldSpec.FloorTypeField
import com.doorbit.bff.infra.listing.model.listing.fields.FloorType
import com.doorbit.bff.infra.listing.model.listing.fields.FloorType.FULL_STOREY
import com.doorbit.bff.infra.listing.model.listing.fields.FloorType.OTHER

data class Floor(
    override val id: String,
    override var displayId: String? = null,
    val shapeRepresentation: ShapeRepresentation,
    val level: Int,
    var levelInfo: FloorLevelInfo = FloorLevelInfo(FloorLevelType.EG, 0),
    val rooms: List<Room> = emptyList(),
    val unrecognizedRooms: List<Room> = emptyList(),
    val walls: List<Wall> = emptyList(),
    val slabs: List<ConstructionPart> = emptyList(),
    var customData: Map<FieldName, List<ListingField>>? = null,
    var calculations: CalculatedFloor = CalculatedFloor.EMPTY,
    var furniture: List<Furniture> = emptyList(),
    val pointsOfInterest: List<BuildingPointOfInterest> = emptyList(),
    val roofAreas: List<RoofArea> = emptyList()
) : Identifiable {

    fun findSlab(id: String): ConstructionPart? {
        return slabs.firstOrNull { it.id == id }
    }

    fun findFurniture(id: String): Furniture? {
        return furniture.firstOrNull { it.id == id }
    }

    fun findPointOfInterest(id: String): BuildingPointOfInterest? {
        return pointsOfInterest.firstOrNull { it.id == id }
    }

    fun calculateMasses() {
        roofAreas.forEach(RoofArea::calculateMasses)
        walls.forEach(Wall::calculateMasses)
        slabs.forEach(ConstructionPart::calculateMasses)
        rooms.forEach { it.calculateMasses(walls) }

        val insideWallArea = Area(
            grossArea = walls.sumOf { it.calculations.insideArea.grossArea },
            netArea = walls.sumOf { it.calculations.insideArea.netArea }
        )
        val outsideWallArea = Area(
            grossArea = walls.sumOf { it.calculations.outsideArea.grossArea },
            netArea = walls.sumOf { it.calculations.outsideArea.netArea }
        )
        val windowArea = walls.sumOf { it.calculateWindowArea() }
        val doorArea = walls.sumOf { it.calculateDoorArea() }
        val roofArea = roofAreas.sumOf(RoofArea::area)
        val windowCount = walls.sumOf { it.windowCount() }
        val doorCount = walls.sumOf { it.doorCount() }
        val outsideWindowArea = walls.sumOf { it.calculateWindowArea(outsideWindowsOnly = true) }
        val outsideDoorArea = walls.sumOf { it.calculateDoorArea(outsideDoorsOnly = true) }
        val outsideWindowCount = walls.sumOf { it.windowCount(outsideWindowsOnly = true) }
        val outsideDoorCount = walls.sumOf { it.doorCount(outsideDoorsOnly = true) }
        val floorHeight = walls.maxOfOrNull { it.height(outside = true) }
        val perimeter = calculatePerimeter()
        val din277 = Din277(
            nettoRauminhalt = rooms.sumOf { it.calculations.din277.nettoRauminhalt },
            nettoRaumflaeche = rooms.sumOf { it.calculations.din277.nettoRaumflaeche },
            bruttoRauminhalt = rooms.sumOf { it.calculations.din277.bruttoRauminhalt },
            bruttoGrundflaeche = rooms.sumOf { it.calculations.din277.bruttoGrundflaeche },
            technikFlaeche = rooms.sumOf { it.calculations.din277.technikFlaeche ?: 0.0 }.nullIfZero(),
            verkehrsFlaeche = rooms.sumOf { it.calculations.din277.verkehrsFlaeche ?: 0.0 }.nullIfZero()
        )
        calculations = CalculatedFloor(
            insideWallArea = insideWallArea.rounded(2),
            outsideWallArea = outsideWallArea.rounded(2),
            windowArea = windowArea.rounded(2),
            doorArea = doorArea.rounded(2),
            roofArea = roofArea.rounded(2),
            windowCount = windowCount,
            doorCount = doorCount,
            outsideWindowArea = outsideWindowArea.rounded(2),
            outsideDoorArea = outsideDoorArea.rounded(2),
            outsideWindowCount = outsideWindowCount,
            outsideDoorCount = outsideDoorCount,
            floorHeight = floorHeight?.rounded(2) ?: 0.0,
            perimeter = perimeter.rounded(2),
            din277 = din277.rounded(2),
            dimensions = shapeRepresentation.shape.dimensions(OrientationType.LYING)
        )

        customData = CustomDataSetter.updateCustomData(customData, calculations.toCustomData())
    }

    private fun calculatePerimeter() =
        slabs.filter { it.type == ConstructionPartType.FLOOR }
            .sumOf { it.shapeRepresentation.shape.polygon?.calculatePerimeter() ?: 0.0 }

    fun findWall(id: String): Wall? = walls.firstOrNull { it.id == id }
    fun findRoom(id: String): Room? = rooms.firstOrNull { it.id == id }
    fun findRoofArea(id: String): RoofArea? = roofAreas.firstOrNull { it.id == id }

    private fun determineAndSetFloorType(customData: Map<FieldName, List<ListingField>>?) {

        val fullStorey = FullStoreyCalculator.isFullStorey(walls, calculations.din277.nettoRaumflaeche)

        if (fullStorey) {
            this.customData = CustomDataSetter.updateCustomData(customData, createSingleFieldForMap(FloorTypeField.fieldName, FULL_STOREY.name))
            return
        }

        this.customData = CustomDataSetter.updateCustomData(customData, createSingleFieldForMap(FloorTypeField.fieldName, OTHER.name))
    }

    fun floorType(): FloorType {
        return customData?.get(FloorTypeField.fieldName)?.firstOrNull()?.value?.let { FloorType.valueOf(it.toString()) } ?: OTHER
    }

    fun applyCustomLogicOnce() {
        determineAndSetFloorType(customData)
    }

    fun allOpenings(): List<ConstructionPart> = walls.flatMap { it.openings }
    fun hasRollladenkasten(): Boolean = walls.flatMap { it.openings }.any(ConstructionPart::hasRolladen)
    fun hasHeizkoerpernische(): Boolean = walls.flatMap { it.openings }.any(ConstructionPart::hasHeizkoerpernische)
}
