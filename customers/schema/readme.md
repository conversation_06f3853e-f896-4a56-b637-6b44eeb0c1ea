Grundsätzlicher Aufbau

```json
{
  "id": "doorbit_esg",
  "url-key": "esg",
  "name": "Doorbit Energieberater Flow",
  "title": {
    "en": "My Projects",
    "de": "Meine Projekte"
  },
  "icon": "mdiHeatingCoil",
  "pages_edit": [
    // <PERSON><PERSON><PERSON>, siehe unten
  ],
  "pages_view": [
    // <PERSON><PERSON><PERSON>, siehe unten
  ]
}
```

Definition einer Page:
```
    {
      "pattern_type": "CustomUIElement",
      "id": "edit-general",
      "layout": "2_COL_RIGHT_FILL",
      "related_pages": [
        {
          "viewing_context": "VIEW",
          "page_id": "view-general"
        }
      ],
      "short_title": {
        "en": "Building",
        "de": "Gebäude"
      },
      "elements": [
        // A<PERSON><PERSON> von Elementen, siehe unten
      ]
    }
```
Es gibt eine Reihe von möglichen Elementen. Hier sind die ersten 3.

TextUIElement:
```json
{
  "element": {
    "pattern_type": "TextUIElement",
    "type": "PARAGRAPH", // PARAGRAPH, HEADING
    "text": {
      "en": "The U-value assistant helps you to determine the U-values of your building elements.",
      "de": "Mit dem U-Wert Assistenten können schnell und einfach die U-Werte von Gebäudebauteilen ermittelt werden. Wir nutzen das Verfahren aus dem Bundesanzeiger, um mit wenigen Angaben eine Ersteinschätzung zu erhalten. Die so errechneten Werte werden automatisch auf das Gebäudemodell übertragen und sind sofort in allen Doorbit Exporten enthalten."
    }
  }
}
```

SingleSelectionUIElement:
```json
{
    "element": {
      "pattern_type": "SingleSelectionUIElement",
      "required": false,
      "type": "BUTTONGROUP", // BUTTONGROUP, DROPDOWN (default), optional
      "default": "NO_CELLAR", // optional
      "required": true, // optional
      "field_id": {
        "field_name": "cellar_type"
      },
      "title": {
        "en": "What is beneath the ground floor?",
        "de": "Was liegt unterhalb des Erdgeschosses?"
      },
      "description": "Ein längerer aber nicht zu langer Text als zus. Description",
      "example_image": "https://www.doorbit.de/assets/images/doorbit_logo.png",
      "icon": "mdiHeatingCoil",
      "options": [
        {
          "key": "NO_CELLAR",
          "label": {
            "en": "Ground plate",
            "de": "Bodenplatte"
          }
        },
        {
          "key": "UNHEATED_CELLAR",
          "label": {
            "en": "Unheated cellar",
            "de": "Unbeheizter Keller"
          }
        }
      ]
    }
}
```

Jedes UIElement kann auch von einem GroupUIElement genested werden.

GroupUIElement:
```json
{
  "element": {
    "pattern_type": "GroupUIElement",
    "title": {
      "en": "Ground plates",
      "de": "Unterste Geschossböden"
    },
    "elements": [
      // Array von Elementen (Aller Art, Rekursion ist möglich aber nur maximal eine weitere Ebene)
    ]
  }
}
```

Es gibt noch mehr Elemente:

NumberUIElement:

```json
{
    "element": {
      "pattern_type": "NumberUIElement",
      "type": "DOUBLE", // DOUBLE, INTEGER
      "required": false,
      "field_id": {
        "field_name": "wall_u_value"
      },
      "title": {
        "en": "U-value",
        "de": "U-Wert"
      },
      "description": {
        "en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.",
        "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."
      }
    }
  }
```

BooleanUIElement:

```json
{
  "element": {
    "pattern_type": "BooleanUIElement",
    "required": false,
    "field_id": {
      "field_name": "has_different_window_year_of_construction"
    },
    "default": true,
    "title": {
      "en": "Different year of construction than building",
      "de": "Baujahr der Fenster weicht vom Gebäudebaujahr ab"
    },
    "description": {
      "en": "If the majority of the windows have already been replaced, please indicate.",
      "de": "Falls die Mehrheit der Fenster bereits erneuert wurden, bitte angeben."
    }
  }
}
```

DateUIElement:

```json
{
  "element": {
    "pattern_type": "DateUIElement",
    "type": "Y", // Y, YM, YMD, YMDT
    "required": true,
    "field_id": {
      "field_name": "year_of_construction"
    },
    "title": {
      "en": "Building year of construction",
      "de": "Gebäudebaujahr"
    },
    "minimum": "-1Y", // -1Y, -1M, -1D
    "maximum": "+1Y"
  }
}
```

Und wenn das alles funktioniert, dann gibt es noch ArrayUIElement um Dinge zu realisieren wie: "Gib 5 Fenster an jeweils mit Breite, Höhe, U-Wert und Baujahr".

ArrayUIElement:
```json
{
  "element": {
    "pattern_type": "ArrayUIElement",
    "title": {
      "en": "Dormers",
      "de": "Dachgauben"
    },
    "example_image": "https://flow-configs.doorbit.com/assets/dormers/example.jpg",
    "min_count": 0,
    "max_count": 20,
    "elements": [
      // Array von beliebigen Elementen
    ]
  }
}
```

Außerdem gibt es noch FileUIElement für Bilderuploads z. B.

FileUIElement

```json
{
  "element": {
    "pattern_type": "FileUIElement",
    "file_type": "IMAGE", // IMAGE, FILE
    "title": {
      "en": "Energy consumption data",
      "de": "Verbrauchsdaten"
    },
    "min_count": 0,
    "max_count": 10,
    "required": false,
    "id_field_id": {
      "field_name": "energy_consumption_file_id"
    },
    "caption_field_id": {
      "field_name": "energy_consumption_file_caption"
    },
    "allowed_file_types": [
      "image/jpeg",
      "image/png",
      "image/webp",
      "image/tiff"
    ]
  }
}
```

Und zuletzt kommt ein spezieller Fall eines CustomUIElements:

Das CustomUIElement vom Typ "SCANNER":

```json
{
  "element": {
    "pattern_type": "CustomUIElement",
    "type": "SCANNER",
    "id": "model-edit",
    "related_custom_ui_element_id": "model-view",
    "display_position": "RIGHT",
    "sub_flows": [
      {
        "type": "POI_PHOTO", // POI_PHOTO, POI, ROOM, ROOM_GROUP, WINDOW, WINDOW_GROUP, DOOR, DOOR_GROUP, WALL, WALL_GROUP, WALL_EVEBI, ROOF_AREA, FLOOR, FLOOR_SLAB, BUILDING
        "elements": [
          // Array von beliebigen UI Elementen
        ]
      }
    ]
  }
}
```

Erklärungen der Subflow Typen:

- POI_PHOTO: Subflow für Schnappschüsse während des Scans
- POI: Subflow für POIs (Point of Interest)
- ROOM: Subflow für Räume
- ROOM_GROUP: Subflow für Räume mit Mehrfachauswahl im Doorbit Studio
- WINDOW: Subflow für Fenster
- WINDOW_GROUP: Subflow für Fenster mit Mehrfachauswahl im Doorbit Studio
- DOOR: Subflow für Türen
- DOOR_GROUP: Subflow für Türen mit Mehrfachauswahl im Doorbit Studio
- WALL: Subflow für Wände
- WALL_GROUP: Subflow für Wände mit Mehrfachauswahl im Doorbit Studio
- WALL_EVEBI: Subflow für Wände im Evebi Modus
- ROOF_AREA: Subflow für Dachflächen
- FLOOR: Subflow für Stockwerke
- FLOOR_SLAB: Subflow für Bodenplatten
- BUILDING: Subflow für das Gebäude


Zudem gibt es für CustomUIElements noch folgende Typen zur Auswahl:

- SCANNER -> Zeigt den Scanner bzw. Doorbit Studio an
- ADDRESS -> Zeigt die Adresseingabe an
- LOCATION -> Zeigt den Standort auf einer interaktiven Karte an
- ADMIN_BOUNDARY -> Zeigt die Nachbarschaft, den Landkreis und Statistiken der Makroökonomie an
- PROJECT_CONFIGURATOR -> Zeigt den Bau- und Projektkostenkonfigurator an