#!/bin/bash

# URL zu der die Anfragen gesendet werden sollen
url="https://doorbit.com/listing/listing-flows"

# Header
content_type="Content-Type: application/json"
# cookie wird als Umgebungsvariable erwartet (z.B. cookie="Cookie: session=xyz")

# Alle JSON-Dateien im Ordner durchlaufen
for json_file in *.json
do
  if [ -f "$json_file" ]; then
    echo "Sende Datei: $json_file"

    # Curl-Befehl mit der aktuellen JSON-Datei als Payload ausführen
    response=$(curl --location --silent --show-error --fail \
                 --header "$content_type" \
                 --header "$cookie" \
                 --data @"$json_file" \
                 "$url")

    if [ $? -ne 0 ]; then
      echo "Fehler beim Senden der Datei $json_file"
    else
      echo "Datei $json_file erfolgreich gesendet."
    fi

    echo "Datei $json_file gesendet."
  else
    echo "Keine JSON-Dateien im Ordner gefunden."
  fi
done
