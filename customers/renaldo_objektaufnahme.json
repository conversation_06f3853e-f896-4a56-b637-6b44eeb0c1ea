{"id": "renaldo-object-capture", "url-key": "renaldo", "name": "Projekte", "title": {"en": "Projects", "de": "Projekte"}, "icon": "mdiAccountHardHat", "pages_edit": [{"pattern_type": "CustomUIElement", "id": "edit-summary", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-summary"}], "short_title": {"en": "Project summary", "de": "Projektzusammenfassung"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Project organization", "de": "Projektorganisation"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "ext_id"}, "title": {"en": "Project title", "de": "Projekttitel"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "display_position": "LEFT", "required": true, "field_id": {"field_name": "custom_status_type"}, "default": "PLANNED", "title": {"en": "Status", "de": "Status"}, "options": [{"key": "PLANNED", "label": {"en": "Planned", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "IN_PROGRESS", "label": {"en": "In progress", "de": "In Bearbeitung"}}, {"key": "DONE", "label": {"en": "Done", "de": "Abgeschlossen"}}, {"key": "ARCHIVED", "label": {"en": "Archived", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "DateUIElement", "type": "YMDT", "required": false, "field_id": {"field_name": "appointment_date"}, "title": {"en": "Appointment for the object recording", "de": "<PERSON><PERSON><PERSON>Be<PERSON>cht<PERSON>"}, "minimum": "-1Y", "maximum": "+8M"}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "VOB-Scouts", "de": "VOB-Scouts"}, "min_count": 0, "max_count": 10, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "appointment_scout"}, "title": {"en": "Scout name", "de": "Scout-Name"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "appointment_status"}, "default": "OPEN", "title": {"en": "Appointment status", "de": "Terminstatus"}, "options": [{"key": "OPEN", "label": {"en": "Open", "de": "<PERSON>en"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "CONFIRMED", "label": {"en": "Confirmed", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DONE", "label": {"en": "Done", "de": "<PERSON><PERSON><PERSON> du<PERSON>ü<PERSON>"}}]}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Building overview", "de": "Überblick Gebäude"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "year_of_construction"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "space_area"}, "title": {"en": "Living area", "de": "Wohnfläche"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_area"}, "title": {"en": "Roof area", "de": "Dachfläche"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "sub_type"}, "title": {"en": "Building type", "de": "Gebäudetyp"}, "options": [{"key": "DETACHED", "label": {"en": "Detached house", "de": "Einfamilienhaus"}}, {"key": "SEMI_DETACHED", "label": {"en": "Semi-detached house", "de": "Doppelhaushälfte"}}, {"key": "TOWNHOUSE_END", "label": {"en": "Townhouse (corner)", "de": "Re<PERSON><PERSON>ndhaus"}}, {"key": "TOWNHOUSE_MIDDLE", "label": {"en": "Townhouse (middle)", "de": "Reihenmittelhaus"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_source"}, "title": {"en": "Primary energy source", "de": "Hauptenergieträger"}, "options": [{"key": "GAS", "label": {"en": "Gas", "de": "Gas"}}, {"key": "OIL", "label": {"en": "Oil", "de": "<PERSON><PERSON>"}}, {"key": "PELLETS", "label": {"en": "Pellets", "de": "Pellets"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ELECTRICITY", "label": {"en": "Electricity", "de": "<PERSON><PERSON>"}}, {"key": "ENVIRONMENTAL", "label": {"en": "Heat pump", "de": "Wärmepumpe"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "number_of_living_units"}, "title": {"en": "Living units", "de": "Wohneinheiten"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Covershot", "de": "Cover Foto"}, "description": {"en": "This photo appears as a preview image in the overview.", "de": "Dieses Foto erscheint als Vorschaubild in der Übersicht."}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "covershot_image_id"}, "caption_field_id": {"field_name": "covershot_image_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Building pictures", "de": "<PERSON><PERSON><PERSON>ude<PERSON><PERSON>"}, "description": {"en": "", "de": "Es sind mindestens 5 Gebäudefotos erforderlich: 4 Ansichten der Fassade (von jeder Seite) und 1x Hauptansicht (Haus gesamt im Profil)"}, "min_count": 0, "max_count": 200, "required": false, "id_field_id": {"field_name": "building_pictures_id"}, "caption_field_id": {"field_name": "building_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"pattern_type": "CustomUIElement", "type": "ADDRESS", "id": "edit-address", "related_pages": [{"viewing_context": "VIEW", "page_id": "location"}], "title": {"en": "Determine address", "de": "<PERSON><PERSON><PERSON> er<PERSON>n"}, "short_title": {"en": "Location", "de": "Lage"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_zipcode"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_city"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_street"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_house_number"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_latitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_longitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_latitude_obfuscated"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_longitude_obfuscated"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "address_show_obfuscated_location"}, "default_value": false, "title": {"en": "Hide address?", "de": "<PERSON>resse verbergen?"}, "description": {"en": "If the address is hidden, it won't be publicly visible. Instead, an approximate location will be displayed.", "de": "Wird die Adresse verborgen, ist sie nicht öffentlich sichtbar. Stattdessen wird ein ungefährer Standort präsentiert."}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-scan", "layout": "2_COL_RIGHT_FILL", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-scan"}], "short_title": {"en": "Building-scan", "de": "Gebäude-Scan"}, "elements": [{"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-edit", "display_position": "RIGHT", "related_custom_ui_element_id": "model-view", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightSwitch", "field_id": {"field_name": "building_poi_is_light_switch"}, "default_value": false, "title": {"en": "Light switch", "de": "Lichtschalter"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiPowerSocketDe", "field_id": {"field_name": "building_poi_is_wall_socket"}, "default_value": false, "title": {"en": "Wall socket", "de": "Steckdose"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWindowClosed", "field_id": {"field_name": "building_poi_is_window_note"}, "default_value": false, "title": {"en": "Window", "de": "<PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiAddBox", "field_id": {"field_name": "building_poi_is_special_thermal_component"}, "default_value": false, "title": {"en": "Special thermal envelope component", "de": "Sonderbauteil thermische Hülle"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiHomeRoof", "field_id": {"field_name": "building_poi_is_dormer"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgaube"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Details", "de": "Fensterdetails"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Glazing", "de": "Verglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach Verglasung"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach Verglasung"}}, {"key": "DOUBLE_THERMAL", "label": {"en": "Double thermal insulation glazing", "de": "2-fach Wärmeschutzverglasung"}}, {"key": "TRIPLE_THERMAL", "label": {"en": "Triple thermal insulation glazing", "de": "3-fach Wärmeschutzverglasung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_shutter_type"}, "title": {"en": "Window shutters/blinds", "de": "Fensterläden/Rollläden/Raffstores"}, "options": [{"key": "NONE", "label": {"en": "None", "de": "<PERSON><PERSON>"}}, {"key": "SHUTTERS", "label": {"en": "Window shutters", "de": "Fensterläden"}}, {"key": "ROLLER_BLINDS", "label": {"en": "Roller blinds", "de": "Rollläden"}}, {"key": "VENETIAN_BLINDS", "label": {"en": "Venetian blinds", "de": "Raffstores"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Position", "de": "Fensterposition"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_inner_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Inner reveal depth", "de": "Laibungstiefe innen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_thickness_cm"}, "unit": "CENTIMETER", "title": {"en": "Window thickness", "de": "Fensterstärke"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_outer_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Outer reveal depth", "de": "Laibungstiefe außen"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Special thermal envelope component details", "de": "Details zu Sonderbauteilen der thermischen Hülle"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "required": false, "field_id": {"field_name": "special_thermal_component_material"}, "title": {"en": "Material", "de": "Material"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "minimum": "-100Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "special_thermal_component_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Dormer details", "de": "Dachgauben-Details"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Primary Type", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}, {"key": "SPECIAL_TYPE", "label": {"en": "Special heater", "de": "Sonderbauform"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_sub_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "eq", "value": "PANEL_RADIATOR", "operator_type": "RFO"}, "title": {"en": "Secondary Type", "de": "Subtyp"}, "options": [{"key": "TYPE_10", "label": {"en": "Typ 10", "de": "Typ 10"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_11", "label": {"en": "Typ 11", "de": "Typ 11"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_20", "label": {"en": "Typ 20", "de": "Typ 20"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_21", "label": {"en": "Typ 21", "de": "Typ 21"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_22", "label": {"en": "Typ 22", "de": "Typ 22"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_23", "label": {"en": "Typ 23", "de": "Typ 23"}, "icon": "mdiRadiatorDisabled"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "unit": "CENTIMETER", "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "ne", "value": "PANEL_RADIATOR", "operator_type": "RFO"}]}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "radiator_ventildurchmesser"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Valve diameter", "de": "DN (Ventildurchmesser)"}, "options": [{"key": "MM_10", "label": {"en": "10mm", "de": "10mm"}, "icon": "mdiRuler"}, {"key": "MM_15", "label": {"en": "15mm", "de": "15mm"}, "icon": "mdiRuler"}, {"key": "MM_20", "label": {"en": "20mm", "de": "20mm"}, "icon": "mdiRuler"}, {"key": "MM_25", "label": {"en": "25mm", "de": "25mm"}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please upload at least one photo of the radiator and one photo of the valve.", "de": "Bitte mindestens Heizkörperfoto und Großaufnahme vom Ventil hochladen"}}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please add a photo from the outside and from the inside.", "de": "Bitte Foto von außen und von Innen hinzufügen"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 5, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Heating information", "de": "Angaben zur Beheizung"}, "description": {"en": "Select how the room is heated. A room can also be heated by adjacent rooms if the rooms are connected by a breakthrough.", "de": "<PERSON><PERSON><PERSON><PERSON> aus, wie der Raum beheizt ist. Ein Raum kann auch durch angrenzende Räume mitbeheizt sein, wenn die Räume mittels Durchbruch verbunden sind."}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_room_heated"}, "default_value": true, "title": {"en": "Is room heated", "de": "Ist der Raum beheizt?"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_temperature_type"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Approx. room temperature", "de": "Ungefähre Raumtemperatur"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room (ca. 20°C)", "de": "Wohnraum (ca. 20°C)"}}, {"key": "BATHROOM", "label": {"en": "Bathroom (ca. 24°C)", "de": "Bad<PERSON>immer (ca. 24°C)"}}, {"key": "PASSAGE_ROOM", "label": {"en": "Passage room (ca. 15°C)", "de": "Flur / Abstellraum (ca. 15°C)"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "More details", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_category"}, "title": {"en": "Room category", "de": "Raumkategorie"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "KITCHEN", "label": {"en": "Kitchen", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "OPEN_KITCHEN", "label": {"en": "Open kitchen", "de": "<PERSON><PERSON>nkü<PERSON>"}}, {"key": "BEDROOM", "label": {"en": "Bedroom", "de": "Schlaf-/Kinderzimmer"}}, {"key": "BATHROOM_GUEST_WC", "label": {"en": "Bathroom/WC", "de": "<PERSON><PERSON><PERSON>/<PERSON>"}}, {"key": "HOMEOFFICE", "label": {"en": "Homeoffice", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER_LIVING_SPACE", "label": {"en": "Other living space (e.g. hobby room)", "de": "<PERSON><PERSON> (z. B. <PERSON>, Fitnessraum, Hallenbad)"}}, {"key": "WINTERGARDEN", "label": {"en": "Winter garden", "de": "Wintergarten"}}, {"key": "HALLWAY_STAIRS", "label": {"en": "Hallway/stairs", "de": "Flur/Treppenhaus"}}, {"key": "STORAGE", "label": {"en": "Storage", "de": "Abstellraum"}}, {"key": "UTILITY_ROOM", "label": {"en": "Utility room", "de": "Hauswirtschaftsraum"}}, {"key": "HEATING_ROOM", "label": {"en": "Heating room", "de": "Heizungsraum"}}, {"key": "GARAGE", "label": {"en": "Garage", "de": "Garage"}}, {"key": "ATTIC", "label": {"en": "Attic", "de": "Dachboden"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "room_name"}, "title": {"en": "Custom room name", "de": "Eigener Raumname"}, "description": {"en": "Will be generated from the room category if nothing is maintained.", "de": "Wird aus der Raumkategorie erzeugt, wenn nichts gepflegt wird."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Room height", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_height"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "ROOM_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "default_value": false, "field_id": {"field_name": "has_roller_shutters"}, "title": {"en": "Has roller shutters", "de": "Rollladen vorhanden"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "visibility_condition": {"field_id": {"field_name": "has_roller_shutters"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "roller_shutters_height"}, "title": {"en": "Roller shutters height", "de": "Höhe des Rollladenkastens"}, "icon": "mdiArrowExpandVertical"}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "default_value": false, "field_id": {"field_name": "has_heizkoerpernische"}, "title": {"en": "Has radiator niche", "de": "Heizkörpernische vorhanden"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "visibility_condition": {"field_id": {"field_name": "has_heizkoerpernische"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "heizkoerpernischen_height"}, "title": {"en": "Radiator niche height", "de": "Höhe der HZK-Nische"}, "icon": "mdiArrowExpandVertical"}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach"}}, {"key": "DOUBLE_ISOLATED", "label": {"en": "Double glazing with thermal insulation", "de": "2-fach (Wärmeschutzverglasung)"}}, {"key": "TRIPLE", "label": {"en": "Triple glazing", "de": "3-fach (Wärmeschutzverglasung)"}}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "window_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "WINDOW_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "door_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "DOOR_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "wall_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "WALL_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Inside dimensions", "de": "Innenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "inside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside dimensions", "de": "Außenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WALL_EVEBI", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Construction", "de": "Konstruktion"}, "description": {"en": "By default, a construction is created in Evebi for each different U-Value. Components can also be assigned to their own construction groups.", "de": "Es wird standardmäßig pro unterschiedlichem U-Wert eine Konstruktion in Evebi erzeugt. Bauteile können auch eigenen Konstruktionsgruppen zugeordnet werden."}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "construction_type"}, "title": {"en": "Assignment", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "BY_U_VALUE", "options": [{"key": "BY_U_VALUE", "label": {"en": "By U-Value", "de": "Basierend auf U-Wert dieses Elements"}}, {"key": "CONSTRUCTION_A", "label": {"en": "Construction group A", "de": "Konstruktionsgruppe A"}}, {"key": "CONSTRUCTION_B", "label": {"en": "Construction group B", "de": "Konstruktionsgruppe B"}}, {"key": "CONSTRUCTION_C", "label": {"en": "Construction group C", "de": "Konstruktionsgruppe C"}}, {"key": "CONSTRUCTION_D", "label": {"en": "Construction group D", "de": "Konstruktionsgruppe D"}}, {"key": "CONSTRUCTION_E", "label": {"en": "Construction group E", "de": "Konstruktionsgruppe E"}}, {"key": "CONSTRUCTION_F", "label": {"en": "Construction group F", "de": "Konstruktionsgruppe F"}}, {"key": "CONSTRUCTION_G", "label": {"en": "Construction group G", "de": "Konstruktionsgruppe G"}}]}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "floor_type"}, "title": {"en": "Storey type", "de": "Geschossart"}, "options": [{"key": "FULL_STOREY", "label": {"en": "Full storey", "de": "Vollgeschoss"}}, {"key": "CELLAR", "label": {"en": "Cellar", "de": "Untergeschoss/Keller"}}, {"key": "ROOF_STOREY", "label": {"en": "Roof storey", "de": "Dachgeschoss"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Storey", "de": "Geschoss"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-general", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-general"}], "short_title": {"en": "General building information", "de": "Allgemeine Gebäudeinformationen"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_slab_construction"}, "default_value": false, "title": {"en": "Has slab construction", "de": "Bodenaufbauten vorhanden?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "has_slab_construction"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "slab_construction"}, "title": {"en": "Slab construction", "de": "Bodenaufbauten"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_construction_parts_to_open"}, "default_value": false, "title": {"en": "Construction parts to open", "de": "Zu öffnende Bauteile vorhanden?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "has_construction_parts_to_open"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "construction_parts_to_open"}, "title": {"en": "Construction parts to open", "de": "<PERSON>u <PERSON> Ba<PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_extension_building"}, "default_value": false, "title": {"en": "Has extension building", "de": "Anbau vorhanden"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_outside_stairs"}, "title": {"en": "Outside stairs present?", "de": "Außentreppen vorhanden?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "has_outside_stairs"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "outside_stairs_notes"}, "title": {"en": "Notes on the outside stairs", "de": "Außentreppen (Anzahl/Art)"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_inside_stairs"}, "title": {"en": "Outside stairs present?", "de": "Innentreppen vorhanden?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "has_inside_stairs"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "inside_stairs_notes"}, "title": {"en": "Inside stairs (Amount/type)", "de": "Innentreppen (Anzahl/Art)"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_mold_or_pests"}, "title": {"en": "Has mold or pests?", "de": "Schimmel oder Schädlingsbefall"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "mold_notes"}, "visibility_condition": {"field_id": {"field_name": "has_mold_or_pests"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Notes about the mold", "de": "Notizen zum Schimmel/Schädlingsbefall"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "notes"}, "title": {"en": "Notes", "de": "Weitere Besonderheiten"}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-building", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-building"}], "short_title": {"en": "Building envelope", "de": "Geb<PERSON>udeh<PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Cellar", "de": "<PERSON>"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_cellar"}, "default_value": true, "title": {"en": "Has cellar", "de": "<PERSON> v<PERSON>"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Cellar details", "de": "Kellerdetails"}, "visibility_condition": {"field_id": {"field_name": "has_cellar"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "cellar_notes"}, "title": {"en": "Cellar details", "de": "Kellerdetails"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "basement_ceiling_insulation"}, "title": {"en": "Basement ceiling insulation", "de": "Kellerdeckendämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "cellar_wall_insulation"}, "title": {"en": "Basement wall insulation", "de": "Kelleraußenwanddämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "basement_moisture"}, "title": {"en": "Moisture in the basement", "de": "Feuchtigkeit Keller"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "basement_pipes"}, "title": {"en": "Pipes in the basement", "de": "Kellerleitungen"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "basement_special_features"}, "title": {"en": "Special features of the basement", "de": "Besonderheiten Keller"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "meter_place"}, "title": {"en": "Meter place", "de": "Zählerplatz"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "basement_wiring"}, "title": {"en": "Wiring in the basement", "de": "Leitungsführung Keller"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "cellar_thermal_hull_type"}, "title": {"en": "Cellar thermal hull", "de": "Keller thermische Hülle"}, "options": [{"key": "NO", "label": {"en": "No", "de": "<PERSON><PERSON>"}}, {"key": "YES", "label": {"en": "Yes", "de": "<PERSON>a"}}, {"key": "TO_BE_CHECKED", "label": {"en": "To be checked", "de": "Muss geprüft werden"}}]}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Facade", "de": "Fassade"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_different_wall_thickness"}, "title": {"en": "Has the building different wall thicknesses?", "de": "Sind unterschiedliche Wandstärken vorhanden?"}}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "Different wall thicknesses", "de": "Unterschiedliche Wandstärken aufnehmen"}, "min_count": 0, "max_count": 10, "visibility_condition": {"field_id": {"field_name": "has_different_wall_thickness"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": true, "field_id": {"field_name": "different_wall_thickness_location"}, "title": {"en": "Location in the building", "de": "Lage im Gebäude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": true, "field_id": {"field_name": "different_wall_thickness"}, "title": {"en": "Wall thickness", "de": "Wandstärke"}}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_outside_wall_insulation_known"}, "title": {"en": "Is the outside wall insulation known?", "de": "Ist die Außenwanddämmung bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "outside_wall_insulation_thickness"}, "title": {"en": "Outside wall insulation thickness", "de": "Stärke der Außenwanddämmung"}, "visibility_condition": {"field_id": {"field_name": "is_outside_wall_insulation_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/outside_wall_insulation_thickness/example.jpg"}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "facade_insulation"}, "title": {"en": "Facade insulation", "de": "Fassadendämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "facade_special_features"}, "title": {"en": "Special features of the facade", "de": "Besonderheiten Fassade"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows", "de": "<PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_different_windows_on_heated_area"}, "title": {"en": "Is the energy consumption known?", "de": "Gibt es unterschiedliche Fenster auf den beheizten Wohnflächen?"}, "description": {"en": "excl. roof windows", "de": "exkl. Dachflächenfenster"}}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "Up to 5 different windows", "de": "Erfassung von bis zu 5 unterschiedlichen Fenstern"}, "min_count": 0, "max_count": 5, "visibility_condition": {"field_id": {"field_name": "has_different_windows_on_heated_area"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_cardinal_direction"}, "title": {"en": "Cardinal Direction of Window", "de": "Himmelsrichtung des Fensters"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_frame_material_type"}, "title": {"en": "Window frames material", "de": "Aus welchem Material bestehen die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ALUMINIUM", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "STEEL", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "Double", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "TRIPLE", "label": {"en": "Triple", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_window_u_value_known"}, "title": {"en": "U-Value known?", "de": "U-<PERSON>rt bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "window_u_value"}, "visibility_condition": {"field_id": {"field_name": "is_window_u_value_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "U-Value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Window pictures", "de": "Fotos der Fenster"}, "visibility_condition": {"field_id": {"field_name": "has_different_windows_on_heated_area"}, "op": "eq", "value": true, "operator_type": "RFO"}, "description": {"en": "", "de": "Material und Art des Fensters sollte klar erkennbar sein; möglichst seitlich bzw. schräg fotografieren"}, "min_count": 0, "max_count": 2, "required": false, "id_field_id": {"field_name": "window_pictures_id"}, "caption_field_id": {"field_name": "window_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows", "de": "<PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_different_windows_on_heated_area"}, "op": "eq", "value": false, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Window picture", "de": "Fotos des Fensters"}, "description": {"en": "", "de": "Material und Art des Fensters sollte klar erkennbar sein; möglichst seitlich bzw. schräg fotografieren"}, "min_count": 0, "max_count": 2, "required": false, "id_field_id": {"field_name": "window_pictures_id"}, "caption_field_id": {"field_name": "window_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_frame_material_type"}, "title": {"en": "Window frames material", "de": "Aus welchem Material bestehen die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ALUMINIUM", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "STEEL", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "Double", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "TRIPLE", "label": {"en": "Triple", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_window_u_value_known"}, "title": {"en": "U-Value known?", "de": "U-<PERSON>rt bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "window_u_value"}, "visibility_condition": {"field_id": {"field_name": "is_window_u_value_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "U-Value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "window_condition"}, "title": {"en": "Condition of the windows", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "window_special_features"}, "title": {"en": "Special features of the windows", "de": "Besonderheiten Fenster"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "window_refurbished"}, "title": {"en": "Windows refurbished", "de": "<PERSON><PERSON> er<PERSON>"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "<PERSON><PERSON>", "de": "<PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "roof_type"}, "title": {"en": "Type of roof", "de": "Dachart"}, "example_image": "https://flow-configs.doorbit.com/assets/roof_type/example.jpg", "options": [{"key": "FLAT", "label": {"en": "Flat roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "PITCHED", "label": {"en": "Pitched roof", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "TENT", "label": {"en": "Tent roof", "de": "Z<PERSON>tda<PERSON>"}}, {"key": "GABLE", "label": {"en": "Gable roof", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HIPPED", "label": {"en": "Hipped roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "BUTTERFLY", "label": {"en": "Butterfly roof", "de": "Schmetterlingsdach"}}]}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_chimney"}, "default_value": false, "title": {"en": "Chimneys", "de": "Schornsteine"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_photovoltaics_installation"}, "default_value": false, "title": {"en": "PV installation", "de": "PV-Anlage"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_solar_installation"}, "default_value": false, "title": {"en": "Solar installation", "de": "Solarthermie<PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "top_storey_ceiling_insulation_thickness"}, "title": {"en": "Top storey ceiling insulation", "de": "Dämmungsstärke oberste Geschossdecke"}, "description": {"en": "Top storey ceiling insulation", "de": "Dämmung oberste Geschossdecke, falls dies die obere Gebäudegrenze ist (also bei Flachdächern und nicht ausgebauten Dachgeschoss)"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_pitch"}, "title": {"en": "Pitch", "de": "Neigung"}, "description": {"en": "Roof pitch", "de": "Dachneigung (0° Flachdach bis 70° Steildach)"}, "example_image": "https://flow-configs.doorbit.com/assets/roof_pitch/example.jpg"}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_rafter_thickness"}, "title": {"en": "Roof rafter thickness", "de": "Dachsparrenstärke"}, "example_image": "https://flow-configs.doorbit.com/assets/roof_rafter_thickness/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_roof_pitch_insulation"}, "title": {"en": "Is the roof pitches insulated?", "de": "Sind die Dachschrägen gedämmt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_pitch_insulation_thickness"}, "visibility_condition": {"field_id": {"field_name": "has_roof_pitch_insulation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Roof pitch insulation thickness", "de": "Dämmungsstärke der Dachschrägen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_height"}, "title": {"en": "Roof height", "de": "Dachhöhe"}, "example_image": "https://flow-configs.doorbit.com/assets/roof_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_attic"}, "title": {"en": "Is an attic present?", "de": "Ist ein Spitzboden vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "attic_height"}, "title": {"en": "Attic height", "de": "Spitzbodenhöhe"}, "visibility_condition": {"field_id": {"field_name": "has_attic"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/attic_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_kneeling"}, "title": {"en": "Is a kneeling wall present?", "de": "Ist ein Kniestock vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "knee_height"}, "title": {"en": "Knee height", "de": "Kniestockhöhe"}, "visibility_condition": {"field_id": {"field_name": "has_kneeling"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/knee_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "kneeling_offset_inwards"}, "default_value": false, "title": {"en": "Is the kneeling offset inwards?", "de": "Ist der Kniestock nach innen versetzt?"}, "visibility_condition": {"field_id": {"field_name": "has_kneeling"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/kneeling_offset_inwards/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_dormers"}, "title": {"en": "Are dormers present?", "de": "Dachgauben vorhanden?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_dormers"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Dormer Pictures", "de": "Fotos der Gauben"}, "example_image": "https://flow-configs.doorbit.com/assets/dormer_pictures/example.jpg", "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "dormer_pictures_id"}, "caption_field_id": {"field_name": "dormer_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgauben"}, "example_image": "https://flow-configs.doorbit.com/assets/dormers/example.jpg", "min_count": 0, "max_count": 20, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "dormer_type"}, "title": {"en": "Type of dormer", "de": "Um welche Art von Gaube handelt es sich?"}, "options": [{"key": "FLAT", "label": {"en": "Flat roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "GABLE", "label": {"en": "Gable roof", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HIPPED", "label": {"en": "Hipped roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "SHED", "label": {"en": "Shed roof", "de": "Schleppdachgaube"}}, {"key": "TRAPEZOIDAL", "label": {"en": "Trapezoidal roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "POINTED", "label": {"en": "Pointed roof", "de": "Spitzdach"}}, {"key": "ROUND", "label": {"en": "Rounded roof", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "BAT", "label": {"en": "Bat roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_amount"}, "title": {"en": "Amount", "de": "<PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Anzahl der Gauben dieser Art"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Gaubenbreite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height"}, "title": {"en": "Dormer height", "de": "Traufhöhe"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_roof_height"}, "title": {"en": "Dormer roof height", "de": "Firsthöhe"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_length"}, "title": {"en": "Dormer length", "de": "Gaubenlänge"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Roof panel windows", "de": "Dachflächenfenster"}, "visibility_condition": {"field_id": {"field_name": "roof_type"}, "op": "ne", "value": "FLAT", "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "roof_window_frame_material_type"}, "title": {"en": "Roof window frames material", "de": "Dachflächenfensterrahmen-Material"}, "description": {"en": "Roof window frames material", "de": "Aus welchem Material bestehen die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ALUMINIUM", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "STEEL", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "roof_window_glazing_type"}, "title": {"en": "Roof window glazing", "de": "Dachflächenfensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "Double", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "TRIPLE", "label": {"en": "Triple", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "3 Example roof window", "de": "Erfassung von bis zu 3 Beispiel-Dachflächenfenstern"}, "min_count": 0, "max_count": 3, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "roof_window_type"}, "title": {"en": "Type of roof window", "de": "Dachfenster-Art"}, "options": [{"key": "RECTANGULAR", "label": {"en": "Rectangular", "de": "Quadratisch od. recht<PERSON>ig"}}, {"key": "ROUND", "label": {"en": "Round", "de": "Rundfenster"}}, {"key": "MUNTIN", "label": {"en": "Muntin window", "de": "Sprossenfenster"}}, {"key": "GLASS_BLOCK", "label": {"en": "Glass blocks", "de": "Glasbausteine"}}, {"key": "ROUND_ARCH", "label": {"en": "Round arch", "de": "Rundbogenfenster"}}, {"key": "BOX", "label": {"en": "Box window", "de": "Kastenfenster"}}, {"key": "FLOOR_TO_CEILING", "label": {"en": "Floor-to-ceiling", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "SPECIAL", "label": {"en": "Special", "de": "Spezial/Sonderanfertigung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "roof_window_cardinal_direction"}, "title": {"en": "Cardinal Direction of Roof Window", "de": "Himmelsrichtung des Dachfensters"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "roof_window_year_of_construction"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_window_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_window_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_window_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}}}]}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Weakness Pictures", "de": "<PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Wir benötigen Foto<PERSON> von Schwachstellen im Bereich Dachgeschoss. Das können z. B. ein ungedämmtes Dach oder eine ungedämmte Dachbodendecke sein."}, "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "weakness_roof_pictures_id"}, "caption_field_id": {"field_name": "weakness_roof_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_different_doors_on_heated_area"}, "title": {"en": "Are there multiple different exterior doors?", "de": "Gibt es mehrere verschiedene Außentüren?"}}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "Caption of up to 5 outside door pictures", "de": "Erfassung von bis zu 5 unterschiedlichen Außentüren"}, "visibility_condition": {"field_id": {"field_name": "has_different_doors_on_heated_area"}, "op": "eq", "value": true, "operator_type": "RFO"}, "min_count": 0, "max_count": 5, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "outside_door_material_type"}, "title": {"en": "Outside door material", "de": "Material der Außentür"}, "options": [{"key": "SINGLE", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "TRIPLE", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "OTHER", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "door_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "door_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_door_u_value_known"}, "title": {"en": "U-Value known?", "de": "U-<PERSON>rt bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "door_u_value"}, "visibility_condition": {"field_id": {"field_name": "is_door_u_value_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "U-Value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Picture", "de": "Foto der Außentür"}, "visibility_condition": {"field_id": {"field_name": "has_different_doors_on_heated_area"}, "op": "eq", "value": true, "operator_type": "RFO"}, "min_count": 0, "max_count": 2, "required": false, "id_field_id": {"field_name": "outside_door_pictures_id"}, "caption_field_id": {"field_name": "outside_door_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_different_doors_on_heated_area"}, "op": "eq", "value": false, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Pictures of the outside doors", "de": "Fotos der Außentüren"}, "min_count": 0, "max_count": 2, "required": false, "id_field_id": {"field_name": "outside_door_pictures_id"}, "caption_field_id": {"field_name": "outside_door_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "outside_door_material_type"}, "title": {"en": "Outside door material", "de": "Material der Außentür"}, "options": [{"key": "SINGLE", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "TRIPLE", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "OTHER", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "door_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "door_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_door_u_value_known"}, "title": {"en": "U-Value known?", "de": "U-<PERSON>rt bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "door_u_value"}, "visibility_condition": {"field_id": {"field_name": "is_door_u_value_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "U-Value", "de": "U-Wert"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Balconies / Terraces", "de": "Balkone / Terrassen"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_balcony_or_terrace"}, "title": {"en": "Is a terrace or balcony present?", "de": "Terrasse oder Balkon vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "balcony_count"}, "visibility_condition": {"field_id": {"field_name": "has_balcony_or_terrace"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Balcony count", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "balcony_or_terrace_notes"}, "visibility_condition": {"field_id": {"field_name": "has_balcony_or_terrace"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Notes about the balcony or terrace", "de": "Notizen zu Balkon/Terrasse"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-uvalues", "title": {"en": "U-value assistant", "de": "<PERSON><PERSON><PERSON><PERSON> Assistent"}, "icon": "mdiAlphaU", "elements": [{"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "text": {"en": "The U-value assistant helps you to determine the U-values of your building elements.", "de": "Mit dem U-Wert Assistenten können schnell und einfach die U-Werte von Gebäudebauteilen ermittelt werden. Wir nutzen das Verfahren aus dem Bundesanzeiger, um mit wenigen Angaben eine Ersteinschätzung zu erhalten. Die so errechneten Werte werden automatisch auf das Gebäudemodell übertragen und sind sofort in allen Doorbit Exporten enthalten."}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": true, "field_id": {"field_name": "year_of_construction"}, "title": {"en": "Building year of construction", "de": "Gebäudebaujahr"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Ground plates", "de": "Unterste Geschossböden"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_type"}, "title": {"en": "What is beneath the ground floor?", "de": "Was liegt unterhalb des Erdgeschosses?"}, "options": [{"key": "NO_CELLAR", "label": {"en": "Ground plate", "de": "Bodenplatte"}}, {"key": "UNHEATED_CELLAR", "label": {"en": "Unheated cellar", "de": "Unbehe<PERSON><PERSON> Keller"}}, {"key": "PARTIALLY_HEATED_CELLAR", "label": {"en": "Partially heated cellar", "de": "Teilbeheizter Keller"}}, {"key": "HEATED_CELLAR", "label": {"en": "Heated cellar", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_ceiling_type"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "NO_CELLAR", "operator_type": "RFO"}, {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": null, "operator_type": "RFO"}]}, "title": {"en": "Cellar ceiling type", "de": "Kellerdeckentyp"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Redescriptionrced concrete", "de": "Stahlbeton"}}, {"key": "HOLLOW_STONES", "label": {"en": "Hollow stone construction", "de": "Ziegel oder Hohlsteine"}}, {"key": "WOODEN_BEAMS", "label": {"en": "Wooden-beams ceiling", "de": "Holzbalkendecke"}}, {"key": "VAULTED_CELLAR", "label": {"en": "Valuted cellar", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "cellar_ceiling_other_type"}}}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "cellar_ceiling_u_value"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "NO_CELLAR", "operator_type": "RFO"}, {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": null, "operator_type": "RFO"}]}, "title": {"en": "U-value cellar ceiling", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "ground_slab_construction_type"}, "title": {"en": "Construction type of the ground slab", "de": "Bauweise der Bodenplatte"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Reinforced concrete", "de": "Stahlbeton"}}, {"key": "HOLLOW_STONES", "label": {"en": "Massive bricks", "de": "<PERSON>l<PERSON>gel"}}, {"key": "MASSIVE_WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "EMPTY_SPACE_WOODEN_BEAMS", "label": {"en": "Suspended timber floor", "de": "Holzdielen auf Lagerhölzern (Hohlraum)"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "ground_slab_u_value"}, "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside walls", "de": "Außenwände"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "wall_construction_type"}, "title": {"en": "Construction type of the outside walls", "de": "Bauweise der Außenwände"}, "options": [{"key": "SOLID_BRICK", "label": {"en": "Solid brick", "de": "<PERSON>l<PERSON>gel"}}, {"key": "KALKSANDSTEIN", "label": {"en": "Calcium silicate brick", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "CONCRETE", "label": {"en": "Concrete", "de": "<PERSON><PERSON>"}}, {"key": "QUARRY_STONE", "label": {"en": "Quarry stone", "de": "Bruchstein"}}, {"key": "POROBETON", "label": {"en": "Little brick", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "PERFORATED_BRICK", "label": {"en": "Highly perforated brick", "de": "<PERSON><PERSON>lochziegel"}}, {"key": "DOUBLE_SKIN_CONSTRUCTION", "label": {"en": "2-layered construction", "de": "Zweischaliger <PERSON>"}}, {"key": "DOUBLE_SKIN_CONSTRUCTION_WITH_INSULATION", "label": {"en": "2-layered construction with insulation", "de": "Zweischaliger Wandaufbau mit Dämmschicht"}}, {"key": "OTHER_MASSIVE", "label": {"en": "Massive construction (other/unknown)", "de": "<PERSON><PERSON> (Sonstige/Unbekannt)"}}, {"key": "SOLID_WOOD_CONSTRUCTION", "label": {"en": "Solid wood construction", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HALF_TIMBERED_WITH_CLAY_INFILL", "label": {"en": "Half-timbered wall - Clay", "de": "Fachwerk - Lehm"}}, {"key": "HALF_TIMBERED_WITH_SOLID_INFILL", "label": {"en": "Half-timbered wall - Solid bricks", "de": "Fachwerk - Ziegel"}}, {"key": "OTHER_WOOD_CONSTRUCTION", "label": {"en": "Other wood construction", "de": "Sonstige Holzkonstruktion"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "wall_construction_wall_thickness"}, "title": {"en": "Wall thickness", "de": "Wandstärke"}, "options": [{"key": "UNTIL_20CM", "label": {"en": "Up to 20 cm", "de": "Bis 20 cm"}}, {"key": "FROM_20CM_TO_30CM", "label": {"en": "20 - 30 cm", "de": "20 - 30 cm"}}, {"key": "ABOVE_30CM", "label": {"en": "More than 30 cm", "de": "Über 30 cm"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "wall_u_value"}, "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows and windowed doors", "de": "Fenster und Fenstertüren"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_different_window_year_of_construction"}, "title": {"en": "Different year of construction than building", "de": "Baujahr der Fenster weicht vom Gebäudebaujahr ab"}, "description": {"en": "If the majority of the windows have already been replaced, please indicate.", "de": "Falls die Mehrheit der Fenster bereits erneuert wurden, bitte angeben."}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_year_of_construction_period"}, "visibility_condition": {"field_id": {"field_name": "has_different_window_year_of_construction"}, "op": "eq", "value": true, "operator_type": "RFO"}, "type": "BUTTON_GROUP", "title": {"en": "Window construction year", "de": "Abweichendes Baujahr der Fenster"}, "options": [{"key": "BEFORE_1978", "label": {"en": "Until 1978", "de": "bis 1978"}}, {"key": "BETWEEN_1978_AND_1994", "label": {"en": "1979 - 1983", "de": "1979 - 1983"}}, {"key": "BETWEEN_1984_AND_1994", "label": {"en": "1984 - 1994", "de": "1984 - 1994"}}, {"key": "BETWEEN_1995_AND_2001", "label": {"en": "1995 - 2001", "de": "1995 - 2001"}}, {"key": "AFTER_2002", "label": {"en": "After 2002", "de": "ab 2002"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_frame_material_type"}, "title": {"en": "From what material are the window frames?", "de": "Aus welchem Material sind die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "METAL", "label": {"en": "Metal", "de": "Metall"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach"}}, {"key": "DOUBLE_ISOLATED", "label": {"en": "Double glazing with thermal insulation", "de": "2-fach (Wärmeschutzverglasung)"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "window_u_value"}, "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "door_frame_material_type"}, "title": {"en": "From what material are the door frames?", "de": "Aus welchem Material sind die Türrahmen?"}, "options": [{"key": "METAL", "label": {"en": "Mostly metal", "de": "Überwiegend Metall"}}, {"key": "WOOD", "label": {"en": "Mostly Wood or plastic", "de": "Überwiegend Holz oder Kunststoff"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "door_u_value"}, "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Top floor ceiling", "de": "Oberste Geschossdecke"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "top_level_ceiling_construction_type"}, "title": {"en": "Construction type of the top floor ceiling", "de": "Bauweise der obersten Geschossdecke"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Concrete", "de": "Massive <PERSON>"}}, {"key": "MASSIVE_WOOD", "label": {"en": "Wooden beams", "de": "Holzbalkendecke"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "top_level_ceiling_u_value"}, "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-tga", "short_title": {"en": "Technical building equipment", "de": "Haustechnik"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Heating", "de": "Heizung"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "underfloor_heating_notes"}, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "radiators_notes"}, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_source"}, "title": {"en": "Primary energy source", "de": "Hauptenergieträger"}, "options": [{"key": "GAS", "label": {"en": "Gas", "de": "Gas"}}, {"key": "OIL", "label": {"en": "Oil", "de": "<PERSON><PERSON>"}}, {"key": "PELLETS", "label": {"en": "Pellets", "de": "Pellets"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ELECTRICITY", "label": {"en": "Electricity", "de": "<PERSON><PERSON>"}}, {"key": "ENVIRONMENTAL", "label": {"en": "Heat pump", "de": "Wärmepumpe"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "additional_energy_sources_notes"}, "title": {"en": "Additional energy sources", "de": "Weitere Energieträger"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "heating_pipes_insulation_notes"}, "title": {"en": "Insulation of heating pipes", "de": "Dämmung Heizungsleitungen"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Ventilation", "de": "Lüftung"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "ventilation_system_notes"}, "title": {"en": "Ventilation system", "de": "Lüftungsanlage"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "ventilation_type"}, "title": {"en": "Type of ventilation", "de": "Lüftung"}, "options": [{"key": "WINDOW_VENTILATION", "label": {"en": "Window", "de": "Fensterlüftung"}}, {"key": "SHAFT_VENTILATION", "label": {"en": "Shaft ventilation", "de": "Schachtlüftung"}}, {"key": "SIMPLE_VENTILATION_SYSTEM", "label": {"en": "Ventilation system without heat recovery", "de": "Lüftungssystem ohne Wärmerückgewinnung"}}, {"key": "VENTILATION_SYSTEM_WITH_HEAT_RECOVERY", "label": {"en": "Ventilation system with heat recovery", "de": "Lüftungssystem mit Wärmerückgewinnung"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_air_condition"}, "default_value": false, "title": {"en": "Air condition available?", "de": "Ist eine Klimaanlage verbaut?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Air conditioning", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_air_condition"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/air_condition_pictures/example.jpg", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "A/C pictures", "de": "Fotos der Klimaanlage"}, "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "air_condition_pictures_id"}, "caption_field_id": {"field_name": "air_condition_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "cooled_area"}, "title": {"en": "Cooled area of A/C", "de": "Gekühlte Fläche der Klimaanlage"}, "description": {"en": "", "de": "Die Fläche, die von der Klimaanlage typischerweise gekühlt wird."}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "air_condition_thermal_output"}, "title": {"en": "A/C thermal output", "de": "Klimaanlagenleistung"}, "description": {"en": "", "de": "<PERSON>t meist auf dem Typenschild erkennbar. <PERSON><PERSON> in kW"}}}, {"element": {"pattern_type": "DateUIElement", "type": "YM", "required": false, "field_id": {"field_name": "air_condition_last_inspection_date"}, "title": {"en": "Last inspection in case A/C output is greater than 12 KW", "de": "Letzte Inspektive"}, "description": {"en": "", "de": "Nur bei Anlagen größer 12 kW Nennleistung relevant"}, "minimum": "-5Y", "maximum": "-0Y"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "PV-Installation", "de": "PV-Anlage"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_photovoltaics_installation"}, "default_value": false, "title": {"en": "Is PV available?", "de": "Ist eine Photovoltaik Anlage verbaut?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Photovoltaic", "de": "Photovoltaik"}, "visibility_condition": {"field_id": {"field_name": "has_photovoltaics_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "isCollapsible": true, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_type"}, "title": {"en": "Type of PV installation", "de": "Art der Photovoltaik-Anlage"}, "options": [{"key": "MONOCRYSTALLINE_SILICIUM", "label": {"en": "Monocrystalline silicium (black panels)", "de": "Monokristallines Silizium (schwarze Module)"}, "example_image": "https://flow-configs.doorbit.com/assets/pv_type/Monokristallines-Silizium.jpg"}, {"key": "POLYCRYSTALLINE_SILICIUM", "label": {"en": "Polycrystalline silicium (blue panels)", "de": "Polykristallines Silizium (blaue Module)"}, "example_image": "https://flow-configs.doorbit.com/assets/pv_type/Polykristallines-Silizium.jpg"}, {"key": "CADMIUM_TELLURIDE", "label": {"en": "Cadmium telluride thin film", "de": "Cadmium-Tellurid-Dünnschicht"}, "example_image": "https://flow-configs.doorbit.com/assets/pv_type/Cadmium-Tellurid-Duennschicht.jpg"}, {"key": "AMORPHOUS_SILICIUM", "label": {"en": "Thin-film module made of amorphous silicon", "de": "Dünnschichtmodul aus amorphem Silizium"}, "example_image": "https://flow-configs.doorbit.com/assets/pv_type/Duennschichtmodul-aus-amorphem-Silizium.jpg"}, {"key": "COPPER_INDIUM_GALLIUM_SELENIDE", "label": {"en": "Copper indium gallium diselenide", "de": "Kupfer-Indium-Gallium-Diselenid"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "pv_type_other"}}}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_ventilation_type"}, "title": {"en": "Type of ventilation for PV", "de": "Belüftung der PV-Anlage"}, "options": [{"key": "WELL_VENTILATED", "label": {"en": "Well ventilated", "de": "<PERSON><PERSON> belüftet"}}, {"key": "SOMEWHAT_VENTILATED", "label": {"en": "Somewhat ventilated", "de": "Mäßig belüftet"}}, {"key": "UNVENTILATED", "label": {"en": "Unventilated", "de": "Unbelüftet"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_area"}, "title": {"en": "PV area", "de": "PV-Fläche"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_output"}, "title": {"en": "PV output", "de": "PV-Leistung"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_cardinal_direction_type"}, "title": {"en": "PV cardinal direction", "de": "PV Himmelsausrichtung"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_pitch"}, "title": {"en": "PV pitch", "de": "PV-Neigung"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_battery_size"}, "title": {"en": "PV battery capacity", "de": "PV-Batteriekapazität"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Solar installation", "de": "Solarthermie<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_solar_installation"}, "default_value": false, "title": {"en": "Is solar available?", "de": "Ist eine Solaranlage verbaut?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Solar installation", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_solar_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_type"}, "title": {"en": "Type of solar collector", "de": "Art des Sonnenkollektors"}, "options": [{"key": "COVERED_FLAT_COLLECTOR", "label": {"en": "Covered flat collector", "de": "Abgedeck<PERSON> Flach-Kollektor"}, "example_image": "https://flow-configs.doorbit.com/assets/solar_collector_type/Abgedeckter-Flach-Kollektor.jpg"}, {"key": "VACUUM_TUBE_COLLECTOR", "label": {"en": "Vacuum-tubes collector", "de": "Vakuum-Röhren-Kollektor"}, "example_image": "https://flow-configs.doorbit.com/assets/solar_collector_type/Vakuum-Roehren-Kollektor.jpg"}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_usage_type"}, "title": {"en": "Solar collector usage", "de": "Wie wird der Kollektor verwendet?"}, "options": [{"key": "HOT_WATER", "label": {"en": "Hot water generation", "de": "Warmwassererzeugung"}}, {"key": "ROOM_HEATING", "label": {"en": "Room heating", "de": "Raumheizung"}}, {"key": "COMBINED", "label": {"en": "Room heating and hot water generation", "de": "Raumheizung und Warmwassererzeugung"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_area"}, "title": {"en": "Solar collector area", "de": "Sonnenkollektorfläche"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_cardinal_direction_type"}, "title": {"en": "Solar collection cardinal direction", "de": "Solarkollektor Himmelsausrichtung"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_pitch"}, "title": {"en": "Collector pitch", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Bei Schrägdächern in der Regel gleich der Dachneigung"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_waterstorage"}, "title": {"en": "Collector storage size", "de": "Größe des Solarwärmespeichers in Litern"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_cold_water_temp"}, "title": {"en": "Cold water temperature", "de": "Kaltwassertemperatur"}}}]}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "switches_sockets"}, "title": {"en": "Switches and sockets", "de": "Schalter und Steckdosen"}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-documents", "short_title": {"en": "Documents", "de": "Unterlagen"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Documents", "de": "Unterlagen"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_floor_plans"}, "title": {"en": "Floor plans available", "de": "Grundrisse vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "floor_plans_link"}, "visibility_condition": {"field_id": {"field_name": "has_floor_plans"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Floor plans link", "de": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_building_permit"}, "title": {"en": "Building permit available", "de": "Baugenehmigung vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "building_permit_link"}, "visibility_condition": {"field_id": {"field_name": "has_building_permit"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Building permit link", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_building_description"}, "title": {"en": "Building description available", "de": "Baubeschreibung vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "building_description_link"}, "visibility_condition": {"field_id": {"field_name": "has_building_description"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Building description link", "de": "Ba<PERSON>schreibung (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_heating_cost_statement"}, "title": {"en": "Heating cost statement available", "de": "Heizkostenabrechnung vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "heating_cost_statement_link"}, "visibility_condition": {"field_id": {"field_name": "has_heating_cost_statement"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Heating cost statement link", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_chimney_sweep_protocols"}, "title": {"en": "Chimney sweep protocols available", "de": "Protokolle Schornsteinfeger vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "chimney_sweep_protocols_link"}, "visibility_condition": {"field_id": {"field_name": "has_chimney_sweep_protocols"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Chimney sweep protocols link", "de": "<PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_energy_certificate"}, "title": {"en": "Energy certificate available", "de": "Energieausweis vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "energy_certificate_link"}, "visibility_condition": {"field_id": {"field_name": "has_energy_certificate"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Energy certificate link", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_craftsman_quotes"}, "title": {"en": "Craftsman quotes available", "de": "Handwerkerangebote vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "craftsman_quotes_link"}, "visibility_condition": {"field_id": {"field_name": "has_craftsman_quotes"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Craftsman quotes link", "de": "Handwerkerangebote (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_craftsman_invoices"}, "title": {"en": "Craftsman invoices available", "de": "Handwerkerrechnungen vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "craftsman_invoices_link"}, "visibility_condition": {"field_id": {"field_name": "has_craftsman_invoices"}, "op": "eq", "value": "RECEIVED", "operator_type": "RFO"}, "title": {"en": "Craftsman invoices link", "de": "Handwerker<PERSON><PERSON><PERSON> (Link)"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "has_owner_notes"}, "title": {"en": "Owner notes available", "de": "Notizen des Eigentümers vorhanden"}, "options": [{"key": "NOT_AVAILABLE", "label": {"en": "Not available", "de": "Nicht vorhanden"}}, {"key": "REQUESTED", "label": {"en": "Requested", "de": "Ang<PERSON><PERSON><PERSON>"}}, {"key": "RECEIVED", "label": {"en": "Received", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-services", "short_title": {"en": "Services", "de": "Leistungsumfang"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Demolition", "de": "Entkernung"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "gutting_and_demolition"}, "title": {"en": "Gutting and demolition", "de": "Entkernung und Abriss (Zeitpunkt, Eigenleistung)"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Insulation measures", "de": "Dämmmaßnahmen"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_dachdaemmung"}, "default_value": false, "title": {"en": "Roof insulation", "de": "Dachdämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_dachdaemmung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_dachdaemmung_notiz"}, "title": {"en": "Roof insulation note", "de": "Notiz zur Dachdämmung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_fassadendaemmung"}, "default_value": false, "title": {"en": "Facade insulation", "de": "Fassadendämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_fassadendaemmung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_fassadendaemmung_notiz"}, "title": {"en": "Facade insulation note", "de": "Notiz zur Fassadendämmung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_fenster<PERSON><PERSON>ch"}, "default_value": false, "title": {"en": "Window replacement", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_fenster<PERSON><PERSON>ch"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_fensterta<PERSON>ch_notiz"}, "title": {"en": "Window replacement note", "de": "Notiz zum Fenster<PERSON>ch"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_kellerdaemmung"}, "default_value": false, "title": {"en": "Basement insulation", "de": "Kellerdämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_kellerdaemmung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_kellerdaemmung_notiz"}, "title": {"en": "Basement insulation note", "de": "Notiz zur Kellerdämmung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_sockeldaemmung"}, "default_value": false, "title": {"en": "Base insulation", "de": "Sockeldämmung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_sockeldaemmung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_sockeldaemmung_notiz"}, "title": {"en": "Base insulation note", "de": "Notiz zur Sockeldämmung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_abdichtung_kellerwaende"}, "default_value": false, "title": {"en": "Sealing basement walls", "de": "Abdichtung Kellerwände"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_abdichtung_kellerwaende"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_abdichtung_kellerwaende_notiz"}, "title": {"en": "Sealing basement walls note", "de": "Notiz zur Abdichtung der Kellerwände"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_balkone_ueber_wohnraeumen"}, "default_value": false, "title": {"en": "Balconies over living spaces", "de": "Balkone über Wohnräumen"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_balkone_ueber_wohnraeumen"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_balkone_ueber_wohnraeumen_notiz"}, "title": {"en": "Balconies over living spaces note", "de": "Notiz zu Balkonen über Wohnräumen"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_sonstiges"}, "default_value": false, "title": {"en": "Other", "de": "Sonstiges"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_sonstiges"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_sonstiges_notiz"}, "title": {"en": "Other note", "de": "<PERSON><PERSON>"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Rooms / Architecture", "de": "Räume / Architektur"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_grundrissaenderungen"}, "default_value": false, "title": {"en": "Floor plan changes", "de": "Grundrissänderungen"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_grundrissaenderungen"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_grundrissaenderungen_notiz"}, "title": {"en": "Floor plan changes note", "de": "Notiz zu Grundrissänderungen"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_entfernung_balkone"}, "default_value": false, "title": {"en": "Balcony removal", "de": "Entfernung Balkone"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_entfernung_balkone"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_entfernung_balkone_notiz"}, "title": {"en": "Balcony removal note", "de": "Notiz zur Entfernung der Balkone"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_wohnraumerweiterungen"}, "default_value": false, "title": {"en": "Living space extensions", "de": "Wohnraumerweiterungen"}, "description": {"en": "Living space extensions (e.g., addition) typically require a building permit, which involves additional time and cost. Check according to LBO.", "de": "Wohnraumerweiterungen (z.B. Anbau) erfordern in der Regel eine Genehmigung über einen Bauantrag, der zusätzlich Zeit und Geld kostet, jew<PERSON>s nach LBO zu prüfen."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_wohnraumerweiterungen"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_wohnraumerweiterungen_notiz"}, "title": {"en": "Living space extensions note", "de": "<PERSON>iz zu Wohnraumerweiterungen"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_gauben_dachaufbauten"}, "default_value": false, "title": {"en": "Dormers or roof structures", "de": "Gauben oder Dachaufbauten"}, "description": {"en": "For dormer size, check the LBO for exemption/requirement of permit. In Hamburg: dormers up to ⅓ of roof width are exempt from permits.", "de": "<PERSON><PERSON><PERSON>bengröße ist die LBO zu prüfen hinsichtlich Genehmigungsfreiheit /-erfordernis, in Hamburg: Gaube bis ⅓ Dachbreite genehmigungsfrei."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_gauben_dachaufbauten"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_gauben_dachaufbauten_notiz"}, "title": {"en": "Dormers or roof structures note", "de": "<PERSON>iz zu Gauben oder Dachaufbauten"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_entfernung_schornstein"}, "default_value": false, "title": {"en": "Chimney removal", "de": "Entfer<PERSON><PERSON>"}, "description": {"en": "Removal only on the roof or also in rooms (requires closure of openings after removal).", "de": "Nur auf dem Dach oder auch in den Räumen (er<PERSON>ert <PERSON>hl<PERSON>ßung der Öffnungen nach Entfernung)."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_entfernung_schornstein"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_entfernung_schornstein_notiz"}, "title": {"en": "Chimney removal note", "de": "Notiz zur Entfernung des Schornsteins"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_erneuerung_boeden"}, "default_value": false, "title": {"en": "Floor renewal", "de": "Erneuerung Böden"}, "description": {"en": "What flooring is desired for each area? (Best entered into room plan, including stairs).", "de": "Welcher Belag wird jeweils gewünscht? (am besten in Raumplan eintragen, inkl. Treppen)."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_erneuerung_boeden"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_erneuerung_boeden_notiz"}, "title": {"en": "Floor renewal note", "de": "Notiz zur Erneuerung der Böden"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_fussbodenheizung"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}, "description": {"en": "If yes and floor structures are already known, record the initial idea of the desired floor structure.", "de": "Falls ja und Bodenaufbauten schon bekannt: erste Idee Bodenaufbau SOLL festhalten."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_fussbodenheizung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_fussbodenheizung_notiz"}, "title": {"en": "Underfloor heating note", "de": "Notiz zur Fußbodenheizung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_fenster<PERSON><PERSON>ch"}, "default_value": false, "title": {"en": "Window replacement", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "As before or style change desired (e.g., new with bars)? Adjustments to heights, e.g., through new floor-to-ceiling windows with removal of parapets? Color scheme for new windows outside/inside (e.g., anthracite grey/white).", "de": "Wie bisher oder Stiländerung gewünscht (z.B. neu mit Sprossen)? Anpassungen an Höhen, z.B. durch neue bodentiefe Fenster mit Brüstungsentfernung? Farbgebung neue Fenster außen/innen (z.B. anthrazitgrau/weiss)."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_fenster<PERSON><PERSON>ch"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_fensterta<PERSON>ch_notiz"}, "title": {"en": "Window replacement note", "de": "Notiz zum Fenster<PERSON>ch"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_besonderheiten"}, "default_value": false, "title": {"en": "Special features", "de": "Besonderheiten"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_besonderheiten"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_besonderheiten_notiz"}, "title": {"en": "Special features note", "de": "<PERSON><PERSON> zu Besonderheiten"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Equipment wishes", "de": "Ausstattungswünsche"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_baderneuerung"}, "default_value": false, "title": {"en": "Bathroom renovation", "de": "Baderneuerung"}, "description": {"en": "Desired bathroom equipment line. Basic / Advanced / Premium, how many bathrooms of which type (guest WC, shower bath, full bath).", "de": "Gewünschte Badausstattungslinie. Basic / Advanced / Premium, wie viele Bäder welcher Art (Gäste-WC, Duschbad, Vollbad)."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_baderneuerung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_baderneuerung_notiz"}, "title": {"en": "Bathroom renovation note", "de": "Notiz zur Baderneuerung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_sanitaer"}, "default_value": false, "title": {"en": "Sanitary", "de": "Sanitär"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_sanitaer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_sanitaer_notiz"}, "title": {"en": "Sanitary note", "de": "<PERSON><PERSON>itär"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_kueche"}, "default_value": false, "title": {"en": "Kitchen", "de": "<PERSON><PERSON><PERSON>"}, "description": {"en": "What is the budget? Only relevant for cost estimation, not included in the offer later, as <PERSON><PERSON> currently does not offer kitchen installation.", "de": "Welches Budget? nur relevant für Kostenschätzung, im Angebot später nicht enthalten, <PERSON> <PERSON><PERSON>installation aktuell noch nicht mit anbietet."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_kueche"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_kueche_notiz"}, "title": {"en": "Kitchen note", "de": "Notiz zur Küche"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_bodenbelaege"}, "default_value": false, "title": {"en": "Floor coverings", "de": "Bodenbeläge"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_bodenbelaege"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_bodenbelaege_notiz"}, "title": {"en": "Floor coverings note", "de": "<PERSON>iz zu Bodenbeläge"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_hauseingang"}, "default_value": false, "title": {"en": "Entrance", "de": "Hauseingang"}, "description": {"en": "With front door and possibly canopy?", "de": "<PERSON><PERSON> und ggf. <PERSON>?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_hauseingang"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_hauseingang_notiz"}, "title": {"en": "Entrance note", "de": "<PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_verschattung"}, "default_value": false, "title": {"en": "Shading", "de": "Verschattung"}, "description": {"en": "Shutters, screens, venetian blinds?", "de": "Rollladen, Screen, Raffstore?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_verschattung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_verschattung_notiz"}, "title": {"en": "Shading note", "de": "<PERSON><PERSON> zu Verschattung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_aussenfensterbaenke"}, "default_value": false, "title": {"en": "Exterior window sills", "de": "Außen-Fensterbänke"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_aussenfensterbaenke"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_aussenfensterbaenke_notiz"}, "title": {"en": "Exterior window sills note", "de": "Notiz zu Außen-Fensterbänke"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_innentueren"}, "default_value": false, "title": {"en": "Interior doors", "de": "Innentüren"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_innentueren"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_innentueren_notiz"}, "title": {"en": "Interior doors note", "de": "<PERSON><PERSON>ü<PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_treppen"}, "default_value": false, "title": {"en": "Stairs", "de": "Treppen"}, "description": {"en": "Completely new, new covering, refurbish old covering, etc.?", "de": "<PERSON><PERSON><PERSON> neu, neuer Belag, alten Belag aufarbeiten, etc.?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_treppen"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_treppen_notiz"}, "title": {"en": "Stairs note", "de": "<PERSON><PERSON>"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_sonstige_ausstattungswuensche"}, "default_value": false, "title": {"en": "Other equipment wishes", "de": "Sonstige Ausstattungswünsche"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_sonstige_ausstattungswuensche"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_sonstige_ausstattungswuensche_notiz"}, "title": {"en": "Other equipment wishes note", "de": "Notiz zu sonstigen Ausstattungswünschen"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Technical installations", "de": "Technik"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_waermeerzeuger"}, "default_value": false, "title": {"en": "Heat generator", "de": "Wärmeerzeuger"}, "description": {"en": "Standard: Air-water heat pump", "de": "Standard: Luft-Wasser-Wärmepumpe"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_waermeerzeuger"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_waermeerzeuger_notiz"}, "title": {"en": "Heat generator note", "de": "Notiz zum Wärmeerzeuger"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_aufstellort_neuer_waermeerzeuger"}, "default_value": false, "title": {"en": "Location of new heat generator", "de": "Aufstellort neuer Wärmeerzeuger"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_aufstellort_neuer_waermeerzeuger"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_aufstellort_neuer_waermeerzeuger_notiz"}, "title": {"en": "Location of new heat generator note", "de": "Notiz zum Aufstellort neuer Wärmeerzeuger"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_aufstellort_aussengeraet_waermepumpe"}, "default_value": false, "title": {"en": "Location of outdoor unit heat pump", "de": "Aufstellort Außengerät Wärmepumpe"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_aufstellort_aussengeraet_waermepumpe"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_aufstellort_aussengeraet_waermepumpe_notiz"}, "title": {"en": "Location of outdoor unit heat pump note", "de": "Notiz zum Aufstellort Außengerät Wärmepumpe"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_waermeverteilung"}, "default_value": false, "title": {"en": "Heat distribution", "de": "Wärmeverteilung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_waermeverteilung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_waermeverteilung_notiz"}, "title": {"en": "Heat distribution note", "de": "Notiz zur Wärmeverteilung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_kontrollierte_wohnraumlueftung"}, "default_value": false, "title": {"en": "Controlled residential ventilation with heat recovery", "de": "Kontrollierte Wohnraumlüftung mit Wärmerückgewinnung"}, "description": {"en": "Required for EE-Bonus in KfW-EH-Funding.", "de": "Wird für EE-Bonus bei der KfW-EH-Förderung zwingend benötigt."}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_kontrollierte_wohnraumlueftung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_kontrollierte_wohnraumlueftung_notiz"}, "title": {"en": "Controlled residential ventilation note", "de": "Notiz zur kontrollierten Wohnraumlüftung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_neuinstallation_elektrotechnik"}, "default_value": false, "title": {"en": "New electrical installation", "de": "Neuinstallation Elektrotechnik"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_neuinstallation_elektrotechnik"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_neuinstallation_elektrotechnik_notiz"}, "title": {"en": "New electrical installation note", "de": "Notiz zur Neuinstallation Elektrotechnik"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_pv_anlage"}, "default_value": false, "title": {"en": "PV system", "de": "PV-Anlage"}, "description": {"en": "With full roof coverage or other goal? What obstructions?", "de": "Mit Dach-Vollbelegung oder andere Zielstellung? Welche Störflächen?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_pv_anlage"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_pv_anlage_notiz"}, "title": {"en": "PV system note", "de": "Notiz zur PV-Anlage"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Construction site arrangement", "de": "Baustelleneinrichtung"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_toilettennutzung"}, "default_value": false, "title": {"en": "Toilet use during construction", "de": "Toilettennutzung während der Bauphase"}, "description": {"en": "To be used? If so, where?", "de": "Genutzt werden? Wenn ja, wo?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_toilettennutzung"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_toilettennutzung_notiz"}, "title": {"en": "Toilet use note", "de": "Notiz zur Toilettennutzung"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_baustellenbuero"}, "default_value": false, "title": {"en": "Construction site office", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "Is there a place that can be used as a construction site office during the renovation? If so, where?", "de": "Gibt es einen Ort, der als Baustellenbüro während der Sanierung genutzt werden kann? Wenn ja, wo?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_baustellenbuero"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_baustellenbuero_notiz"}, "title": {"en": "Construction site office note", "de": "Notiz zum Baustellenbüro"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "services_park_und_anliefersituation"}, "default_value": false, "title": {"en": "Parking and delivery situation", "de": "Park- und Anliefersituation"}, "description": {"en": "On site? Must a no-parking zone be established?", "de": "Vor Ort? Muss eine Halteverbotszone eingerichtet werden?"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "visibility_condition": {"field_id": {"field_name": "services_park_und_anliefersituation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "services_park_und_anliefersituation_notiz"}, "title": {"en": "Parking and delivery situation note", "de": "Notiz zur Park- und Anliefersituation"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-configuration", "type": "PROJECT_CONFIGURATOR", "icon": "mdiOrderBoolDescendingVariant", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-configuration"}], "short_title": {"en": "Project configuration", "de": "Projekt-Konfigurator"}, "elements": []}], "pages_view": [{"pattern_type": "CustomUIElement", "id": "view-general", "icon": "mdiHome", "layout": "2_COL_RIGHT_WIDER", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-summary"}], "short_title": {"en": "Building", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M_NARROW", "id_field_value": {"field_id": {"field_name": "covershot_image_id"}}}}]}}, {"element": {"pattern_type": "FieldTextUIElement", "type": "HEADING", "field_value": {"field_id": {"field_name": "ext_id"}}}}, {"element": {"pattern_type": "FieldTextUIElement", "type": "PARAGRAPH", "field_value": {"field_id": {"field_name": "sub_type"}}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": null, "key": {"en": "Building width", "de": "Gebäudebreite"}, "field_value": {"field_id": {"field_name": "outline_width"}}}, {"icon": null, "key": {"en": "Building depth", "de": "Gebäudetiefe"}, "field_value": {"field_id": {"field_name": "outline_depth"}}}, {"icon": null, "key": {"en": "Ceiling and slab thickness", "de": "Boden- und Deckenstärke"}, "field_value": {"field_id": {"field_name": "ceiling_thickness"}}}]}}, {"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-view", "related_custom_ui_element_id": "model-edit", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightSwitch", "field_id": {"field_name": "building_poi_is_light_switch"}, "default_value": false, "title": {"en": "Light switch", "de": "Lichtschalter"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiPowerSocketDe", "field_id": {"field_name": "building_poi_is_wall_socket"}, "default_value": false, "title": {"en": "Wall socket", "de": "Steckdose"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWindowClosed", "field_id": {"field_name": "building_poi_is_window_note"}, "default_value": false, "title": {"en": "Window", "de": "<PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiAddBox", "field_id": {"field_name": "building_poi_is_special_thermal_component"}, "default_value": false, "title": {"en": "Special thermal envelope component", "de": "Sonderbauteil thermische Hülle"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiHomeRoof", "field_id": {"field_name": "building_poi_is_dormer"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgaube"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Details", "de": "Fensterdetails"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Glazing", "de": "Verglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach Verglasung"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach Verglasung"}}, {"key": "DOUBLE_THERMAL", "label": {"en": "Double thermal insulation glazing", "de": "2-fach Wärmeschutzverglasung"}}, {"key": "TRIPLE_THERMAL", "label": {"en": "Triple thermal insulation glazing", "de": "3-fach Wärmeschutzverglasung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_shutter_type"}, "title": {"en": "Window shutters/blinds", "de": "Fensterläden/Rollläden/Raffstores"}, "options": [{"key": "NONE", "label": {"en": "None", "de": "<PERSON><PERSON>"}}, {"key": "SHUTTERS", "label": {"en": "Window shutters", "de": "Fensterläden"}}, {"key": "ROLLER_BLINDS", "label": {"en": "Roller blinds", "de": "Rollläden"}}, {"key": "VENETIAN_BLINDS", "label": {"en": "Venetian blinds", "de": "Raffstores"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Position", "de": "Fensterposition"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_inner_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Inner reveal depth", "de": "Laibungstiefe innen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_thickness_cm"}, "unit": "CENTIMETER", "title": {"en": "Window thickness", "de": "Fensterstärke"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_outer_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Outer reveal depth", "de": "Laibungstiefe außen"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Special thermal envelope component details", "de": "Details zu Sonderbauteilen der thermischen Hülle"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "required": false, "field_id": {"field_name": "special_thermal_component_material"}, "title": {"en": "Material", "de": "Material"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "minimum": "-100Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "special_thermal_component_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Dormer details", "de": "Dachgauben-Details"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Primary Type", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}, {"key": "SPECIAL_TYPE", "label": {"en": "Special heater", "de": "Sonderbauform"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_sub_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "eq", "value": "PANEL_RADIATOR", "operator_type": "RFO"}, "title": {"en": "Secondary Type", "de": "Subtyp"}, "options": [{"key": "TYPE_10", "label": {"en": "Typ 10", "de": "Typ 10"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_11", "label": {"en": "Typ 11", "de": "Typ 11"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_20", "label": {"en": "Typ 20", "de": "Typ 20"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_21", "label": {"en": "Typ 21", "de": "Typ 21"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_22", "label": {"en": "Typ 22", "de": "Typ 22"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_23", "label": {"en": "Typ 23", "de": "Typ 23"}, "icon": "mdiRadiatorDisabled"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "unit": "CENTIMETER", "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "ne", "value": "PANEL_RADIATOR", "operator_type": "RFO"}]}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "radiator_ventildurchmesser"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Valve diameter", "de": "DN (Ventildurchmesser)"}, "options": [{"key": "MM_10", "label": {"en": "10mm", "de": "10mm"}, "icon": "mdiRuler"}, {"key": "MM_15", "label": {"en": "15mm", "de": "15mm"}, "icon": "mdiRuler"}, {"key": "MM_20", "label": {"en": "20mm", "de": "20mm"}, "icon": "mdiRuler"}, {"key": "MM_25", "label": {"en": "25mm", "de": "25mm"}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please upload at least one photo of the radiator and one photo of the valve.", "de": "Bitte mindestens Heizkörperfoto und Großaufnahme vom Ventil hochladen"}}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please add a photo from the outside and from the inside.", "de": "Bitte Foto von außen und von Innen hinzufügen"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 5, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WALL_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Inside dimensions", "de": "Innenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "inside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside dimensions", "de": "Außenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"key": {"en": "Roller Shutters", "de": "Rollladen"}, "field_value": {"field_id": {"field_name": "has_roller_shutters"}}, "icon": "mdiW<PERSON>owShutter"}, {"key": {"en": "Roller Shutters", "de": "Rollladen"}, "field_value": {"field_id": {"field_name": "has_roller_shutters"}}, "icon": "mdiW<PERSON>owShutter"}, {"key": {"en": "Glazing", "de": "Verglasung"}, "field_value": {"field_id": {"field_name": "window_glazing_type"}}, "icon": "mdiWindowClosedVariant"}, {"key": {"en": "U-Value", "de": "<PERSON><PERSON><PERSON>lter U-Wert"}, "field_value": {"field_id": {"field_name": "window_u_value"}}, "icon": "mdiAlphaU"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "WINDOW_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Heated room", "de": "Beheizter Raum"}, "field_value": {"field_id": {"field_name": "is_room_heated"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room temperature", "de": "Raumtemperatur"}, "field_value": {"field_id": {"field_name": "room_temperature_type"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room category", "de": "Raumkategorie"}, "field_value": {"field_id": {"field_name": "room_category"}}, "icon": "mdiNotebookEditOutline"}]}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Room height", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_height"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "ROOM_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Total storeys", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Full storeys", "de": "Vollgeschosse"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiBalcony", "key": {"en": "Balconies", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "balcony_count"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Number of windows", "de": "<PERSON><PERSON><PERSON> der Fenster"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Number of doors", "de": "<PERSON><PERSON><PERSON> der Türen"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Rooms", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "TableUIElement", "display_position": "RIGHT", "hide_index_column": true, "columns": [{"header": {"en": "Storey", "de": "Stckw."}, "field_value": {"field_id": {"field_name": "storey_name"}}}, {"header": {"en": "Name", "de": "Name"}, "field_value": {"field_id": {"field_name": "room_name"}}}, {"header": {"en": "NGF", "de": "NGF"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"header": {"en": "Heated", "de": "Beheizt"}, "field_value": {"field_id": {"field_name": "is_room_heated"}}}, {"header": {"en": "Temperature", "de": "Temperatur"}, "field_value": {"field_id": {"field_name": "room_temperature_type"}}}, {"header": {"en": "Heating", "de": "Heizung"}, "field_value": {"field_id": {"field_name": "room_heating_string"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Building pictures", "de": "<PERSON><PERSON><PERSON>ude<PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT"}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "building_pictures_id"}}}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Building weaknesses", "de": "Gebäudeschwachstellen"}, "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "weakness_building_pictures_id"}}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-configuration", "related_pages": [{"viewing_context": "VIEW", "page_id": "edit-configuration"}], "type": "PROJECT_CONFIGURATOR", "icon": "mdiOrderBoolDescendingVariant", "short_title": {"en": "Project configuration", "de": "Projekt-Konfigurator"}, "elements": []}]}