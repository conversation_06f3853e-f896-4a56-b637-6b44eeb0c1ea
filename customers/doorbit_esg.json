{"id": "doorbit_esg", "url-key": "esg", "name": "Doorbit Energieberater Flow", "title": {"en": "My Projects", "de": "<PERSON>ne Projekte"}, "icon": "mdiHeatingCoil", "pages_edit": [{"pattern_type": "CustomUIElement", "id": "edit-general", "layout": "2_COL_RIGHT_FILL", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-general"}], "short_title": {"en": "Building", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-edit", "related_custom_ui_element_id": "model-view", "display_position": "RIGHT", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWindowClosedVariant", "field_id": {"field_name": "building_poi_is_window_note"}, "default_value": false, "title": {"en": "Window", "de": "<PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiPlusBoxOutline", "field_id": {"field_name": "building_poi_is_special_thermal_component"}, "default_value": false, "title": {"en": "Special thermal envelope component", "de": "Sonderbauteil thermische Hülle"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiHomeRoof", "field_id": {"field_name": "building_poi_is_dormer"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgaube"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Details", "de": "Fensterdetails"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Glazing", "de": "Verglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach Verglasung"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach Verglasung"}}, {"key": "DOUBLE_THERMAL", "label": {"en": "Double thermal insulation glazing", "de": "2-fach Wärmeschutzverglasung"}}, {"key": "TRIPLE_THERMAL", "label": {"en": "Triple thermal insulation glazing", "de": "3-fach Wärmeschutzverglasung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_shutter_type"}, "title": {"en": "Window shutters/blinds", "de": "Fensterläden/Rollläden/Raffstores"}, "options": [{"key": "NONE", "label": {"en": "None", "de": "<PERSON><PERSON>"}}, {"key": "SHUTTERS", "label": {"en": "Window shutters", "de": "Fensterläden"}}, {"key": "ROLLER_BLINDS", "label": {"en": "Roller blinds", "de": "Rollläden"}}, {"key": "VENETIAN_BLINDS", "label": {"en": "Venetian blinds", "de": "Raffstores"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Position", "de": "Fensterposition"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_inner_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Inner reveal depth", "de": "Laibungstiefe innen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_thickness_cm"}, "unit": "CENTIMETER", "title": {"en": "Window thickness", "de": "Fensterstärke"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_outer_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Outer reveal depth", "de": "Laibungstiefe außen"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Special thermal envelope component details", "de": "Details zu Sonderbauteilen der thermischen Hülle"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "required": false, "field_id": {"field_name": "special_thermal_component_material"}, "title": {"en": "Material", "de": "Material"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "minimum": "-100Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "special_thermal_component_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Dormer details", "de": "Dachgauben-Details"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Primary Type", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}, {"key": "SPECIAL_TYPE", "label": {"en": "Special heater", "de": "Sonderbauform"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_sub_type"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_radiator_type"}, "op": "eq", "value": "PANEL_RADIATOR", "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}]}, "title": {"en": "Secondary Type", "de": "Subtyp"}, "options": [{"key": "TYPE_10", "label": {"en": "Typ 10", "de": "Typ 10"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_11", "label": {"en": "Typ 11", "de": "Typ 11"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_20", "label": {"en": "Typ 20", "de": "Typ 20"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_21", "label": {"en": "Typ 21", "de": "Typ 21"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_22", "label": {"en": "Typ 22", "de": "Typ 22"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_33", "label": {"en": "Typ 33", "de": "Typ 33"}, "icon": "mdiRadiatorDisabled"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "unit": "CENTIMETER", "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "ne", "value": "PANEL_RADIATOR", "operator_type": "RFO"}]}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "radiator_ventildurchmesser"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Valve diameter", "de": "DN (Ventildurchmesser)"}, "options": [{"key": "MM_10", "label": {"en": "10mm", "de": "10mm"}, "icon": "mdiRuler"}, {"key": "MM_15", "label": {"en": "15mm", "de": "15mm"}, "icon": "mdiRuler"}, {"key": "MM_20", "label": {"en": "20mm", "de": "20mm"}, "icon": "mdiRuler"}, {"key": "MM_25", "label": {"en": "25mm", "de": "25mm"}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please upload at least one photo of the radiator and one photo of the valve.", "de": "Bitte mindestens Heizkörperfoto und Großaufnahme vom Ventil hochladen"}}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please add a photo from the outside and from the inside.", "de": "Bitte Foto von außen und von Innen hinzufügen"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 5, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Heating information", "de": "Angaben zur Beheizung"}, "description": {"en": "Select how the room is heated. A room can also be heated by adjacent rooms if the rooms are connected by a breakthrough.", "de": "<PERSON><PERSON><PERSON><PERSON> aus, wie der Raum beheizt ist. Ein Raum kann auch durch angrenzende Räume mitbeheizt sein, wenn die Räume mittels Durchbruch verbunden sind."}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_room_heated"}, "default_value": true, "title": {"en": "Is room heated", "de": "Ist der Raum beheizt?"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_temperature_type"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Approx. room temperature", "de": "Ungefähre Raumtemperatur"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room (ca. 20°C)", "de": "Wohnraum (ca. 20°C)"}}, {"key": "BATHROOM", "label": {"en": "Bathroom (ca. 24°C)", "de": "Bad<PERSON>immer (ca. 24°C)"}}, {"key": "PASSAGE_ROOM", "label": {"en": "Passage room (ca. 15°C)", "de": "Flur / Abstellraum (ca. 15°C)"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "More details", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_category"}, "title": {"en": "Room category", "de": "Raumkategorie"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "KITCHEN", "label": {"en": "Kitchen", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "OPEN_KITCHEN", "label": {"en": "Open kitchen", "de": "<PERSON><PERSON>nkü<PERSON>"}}, {"key": "BEDROOM", "label": {"en": "Bedroom", "de": "Schlaf-/Kinderzimmer"}}, {"key": "BATHROOM_GUEST_WC", "label": {"en": "Bathroom/WC", "de": "<PERSON><PERSON><PERSON>/<PERSON>"}}, {"key": "HOMEOFFICE", "label": {"en": "Homeoffice", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER_LIVING_SPACE", "label": {"en": "Other living space (e.g. hobby room)", "de": "<PERSON><PERSON> (z. B. <PERSON>, Fitnessraum, Hallenbad)"}}, {"key": "WINTERGARDEN", "label": {"en": "Winter garden", "de": "Wintergarten"}}, {"key": "HALLWAY_STAIRS", "label": {"en": "Hallway/stairs", "de": "Flur/Treppenhaus"}}, {"key": "STORAGE", "label": {"en": "Storage", "de": "Abstellraum"}}, {"key": "UTILITY_ROOM", "label": {"en": "Utility room", "de": "Hauswirtschaftsraum"}}, {"key": "HEATING_ROOM", "label": {"en": "Heating room", "de": "Heizungsraum"}}, {"key": "GARAGE", "label": {"en": "Garage", "de": "Garage"}}, {"key": "ATTIC", "label": {"en": "Attic", "de": "Dachboden"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "room_name"}, "title": {"en": "Custom room name", "de": "Eigener Raumname"}, "description": {"en": "Will be generated from the room category if nothing is maintained.", "de": "Wird aus der Raumkategorie erzeugt, wenn nichts gepflegt wird."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Room height", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_height"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "ROOM_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "default_value": false, "field_id": {"field_name": "has_roller_shutters"}, "title": {"en": "Has roller shutters", "de": "Rollladen vorhanden"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "default": 0.25, "visibility_condition": {"field_id": {"field_name": "has_roller_shutters"}, "op": "eq", "value": true, "operator_type": "RFO"}, "unit": "CENTIMETER", "field_id": {"field_name": "roller_shutters_height"}, "title": {"en": "Roller shutters height", "de": "Höhe des Rollladenkastens"}, "icon": "mdiArrowExpandVertical"}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "default_value": false, "field_id": {"field_name": "has_heizkoerpernische"}, "title": {"en": "Has radiator niche", "de": "Heizkörpernische vorhanden"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "visibility_condition": {"field_id": {"field_name": "has_heizkoerpernische"}, "op": "eq", "value": true, "operator_type": "RFO"}, "unit": "CENTIMETER", "field_id": {"field_name": "heizkoerpernischen_height"}, "title": {"en": "Radiator niche height", "de": "Höhe der HZK-Nische"}, "icon": "mdiArrowExpandVertical"}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach"}}, {"key": "DOUBLE_ISOLATED", "label": {"en": "Double glazing with thermal insulation", "de": "2-fach (Wärmeschutzverglasung)"}}, {"key": "TRIPLE", "label": {"en": "Triple glazing", "de": "3-fach (Wärmeschutzverglasung)"}}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "window_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "WINDOW_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "door_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "DOOR_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "wall_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "WALL_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Inside dimensions", "de": "Innenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "inside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside dimensions", "de": "Außenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WALL_EVEBI", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Construction", "de": "Konstruktion"}, "description": {"en": "By default, a construction is created in Evebi for each different U-Value. Components can also be assigned to their own construction groups.", "de": "Es wird standardmäßig pro unterschiedlichem U-Wert eine Konstruktion in Evebi erzeugt. Bauteile können auch eigenen Konstruktionsgruppen zugeordnet werden."}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "construction_type"}, "title": {"en": "Assignment", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "BY_U_VALUE", "options": [{"key": "BY_U_VALUE", "label": {"en": "By U-Value", "de": "Basierend auf U-Wert dieses Elements"}}, {"key": "CONSTRUCTION_A", "label": {"en": "Construction group A", "de": "Konstruktionsgruppe A"}}, {"key": "CONSTRUCTION_B", "label": {"en": "Construction group B", "de": "Konstruktionsgruppe B"}}, {"key": "CONSTRUCTION_C", "label": {"en": "Construction group C", "de": "Konstruktionsgruppe C"}}, {"key": "CONSTRUCTION_D", "label": {"en": "Construction group D", "de": "Konstruktionsgruppe D"}}, {"key": "CONSTRUCTION_E", "label": {"en": "Construction group E", "de": "Konstruktionsgruppe E"}}, {"key": "CONSTRUCTION_F", "label": {"en": "Construction group F", "de": "Konstruktionsgruppe F"}}, {"key": "CONSTRUCTION_G", "label": {"en": "Construction group G", "de": "Konstruktionsgruppe G"}}]}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "floor_type"}, "title": {"en": "Storey type", "de": "Geschossart"}, "options": [{"key": "FULL_STOREY", "label": {"en": "Full storey", "de": "Vollgeschoss"}}, {"key": "CELLAR", "label": {"en": "Cellar", "de": "Untergeschoss/Keller"}}, {"key": "ROOF_STOREY", "label": {"en": "Roof storey", "de": "Dachgeschoss"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Storey", "de": "Geschoss"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Administration", "de": "Verwaltung"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "display_position": "LEFT", "required": true, "field_id": {"field_name": "custom_status_type"}, "default": "PLANNED", "title": {"en": "Status", "de": "Status"}, "options": [{"key": "PLANNED", "label": {"en": "Planned", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "IN_PROGRESS", "label": {"en": "In progress", "de": "In Bearbeitung"}}, {"key": "DONE", "label": {"en": "Done", "de": "Abgeschlossen"}}, {"key": "ARCHIVED", "label": {"en": "Archived", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Basic data", "de": "Basisdaten"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "ext_id"}, "title": {"en": "Project title", "de": "Projekttitel"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "sub_type"}, "title": {"en": "What type of building is it?", "de": "Um welche Gebäudeart handelt es sich?"}, "default": "DETACHED", "options": [{"key": "DETACHED", "label": {"en": "Detached house", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "SEMI_DETACHED", "label": {"en": "Semi-Detached house", "de": "Doppelhaushälfte"}}, {"key": "TOWNHOUSE_MIDDLE", "label": {"en": "Townhouse (middle)", "de": "Reihenmittelhaus"}}, {"key": "TOWNHOUSE_END", "label": {"en": "Townhouse (end)", "de": "Re<PERSON><PERSON>ndhaus"}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Covershot", "de": "Cover Foto"}, "description": {"en": "This photo appears as a preview image in the overview.", "de": "Dieses Foto erscheint als Vorschaubild in der Übersicht."}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "covershot_image_id"}, "caption_field_id": {"field_name": "covershot_image_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Building pictures", "de": "<PERSON><PERSON><PERSON>ude<PERSON><PERSON>"}, "description": {"en": "", "de": "Es sind mindestens 5 Gebäudefotos erforderlich: 4 Ansichten der Fassade (von jeder Seite) und 1x Hauptansicht (Haus gesamt im Profil)"}, "min_count": 0, "max_count": 200, "required": false, "id_field_id": {"field_name": "building_pictures_id"}, "caption_field_id": {"field_name": "building_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Weakness Pictures", "de": "<PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Wir benötigen Fotos von Schwachstellen des Außengebäudes. Das können z. B. Risse in der Fassade, Zustand des Daches oder sonstige auffällige Dinge sein."}, "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "weakness_building_pictures_id"}, "caption_field_id": {"field_name": "weakness_building_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}}]}, {"pattern_type": "CustomUIElement", "type": "ADDRESS", "id": "edit-address", "related_pages": [{"viewing_context": "VIEW", "page_id": "location"}], "title": {"en": "Determine address", "de": "<PERSON><PERSON><PERSON> er<PERSON>n"}, "short_title": {"en": "Location", "de": "Lage"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_zipcode"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_city"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_street"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_house_number"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_latitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_longitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_latitude_obfuscated"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_longitude_obfuscated"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "address_show_obfuscated_location"}, "default_value": false, "title": {"en": "Hide address?", "de": "<PERSON>resse verbergen?"}, "description": {"en": "If the address is hidden, it won't be publicly visible. Instead, an approximate location will be displayed.", "de": "Wird die Adresse verborgen, ist sie nicht öffentlich sichtbar. Stattdessen wird ein ungefährer Standort präsentiert."}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-uvalues", "title": {"en": "U-value assistant", "de": "<PERSON><PERSON><PERSON><PERSON> Assistent"}, "icon": "mdiAlphaU", "elements": [{"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "text": {"en": "The U-value assistant helps you to determine the U-values of your building elements.", "de": "Mit dem U-Wert Assistenten können schnell und einfach die U-Werte von Gebäudebauteilen ermittelt werden. Wir nutzen das Verfahren aus dem Bundesanzeiger, um mit wenigen Angaben eine Ersteinschätzung zu erhalten. Die so errechneten Werte werden automatisch auf das Gebäudemodell übertragen und sind sofort in allen Doorbit Exporten enthalten."}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": true, "field_id": {"field_name": "year_of_construction"}, "title": {"en": "Building year of construction", "de": "Gebäudebaujahr"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Ground plates", "de": "Unterste Geschossböden"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_type"}, "title": {"en": "What is beneath the ground floor?", "de": "Was liegt unterhalb des Erdgeschosses?"}, "options": [{"key": "NO_CELLAR", "label": {"en": "Ground plate", "de": "Bodenplatte"}}, {"key": "UNHEATED_CELLAR", "label": {"en": "Unheated cellar", "de": "Unbehe<PERSON><PERSON> Keller"}}, {"key": "PARTIALLY_HEATED_CELLAR", "label": {"en": "Partially heated cellar", "de": "Teilbeheizter Keller"}}, {"key": "HEATED_CELLAR", "label": {"en": "Heated cellar", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_ceiling_type"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "NO_CELLAR", "operator_type": "RFO"}, {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": null, "operator_type": "RFO"}]}, "title": {"en": "Cellar ceiling type", "de": "Kellerdeckentyp"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Redescriptionrced concrete", "de": "Stahlbeton"}}, {"key": "HOLLOW_STONES", "label": {"en": "Hollow stone construction", "de": "Ziegel oder Hohlsteine"}}, {"key": "WOODEN_BEAMS", "label": {"en": "Wooden-beams ceiling", "de": "Holzbalkendecke"}}, {"key": "VAULTED_CELLAR", "label": {"en": "Valuted cellar", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "cellar_ceiling_other_type"}}}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "cellar_ceiling_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "NO_CELLAR", "operator_type": "RFO"}, {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": null, "operator_type": "RFO"}]}, "title": {"en": "U-value cellar ceiling", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "ground_slab_construction_type"}, "title": {"en": "Construction type of the ground slab", "de": "Bauweise der Bodenplatte"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Reinforced concrete", "de": "Stahlbeton"}}, {"key": "HOLLOW_STONES", "label": {"en": "Massive bricks", "de": "<PERSON>l<PERSON>gel"}}, {"key": "MASSIVE_WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "EMPTY_SPACE_WOODEN_BEAMS", "label": {"en": "Suspended timber floor", "de": "Holzdielen auf Lagerhölzern (Hohlraum)"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "ground_slab_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside walls", "de": "Außenwände"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "wall_construction_type"}, "title": {"en": "Construction type of the outside walls", "de": "Bauweise der Außenwände"}, "options": [{"key": "SOLID_BRICK", "label": {"en": "Solid brick", "de": "<PERSON>l<PERSON>gel"}}, {"key": "KALKSANDSTEIN", "label": {"en": "Calcium silicate brick", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "CONCRETE", "label": {"en": "Concrete", "de": "<PERSON><PERSON>"}}, {"key": "QUARRY_STONE", "label": {"en": "Quarry stone", "de": "Bruchstein"}}, {"key": "POROBETON", "label": {"en": "Little brick", "de": "Poroton und Gasbeton"}}, {"key": "PERFORATED_BRICK", "label": {"en": "Highly perforated brick", "de": "<PERSON><PERSON>lochziegel"}}, {"key": "DOUBLE_SKIN_CONSTRUCTION", "label": {"en": "2-layered construction", "de": "Zweischaliger <PERSON>"}}, {"key": "DOUBLE_SKIN_CONSTRUCTION_WITH_INSULATION", "label": {"en": "2-layered construction with insulation", "de": "Zweischaliger Wandaufbau mit Dämmschicht"}}, {"key": "OTHER_MASSIVE", "label": {"en": "Massive construction (other/unknown)", "de": "<PERSON><PERSON> (Sonstige/Unbekannt)"}}, {"key": "SOLID_WOOD_CONSTRUCTION", "label": {"en": "Solid wood construction", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HALF_TIMBERED_WITH_CLAY_INFILL", "label": {"en": "Half-timbered wall - Clay", "de": "Fachwerk - Lehm"}}, {"key": "HALF_TIMBERED_WITH_SOLID_INFILL", "label": {"en": "Half-timbered wall - Solid bricks", "de": "Fachwerk - Ziegel"}}, {"key": "OTHER_WOOD_CONSTRUCTION", "label": {"en": "Other wood construction", "de": "Sonstige Holzkonstruktion"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "wall_construction_wall_thickness"}, "title": {"en": "Wall thickness", "de": "Wandstärke"}, "options": [{"key": "UNTIL_20CM", "label": {"en": "Up to 20 cm", "de": "Bis 20 cm"}}, {"key": "FROM_20CM_TO_30CM", "label": {"en": "20 - 30 cm", "de": "20 - 30 cm"}}, {"key": "ABOVE_30CM", "label": {"en": "More than 30 cm", "de": "Über 30 cm"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "wall_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows and windowed doors", "de": "Fenster und Fenstertüren"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_different_window_year_of_construction"}, "title": {"en": "Different year of construction than building", "de": "Baujahr der Fenster weicht vom Gebäudebaujahr ab"}, "description": {"en": "If the majority of the windows have already been replaced, please indicate.", "de": "Falls die Mehrheit der Fenster bereits erneuert wurden, bitte angeben."}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_year_of_construction_period"}, "visibility_condition": {"field_id": {"field_name": "has_different_window_year_of_construction"}, "op": "eq", "value": true, "operator_type": "RFO"}, "type": "BUTTON_GROUP", "title": {"en": "Window construction year", "de": "Abweichendes Baujahr der Fenster"}, "options": [{"key": "BEFORE_1978", "label": {"en": "Until 1978", "de": "bis 1978"}}, {"key": "BETWEEN_1978_AND_1994", "label": {"en": "1979 - 1983", "de": "1979 - 1983"}}, {"key": "BETWEEN_1984_AND_1994", "label": {"en": "1984 - 1994", "de": "1984 - 1994"}}, {"key": "BETWEEN_1995_AND_2001", "label": {"en": "1995 - 2001", "de": "1995 - 2001"}}, {"key": "AFTER_2002", "label": {"en": "After 2002", "de": "ab 2002"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_frame_material_type"}, "title": {"en": "From what material are the window frames?", "de": "Aus welchem Material sind die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "METAL", "label": {"en": "Metal", "de": "Metall"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach"}}, {"key": "DOUBLE_ISOLATED", "label": {"en": "Double glazing with thermal insulation", "de": "2-fach (Wärmeschutzverglasung)"}}, {"key": "TRIPLE", "label": {"en": "Triple glazing", "de": "3-fach (Wärmeschutzverglasung)"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "window_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "door_frame_material_type"}, "title": {"en": "From what material are the door frames?", "de": "Aus welchem Material sind die Türrahmen?"}, "options": [{"key": "METAL", "label": {"en": "Mostly metal", "de": "Überwiegend Metall"}}, {"key": "WOOD", "label": {"en": "Mostly Wood or plastic", "de": "Überwiegend Holz oder Kunststoff"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "door_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Top floor ceiling", "de": "Oberste Geschossdecke"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "top_level_ceiling_construction_type"}, "title": {"en": "Construction type of the top floor ceiling", "de": "Bauweise der obersten Geschossdecke"}, "options": [{"key": "REINFORCED_CONCRETE", "label": {"en": "Concrete", "de": "Massive <PERSON>"}}, {"key": "MASSIVE_WOOD", "label": {"en": "Wooden beams", "de": "Holzbalkendecke"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "top_level_ceiling_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}, "description": {"en": "After answering the questions, the U-value is automatically calculated. You can also enter the value manually.", "de": "Nach Beantwortung der Fragen wird der U-Wert automatisch ermittelt. Du kannst den Wert auch manuell eingeben."}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-cellar", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-cellar"}], "title": {"en": "Cellar and heat-generator", "de": "Keller und Heizung"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Cellar", "de": "<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "NO_CELLAR", "operator_type": "RFO"}, {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": null, "operator_type": "RFO"}]}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "heated_cellar_area"}, "unit": "SQUARE_METER", "title": {"en": "Heated cellar area in m²", "de": "Beheizte Kellerfläche"}, "visibility_condition": {"field_id": {"field_name": "cellar_type"}, "op": "ne", "value": "UNHEATED_CELLAR", "operator_type": "RFO"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_entry_location"}, "title": {"en": "Cellar entry", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "OUTSIDE_BUILDING", "label": {"en": "Outside building", "de": "Außerhalb des Gebäudes"}}, {"key": "IN_HEATED_BUILDING", "label": {"en": "Full in heated building volume", "de": "Vollständig im beheizten Gebäudevolumen"}}, {"key": "NOT_IN_HEATED_BUILDING", "label": {"en": "Not in heated building volume", "de": "Nicht im beheizten Gebäudevolumen"}}, {"key": "OPEN_CONNECTION", "label": {"en": "Open connection to unheated cellar", "de": "Offene Verbindung zu unbeheiz<PERSON> Keller"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_cellar_ceiling_insulation"}, "title": {"en": "Is a cellar ceiling insulation available", "de": "Kellerdeckendämmung vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "cellar_ceiling_insulation_thickness"}, "unit": "CENTIMETER", "title": {"en": "Cellar ceiling insulation", "de": "Kellerdeckendämmung"}, "visibility_condition": {"field_id": {"field_name": "has_cellar_ceiling_insulation"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_cellar_wall_insulation"}, "title": {"en": "Is a cellar wall insulation available", "de": "Kellerwanddämmung vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "cellar_wall_insulation_thickness"}, "unit": "CENTIMETER", "title": {"en": "Ceiling wall insulation", "de": "Kellerwanddämmung"}, "visibility_condition": {"field_id": {"field_name": "has_cellar_wall_insulation"}, "op": "eq", "value": true, "operator_type": "RFO"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "heat_generator_location"}, "title": {"en": "Location of heat-generator", "de": "Stellplatz des Wärmeerzeugers"}, "options": [{"key": "LIVING_AREA", "label": {"en": "In living area", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HEATING_ROOM", "label": {"en": "Heating room", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OUTSIDE_BUILDING", "label": {"en": "Outside building", "de": "Heizraum außerhalb des Gebäudes"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "heating_type"}, "title": {"en": "Heat-generator type", "de": "Art der Heizungsanlage"}, "options": [{"key": "LOW_TEMPERATURE_BOILER", "label": {"en": "Low temperature boiler", "de": "Niedertemperatur"}}, {"key": "CONDENSING_BOILER", "label": {"en": "Condensing boiler", "de": "Brennwert (Kunststoffabgasrohr, Kondensatabfluss)"}}, {"key": "FURNACE", "label": {"en": "Standard furnace without regulation", "de": "Standardkessel ohne Regelung"}}, {"key": "CENTRAL_HEATING", "label": {"en": "Central heating", "de": "Zentralheizung"}}, {"key": "FIREPLACE_AND_TILED_STOVES", "label": {"en": "Stove heating", "de": "Ofen- / Kaminheizung"}}, {"key": "BLOCK_HEATING", "label": {"en": "Block heating", "de": "Nahwärme (Blockheizkraftwerk)"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "AIR_WATER_HEAT_PUMP", "label": {"en": "Air/Water heatpump", "de": "Luft/Wasser - Wärmepumpe"}}, {"key": "WATER_WATER_HEAT_PUMP", "label": {"en": "Water/water heatpump", "de": "Wasser/Wasser - Wärmepumpe"}}, {"key": "SOIL_WATER_HEAT_PUMP", "label": {"en": "Soil/Water heatpump", "de": "Sole/Wasser - Wärmepumpe"}}, {"key": "AIR_AIR_HEAT_PUMP", "label": {"en": "Air/Air heatpump", "de": "Luft/Luft - Wärmepumpe"}}, {"key": "ELECTRIC_HEATING", "label": {"en": "Electric heating", "de": "Stromdirektheizung"}}, {"key": "ELECTRIC_HEATING_WITH_STORAGE", "label": {"en": "Eletric storage heating", "de": "Stromspeicherheizung"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "heating_type_other"}}}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_source"}, "title": {"en": "Energy source", "de": "Energieträger"}, "description": {"en": "In case of heatpump please choose \"electricity\".", "de": "Bei Wärmepumpe bitte \"Strom\" auswählen."}, "options": [{"key": "GAS", "label": {"en": "Gas", "de": "Gas"}}, {"key": "OIL", "label": {"en": "Oil", "de": "<PERSON><PERSON>"}}, {"key": "ELECTRICITY", "label": {"en": "Electricity", "de": "<PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "PELLETS", "label": {"en": "Pellets", "de": "Pellets"}}, {"key": "COAL", "label": {"en": "Coal", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "ENVIRONMENTAL", "label": {"en": "Environmental (e.g. solar, wind, water)", "de": "Umweltenergie (z.B. Solar, Wärmepumpe, Wasser)"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Heat generator pictures", "de": "Fotos der Heizungsanlage"}, "description": {"en": "", "de": "Bitte Foto der Heizungsanlage (Typenschild, Rohrleitungen, Gesamtansicht) hochladen."}, "example_image": "https://flow-configs.doorbit.com/assets/heat_generator_pictures/example.jpg", "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "heat_generator_pictures_id"}, "caption_field_id": {"field_name": "heat_generator_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "heat_generator_year_of_construction"}, "title": {"en": "Year of construction of heat-generator", "de": "Baujahr der Heizungsanlage"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "heat_generator_thermal_output"}, "unit": "KILOWATT", "title": {"en": "Thermal output of heat-generator", "de": "Leistung der Heizungsanlage"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "heating_pump_type"}, "title": {"en": "Heating-pump type", "de": "Heizungspumpe"}, "description": {"en": "", "de": "Mehrstufige Pumpen haben in der Regel eine Aufschrift, aus der die Leistungsstufen hervorgehen. Moderne Hocheffizienzpumpen haben häufig ein digitales Display, an dem die Leistung in Watt dynamisch angezeigt wird."}, "options": [{"key": "UNREGULATED", "label": {"en": "Unregulated", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "MULTI_STAGE_PUMP", "label": {"en": "Multi-staged regulated", "de": "Mehrstufig geregelt"}}, {"key": "HIGH_EFFICIENCY_PUMP", "label": {"en": "High efficiency pump", "de": "Hocheffizienzpumpe"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "heat_generator_overnight_shutdown_type"}, "title": {"en": "Heat-generator nightly shutdown", "de": "Nachtabschaltung der Heizungsanlage"}, "options": [{"key": "NO_SHUTDOWN", "label": {"en": "No nightly shutdown", "de": "<PERSON><PERSON>"}}, {"key": "NIGHT_SHUTDOWN", "label": {"en": "Shutdown", "de": "Nachtabschaltung"}}, {"key": "NIGHT_SETBACK", "label": {"en": "Setback", "de": "Nachtabsenkung"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "default_value": false, "field_id": {"field_name": "has_water_buffer_storage"}, "title": {"en": "Water buffer storage available?", "de": "<PERSON>ufferspeicher vorhanden?"}, "example_image": "https://flow-configs.doorbit.com/assets/has_water_buffer_storage/example.jpg"}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Water buffer storage", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_water_buffer_storage"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "water_buffer_storage_size"}, "unit": "LITER", "title": {"en": "Size water buffer storage", "de": "Größe Pufferspeicher"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Water buffer storage pictures", "de": "Fotos des Pufferspeichers"}, "description": {"en": "", "de": "Bitte Fotos des Pufferspeichers (Typenplakette, Gesamtansicht) hochladen."}, "example_image": "https://flow-configs.doorbit.com/assets/water_buffer_storage_pictures/example.jpg", "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "water_buffer_storage_pictures_id"}, "caption_field_id": {"field_name": "water_buffer_storage_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "water_heating_type"}, "title": {"en": "Warmwater creation", "de": "Warmwasserbereitung"}, "description": {"en": "", "de": "\"Zusätzliche Warmwasserbereitung\" meint z. B. Durchlauferhitzer oder Boiler"}, "options": [{"key": "WITH_HEAT_GENERATOR", "label": {"en": "with heat-generator", "de": "mit Heizungsanlage"}}, {"key": "SEPARATE_DEVICE", "label": {"en": "additional Warmwater creation (decentral)", "de": "zusätzliche Warmwasserbereitung (dezentral)"}}, {"key": "SEPARATE_DEVICE_CENTRAL", "label": {"en": "additional Warmwater creation (central)", "de": "zusätzliche Warmwasserbereitung (zentral)"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Additional warmwater creation", "de": "Zusätzliche Warmwasserbereitung"}, "visibility_condition": {"operator": "OR", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "water_heating_type"}, "op": "eq", "value": "SEPARATE_DEVICE", "operator_type": "RFO"}, {"field_id": {"field_name": "water_heating_type"}, "op": "eq", "value": "SEPARATE_DEVICE_CENTRAL", "operator_type": "RFO"}]}, "elements": [{"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "year_of_construction_decentral_water_heating"}, "title": {"en": "Year of construction warmwater creation", "de": "Baujahr Warmwassersystem"}, "description": {"en": "", "de": "Sofern eine separate Warmwasseraufbereitung durch z. B. Boiler oder Durchlauferhitzer installiert ist, bitte deren Baujahr angeben"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "decentral_water_heating_type"}, "title": {"en": "Warmwater creation system", "de": "Warmwassersystem"}, "options": [{"key": "ELECTRIC_FLOW_HEATER", "label": {"en": "Electric flow heater", "de": "Elektro-Durchlauferhitzer"}}, {"key": "GAS_FLOW_HEATER", "label": {"en": "Gas flow heater", "de": "Gas-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "ELECTRIC_BOILER", "label": {"en": "Electric storage (Boiler)", "de": "Elektro-Speicher (Boiler)"}}, {"key": "HEATPUMP", "label": {"en": "TW-Heatpump", "de": "TW-Wärmepumpe"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "decentral_water_heating_other_type"}}}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Shares", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "heat_transfer_type"}, "op": "eq", "value": "COMBINED", "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "share_radiators"}, "unit": "PERCENTAGE", "title": {"en": "Radiators share", "de": "Anteil <PERSON>rper insgesamt"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "share_underfloor_heating"}, "unit": "PERCENTAGE", "title": {"en": "Underfloor-heating share", "de": "Anteil Fußbodenheizung insgesamt"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-storeys", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-storeys"}], "title": {"en": "Storey details", "de": "Geschossdetails"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_outside_wall_insulation_known"}, "title": {"en": "Is the outside wall insulation known?", "de": "Ist die Außenwanddämmung bekannt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "outside_wall_insulation_thickness"}, "unit": "CENTIMETER", "title": {"en": "Outside wall insulation thickness", "de": "Stärke der Außenwanddämmung"}, "visibility_condition": {"field_id": {"field_name": "is_outside_wall_insulation_known"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/outside_wall_insulation_thickness/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_building_tight"}, "title": {"en": "Is the building sealed?", "de": "Ist das Gebäude dicht?"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_air_condition"}, "default_value": false, "title": {"en": "Air condition available?", "de": "Ist eine Klimaanlage verbaut?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Air conditioning", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_air_condition"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/air_condition_pictures/example.jpg", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "A/C pictures", "de": "Fotos der Klimaanlage"}, "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "air_condition_pictures_id"}, "caption_field_id": {"field_name": "air_condition_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "cooled_area"}, "unit": "SQUARE_METER", "title": {"en": "Cooled area of A/C", "de": "Gekühlte Fläche der Klimaanlage"}, "description": {"en": "", "de": "Die Fläche, die von der Klimaanlage typischerweise gekühlt wird."}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "air_condition_thermal_output"}, "unit": "KILOWATT", "title": {"en": "A/C thermal output", "de": "Klimaanlagenleistung"}, "description": {"en": "", "de": "<PERSON>t meist auf dem Typenschild erkennbar. <PERSON><PERSON> in kW"}}}, {"element": {"pattern_type": "DateUIElement", "type": "YM", "required": false, "field_id": {"field_name": "air_condition_last_inspection_date"}, "title": {"en": "Last inspection in case A/C output is greater than 12 KW", "de": "Letzte Inspektive"}, "description": {"en": "", "de": "Nur bei Anlagen größer 12 kW Nennleistung relevant"}, "minimum": "-5Y", "maximum": "-0Y"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_photovoltaics_installation"}, "default_value": false, "title": {"en": "Is PV available?", "de": "Ist eine Photovoltaik Anlage verbaut?"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_solar_installation"}, "default_value": false, "title": {"en": "Is solar available?", "de": "Ist eine Solarthermieanlage verbaut?"}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-<PERSON><PERSON>y", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-roofstorey"}], "title": {"en": "Roof storey", "de": "Dachgeschoss"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "attic_type"}, "title": {"en": "Attic", "de": "Dachboden"}, "options": [{"key": "AVAILABLE", "label": {"en": "Available", "de": "Vorhand<PERSON>"}}, {"key": "DEVELOPED_ATTIC", "label": {"en": "Developed Attic", "de": "Ausgebauter Dachboden"}}, {"key": "NO_ATTIC", "label": {"en": "Not Available", "de": "Nicht vorhanden"}}, {"key": "PARTIALLY_DEVELOPED_ATTIC", "label": {"en": "Partially developed attic", "de": "Teilweise ausgebauter Dachboden"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "top_storey_ceiling_insulation_thickness"}, "title": {"en": "Top storey ceiling insulation", "de": "Dämmungsstärke oberste Geschossdecke"}, "unit": "CENTIMETER", "description": {"en": "Top storey ceiling insulation", "de": "Dämmung oberste Geschossdecke, falls dies die obere Gebäudegrenze ist (also bei Flachdächern und nicht ausgebauten Dachgeschoss)"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_pitch"}, "title": {"en": "Pitch", "de": "Neigung"}, "unit": "DEGREE_OF_ARC", "description": {"en": "Roof pitch", "de": "Dachneigung (0° Flachdach bis 70° Steildach)"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_rafter_thickness"}, "unit": "CENTIMETER", "title": {"en": "Roof rafter thickness", "de": "Dachsparrenstärke"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_roof_pitch_insulation"}, "title": {"en": "Is the roof pitches insulated?", "de": "Sind die Dachschrägen gedämmt?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_pitch_insulation_thickness"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "has_roof_pitch_insulation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Roof pitch insulation thickness", "de": "Dämmungsstärke der Dachschrägen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_height"}, "unit": "METER", "title": {"en": "Roof height", "de": "Dachhöhe"}, "example_image": "https://flow-configs.doorbit.com/assets/roof_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_attic"}, "title": {"en": "Is an attic present?", "de": "Ist ein Spitzboden vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "attic_height"}, "unit": "METER", "title": {"en": "Attic height", "de": "Spitzbodenhöhe"}, "visibility_condition": {"field_id": {"field_name": "has_attic"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/attic_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_kneeling"}, "title": {"en": "Is a kneeling wall present?", "de": "Ist ein Kniestock vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "knee_height"}, "unit": "CENTIMETER", "title": {"en": "Knee height", "de": "Kniestockhöhe"}, "visibility_condition": {"field_id": {"field_name": "has_kneeling"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/knee_height/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "kneeling_offset_inwards"}, "default_value": false, "title": {"en": "Is the kneeling offset inwards?", "de": "Ist der Kniestock nach innen versetzt?"}, "visibility_condition": {"field_id": {"field_name": "has_kneeling"}, "op": "eq", "value": true, "operator_type": "RFO"}, "example_image": "https://flow-configs.doorbit.com/assets/kneeling_offset_inwards/example.jpg"}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "has_dormers"}, "title": {"en": "Are dormers present?", "de": "Dachgauben vorhanden?"}}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_dormers"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Dormer Pictures", "de": "Fotos der Gauben"}, "example_image": "https://flow-configs.doorbit.com/assets/dormer_pictures/example.jpg", "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "dormer_pictures_id"}, "caption_field_id": {"field_name": "dormer_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgauben"}, "example_image": "https://flow-configs.doorbit.com/assets/dormers/example.jpg", "min_count": 0, "max_count": 20, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "dormer_type"}, "title": {"en": "Type of dormer", "de": "Um welche Art von Gaube handelt es sich?"}, "options": [{"key": "FLAT", "label": {"en": "Flat roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "GABLE", "label": {"en": "Gable roof", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "HIPPED", "label": {"en": "Hipped roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "SHED", "label": {"en": "Shed roof", "de": "Schleppdachgaube"}}, {"key": "TRAPEZOIDAL", "label": {"en": "Trapezoidal roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "POINTED", "label": {"en": "Pointed roof", "de": "Spitzdach"}}, {"key": "ROUND", "label": {"en": "Rounded roof", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "BAT", "label": {"en": "Bat roof", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_amount"}, "title": {"en": "Amount", "de": "<PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Anzahl der Gauben dieser Art"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Gaubenbreite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height"}, "unit": "CENTIMETER", "title": {"en": "Dormer height", "de": "Traufhöhe"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_roof_height"}, "unit": "CENTIMETER", "title": {"en": "Dormer roof height", "de": "Firsthöhe"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_length"}, "unit": "CENTIMETER", "title": {"en": "Dormer length", "de": "Gaubenlänge"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Roof panel windows", "de": "Dachflächenfenster"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "roof_window_frame_material_type"}, "title": {"en": "Roof window frames material", "de": "Dachflächenfensterrahmen-Material"}, "description": {"en": "Roof window frames material", "de": "Aus welchem Material bestehen die Fensterrahmen?"}, "options": [{"key": "PLASTIC", "label": {"en": "Plastic", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ALUMINIUM", "label": {"en": "Aluminium", "de": "Aluminium"}}, {"key": "STEEL", "label": {"en": "Steel", "de": "<PERSON><PERSON>"}}, {"key": "Other", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "roof_window_glazing_type"}, "title": {"en": "Roof window glazing", "de": "Dachflächenfensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "DOUBLE", "label": {"en": "Double", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "TRIPLE", "label": {"en": "Triple", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "3 Example roof window", "de": "Erfassung von bis zu 3 Beispiel-Dachflächenfenstern"}, "min_count": 0, "max_count": 3, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "roof_window_type"}, "title": {"en": "Type of roof window", "de": "Dachfenster-Art"}, "options": [{"key": "RECTANGULAR", "label": {"en": "Rectangular", "de": "Quadratisch od. recht<PERSON>ig"}}, {"key": "ROUND", "label": {"en": "Round", "de": "Rundfenster"}}, {"key": "MUNTIN", "label": {"en": "Muntin window", "de": "Sprossenfenster"}}, {"key": "GLASS_BLOCK", "label": {"en": "Glass blocks", "de": "Glasbausteine"}}, {"key": "ROUND_ARCH", "label": {"en": "Round arch", "de": "Rundbogenfenster"}}, {"key": "BOX", "label": {"en": "Box window", "de": "Kastenfenster"}}, {"key": "FLOOR_TO_CEILING", "label": {"en": "Floor-to-ceiling", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "SPECIAL", "label": {"en": "Special", "de": "Spezial/Sonderanfertigung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "roof_window_cardinal_direction"}, "title": {"en": "Cardinal Direction of Roof Window", "de": "Himmelsrichtung des Dachfensters"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "roof_window_year_of_construction"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_window_width"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "roof_window_height"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "roof_window_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-Value", "de": "U-Wert"}}}]}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-renewables", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-renewables"}], "title": {"en": "Renewable energies", "de": "Erneuerbare Energien"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Solar installation", "de": "Solarthermieanlage"}, "visibility_condition": {"field_id": {"field_name": "has_solar_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_type"}, "title": {"en": "Type of solar collector", "de": "Art des Solarthermiekollektors"}, "options": [{"key": "COVERED_FLAT_COLLECTOR", "label": {"en": "Covered flat collector", "de": "Abgedeck<PERSON> Flach-Kollektor"}}, {"key": "VACUUM_TUBE_COLLECTOR", "label": {"en": "Vacuum-tubes collector", "de": "Vakuum-Röhren-Kollektor"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_usage_type"}, "title": {"en": "Solar collector usage", "de": "Wie wird der Kollektor verwendet?"}, "options": [{"key": "HOT_WATER", "label": {"en": "Hot water generation", "de": "Warmwassererzeugung"}}, {"key": "ROOM_HEATING", "label": {"en": "Room heating", "de": "Raumheizung"}}, {"key": "COMBINED", "label": {"en": "Room heating and hot water generation", "de": "Raumheizung und Warmwassererzeugung"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_area"}, "unit": "SQUARE_METER", "title": {"en": "Solar collector area", "de": "Sonnenkollektorfläche"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "solar_collector_cardinal_direction_type"}, "title": {"en": "Solar collection cardinal direction", "de": "Solarkollektor Himmelsausrichtung"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_pitch"}, "unit": "DEGREE_OF_ARC", "title": {"en": "Collector pitch", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"en": "", "de": "Bei Schrägdächern in der Regel gleich der Dachneigung"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_waterstorage"}, "unit": "LITER", "title": {"en": "Collector storage size", "de": "Größe des Solarwärmespeichers in Litern"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "solar_collector_cold_water_temp"}, "unit": "DEGREE_CELSIUS", "title": {"en": "Cold water temperature", "de": "Kaltwassertemperatur"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Photovoltaic", "de": "Photovoltaik"}, "visibility_condition": {"field_id": {"field_name": "has_photovoltaics_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}, "isCollapsible": true, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_type"}, "title": {"en": "Type of PV installation", "de": "Art der Photovoltaik-Anlage"}, "options": [{"key": "MONOCRYSTALLINE_SILICIUM", "label": {"en": "Monocrystalline silicium (black panels)", "de": "Monokristallines Silizium (schwarze Module)"}}, {"key": "POLYCRYSTALLINE_SILICIUM", "label": {"en": "Polycrystalline silicium (blue panels)", "de": "Polykristallines Silizium (blaue Module)"}}, {"key": "CADMIUM_TELLURIDE", "label": {"en": "Cadmium telluride thin film", "de": "Cadmium-Tellurid-Dünnschicht"}}, {"key": "AMORPHOUS_SILICIUM", "label": {"en": "Thin-film module made of amorphous silicon", "de": "Dünnschichtmodul aus amorphem Silizium"}}, {"key": "COPPER_INDIUM_GALLIUM_SELENIDE", "label": {"en": "Copper indium gallium diselenide", "de": "Kupfer-Indium-Gallium-Diselenid"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "pv_type_other"}}}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_ventilation_type"}, "title": {"en": "Type of ventilation for PV", "de": "Belüftung der PV-Anlage"}, "options": [{"key": "WELL_VENTILATED", "label": {"en": "Well ventilated", "de": "<PERSON><PERSON> belüftet"}}, {"key": "SOMEWHAT_VENTILATED", "label": {"en": "Somewhat ventilated", "de": "Mäßig belüftet"}}, {"key": "UNVENTILATED", "label": {"en": "Unventilated", "de": "Unbelüftet"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_area"}, "unit": "SQUARE_METER", "title": {"en": "PV area", "de": "PV-Fläche"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_output"}, "unit": "KILOWATT", "title": {"en": "PV output", "de": "PV-Leistung"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "pv_cardinal_direction_type"}, "title": {"en": "PV cardinal direction", "de": "PV Himmelsausrichtung"}, "options": [{"key": "NORTH", "label": {"en": "North", "de": "Nord"}}, {"key": "NORTH_EAST", "label": {"en": "North East", "de": "Nord-Ost"}}, {"key": "EAST", "label": {"en": "East", "de": "Ost"}}, {"key": "SOUTH_EAST", "label": {"en": "South East", "de": "Süd-Ost"}}, {"key": "SOUTH", "label": {"en": "South", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "SOUTH_WEST", "label": {"en": "South West", "de": "Süd-West"}}, {"key": "WEST", "label": {"en": "West", "de": "West"}}, {"key": "NORTH_WEST", "label": {"en": "North West", "de": "Nord-West"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_pitch"}, "unit": "DEGREE_OF_ARC", "title": {"en": "PV pitch", "de": "PV-Neigung"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "pv_battery_size"}, "unit": "KILOWATT_HOUR", "title": {"en": "PV battery capacity", "de": "PV-Batteriekapazität"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-misc", "icon": "mdiNoteText", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-misc"}], "title": {"en": "Other information", "de": "Sonstige Infos"}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_energy_consumption_known"}, "title": {"en": "Is the energy consumption known?", "de": "Verbrauch der letzten 36 <PERSON><PERSON> be<PERSON>nt?"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "upload_energy_consumption_data"}, "title": {"en": "Upload energy consumption data?", "de": "Verbrauchsdaten hochladen?"}, "visibility_condition": {"field_id": {"field_name": "is_energy_consumption_known"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "ArrayUIElement", "title": {"en": "Energy consumption", "de": "Verbrauchsdaten der letzten 36 Monate"}, "description": {"en": "", "de": "Bitte für die letzten 3 Jahre jeweils in 12-Monats-Zeiträumen den Energieverbrauch angeben."}, "visibility_condition": {"field_id": {"field_name": "upload_energy_consumption_data"}, "op": "eq", "value": false, "operator_type": "RFO"}, "min_count": 0, "max_count": 3, "elements": [{"element": {"pattern_type": "DateUIElement", "type": "YM", "required": true, "field_id": {"field_name": "energy_consumption_from"}, "title": {"en": "From", "de": "<PERSON>"}, "minimum": "-5Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "DateUIElement", "type": "YM", "required": true, "field_id": {"field_name": "energy_consumption_to"}, "title": {"en": "To", "de": "Bis"}, "minimum": "-5Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "energy_consumption_value"}, "unit": "KILOWATT_HOUR", "title": {"en": "Consumption", "de": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": true, "field_id": {"field_name": "energy_consumption_unit"}, "title": {"en": "Unit", "de": "Verbrauchseinheit"}}}]}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Energy consumption data", "de": "Verbrauchsdaten"}, "min_count": 0, "max_count": 10, "required": false, "id_field_id": {"field_name": "energy_consumption_file_id"}, "caption_field_id": {"field_name": "energy_consumption_file_caption"}, "visibility_condition": {"field_id": {"field_name": "upload_energy_consumption_data"}, "op": "eq", "value": true, "operator_type": "RFO"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "inhabitants_presence_type"}, "title": {"en": "Presence of inhabitants", "de": "Anwesenheit der Nutzer"}, "options": [{"key": "ALL_DAY_EVERY_DAY", "label": {"en": "All day, every day", "de": "Ganz<PERSON><PERSON><PERSON>g, jeden Tag"}}, {"key": "EVENINGS_AND_WEEKENDS", "label": {"en": "Evenings and weekends", "de": "Abends und am Wochenende (berufstätig)"}}, {"key": "ONLY_WEEKENDS", "label": {"en": "Only weekends", "de": "Nur am Wochenende"}}, {"key": "ONLY_BUSINESS_HOURS", "label": {"en": "Only during working hours", "de": "Nur werktags"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "hot_water_usage"}, "title": {"en": "Hot water usage", "de": "Warmwassernutzung"}, "options": [{"key": "LOW", "label": {"en": "Low use of hot water", "de": "Geringe Warmwassernutzung"}}, {"key": "NORMAL", "label": {"en": "Normal use", "de": "Normale Warmwassernutzung"}}, {"key": "DAILY_SHOWERS_AND_BATHS", "label": {"en": "Daily showers and baths", "de": "Tägliches Duschen und Baden"}}, {"key": "HIGH_DUE_TO_SWIMMINGPOOL_AND_SAUNA", "label": {"en": "High usage due to swimmingpool and sauna", "de": "Hohe Nutzung wegen Sauna und Schwimmbad"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "ventilation_behaviour"}, "title": {"en": "Ventilation behaviour", "de": "Wie wird gelüftet?"}, "options": [{"key": "TILT_VENTILATION", "label": {"en": "Tilt ventilating windows", "de": "Kipplüften"}}, {"key": "BURST_VENTILATION", "label": {"en": "Ventilating in bursts", "de": "Stoßlüften"}}, {"key": "TILT_AND_BURST_VENTILATION", "label": {"en": "Tilt and burst ventilation", "de": "Kipp- und Stoßlüften"}}, {"key": "VENTILATION_SYSTEM", "label": {"en": "Ventilation system", "de": "Lüftungsanlage"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "customer_wishes"}, "title": {"en": "Customer wishes", "de": "Kundenwünsche"}, "description": {"en": "", "de": "Hier bitte Kundenwünsche / Vorstellungen von der Energieberatung notieren"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "notes"}, "title": {"en": "Notes", "de": "Notizen"}, "description": {"en": "", "de": "<PERSON>er bitte alle sonstigen relevanten Punkte erfassen"}}}]}], "pages_view": [{"pattern_type": "CustomUIElement", "id": "view-general", "icon": "mdiHome", "layout": "2_COL_RIGHT_WIDER", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-general"}], "short_title": {"en": "Building", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M_NARROW", "id_field_value": {"field_id": {"field_name": "covershot_image_id"}}}}]}}, {"element": {"pattern_type": "FieldTextUIElement", "type": "HEADING", "field_value": {"field_id": {"field_name": "ext_id"}}}}, {"element": {"pattern_type": "FieldTextUIElement", "type": "PARAGRAPH", "field_value": {"field_id": {"field_name": "sub_type"}}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": null, "key": {"en": "Building width", "de": "Gebäudebreite"}, "field_value": {"field_id": {"field_name": "outline_width"}}}, {"icon": null, "key": {"en": "Building depth", "de": "Gebäudetiefe"}, "field_value": {"field_id": {"field_name": "outline_depth"}}}, {"icon": null, "key": {"en": "Ceiling and slab thickness", "de": "Boden- und Deckenstärke"}, "field_value": {"field_id": {"field_name": "ceiling_thickness"}}}]}}, {"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-view", "related_custom_ui_element_id": "model-edit", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWindowClosed", "field_id": {"field_name": "building_poi_is_window_note"}, "default_value": false, "title": {"en": "Window", "de": "<PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiAddBox", "field_id": {"field_name": "building_poi_is_special_thermal_component"}, "default_value": false, "title": {"en": "Special thermal envelope component", "de": "Sonderbauteil thermische Hülle"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiHomeRoof", "field_id": {"field_name": "building_poi_is_dormer"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgaube"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Details", "de": "Fensterdetails"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_window_note"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Glazing", "de": "Verglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach Verglasung"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach Verglasung"}}, {"key": "DOUBLE_THERMAL", "label": {"en": "Double thermal insulation glazing", "de": "2-fach Wärmeschutzverglasung"}}, {"key": "TRIPLE_THERMAL", "label": {"en": "Triple thermal insulation glazing", "de": "3-fach Wärmeschutzverglasung"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "window_shutter_type"}, "title": {"en": "Window shutters/blinds", "de": "Fensterläden/Rollläden/Raffstores"}, "options": [{"key": "NONE", "label": {"en": "None", "de": "<PERSON><PERSON>"}}, {"key": "SHUTTERS", "label": {"en": "Window shutters", "de": "Fensterläden"}}, {"key": "ROLLER_BLINDS", "label": {"en": "Roller blinds", "de": "Rollläden"}}, {"key": "VENETIAN_BLINDS", "label": {"en": "Venetian blinds", "de": "Raffstores"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Window Position", "de": "Fensterposition"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_inner_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Inner reveal depth", "de": "Laibungstiefe innen"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_thickness_cm"}, "unit": "CENTIMETER", "title": {"en": "Window thickness", "de": "Fensterstärke"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "window_outer_reveal_depth_cm"}, "unit": "CENTIMETER", "title": {"en": "Outer reveal depth", "de": "Laibungstiefe außen"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Details", "de": "Details"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_special_thermal_component"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "StringUIElement", "required": false, "field_id": {"field_name": "special_thermal_component_material"}, "title": {"en": "Material", "de": "Material"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "minimum": "-100Y", "maximum": "-0Y"}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "special_thermal_component_u_value"}, "unit": "WATT_PER_SQUARE_METER_PER_KELVIN", "title": {"en": "U-value", "de": "U-Wert"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Dormer details", "de": "Dachgauben-Details"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_width_cm"}, "unit": "CENTIMETER", "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "dormer_height_cm"}, "unit": "CENTIMETER", "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Primary Type", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}, {"key": "SPECIAL_TYPE", "label": {"en": "Special heater", "de": "Sonderbauform"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_sub_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "eq", "value": "PANEL_RADIATOR", "operator_type": "RFO"}, "title": {"en": "Secondary Type", "de": "Subtyp"}, "options": [{"key": "TYPE_10", "label": {"en": "Typ 10", "de": "Typ 10"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_11", "label": {"en": "Typ 11", "de": "Typ 11"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_20", "label": {"en": "Typ 20", "de": "Typ 20"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_21", "label": {"en": "Typ 21", "de": "Typ 21"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_22", "label": {"en": "Typ 22", "de": "Typ 22"}, "icon": "mdiRadiatorDisabled"}, {"key": "TYPE_23", "label": {"en": "Typ 23", "de": "Typ 23"}, "icon": "mdiRadiatorDisabled"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "unit": "CENTIMETER", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "unit": "CENTIMETER", "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_radiator_type"}, "op": "ne", "value": "PANEL_RADIATOR", "operator_type": "RFO"}]}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "radiator_ventildurchmesser"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Valve diameter", "de": "DN (Ventildurchmesser)"}, "options": [{"key": "MM_10", "label": {"en": "10mm", "de": "10mm"}, "icon": "mdiRuler"}, {"key": "MM_15", "label": {"en": "15mm", "de": "15mm"}, "icon": "mdiRuler"}, {"key": "MM_20", "label": {"en": "20mm", "de": "20mm"}, "icon": "mdiRuler"}, {"key": "MM_25", "label": {"en": "25mm", "de": "25mm"}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please upload at least one photo of the radiator and one photo of the valve.", "de": "Bitte mindestens Heizkörperfoto und Großaufnahme vom Ventil hochladen"}}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "visibility_condition": {"field_id": {"field_name": "building_poi_is_dormer"}, "op": "eq", "value": true, "operator_type": "RFO"}, "text": {"en": "Please add a photo from the outside and from the inside.", "de": "Bitte Foto von außen und von Innen hinzufügen"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 5, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WALL_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Inside dimensions", "de": "Innenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "inside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outside dimensions", "de": "Außenmaße"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiArrowExpandHorizontal", "key": {"en": "Length", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_wall_length"}}}, {"icon": "mdiWall", "key": {"en": "Area (net)", "de": "Fläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Area (gross)", "de": "Fläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"key": {"en": "Roller Shutters", "de": "Rollladen"}, "field_value": {"field_id": {"field_name": "has_roller_shutters"}}, "icon": "mdiW<PERSON>owShutter"}, {"key": {"en": "Roller Shutters", "de": "Rollladen"}, "field_value": {"field_id": {"field_name": "has_roller_shutters"}}, "icon": "mdiW<PERSON>owShutter"}, {"key": {"en": "Glazing", "de": "Verglasung"}, "field_value": {"field_id": {"field_name": "window_glazing_type"}}, "icon": "mdiWindowClosedVariant"}, {"key": {"en": "U-Value", "de": "<PERSON><PERSON><PERSON>lter U-Wert"}, "field_value": {"field_id": {"field_name": "window_u_value"}}, "icon": "mdiAlphaU"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "WINDOW_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Heated room", "de": "Beheizter Raum"}, "field_value": {"field_id": {"field_name": "is_room_heated"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room temperature", "de": "Raumtemperatur"}, "field_value": {"field_id": {"field_name": "room_temperature_type"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room category", "de": "Raumkategorie"}, "field_value": {"field_id": {"field_name": "room_category"}}, "icon": "mdiNotebookEditOutline"}]}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Room height", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_height"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "ROOM_GROUP", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Total storeys", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Full storeys", "de": "Vollgeschosse"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Rooms", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "TableUIElement", "display_position": "RIGHT", "hide_index_column": true, "columns": [{"header": {"en": "Storey", "de": "Stckw."}, "field_value": {"field_id": {"field_name": "storey_name"}}}, {"header": {"en": "Name", "de": "Name"}, "field_value": {"field_id": {"field_name": "room_name"}}}, {"header": {"en": "NGF", "de": "NGF"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"header": {"en": "Heated", "de": "Beheizt"}, "field_value": {"field_id": {"field_name": "is_room_heated"}}}, {"header": {"en": "Temperature", "de": "Temperatur"}, "field_value": {"field_id": {"field_name": "room_temperature_type"}}}, {"header": {"en": "Heating", "de": "Heizung"}, "field_value": {"field_id": {"field_name": "room_heating_string"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Building pictures", "de": "<PERSON><PERSON><PERSON>ude<PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT"}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "building_pictures_id"}}}}]}}, {"element": {"pattern_type": "GroupUIElement", "display_position": "RIGHT", "title": {"en": "Building weaknesses", "de": "Gebäudeschwachstellen"}, "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "weakness_building_pictures_id"}}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-cellar", "icon": "mdiHeatingCoil", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-cellar"}], "title": {"en": "Cellar and heat-generator", "de": "Keller und Heizung"}, "elements": [{"element": {"pattern_type": "TextUIElement", "type": "HEADING", "display_position": "RIGHT", "text": {"en": "Heat generator pictures", "de": "Fotos der Heizungsanlage"}}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "heat_generator_pictures_id"}}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "display_position": "RIGHT", "text": {"en": "Water buffer storage pictures", "de": "Fotos des Pufferspeichers"}}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "water_buffer_storage_pictures_id"}}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Cellar and ground plate", "de": "Keller und Bodenplatte"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Cellar type", "de": "Kellertyp"}, "field_value": {"field_id": {"field_name": "cellar_type"}}}, {"key": {"en": "Heated cellar area", "de": "Beheizte Kellerfläche"}, "field_value": {"field_id": {"field_name": "heated_cellar_area"}}}, {"key": {"en": "Cellar entry", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "cellar_entry_location"}}}, {"key": {"en": "Cellar ceiling insulation", "de": "Kellerdeckendämmung"}, "field_value": {"field_id": {"field_name": "cellar_ceiling_insulation_thickness"}}}, {"key": {"en": "Cellar wall insulation", "de": "Kellerwanddämmung"}, "field_value": {"field_id": {"field_name": "cellar_wall_insulation_thickness"}}}, {"key": {"en": "Cellar ceiling type", "de": "Kellerdeckentyp"}, "field_value": {"field_id": {"field_name": "cellar_ceiling_type"}}}, {"key": {"en": "U-Value ground plate", "de": "U-Wert Bodenplatte"}, "field_value": {"field_id": {"field_name": "u_value_ground_plate"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Heat generator", "de": "Heizung"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Heat-generator type", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "heating_type"}}}, {"key": {"en": "Location", "de": "Stellplatz"}, "field_value": {"field_id": {"field_name": "heat_generator_location"}}}, {"key": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON> "}, "field_value": {"field_id": {"field_name": "heat_generator_year_of_construction"}}}, {"key": {"en": "Thermal output", "de": "Le<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "heat_generator_thermal_output"}}}, {"key": {"en": "Heat-generator pump", "de": "Heizungspumpe"}, "field_value": {"field_id": {"field_name": "heating_pump_type"}}}, {"key": {"en": "Nightly shutdown", "de": "Nachtabschaltung"}, "field_value": {"field_id": {"field_name": "heat_generator_overnight_shutdown_type"}}}, {"key": {"en": "Size water buffer storage", "de": "Pufferspeichergröße"}, "field_value": {"field_id": {"field_name": "water_buffer_storage_size"}}}, {"key": {"en": "Warmwater creation", "de": "Warmwasserbereitung"}, "field_value": {"field_id": {"field_name": "water_heating_type"}}}, {"key": {"en": "Warmwater creation system", "de": "Warmwassersystem"}, "field_value": {"field_id": {"field_name": "decentral_water_heating_type"}}}, {"key": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "year_of_construction_decentral_water_heating"}}}, {"key": {"en": "Radiators share", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "share_radiators"}}}, {"key": {"en": "Underfloor-heating share", "de": "Anteil <PERSON>heizung"}, "field_value": {"field_id": {"field_name": "share_underfloor_heating"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-storeys", "icon": "mdiWall", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-storeys"}], "title": {"en": "Storeys", "de": "Geschosse"}, "elements": [{"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Walls", "de": "Wände"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Wall construction type", "de": "Wandaufbautyp"}, "field_value": {"field_id": {"field_name": "wall_construction_type"}}}, {"key": {"en": "Wall thickness", "de": "Wandstärke"}, "field_value": {"field_id": {"field_name": "wall_construction_wall_thickness"}}}, {"key": {"en": "Outside wall insulation", "de": "Außenwanddämmung"}, "field_value": {"field_id": {"field_name": "outside_wall_insulation_thickness"}}}, {"key": {"en": "Building is sealed", "de": "Gebäude ist dicht"}, "field_value": {"field_id": {"field_name": "is_building_tight"}}}, {"key": {"en": "Blower-door test", "de": "Luftdichtheitsprüfung (BD-Test) durchgeführt"}, "field_value": {"field_id": {"field_name": "is_blower_door_tested"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Storeys", "de": "Geschosse"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Number of storeys", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "floor_count"}}}, {"key": {"en": "Clearance height", "de": "Lichte Höhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Other storeys", "de": "<PERSON><PERSON><PERSON> Geschosshöhen"}, "visibility_condition": {"field_id": {"field_name": "has_other_ceiling_heights"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "TableUIElement", "columns": [{"header": {"en": "Storey", "de": "Stockwerk"}, "field_value": {"field_id": {"field_name": "storey_number_other_ceiling_height"}}}, {"header": {"en": "Clearance height", "de": "Lichte Höhe"}, "field_value": {"field_id": {"field_name": "other_ceiling_height"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Other features", "de": "Weitere Merkmale"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "PV present", "de": "PV vorhanden"}, "field_value": {"field_id": {"field_name": "has_photovoltaics_installation"}}}, {"key": {"en": "Solar-thermal present", "de": "Solarthermie vorhanden"}, "field_value": {"field_id": {"field_name": "has_solar_installation"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-roofstorey", "icon": "mdiHomeRoof", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-<PERSON><PERSON>y"}], "title": {"en": "Roof storey", "de": "Dachgeschoss"}, "elements": [{"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Roof type and insulation", "de": "Dachtyp & Dämmung"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Attic type", "de": "Dachbodentyp"}, "field_value": {"field_id": {"field_name": "attic_type"}}}, {"key": {"en": "Top storey ceiling insulation thickness", "de": "Dämmungsstärke oberste Geschossdecke"}, "field_value": {"field_id": {"field_name": "top_storey_ceiling_insulation_thickness"}}}, {"key": {"en": "Roof pitch insulation thickness", "de": "Dämmungsstärke der Dachschrägen"}, "field_value": {"field_id": {"field_name": "roof_pitch_insulation_thickness"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Roof construction & dimensions", "de": "Dachkonstruktion & Maße"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Roof pitch", "de": "Dachneigung"}, "field_value": {"field_id": {"field_name": "roof_pitch"}}}, {"key": {"en": "Roof rafter thickness", "de": "Dachsparrenstärke"}, "field_value": {"field_id": {"field_name": "roof_rafter_thickness"}}}, {"key": {"en": "Roof height", "de": "Dachhöhe"}, "field_value": {"field_id": {"field_name": "roof_height"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "<PERSON><PERSON><PERSON>", "de": "Dachgauben"}, "visibility_condition": {"field_id": {"field_name": "has_dormers"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "TableUIElement", "type": "TABLE", "columns": [{"header": {"en": "Type", "de": "Art"}, "field_value": {"field_id": {"field_name": "dormer_type"}}}, {"header": {"en": "Count", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "dormer_amount"}}}, {"header": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "field_value": {"field_id": {"field_name": "dormer_width"}}}, {"header": {"en": "Height", "de": "Traufhöhe"}, "field_value": {"field_id": {"field_name": "dormer_height"}}}, {"header": {"en": "Roof height", "de": "Firsthöhe"}, "field_value": {"field_id": {"field_name": "dormer_roof_height"}}}, {"header": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "dormer_length"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Roof characteristics", "de": "Dachmerkmale"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Attic presence", "de": "Spitzboden vorhanden"}, "field_value": {"field_id": {"field_name": "has_attic"}}}, {"key": {"en": "Attic height", "de": "Spitzbodenhöhe"}, "field_value": {"field_id": {"field_name": "attic_height"}}}, {"key": {"en": "Kneeling wall presence", "de": "Kniestock vorhanden"}, "field_value": {"field_id": {"field_name": "has_kneeling"}}}, {"key": {"en": "Knee height", "de": "Kniestockhöhe"}, "field_value": {"field_id": {"field_name": "knee_height"}}}, {"key": {"en": "Kneeling offset inwards", "de": "Kniestock nach innen versetzt"}, "field_value": {"field_id": {"field_name": "kneeling_offset_inwards"}}}, {"key": {"en": "Roof window frame material", "de": "Material der Dachfensterrahmen"}, "field_value": {"field_id": {"field_name": "roof_window_frame_material_type"}}}, {"key": {"en": "Roof window glazing", "de": "Verglasung der Dachfenster"}, "field_value": {"field_id": {"field_name": "roof_window_glazing_type"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-renewables", "icon": "mdiSolarPowerVariant", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-renewables"}], "visibility_condition": {"operator": "OR", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "has_solar_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "has_photovoltaics_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}]}, "title": {"en": "Renewable energies", "de": "Erneuerbare Energien"}, "elements": [{"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Solar thermal", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "has_solar_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Type", "de": "Art der Anlage"}, "field_value": {"field_id": {"field_name": "solar_collector_type"}}}, {"key": {"en": "Usage", "de": "Verwendung"}, "field_value": {"field_id": {"field_name": "solar_collector_usage_type"}}}, {"key": {"en": "Collector area", "de": "Kollektorfläche"}, "field_value": {"field_id": {"field_name": "solar_collector_area"}}}, {"key": {"en": "Cardinal direction", "de": "Himmelsausrichtung"}, "field_value": {"field_id": {"field_name": "solar_collector_cardinal_direction_type"}}}, {"key": {"en": "Collector pitch", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "solar_collector_pitch"}}}, {"key": {"en": "Collector storage size", "de": "Größe des Solarwärmespeichers"}, "field_value": {"field_id": {"field_name": "solar_collector_waterstorage"}}}, {"key": {"en": "Cold water temperature", "de": "Vorlauftemperatur (Kaltwasser)"}, "field_value": {"field_id": {"field_name": "solar_collector_cold_water_temp"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Photovoltaics", "de": "Photovoltaik"}, "visibility_condition": {"field_id": {"field_name": "has_photovoltaics_installation"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Type of PV installation", "de": "Art der Anlage"}, "field_value": {"field_id": {"field_name": "pv_type"}}}, {"key": {"en": "Type of ventilation for PV", "de": "Belüftung"}, "field_value": {"field_id": {"field_name": "pv_ventilation_type"}}}, {"key": {"en": "PV area", "de": "Kollektorfläche"}, "field_value": {"field_id": {"field_name": "pv_area"}}}, {"key": {"en": "Electric rating", "de": "Elektrische Leistung"}, "field_value": {"field_id": {"field_name": "pv_output"}}}, {"key": {"en": "Cardinal direction", "de": "Himmelsausrichtung"}, "field_value": {"field_id": {"field_name": "pv_cardinal_direction_type"}}}, {"key": {"en": "Pitch", "de": "Neigung"}, "field_value": {"field_id": {"field_name": "pv_pitch"}}}, {"key": {"en": "Battery capacity", "de": "Batteriekapazität"}, "field_value": {"field_id": {"field_name": "pv_battery_size"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-misc", "icon": "mdiNoteText", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-misc"}], "title": {"en": "Other information", "de": "Sonstige Infos"}, "elements": [{"element": {"pattern_type": "TextUIElement", "display_position": "RIGHT", "type": "HEADING", "text": {"en": "Energy consumption", "de": "Verbräuche der letzten 36 Monate"}}}, {"element": {"pattern_type": "TableUIElement", "display_position": "RIGHT", "columns": [{"header": {"en": "From", "de": "<PERSON>"}, "field_value": {"field_id": {"field_name": "energy_consumption_from"}}}, {"header": {"en": "To", "de": "Bis"}, "field_value": {"field_id": {"field_name": "energy_consumption_to"}}}, {"header": {"en": "Consumption", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "energy_consumption_value"}}}, {"header": {"en": "Unit", "de": "Einheit"}, "field_value": {"field_id": {"field_name": "energy_consumption_unit"}}}]}}, {"element": {"pattern_type": "TextUIElement", "display_position": "RIGHT", "type": "HEADING", "text": {"en": "Energy consumption", "de": "Verbrauchsnachweise"}}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "M", "display_position": "RIGHT", "id_field_value": {"field_id": {"field_name": "energy_consumption_file_id"}}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Usage behaviour", "de": "Nutzungsverhalten"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Is the energy consumption known?", "de": "Verbrauch der letzten 36 <PERSON><PERSON> be<PERSON>nt?"}, "field_value": {"field_id": {"field_name": "is_energy_consumption_known"}}}, {"key": {"en": "Presence of inhabitants", "de": "Anwesenheit der Nutzer"}, "field_value": {"field_id": {"field_name": "inhabitants_presence_type"}}}, {"key": {"en": "Hot water usage", "de": "Warmwassernutzung"}, "field_value": {"field_id": {"field_name": "hot_water_usage"}}}, {"key": {"en": "Ventilation behaviour", "de": "Wie wird gelüftet?"}, "field_value": {"field_id": {"field_name": "ventilation_behaviour"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Customer wishes", "de": "Kundenwünsche"}}}, {"element": {"pattern_type": "FieldTextUIElement", "field_value": {"field_id": {"field_name": "customer_wishes"}}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Notes", "de": "Notizen"}}}, {"element": {"pattern_type": "FieldTextUIElement", "field_value": {"field_id": {"field_name": "notes"}}}}]}, {"pattern_type": "CustomUIElement", "id": "location", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-address"}], "type": "LOCATION", "icon": "mdiMapMarker", "short_title": {"en": "Location", "de": "Lage"}, "elements": []}, {"pattern_type": "CustomUIElement", "id": "admin-boundary", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-address"}], "type": "ADMIN_BOUNDARY", "icon": "mdiHomeGroup", "short_title": {"en": "Administrative boundary", "de": "Nachbarschaft"}, "elements": []}]}