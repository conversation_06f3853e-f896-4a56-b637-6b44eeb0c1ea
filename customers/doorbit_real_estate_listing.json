{"id": "doorbit_real_estate_listing", "url-key": "listing-realestate", "name": "Doorbit Immobilienexposé", "title": {"en": "Listing", "de": "Immobilienexposé"}, "icon": "mdiHome", "pages_edit": [{"pattern_type": "CustomUIElement", "id": "edit-start", "short_title": {"en": "Create exposé", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Administration", "de": "Verwaltung"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "display_position": "LEFT", "required": true, "field_id": {"field_name": "custom_status_type"}, "default": "PLANNED", "title": {"en": "Status", "de": "Status"}, "options": [{"key": "PLANNED", "label": {"en": "Planned", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "IN_PROGRESS", "label": {"en": "In progress", "de": "In Bearbeitung"}}, {"key": "DONE", "label": {"en": "Done", "de": "Abgeschlossen"}}, {"key": "ARCHIVED", "label": {"en": "Archived", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "display_position": "RIGHT", "required": true, "field_id": {"field_name": "main_type"}, "title": {"en": "What kind of property is it?", "de": "Um welche Art von Immobilie handelt es sich?"}, "options": [{"key": "HOUSE", "icon": "mdiHome", "label": {"en": "House", "de": "Haus"}}, {"key": "APARTMENT", "icon": "mdiOfficeBuilding", "label": {"en": "Apartment", "de": "<PERSON><PERSON><PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "display_position": "RIGHT", "required": true, "field_id": {"field_name": "offer_type"}, "title": {"en": "What do you want to do?", "de": "Was möchtest Du tun?"}, "options": [{"key": "SELLING", "label": {"en": "<PERSON>ll", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "RENTING", "label": {"en": "Rent", "de": "Vermietung"}}]}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "INTEGER", "required": true, "field_id": {"field_name": "price"}, "title": {"en": "Price", "de": "Kaufpreis"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "SELLING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "DOUBLE", "required": true, "field_id": {"field_name": "condo_fee"}, "title": {"en": "Condo fee", "de": "Hausgeld"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "SELLING", "operator_type": "RFO"}, {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}]}}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "INTEGER", "required": true, "field_id": {"field_name": "price"}, "title": {"en": "Rent", "de": "Warmmiete"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "DOUBLE", "required": true, "field_id": {"field_name": "additional_rent_costs"}, "title": {"en": "Additional rent costs", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "DOUBLE", "required": true, "field_id": {"field_name": "cold_rent"}, "title": {"en": "Cold rent", "de": "Kaltmiete"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "display_position": "RIGHT", "type": "DOUBLE", "required": true, "field_id": {"field_name": "security_deposit"}, "title": {"en": "Deposit or cooperative shares", "de": "Kaution oder Genossenschaftsanteile"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "DateUIElement", "display_position": "RIGHT", "type": "YMD", "required": true, "field_id": {"field_name": "earliest_move_in_date"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}, "title": {"en": "Earliest move-in date", "de": "Frühester Bezugstermin"}, "minimum": "-1M", "maximum": "+1Y"}}, {"element": {"pattern_type": "BooleanUIElement", "display_position": "RIGHT", "required": false, "field_id": {"field_name": "is_suitable_for_flatshare"}, "default_value": false, "title": {"en": "Suitable for flatshare", "de": "WG-geeignet"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "BooleanUIElement", "display_position": "RIGHT", "required": false, "field_id": {"field_name": "pet_allowance"}, "default_value": false, "title": {"en": "Pets allowed", "de": "<PERSON><PERSON><PERSON><PERSON> erlaubt"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "BooleanUIElement", "display_position": "RIGHT", "required": false, "field_id": {"field_name": "is_user_inside_property"}, "default_value": false, "title": {"en": "Are you at the property at the moment?", "de": "Befindest Du dich gerade vor Or<PERSON>?"}}}]}, {"pattern_type": "CustomUIElement", "type": "ADDRESS", "id": "edit-address", "related_pages": [{"viewing_context": "VIEW", "page_id": "location"}], "short_title": {"en": "Location", "de": "Lage"}, "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_zipcode"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_city"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_street"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "address_house_number"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_latitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "address_longitude"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_latitude_obfuscated"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": false, "field_id": {"field_name": "address_longitude_obfuscated"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "address_show_obfuscated_location"}, "default_value": false, "title": {"en": "Hide address?", "de": "<PERSON>resse verbergen?"}, "description": {"en": "If the address is hidden, it won't be publicly visible. Instead, an approximate location will be displayed.", "de": "Wird die Adresse verborgen, ist sie nicht öffentlich sichtbar. Stattdessen wird ein ungefährer Standort präsentiert."}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-building", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-building"}], "layout": "2_COL_RIGHT_FILL", "short_title": {"en": "Building", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-edit", "related_custom_ui_element_id": "model-view", "display_position": "RIGHT", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightSwitch", "field_id": {"field_name": "building_poi_is_light_switch"}, "default_value": false, "title": {"en": "Light switch", "de": "Lichtschalter"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiPowerSocketDe", "field_id": {"field_name": "building_poi_is_wall_socket"}, "default_value": false, "title": {"en": "Wall socket", "de": "Steckdose"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Type", "de": "<PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 3, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Heating information", "de": "Angaben zur Beheizung"}, "description": {"en": "Select how the room is heated. A room can also be heated by adjacent rooms if the rooms are connected by a breakthrough.", "de": "<PERSON><PERSON><PERSON><PERSON> aus, wie der Raum beheizt ist. Ein Raum kann auch durch angrenzende Räume mitbeheizt sein, wenn die Räume mittels Durchbruch verbunden sind."}, "elements": [{"element": {"pattern_type": "BooleanUIElement", "required": true, "field_id": {"field_name": "is_room_heated"}, "default_value": true, "title": {"en": "Is room heated", "de": "Ist der Raum beheizt?"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_temperature_type"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Approx. room temperature", "de": "Ungefähre Raumtemperatur"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room (ca. 20°C)", "de": "Wohnraum (ca. 20°C)"}}, {"key": "BATHROOM", "label": {"en": "Bathroom (ca. 24°C)", "de": "Bad<PERSON>immer (ca. 24°C)"}}, {"key": "PASSAGE_ROOM", "label": {"en": "Passage room (ca. 15°C)", "de": "Flur / Abstellraum (ca. 15°C)"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "More details", "de": "<PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "room_category"}, "title": {"en": "Room category", "de": "Raumkategorie"}, "options": [{"key": "LIVING_ROOM", "label": {"en": "Living room", "de": "<PERSON><PERSON><PERSON><PERSON>"}}, {"key": "KITCHEN", "label": {"en": "Kitchen", "de": "<PERSON><PERSON><PERSON>"}}, {"key": "OPEN_KITCHEN", "label": {"en": "Open kitchen", "de": "<PERSON><PERSON>nkü<PERSON>"}}, {"key": "BEDROOM", "label": {"en": "Bedroom", "de": "Schlaf-/Kinderzimmer"}}, {"key": "BATHROOM_GUEST_WC", "label": {"en": "Bathroom/WC", "de": "<PERSON><PERSON><PERSON>/<PERSON>"}}, {"key": "HOMEOFFICE", "label": {"en": "Homeoffice", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "OTHER_LIVING_SPACE", "label": {"en": "Other living space (e.g. hobby room)", "de": "<PERSON><PERSON> (z. B. <PERSON>, Fitnessraum, Hallenbad)"}}, {"key": "WINTERGARDEN", "label": {"en": "Winter garden", "de": "Wintergarten"}}, {"key": "HALLWAY_STAIRS", "label": {"en": "Hallway/stairs", "de": "Flur/Treppenhaus"}}, {"key": "STORAGE", "label": {"en": "Storage", "de": "Abstellraum"}}, {"key": "UTILITY_ROOM", "label": {"en": "Utility room", "de": "Hauswirtschaftsraum"}}, {"key": "HEATING_ROOM", "label": {"en": "Heating room", "de": "Heizungsraum"}}, {"key": "GARAGE", "label": {"en": "Garage", "de": "Garage"}}, {"key": "ATTIC", "label": {"en": "Attic", "de": "Dachboden"}}]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "room_name"}, "title": {"en": "Custom room name", "de": "Eigener Raumname"}, "description": {"en": "Will be generated from the room category if nothing is maintained.", "de": "Wird aus der Raumkategorie erzeugt, wenn nichts gepflegt wird."}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Room height", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_height"}}}, {"key": {"en": "Room perimeter", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, net)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, gross)", "de": "Wand<PERSON><PERSON><PERSON><PERSON> (innen, brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}]}}]}}]}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "window_glazing_type"}, "title": {"en": "Window glazing", "de": "Fensterverglasung"}, "options": [{"key": "SINGLE", "label": {"en": "Single glazing", "de": "1-fach"}}, {"key": "DOUBLE", "label": {"en": "Double glazing", "de": "2-fach"}}, {"key": "DOUBLE_ISOLATED", "label": {"en": "Double glazing with thermal insulation", "de": "2-fach (Wärmeschutzverglasung)"}}, {"key": "TRIPLE", "label": {"en": "Triple glazing", "de": "3-fach (Wärmeschutzverglasung)"}}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "window_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "door_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H × T)", "de": "Maße (L × H × S)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiTextureBox", "key": {"en": "Wall area (inside)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside)", "de": "Wandfläche (außen, netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "U-Value", "de": "U-Wert"}, "description": {"en": "Can also be calculated with the U-Value Assistant from the process form.", "de": "Kann auch mit dem U-Wert Assistenten aus dem Prozessformular ermittelt werden."}, "elements": [{"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "wall_u_value"}, "title": {"en": "U-Value", "de": "U-Wert"}, "icon": "mdiAlphaU"}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "floor_type"}, "title": {"en": "Storey type", "de": "Geschossart"}, "options": [{"key": "FULL_STOREY", "label": {"en": "Full storey", "de": "Vollgeschoss"}}, {"key": "CELLAR", "label": {"en": "Cellar", "de": "Untergeschoss/Keller"}}, {"key": "ROOF_STOREY", "label": {"en": "Roof storey", "de": "Dachgeschoss"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"key": {"en": "Storey type", "de": "Geschossart"}, "field_value": {"field_id": {"field_name": "floor_type"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Storey", "de": "Geschoss"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, net)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, gross)", "de": "Wand<PERSON><PERSON><PERSON><PERSON> (innen, brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, net)", "de": "Wandfläche (außen, netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, gross)", "de": "Wandflä<PERSON> (außen, brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Number of windows", "de": "<PERSON><PERSON><PERSON> der Fenster"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "..thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Number of doors", "de": "<PERSON><PERSON><PERSON> der Türen"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Bruttogrundfläche (BGF)", "de": "Bruttogrundfläche (BGF)"}, "field_value": {"field_id": {"field_name": "brutto_grundflaeche"}}}, {"icon": "mdiCube", "key": {"en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (BRI)"}, "field_value": {"field_id": {"field_name": "brutto_rauminhalt"}}}, {"icon": "mdiHeatingCoil", "key": {"en": "Technikfläche (TF)", "de": "Technikfläche (TF)"}, "field_value": {"field_id": {"field_name": "technik_flaeche"}}}, {"icon": "mdiWalk", "key": {"en": "Verkehrsfläche (VF)", "de": "Verkehrsfläche (VF)"}, "field_value": {"field_id": {"field_name": "verkehrs_flaeche"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": true, "field_id": {"field_name": "year_of_construction"}, "title": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "maximum": "+6M"}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "sub_type"}, "title": {"en": "Building type", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "DETACHED", "label": {"en": "Detached house", "de": "Einfamilienhaus"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "SEMI_DETACHED", "label": {"en": "Semi-detached house", "de": "Doppelhaushälfte"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "BUNGALOW", "label": {"en": "Bungalow", "de": "Bungalow"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "TOWNHOUSE", "label": {"en": "Townhouse", "de": "Re<PERSON>enhaus"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "VILLA", "label": {"en": "Villa", "de": "Villa"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "CASTLE", "label": {"en": "Castle", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "COUNTRY_HOUSE", "label": {"en": "Country house", "de": "Landhaus"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"key": "FLOOR", "label": {"en": "Floor", "de": "Etagenwohnung"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "GROUND_FLOOR", "label": {"en": "Ground floor", "de": "Erdgeschosswohnung"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "RAISED_GROUND_FLOOR", "label": {"en": "Raised ground floor", "de": "Hochparterre"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "DUPLEX", "label": {"en": "Duplex", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "PENTHOUSE", "label": {"en": "Penthouse", "de": "Penthouse"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "LOFT", "label": {"en": "Loft", "de": "Loft"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}, {"key": "BELOW_GROUND_FLOOR", "label": {"en": "Below ground floor", "de": "Souterrain"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "space_area"}, "title": {"en": "Living space", "de": "Wohnfläche"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "plot_area"}, "title": {"en": "Plot area", "de": "Grundstücksfläche"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "room_count"}, "title": {"en": "Room count", "de": "<PERSON><PERSON>"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "floor"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}, "title": {"en": "Storey", "de": "Stockwerk"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_elevator"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}, "title": {"en": "Elevator", "de": "Aufzug"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "bathroom_count"}, "title": {"en": "Bathrooms", "de": "<PERSON><PERSON><PERSON>"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "floor_count"}, "title": {"en": "Storeys", "de": "Anzahl der Etagen"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "kitchen_concept_type"}, "title": {"en": "Kitchen type", "de": "Küchentyp"}, "options": [{"key": "OPEN", "label": {"en": "Open kitchen", "de": "<PERSON><PERSON> / <PERSON>"}}, {"key": "CLOSED", "label": {"en": "Closed kitchen", "de": "Geschlossene Küche"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_monument_protection"}, "default_value": false, "title": {"en": "Monument protection", "de": "Besteht Denkmalschutz?"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "SELLING", "operator_type": "RFO"}}}, {"element": {"pattern_type": "DateUIElement", "type": "Y", "required": true, "field_id": {"field_name": "last_upgrade_year"}, "title": {"en": "Year of last renovation", "de": "Letzte Modernisierung"}, "maximum": "-0Y"}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_balcony_or_terrace"}, "title": {"en": "Is a terrace or balcony present?", "de": "Terrasse oder Balkon vorhanden?"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "balcony_count"}, "visibility_condition": {"field_id": {"field_name": "has_balcony_or_terrace"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Balcony count", "de": "<PERSON><PERSON><PERSON>"}}}]}, {"pattern_type": "CustomUIElement", "id": "edit-energy", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-energy"}], "short_title": {"en": "Energy", "de": "Energie"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Energy rating", "de": "Energiebedarf"}, "isCollapsible": false, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_efficiency_class"}, "title": {"en": "Energy efficiency class", "de": "Energieeffizienzklasse"}, "options": [{"key": "A_PLUS", "label": {"en": "A+", "de": "A+"}}, {"key": "A", "label": {"en": "A", "de": "A"}}, {"key": "B", "label": {"en": "B", "de": "B"}}, {"key": "C", "label": {"en": "C", "de": "C"}}, {"key": "D", "label": {"en": "D", "de": "D"}}, {"key": "E", "label": {"en": "E", "de": "E"}}, {"key": "F", "label": {"en": "F", "de": "F"}}, {"key": "G", "label": {"en": "G", "de": "G"}}, {"key": "H", "label": {"en": "H", "de": "H"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "DOUBLE", "required": true, "field_id": {"field_name": "energy_consumption"}, "title": {"en": "Energy consumption", "de": "Energieverbrauch"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_pass_type"}, "title": {"en": "Energy certificate type", "de": "Art des Energieausweises"}, "options": [{"key": "DEMAND_PASS", "label": {"en": "Demand pass", "de": "Bedarfsaus<PERSON><PERSON>"}}, {"key": "CONSUMPTION_PASS", "label": {"en": "Consumption pass", "de": "Verbrauchsaus<PERSON>s"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Heating", "de": "Heizung"}, "isCollapsible": false, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "heating_type"}, "title": {"en": "Heating type", "de": "Heizungsart"}, "options": [{"key": "LOW_TEMPERATURE_BOILER", "label": {"en": "Low temperature boiler", "de": "Niedertemperatur Kessel"}}, {"key": "CONDENSING_BOILER", "label": {"en": "Condensing boiler", "de": "Brennwerttherme"}}, {"key": "FURNACE", "label": {"en": "Standard furnace without regulation", "de": "Standardkessel"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "HEAT_PUMP", "label": {"en": "Heat pump", "de": "Wärmepumpe"}}, {"key": "CENTRAL_HEATING", "label": {"en": "Central heating", "de": "Zentralheizung"}}, {"key": "BLOCK_HEATING", "label": {"en": "Block heating", "de": "Blockheizkraftwerk"}}, {"key": "ELECTRIC_HEATING", "label": {"en": "Electric heating", "de": "Stromdirektheizung"}}, {"key": "ELECTRIC_HEATING_WITH_STORAGE", "label": {"en": "Eletric storage heating", "de": "Stromspeicherheizung"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}], "other_user_value": {"activates_on_value_selection": "OTHER", "text_ui_element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": false, "field_id": {"field_name": "heating_type_other"}}}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "energy_source"}, "title": {"en": "Primary energy source", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "options": [{"key": "GAS", "label": {"en": "Gas", "de": "Gas"}}, {"key": "OIL", "label": {"en": "Oil", "de": "<PERSON><PERSON>"}}, {"key": "PELLETS", "label": {"en": "Pellets", "de": "Pellets"}}, {"key": "DISTANCE_HEATING", "label": {"en": "Distance heating", "de": "Fernwärme"}}, {"key": "WOOD", "label": {"en": "<PERSON>", "de": "<PERSON><PERSON>z"}}, {"key": "ELECTRICITY", "label": {"en": "Electricity", "de": "<PERSON><PERSON>"}}, {"key": "ENVIRONMENTAL", "label": {"en": "Environmental", "de": "Umweltwärme (Luft, Wasser, Sonne, Biomasse, etc.)"}}, {"key": "OTHER", "label": {"en": "Other", "de": "Sonstige"}}]}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ventilation_system"}, "default_value": false, "title": {"en": "Ventilation system", "de": "Belüftungsanlage"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_photovoltaics_installation"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Photovoltaics installation", "de": "Photovoltaikanlage"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_solar_installation"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Solar installation", "de": "<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_green_roof"}, "default_value": false, "title": {"en": "Green roof", "de": "Begr<PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "electric_car_charger"}, "default_value": false, "title": {"en": "Electric car charger", "de": "Elektroauto-Ladestation"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "electric_car_charger_type"}, "title": {"en": "Charger type", "de": "Ladestationstyp"}, "visibility_condition": {"field_id": {"field_name": "electric_car_charger"}, "op": "eq", "value": true, "operator_type": "RFO"}, "options": [{"key": "WALLBOX_AC_7KW", "label": {"en": "until 7kW", "de": "bis 7kW"}}, {"key": "WALLBOX_AC_11KW", "label": {"en": "until 11kW", "de": "bis 11kW"}}, {"key": "WALLBOX_AC_22KW", "label": {"en": "until 22kW", "de": "bis 22kW"}}, {"key": "WALLBOX_DC_50KW", "label": {"en": "until 50kW", "de": "bis 50kW"}}, {"key": "WALLBOX_DC_100KW", "label": {"en": "until 100kW", "de": "bis 100kW"}}, {"key": "WALLBOX_DC_150KW", "label": {"en": "until 150kW", "de": "bis 150kW"}}, {"key": "WALLBOX_DC_350KW", "label": {"en": "until 350kW", "de": "bis 350kW"}}]}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-features", "icon": "mdiListBoxOutline", "related_pages": [{"viewing_context": "VIEW", "page_id": "view-overview"}], "short_title": {"en": "Features", "de": "Ausstattung"}, "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Cellar & attic", "de": "Keller & Dachboden"}, "isCollapsible": false, "elements": [{"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "cellar_type"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "title": {"en": "Basement", "de": "<PERSON>"}, "options": [{"key": "AVAILABLE", "label": {"en": "Available", "de": "Vorhand<PERSON>"}}, {"key": "FULL_CELLAR", "label": {"en": "Full cellar", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "PARTIAL_CELLAR", "label": {"en": "Partial cellar", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "NO_CELLAR", "label": {"en": "No cellar", "de": "<PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "attic_type"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "title": {"en": "Attic", "de": "Dachboden"}, "options": [{"key": "AVAILABLE", "label": {"en": "Available as storage space", "de": "Als Stauraum nutzbar"}}, {"key": "DEVELOPED_ATTIC", "label": {"en": "Fully developed", "de": "Vollständig ausgebaut"}}, {"key": "PARTIALLY_DEVELOPED_ATTIC", "label": {"en": "Partially developed", "de": "Teilweise ausgebaut"}}, {"key": "NO_ATTIC", "label": {"en": "No attic", "de": "<PERSON><PERSON>"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_cellar_compartment"}, "default_value": false, "title": {"en": "Cellar compartment", "de": "Kellerabteil"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_attic_compartment"}, "default_value": false, "title": {"en": "Attic compartment", "de": "Dachbodenabteil"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Indoor features", "de": "Innenausstattung"}, "isCollapsible": false, "elements": [{"element": {"pattern_type": "ChipGroupUIElement", "required": false, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_sauna"}, "default_value": false, "title": {"en": "Sauna", "de": "Sauna"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indoor_pool"}, "default_value": false, "title": {"en": "Indoor pool", "de": "Hallenbad"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fireplace", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_air_condition"}, "default_value": false, "title": {"en": "Air condition", "de": "<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "barrier_free"}, "default_value": false, "title": {"en": "Barrier-free", "de": "Barrierefrei"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_security_system"}, "default_value": false, "title": {"en": "Security system", "de": "<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_shower"}, "default_value": false, "title": {"en": "Shower", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_bathtub"}, "default_value": false, "title": {"en": "Bathtub", "de": "<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_built_in_kitchen"}, "default_value": false, "title": {"en": "Built-in kitchen", "de": "Einbauküche"}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "furniture_type"}, "title": {"en": "Furniture", "de": "Möblierung"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}, "options": [{"key": "UNFURNISHED", "label": {"en": "Unfurnished", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "FURNISHED", "label": {"en": "Furnished", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"key": "PARTIALLY_FURNISHED", "label": {"en": "Partially furnished", "de": "Te<PERSON>m<PERSON><PERSON><PERSON><PERSON>"}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Outdoor features", "de": "Außenausstattung"}, "isCollapsible": false, "elements": [{"element": {"pattern_type": "ChipGroupUIElement", "required": false, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_balcony"}, "default_value": false, "title": {"en": "Balcony", "de": "Balkon"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_terrace"}, "default_value": false, "title": {"en": "Terrace", "de": "Terrasse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_outdoor_pool"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Swimmingpool", "de": "Swimmingpool"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_pond"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Pond", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_cistern"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "HOUSE", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Cistern", "de": "<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_garden"}, "visibility_condition": {"field_id": {"field_name": "main_type"}, "op": "eq", "value": "APARTMENT", "operator_type": "RFO"}, "default_value": false, "title": {"en": "Garden", "de": "Garten zur Mitbenutzung"}}]}}, {"element": {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_parking_space"}, "default_value": false, "title": {"en": "Parking space", "de": "Parkplatz"}}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": false, "field_id": {"field_name": "parking_space_type"}, "visibility_condition": {"field_id": {"field_name": "has_parking_space"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Parking space type", "de": "Parkplatztyp"}, "options": [{"key": "UNDERGROUND_PARKING_SPACE", "label": {"en": "Underground garage", "de": "<PERSON><PERSON>garage"}}, {"key": "GARAGE", "label": {"en": "Garage", "de": "Garage"}}, {"key": "CARPORT", "label": {"en": "Carport", "de": "Carport"}}, {"key": "PARKING_SPACE", "label": {"en": "Parking space", "de": "Eigener Parkplatz"}}, {"key": "COMMUNITY_PARKING_LOT", "label": {"en": "Shared parking lot", "de": "Gemeinschaftl. Parkplatz"}}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": true, "visibility_condition": {"field_id": {"field_name": "has_parking_space"}, "op": "eq", "value": true, "operator_type": "RFO"}, "field_id": {"field_name": "parking_space_count"}, "title": {"en": "Number of parking spaces", "de": "<PERSON><PERSON><PERSON>"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "edit-descriptions", "short_title": {"en": "Descriptions and photos", "de": "Texte und Fotos"}, "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"en": "Photos", "de": "Fotos"}, "min_count": 0, "max_count": 50, "required": false, "id_field_id": {"field_name": "building_pictures_id"}, "caption_field_id": {"field_name": "building_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT", "required": true, "field_id": {"field_name": "title"}, "title": {"en": "Title", "de": "Titel"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "listing_description"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "location_description"}, "title": {"en": "Location description", "de": "Lagebeschreibung"}}}]}], "pages_view": [{"pattern_type": "CustomUIElement", "id": "view-overview", "icon": "mdiHome", "layout": "2_COL_RIGHT_WIDER", "short_title": {"en": "Overview", "de": "Übersicht"}, "elements": [{"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "L", "display_position": "RIGHT", "show_dummy_on_empty_gallery": false}}, {"element": {"pattern_type": "ImageGalleryUIElement", "preferred_size": "L", "display_position": "RIGHT", "show_dummy_on_empty_gallery": false, "id_field_value": {"field_id": {"field_name": "building_pictures_id"}}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "COUNTER_BAR", "items": [{"icon": null, "key": {"en": "Price", "de": "Kaufpreis"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "SELLING", "operator_type": "RFO"}, "field_value": {"field_id": {"field_name": "price"}}}, {"icon": null, "key": {"en": "Rent", "de": "Warmmiete"}, "visibility_condition": {"field_id": {"field_name": "offer_type"}, "op": "eq", "value": "RENTING", "operator_type": "RFO"}, "field_value": {"field_id": {"field_name": "warm_rent"}}}, {"icon": null, "key": {"en": "Year of construction", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "year_of_construction"}}}, {"icon": null, "key": {"en": "Living area", "de": "Wohnfläche"}, "field_value": {"field_id": {"field_name": "space_area"}}}, {"icon": null, "key": {"en": "Room count", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_count"}}}, {"icon": null, "key": {"en": "Energy efficiency class", "de": "Energieeffizienzklasse"}, "field_value": {"field_id": {"field_name": "energy_efficiency_class"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "FieldTextUIElement", "field_value": {"field_id": {"field_name": "listing_description"}}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Indoor features", "de": "Innenausstattung"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Type", "de": "Art"}, "field_value": {"field_id": {"field_name": "sub_type"}}}, {"key": {"en": "Plot area", "de": "Grundstücksfläche"}, "field_value": {"field_id": {"field_name": "plot_area"}}}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Furniture", "de": "Möblierung"}, "field_value": {"field_id": {"field_name": "furniture_type"}}}, {"key": {"en": "Sauna", "de": "Sauna"}, "field_value": {"field_id": {"field_name": "has_sauna"}}}, {"key": {"en": "Indoor pool", "de": "Hallenbad"}, "field_value": {"field_id": {"field_name": "has_indoor_pool"}}}, {"key": {"en": "Fireplace", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_fire_place"}}}, {"key": {"en": "Air condition", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_air_condition"}}}, {"key": {"en": "Barrier-free", "de": "Barrierefrei"}, "field_value": {"field_id": {"field_name": "barrier_free"}}}, {"key": {"en": "Security system", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_security_system"}}}, {"key": {"en": "Shower", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_shower"}}}, {"key": {"en": "Bathtub", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_bathtub"}}}, {"icon": null, "key": {"en": "Kitchen type", "de": "Küchentyp"}, "field_value": {"field_id": {"field_name": "kitchen_concept_type"}}}, {"key": {"en": "Built-in kitchen", "de": "Einbauküche"}, "field_value": {"field_id": {"field_name": "has_built_in_kitchen"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Outdoor features", "de": "Außenausstattung"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Parking space", "de": "Parkplatz"}, "field_value": {"field_id": {"field_name": "has_parking_space"}}}, {"key": {"en": "Parking space type", "de": "Parkplatztyp"}, "field_value": {"field_id": {"field_name": "parking_space_type"}}}, {"key": {"en": "Number of parking spaces", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "parking_space_count"}}}, {"key": {"en": "Balcony", "de": "Balkon"}, "field_value": {"field_id": {"field_name": "has_balcony"}}}, {"key": {"en": "Terrace", "de": "Terrasse"}, "field_value": {"field_id": {"field_name": "has_terrace"}}}, {"key": {"en": "Swimmingpool", "de": "Swimmingpool"}, "field_value": {"field_id": {"field_name": "has_outdoor_pool"}}}, {"key": {"en": "Pond", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_pond"}}}, {"key": {"en": "Cistern", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_cistern"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-building", "icon": "mdiFloorPlan", "layout": "2_COL_RIGHT_FILL", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-building"}], "short_title": {"en": "Building", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "elements": [{"element": {"pattern_type": "CustomUIElement", "type": "SCANNER", "id": "model-view", "related_custom_ui_element_id": "model-edit", "display_position": "RIGHT", "sub_flows": [{"type": "POI_PHOTO", "elements": [{"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {}, "min_count": 0, "max_count": 1, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": []}}]}, {"type": "POI", "elements": [{"element": {"pattern_type": "StringUIElement", "type": "TEXT_AREA", "required": false, "field_id": {"field_name": "building_poi_text"}, "title": {"en": "Description", "de": "Beschreibung"}}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdi<PERSON><PERSON><PERSON>", "field_id": {"field_name": "building_poi_is_defect"}, "default_value": false, "title": {"en": "Defect", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiWrench", "field_id": {"field_name": "building_poi_is_todo"}, "default_value": false, "title": {"en": "Todo", "de": "Todo"}}]}}, {"element": {"visibility_condition": {"field_id": {"field_name": "building_poi_is_todo"}, "op": "eq", "value": true, "operator_type": "RFO"}, "pattern_type": "DateUIElement", "type": "YMD", "required": false, "field_id": {"field_name": "building_poi_deadline_date"}, "title": {"en": "To be done until", "de": "Zu erledigen bis"}, "minimum": "-1Y"}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiRadiator", "field_id": {"field_name": "building_poi_is_radiator"}, "default_value": false, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Heizk<PERSON><PERSON><PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightSwitch", "field_id": {"field_name": "building_poi_is_light_switch"}, "default_value": false, "title": {"en": "Light switch", "de": "Lichtschalter"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiPowerSocketDe", "field_id": {"field_name": "building_poi_is_wall_socket"}, "default_value": false, "title": {"en": "Wall socket", "de": "Steckdose"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_lamp"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}, {"pattern_type": "BooleanUIElement", "required": false, "icon": "mdiLightbulbOn", "field_id": {"field_name": "building_poi_is_lamp"}, "default_value": false, "title": {"en": "<PERSON><PERSON>", "de": "Lam<PERSON>"}, "visibility_condition": {"operator": "AND", "operator_type": "LO", "conditions": [{"field_id": {"field_name": "building_poi_is_radiator"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_light_switch"}, "op": "ne", "value": true, "operator_type": "RFO"}, {"field_id": {"field_name": "building_poi_is_wall_socket"}, "op": "ne", "value": true, "operator_type": "RFO"}]}}]}}, {"element": {"pattern_type": "SingleSelectionUIElement", "required": true, "field_id": {"field_name": "building_poi_radiator_type"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}, "title": {"en": "Type", "de": "<PERSON><PERSON>"}, "options": [{"key": "RADIATOR", "label": {"en": "<PERSON><PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "icon": "mdiFence"}, {"key": "PANEL_RADIATOR", "label": {"en": "Panel radiator", "de": "Plattenheizkörper"}, "icon": "mdiRadiatorDisabled"}, {"key": "TUBE_RADIATOR", "label": {"en": "Tube radiator", "de": "Badheizkörper"}, "icon": "mdiHeatingCoil"}, {"key": "CONVECTOR_HEATER", "label": {"en": "Convector heater", "de": "Konvektionsheizkörper"}, "icon": "mdiRadiator"}]}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_width"}, "title": {"en": "<PERSON><PERSON><PERSON>", "de": "Breite"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_height"}, "title": {"en": "Height", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "NumberUIElement", "type": "INTEGER", "required": false, "field_id": {"field_name": "building_poi_radiator_depth"}, "title": {"en": "De<PERSON><PERSON>", "de": "<PERSON><PERSON><PERSON>"}, "visibility_condition": {"field_id": {"field_name": "building_poi_is_radiator"}, "op": "eq", "value": true, "operator_type": "RFO"}}}, {"element": {"pattern_type": "FileUIElement", "file_type": "IMAGE", "title": {"de": "Fotos", "en": "Photos"}, "min_count": 0, "max_count": 3, "required": false, "id_field_id": {"field_name": "building_poi_pictures_id"}, "caption_field_id": {"field_name": "building_poi_pictures_caption"}, "allowed_file_types": ["image/jpeg", "image/png", "image/webp", "image/tiff"]}}]}, {"type": "WALL", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H × T)", "de": "Maße (L × H × S)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiTextureBox", "key": {"en": "Wall area (inside)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside)", "de": "Wandfläche (außen, netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}]}}]}}]}, {"type": "WALL_GROUP", "elements": []}, {"type": "WINDOW", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"key": {"en": "Glazing", "de": "Verglasung"}, "field_value": {"field_id": {"field_name": "window_glazing_type"}}, "icon": "mdiWindowClosedVariant"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "DOOR", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (W × H)", "de": "Maße (B × H)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}]}}]}}]}, {"type": "ROOM", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Heated room", "de": "Beheizter Raum"}, "field_value": {"field_id": {"field_name": "is_room_heated"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room temperature", "de": "Raumtemperatur"}, "field_value": {"field_id": {"field_name": "room_temperature_type"}}, "icon": "mdiHomeThermometerOutline"}, {"key": {"en": "Room category", "de": "Raumkategorie"}, "field_value": {"field_id": {"field_name": "room_category"}}, "icon": "mdiNotebookEditOutline"}]}}, {"element": {"pattern_type": "ChipGroupUIElement", "required": false, "title": {"en": "How is the room heated?", "de": "Wie ist der Raum beheizt?"}, "visibility_condition": {"field_id": {"field_name": "is_room_heated"}, "op": "eq", "value": true, "operator_type": "RFO"}, "chips": [{"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_radiators"}, "default_value": false, "title": {"en": "Radiators", "de": "Heizk<PERSON><PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_underfloor_heating"}, "default_value": false, "title": {"en": "Underfloor heating", "de": "Fußbodenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_fire_place"}, "default_value": false, "title": {"en": "Fire place", "de": "<PERSON><PERSON>"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_convector"}, "default_value": false, "title": {"en": "Convector/Fan", "de": "Konvektor/Gebläse"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_wall_heating"}, "default_value": false, "title": {"en": "Wall heating", "de": "Wandh<PERSON><PERSON>ng"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_ceiling_heating"}, "default_value": false, "title": {"en": "Ceiling heating", "de": "Deckenheizung"}}, {"pattern_type": "BooleanUIElement", "required": false, "field_id": {"field_name": "has_indirect_heating"}, "default_value": false, "title": {"en": "Indirect heating", "de": "Mitbeheizt"}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}]}}]}}]}, {"type": "FLOOR", "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Floorplan (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"key": {"en": "Storey type", "de": "Geschossart"}, "field_value": {"field_id": {"field_name": "floor_type"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Storey", "de": "Geschoss"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}, {"icon": "mdiHumanMaleHeight", "key": {"en": "Storey height", "de": "Stockwerkshöhe"}, "field_value": {"field_id": {"field_name": "storey_clearance_height"}}}, {"key": {"en": "Storey perimeter", "de": "Stockwerksumfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}, "icon": "mdiFloorPlan"}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, net)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, gross)", "de": "Wand<PERSON><PERSON><PERSON><PERSON> (innen, brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, net)", "de": "Wandfläche (außen, netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, gross)", "de": "Wandflä<PERSON> (außen, brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Number of windows", "de": "<PERSON><PERSON><PERSON> der Fenster"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "..thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Number of doors", "de": "<PERSON><PERSON><PERSON> der Türen"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": ".. thereof in exterior walls", "de": ".. davon in Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "FLOOR_SLAB", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}]}, {"type": "BUILDING", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Masses", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}, {"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "roof_area"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Storeys", "de": "Geschosse"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "thereof full storeys", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWall", "key": {"en": "Inside area (net)", "de": "Innenfläche (netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Inside area (gross)", "de": "Innenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (net)", "de": "Außenfläche (netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiWall", "key": {"en": "Outside area (gross)", "de": "Außenfläche (brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Windows", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Doors total", "de": "<PERSON><PERSON><PERSON> g<PERSON>t"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Outside doors", "de": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Outside door area", "de": "Außentürfläche"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}, {"type": "ROOF_AREA", "elements": [{"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiHomeRoof", "key": {"en": "Roof area", "de": "Dachfläche"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiHomeRoof", "key": {"en": "Perimeter", "de": "Umfang"}, "field_value": {"field_id": {"field_name": "slab_perimeter"}}}]}}]}}]}]}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": null, "key": {"en": "Room count", "de": "<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "room_count"}}}, {"icon": null, "key": {"en": "Bathrooms", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "bathroom_count"}}}, {"icon": null, "key": {"en": "Storeys", "de": "Etagen"}, "field_value": {"field_id": {"field_name": "floor_count"}}}, {"icon": null, "key": {"en": "Has monument protection", "de": "Denkmalschutz"}, "field_value": {"field_id": {"field_name": "has_monument_protection"}}}, {"icon": null, "key": {"en": "Year of last renovation", "de": "Letzte Modernisierung"}, "field_value": {"field_id": {"field_name": "last_upgrade_year"}}}, {"icon": null, "key": {"en": "Energy efficiency class", "de": "Energieeffizienzklasse"}, "field_value": {"field_id": {"field_name": "energy_efficiency_class"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Cellar & attic", "de": "Keller & Dachboden"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Cellar", "de": "<PERSON>"}, "field_value": {"field_id": {"field_name": "cellar_type"}}}, {"key": {"en": "Attic", "de": "Dachboden"}, "field_value": {"field_id": {"field_name": "attic_type"}}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Bill of quantities", "de": "Massedaten"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Dimensions (L × W)", "de": "Grundriss (L × B)"}, "field_value": {"field_id": {"field_name": "dimensions"}}, "icon": "mdiRuler"}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Storeys", "de": "Geschosse"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiFloorPlan", "key": {"en": "Total", "de": "Gesamt"}, "field_value": {"field_id": {"field_name": "storey_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Full storeys", "de": "Vollgeschosse"}, "field_value": {"field_id": {"field_name": "full_storey_count"}}}, {"icon": "mdiBalcony", "key": {"en": "Balconies", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "balcony_count"}}}, {"icon": "mdiFloorPlan", "key": {"en": "Nettogrundfläche (NGF)", "de": "Nettogrundfläche (NGF)"}, "field_value": {"field_id": {"field_name": "netto_raumflaeche"}}}, {"icon": "mdiCube", "key": {"en": "Netto-Rauminhalt (NRI)", "de": "Netto-Rauminhalt (NRI)"}, "field_value": {"field_id": {"field_name": "netto_rauminhalt"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Walls", "de": "Wände"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, net)", "de": "Wandfläche (innen, netto)"}, "field_value": {"field_id": {"field_name": "inside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (inside, gross)", "de": "Wand<PERSON><PERSON><PERSON><PERSON> (innen, brutto)"}, "field_value": {"field_id": {"field_name": "inside_area_gross"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, net)", "de": "Wandfläche (außen, netto)"}, "field_value": {"field_id": {"field_name": "outside_area_net"}}}, {"icon": "mdiTextureBox", "key": {"en": "Wall area (outside, gross)", "de": "Wandflä<PERSON> (außen, brutto)"}, "field_value": {"field_id": {"field_name": "outside_area_gross"}}}]}}]}}, {"element": {"pattern_type": "GroupUIElement", "title": {"en": "Windows & doors", "de": "Fenster & Türen"}, "elements": [{"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"icon": "mdiWindowClosedVariant", "key": {"en": "Number of windows", "de": "<PERSON><PERSON><PERSON> der Fenster"}, "field_value": {"field_id": {"field_name": "window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_count"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Window area", "de": "Fensterfläche"}, "field_value": {"field_id": {"field_name": "window_area"}}}, {"icon": "mdiWindowClosedVariant", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_window_area"}}}, {"icon": "mdiDoor", "key": {"en": "Number of doors", "de": "<PERSON><PERSON><PERSON> der Türen"}, "field_value": {"field_id": {"field_name": "door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_count"}}}, {"icon": "mdiDoor", "key": {"en": "Door area", "de": "Türfläche"}, "field_value": {"field_id": {"field_name": "door_area"}}}, {"icon": "mdiDoor", "key": {"en": "Thereof in exterior walls", "de": "In Außenwänden"}, "field_value": {"field_id": {"field_name": "outside_door_area"}}}]}}]}}]}}]}, {"pattern_type": "CustomUIElement", "id": "view-energy", "icon": "mdiHeatingCoil", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-energy"}], "short_title": {"en": "Energy", "de": "Energie"}, "elements": [{"element": {"pattern_type": "TextUIElement", "type": "HEADING", "display_position": "RIGHT", "text": {"en": "Renovation calculator", "de": "Sanierungsrechner"}}}, {"element": {"pattern_type": "TextUIElement", "type": "PARAGRAPH", "display_position": "RIGHT", "text": {"en": "Coming soon", "de": "Coming soon"}}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Energy rating", "de": "Energiebedarf"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Energy efficiency class", "de": "Energieeffizienzklasse"}, "field_value": {"field_id": {"field_name": "energy_efficiency_class"}}}, {"key": {"en": "Energy consumption", "de": "Energieverbrauch"}, "field_value": {"field_id": {"field_name": "energy_consumption"}}}, {"key": {"en": "Energy certificate type", "de": "Art des Energieausweises"}, "field_value": {"field_id": {"field_name": "energy_pass_type"}}}]}}, {"element": {"pattern_type": "TextUIElement", "type": "HEADING", "text": {"en": "Heating", "de": "Heizung"}}}, {"element": {"pattern_type": "KeyValueListUIElement", "type": "TABLE", "items": [{"key": {"en": "Heating type", "de": "Heizungsart"}, "field_value": {"field_id": {"field_name": "heating_type"}}}, {"key": {"en": "Primary energy source", "de": "<PERSON><PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "energy_source"}}}, {"key": {"en": "Underfloor heating", "de": "Fußbodenheizung"}, "field_value": {"field_id": {"field_name": "has_underfloor_heating"}}}, {"key": {"en": "Ventilation system", "de": "Belüftungsanlage"}, "field_value": {"field_id": {"field_name": "has_ventilation_system"}}}, {"key": {"en": "Photovoltaics installation", "de": "Photovoltaikanlage"}, "field_value": {"field_id": {"field_name": "has_photovoltaics_installation"}}}, {"key": {"en": "Solar installation", "de": "<PERSON><PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_solar_installation"}}}, {"key": {"en": "Green roof", "de": "Begr<PERSON><PERSON>"}, "field_value": {"field_id": {"field_name": "has_green_roof"}}}, {"key": {"en": "Electric car charger", "de": "Elektroauto-Ladestation"}, "field_value": {"field_id": {"field_name": "electric_car_charger"}}}, {"key": {"en": "Charger type", "de": "Ladestationstyp"}, "field_value": {"field_id": {"field_name": "electric_car_charger_type"}}}]}}]}, {"pattern_type": "CustomUIElement", "id": "location", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-address"}], "type": "LOCATION", "icon": "mdiMapMarker", "short_title": {"en": "Location", "de": "Lage"}, "elements": []}, {"pattern_type": "CustomUIElement", "id": "view-attractiveness", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-address"}], "type": "ATTRACTIVITY", "icon": "mdiMapSearch", "short_title": {"en": "Attractivity", "de": "Attraktivität"}, "elements": []}, {"pattern_type": "CustomUIElement", "id": "admin-boundary", "related_pages": [{"viewing_context": "EDIT", "page_id": "edit-address"}], "type": "ADMIN_BOUNDARY", "icon": "mdiHomeGroup", "short_title": {"en": "Administrative boundary", "de": "Nachbarschaft"}, "elements": []}]}