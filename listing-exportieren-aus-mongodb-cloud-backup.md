MongoDB-Backup runterladen (eig. handelt es sich um eine gesamte MongoDB-Instanz)

tar -xvzf ~/Downloads/XXXXXXXXXXXXX.tar.gz -C ~/Downloads/

docker run -d --name mongodb -p 27017:27017 -v ~/Downloads/XXXXXXXXXXXXX:/data/db mongo:6.0

# Falls man gucken will, ob alles geklappt hat

# docker exec -it mongodb mongosh

docker exec -i mongodb mongoexport --db=listing --collection=buildingDataHistory --query='{ "listingId": "XXXXXXXXXXXXX" }' --out=/listing.json

docker cp mongodb:/listing.json ~/Downloads/listing.json

JSON importieren in buildingDataHistory

"hasScannedBuilding: true" setzen am Listing in der DB

am Ende: docker stop mongodb && docker rm mongodb