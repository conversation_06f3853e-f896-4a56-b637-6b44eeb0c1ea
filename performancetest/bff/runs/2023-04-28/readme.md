# 28.04.2023

## Szenario 1: Tomcat Servlet API (blocking IO) with JDK 19 stable (no Loom)

- JDK 19 stable
- Spring Boot 3.0.0
- Tomcat Servlet API
- Max Tomcat Threads: 1000
- Log Level: WARN
- Max acceptable Median latency: 500ms
- Max acceptable p90 latency: 1200ms
- Max acceptable error rate: 0.00%

## Settings

- Duration: 2 minutes
- 2 routes, Nominatim Geocoding and Save Listing. 
- The adapter latency was simulated by replacing the actual backend calls by:
-- Thread.sleep(Random.nextInt(40, 300).toLong()) for CreateProperty 
-- Thread.sleep(Random.nextInt(30, 500).toLong()) for Nominatim

![img.png](01-threadgroup.png)

### Result

| KPI   | Value  |
|-------|--------|
| req/s | 1992   |
| error | 0.00%  |
| p90   | 1238ms |
| p50   | 540ms  |

PASS

### Attachments

![img.png](01-aggregate-results.png)


## Szenario 2: Tomcat Servlet API (blocking IO) BUT with JDK 19 preview mode enable (Project Loom)

- JDK 19 preview mode (Project Loom enabled)
- Rest same as 1

## Settings

- Duration: 2 minutes
- 2 routes, Nominatim Geocoding and Save Listing.
- The adapter latency was simulated by replacing the actual backend calls by:
  -- Thread.sleep(Random.nextInt(40, 300).toLong()) for CreateProperty
  -- Thread.sleep(Random.nextInt(30, 500).toLong()) for Nominatim

![img.png](01-threadgroup.png)

### Result

No difference. To be fair, the strenghts of Loom cannot be exploited really. Because a simulated latency using 
Thread.sleep() is not a real blocking IO operation. Project Loom has implemented significant optimizations on low API level
in HTTP clients, which we are not calling. We probably need to repeat the test with a real blocking IO operation.

But let's try to take over some Spring recommendations first and run another test.

| KPI   | Value  |
|-------|--------|
| req/s | 1992   |
| error | 0.00%  |
| p90   | 1238ms |
| p50   | 540ms  |

PASS

### Attachments

none

## Szenario 3: Same as 2, additionally with recommended Spring configuration to use Project Loom's virtual Threads for Tomcat Servlet API

- Tomcat is using Virtual Threads instead of platform threads
- Rest is the same than 2

## Settings

- Duration: 2 minutes
- 2 routes, Nominatim Geocoding and Save Listing.
- The adapter latency was simulated by replacing the actual backend calls by:
  -- Thread.sleep(Random.nextInt(40, 300).toLong()) for CreateProperty
  -- Thread.sleep(Random.nextInt(30, 500).toLong()) for Nominatim

![img.png](01-threadgroup.png)

### Result

Increased amount of throughput is several magnitudes of difference. More than 8.000 req/s. Rebuilding test to introduce 
real latency.

| KPI   | Value |
|-------|-------|
| req/s | 8200  |
| error | 0.00% |
| p90   | 600ms |
| p50   | 300ms |

INVALID?

### Attachments

## Szenario 4: Tomcat Servlet API (Without Loom), calling a real backend with "real" latency

- Tomcat Servlet API
- Acceptable error rate 0.5% to find sweet spot with max virtual users
- Rest identical
- 500 virtual users
- 60s ramp up

## Settings

- Duration: 2 minutes
- 1 route, Save Listing. Nominatim is the bottleneck, so we remove it.
- Create Property is calling the PropertyService on localhost, which calls GeoService on localhost too. 
- Added artifical latency to property service by calculating some prime numbers to max out CPU as limiting factor.
- BFF has to wait for that (real blocking I/O).

### Result



| KPI   | Value |
|-------|-------|
| req/s | 325   |
| error | 0.00% |
| p90   | 716ms |
| p50   | 509ms |

PASS

### Attachments

None


## Szenario 5: Tomcat Servlet API (With Loom), calling a real backend with "real" latency

- JDK 19 preview mode (Project Loom enabled)
- Tomcat is using Virtual Threads instead of platform threads
- Rest idential than 4

## Settings

- Duration: 2 minutes
- 1 route, Save Listing. Nominatim is the bottleneck, so we remove it.
- Create Property is calling the PropertyService on localhost, which calls GeoService on localhost too.
- Added artifical latency to property service by calculating some prime numbers to max out CPU as limiting factor. 
- BFF has to wait for that (real blocking I/O).

### Result


| KPI   | Value  |
|-------|--------|
| req/s | 325    |
| error | 0.02%  |
| p90   | 1090ms |
| p50   | 187ms  |

PASS

### Attachments

None